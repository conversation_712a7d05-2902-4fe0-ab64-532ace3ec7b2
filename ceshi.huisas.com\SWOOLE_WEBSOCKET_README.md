# 🚀 Swoole WebSocket 支付通知服务器

基于Swoole扩展开发的高性能WebSocket服务器，专门用于支付通知和语音播报功能。

## 📋 功能特性

- ✅ **高性能协程WebSocket服务**：基于Swoole协程，支持高并发
- ✅ **商户权限隔离**：基于merchant_id的连接分组管理
- ✅ **频道订阅系统**：支持多频道消息订阅
- ✅ **心跳检测机制**：自动检测和清理无效连接
- ✅ **HTTP API支持**：同时支持WebSocket和HTTP协议
- ✅ **支付语音播报**：集成语音合成功能
- ✅ **实时统计监控**：连接数、消息数等实时统计

## 🔧 环境要求

### 必要条件
- **PHP版本**：7.2+
- **Swoole扩展**：4.4.0+
- **操作系统**：Linux/Windows
- **内存**：建议512MB+

### 宝塔面板安装Swoole
1. 登录宝塔面板
2. 进入 **软件商店** → **PHP管理**
3. 选择对应PHP版本，点击 **设置**
4. 进入 **安装扩展** 选项卡
5. 找到 **swoole** 扩展，点击安装
6. 重启PHP服务

## 📁 文件结构

```
ceshi.huisas.com/
├── swoole_websocket_server.php     # 主服务器文件
├── start_swoole_websocket.php      # 简化启动脚本
├── test_swoole_websocket.html      # 前端测试页面
├── logs/                           # 日志目录
│   ├── swoole_websocket.log        # 服务器日志
│   └── swoole_websocket.pid        # 进程PID文件
└── SWOOLE_WEBSOCKET_README.md      # 本说明文档
```

## 🚀 快速开始

### 1. 启动服务器

#### 方法一：使用简化启动脚本
```bash
cd /path/to/ceshi.huisas.com
php start_swoole_websocket.php
```

#### 方法二：使用完整启动脚本
```bash
cd /path/to/ceshi.huisas.com
php swoole_websocket_server.php start
```

### 2. 验证服务器状态

#### 检查服务器是否启动
```bash
php swoole_websocket_server.php status
```

#### 访问健康检查接口
```bash
curl http://localhost:8080/health
```

#### 查看统计信息
```bash
curl http://localhost:8080/stats
```

### 3. 前端测试

打开浏览器访问：`http://localhost/test_swoole_websocket.html`

## 📡 API接口

### WebSocket连接
- **地址**：`ws://your-domain:8080`
- **协议**：JSON消息格式

### HTTP API接口

#### 1. 支付通知接口
```
POST /payment/notify
Content-Type: application/json

{
    "merchant_id": "123",
    "order_id": "ORDER123",
    "amount": "100.00",
    "status": "success",
    "staff_id": "456"  // 可选
}
```

#### 2. 健康检查接口
```
GET /health

响应：
{
    "status": "healthy",
    "timestamp": **********,
    "server_time": "2024-01-01 12:00:00",
    "connections": 10
}
```

#### 3. 统计信息接口
```
GET /stats

响应：
{
    "start_time": **********,
    "total_connections": 100,
    "current_connections": 10,
    "total_messages": 1000,
    "payment_notifications": 50,
    "uptime": 3600,
    "merchants_online": 5,
    "channels_active": 8,
    "memory_usage": 52428800,
    "memory_peak": 67108864
}
```

## 📨 WebSocket消息协议

### 客户端发送消息

#### 1. 认证消息
```json
{
    "type": "auth",
    "data": {
        "merchant_id": "123",
        "staff_id": "456",
        "token": "your_auth_token"
    }
}
```

#### 2. 订阅频道
```json
{
    "type": "subscribe",
    "data": {
        "channel": "merchant_123_payment"
    }
}
```

#### 3. 心跳消息
```json
{
    "type": "ping",
    "data": {
        "timestamp": **********
    }
}
```

### 服务器响应消息

#### 1. 欢迎消息
```json
{
    "type": "welcome",
    "data": {
        "connection_id": 1,
        "server_time": "2024-01-01 12:00:00",
        "app_key": "payment_websocket_2024",
        "message": "连接成功，请进行认证"
    }
}
```

#### 2. 认证结果
```json
{
    "type": "auth_result",
    "data": {
        "success": true,
        "message": "认证成功",
        "merchant_id": "123",
        "staff_id": "456",
        "connection_id": 1
    }
}
```

#### 3. 支付通知
```json
{
    "type": "payment_notification",
    "data": {
        "merchant_id": "123",
        "order_id": "ORDER123",
        "amount": "100.00",
        "status": "success",
        "timestamp": **********,
        "voice_text": "收到100.00元支付"
    }
}
```