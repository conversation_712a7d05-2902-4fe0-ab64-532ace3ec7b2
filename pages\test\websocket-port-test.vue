<template>
  <view class="container">
    <view class="header">
      <text class="title">🔧 WebSocket端口连接测试</text>
    </view>
    
    <view class="test-section">
      <view class="test-item">
        <text class="test-label">测试8080端口 (WebSocket)</text>
        <button @click="testPort8080" :disabled="testing8080" class="test-btn">
          {{ testing8080 ? '测试中...' : '测试连接' }}
        </button>
        <text :class="['status', result8080.status]">{{ result8080.message }}</text>
      </view>
      
      <view class="test-item">
        <text class="test-label">测试8081端口 (API)</text>
        <button @click="testPort8081" :disabled="testing8081" class="test-btn">
          {{ testing8081 ? '测试中...' : '测试连接' }}
        </button>
        <text :class="['status', result8081.status]">{{ result8081.message }}</text>
      </view>
    </view>
    
    <view class="info-section">
      <text class="info-title">📋 端口说明</text>
      <text class="info-text">• 8080: WebSocket服务端口 (前端连接)</text>
      <text class="info-text">• 8081: HTTP API端口 (后端通讯)</text>
      <text class="info-text">• 前端应该连接8080端口</text>
    </view>
    
    <view class="log-section">
      <text class="log-title">📝 测试日志</text>
      <scroll-view class="log-content" scroll-y>
        <text v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </text>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      testing8080: false,
      testing8081: false,
      result8080: { status: 'pending', message: '等待测试' },
      result8081: { status: 'pending', message: '等待测试' },
      logs: []
    }
  },
  
  methods: {
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString()
      this.logs.unshift(`[${timestamp}] ${message}`)
      if (this.logs.length > 50) {
        this.logs.pop()
      }
    },
    
    testPort8080() {
      this.testing8080 = true
      this.result8080 = { status: 'testing', message: '连接中...' }
      this.addLog('开始测试8080端口 (WebSocket)')
      
      const ws = uni.connectSocket({
        url: 'ws://ceshi.huisas.com:8080',
        success: () => {
          this.addLog('8080端口连接请求发送成功')
        },
        fail: (error) => {
          this.addLog(`8080端口连接请求失败: ${JSON.stringify(error)}`)
          this.result8080 = { status: 'error', message: '连接请求失败' }
          this.testing8080 = false
        }
      })
      
      // 设置超时
      const timeout = setTimeout(() => {
        this.addLog('8080端口连接超时')
        this.result8080 = { status: 'error', message: '连接超时' }
        this.testing8080 = false
        uni.closeSocket()
      }, 10000)
      
      ws.onOpen(() => {
        clearTimeout(timeout)
        this.addLog('8080端口连接成功！')
        this.result8080 = { status: 'success', message: '连接成功 ✅' }
        this.testing8080 = false
        uni.closeSocket()
      })
      
      ws.onError((error) => {
        clearTimeout(timeout)
        this.addLog(`8080端口连接错误: ${JSON.stringify(error)}`)
        this.result8080 = { status: 'error', message: '连接失败 ❌' }
        this.testing8080 = false
      })
      
      ws.onClose(() => {
        clearTimeout(timeout)
        this.addLog('8080端口连接已关闭')
        this.testing8080 = false
      })
    },
    
    testPort8081() {
      this.testing8081 = true
      this.result8081 = { status: 'testing', message: '连接中...' }
      this.addLog('开始测试8081端口 (WebSocket)')
      
      const ws = uni.connectSocket({
        url: 'ws://ceshi.huisas.com:8081',
        success: () => {
          this.addLog('8081端口连接请求发送成功')
        },
        fail: (error) => {
          this.addLog(`8081端口连接请求失败: ${JSON.stringify(error)}`)
          this.result8081 = { status: 'error', message: '连接请求失败' }
          this.testing8081 = false
        }
      })
      
      // 设置超时
      const timeout = setTimeout(() => {
        this.addLog('8081端口连接超时')
        this.result8081 = { status: 'error', message: '连接超时' }
        this.testing8081 = false
        uni.closeSocket()
      }, 10000)
      
      ws.onOpen(() => {
        clearTimeout(timeout)
        this.addLog('8081端口连接成功！')
        this.result8081 = { status: 'success', message: '连接成功 ✅' }
        this.testing8081 = false
        uni.closeSocket()
      })
      
      ws.onError((error) => {
        clearTimeout(timeout)
        this.addLog(`8081端口连接错误: ${JSON.stringify(error)}`)
        this.result8081 = { status: 'error', message: '连接失败 ❌' }
        this.testing8081 = false
      })
      
      ws.onClose(() => {
        clearTimeout(timeout)
        this.addLog('8081端口连接已关闭')
        this.testing8081 = false
      })
    }
  }
}
</script>

<style scoped>
.container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 10rpx rgba(0,0,0,0.3);
}

.test-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
}

.test-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
}

.test-label {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.test-btn {
  background: linear-gradient(45deg, #007AFF, #5856D6);
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.test-btn[disabled] {
  background: #ccc;
}

.status {
  font-size: 28rpx;
  font-weight: bold;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  text-align: center;
}

.status.pending {
  color: #666;
  background: #f0f0f0;
}

.status.testing {
  color: #007AFF;
  background: #e3f2fd;
}

.status.success {
  color: #34C759;
  background: #e8f5e8;
}

.status.error {
  color: #FF3B30;
  background: #ffeaea;
}

.info-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.info-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.log-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
}

.log-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.log-content {
  height: 400rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  padding: 20rpx;
}

.log-item {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
  font-family: monospace;
}
</style>
