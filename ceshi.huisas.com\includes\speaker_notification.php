<?php
/**
 * 收款音响播报功能
 * 路径: /includes/speaker_notification.php
 * 功能: 处理收款成功后的音响播报
 */

/**
 * 触发音响播报通知
 * @param int $order_id 订单ID
 * @return bool 是否成功触发播报
 */
function triggerSpeakerNotification($order_id) {
    global $DB, $dbconfig;
    
    try {
        // 获取订单信息
        $order = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_order` WHERE `id`=?", [$order_id]);
        if(!$order) {
            error_log("音响播报: 订单不存在 - Order ID: $order_id");
            return false;
        }
        
        // 获取收款码配置
        $qrconfig = null;
        if($order['qrcode_id']) {
            $qrconfig = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_qrcode_config` WHERE `id`=?", [$order['qrcode_id']]);
        }
        
        // 音响配置优先级：员工 > 收款码 > 商户
        $speaker_config = getSpeakerConfig($order, $qrconfig);
        
        if($speaker_config) {
            return playSpeakerNotification($speaker_config, $order);
        }
        
        return false;
        
    } catch(Exception $e) {
        error_log("音响播报异常: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取音响配置（按优先级）
 * @param array $order 订单信息
 * @param array $qrconfig 收款码配置
 * @return array|null 音响配置
 */
function getSpeakerConfig($order, $qrconfig) {
    global $DB, $dbconfig;
    
    // 1. 检查员工音响配置（最高优先级）
    if($qrconfig && $qrconfig['staff_id']) {
        $staff = $DB->getRow("SELECT `speaker_status`, `speaker_brand`, `speaker_config` 
            FROM `{$dbconfig['dbqz']}_staff` WHERE `id`=?", [$qrconfig['staff_id']]);
        
        if($staff && $staff['speaker_status'] && $staff['speaker_brand'] && $staff['speaker_config']) {
            error_log("音响播报: 使用员工配置 - Staff ID: {$qrconfig['staff_id']}");
            return $staff;
        }
    }
    
    // 2. 检查收款码音响配置
    if($qrconfig && $qrconfig['speaker_status'] && $qrconfig['speaker_brand'] && $qrconfig['speaker_config']) {
        error_log("音响播报: 使用收款码配置 - QR Config ID: {$qrconfig['id']}");
        return $qrconfig;
    }
    
    // 3. 检查商户默认音响配置
    $merchant = $DB->getRow("SELECT `speaker_status`, `speaker_brand`, `speaker_config` 
        FROM `{$dbconfig['dbqz']}_user` WHERE `uid`=?", [$order['uid']]);
    
    if($merchant && $merchant['speaker_status'] && $merchant['speaker_brand'] && $merchant['speaker_config']) {
        error_log("音响播报: 使用商户配置 - UID: {$order['uid']}");
        return $merchant;
    }
    
    error_log("音响播报: 未找到可用的音响配置 - Order ID: {$order['id']}");
    return null;
}

/**
 * 执行音响播报
 * @param array $config 音响配置
 * @param array $order 订单信息
 * @return bool 是否播报成功
 */
function playSpeakerNotification($config, $order) {
    try {
        $speaker_device_config = json_decode($config['speaker_config'], true);
        if(!$speaker_device_config || !isset($speaker_device_config['devid'])) {
            error_log("音响播报: 设备配置无效");
            return false;
        }
        
        $devid = $speaker_device_config['devid'];
        $money = $order['money'] / 100; // 转换为元
        $pay_type = $order['pay_type'];
        
        if($config['speaker_brand'] == 'pinsheng') {
            return playPinshengSpeaker($devid, $pay_type, $money, $order['out_trade_no']);
        } elseif($config['speaker_brand'] == 'bailihua') {
            return playBailihuaSpeaker($devid, $pay_type, $money);
        }
        
        error_log("音响播报: 不支持的音响品牌 - {$config['speaker_brand']}");
        return false;
        
    } catch(Exception $e) {
        error_log("音响播报执行异常: " . $e->getMessage());
        return false;
    }
}

/**
 * 品胜音响播报
 * @param string $devid 设备ID
 * @param string $pay_type 支付类型
 * @param float $money 金额
 * @param string $out_trade_no 订单号
 * @return bool
 */
function playPinshengSpeaker($devid, $pay_type, $money, $out_trade_no) {
    global $conf;
    
    // 检查系统配置
    if(empty($conf['pinsheng_username']) || empty($conf['pinsheng_password'])) {
        error_log("音响播报: 品胜音响系统配置不完整");
        return false;
    }
    
    try {
        // 这里需要根据实际的品胜音响SDK来实现
        // 示例代码：
        /*
        require_once '../includes/speaker/pinsheng.php';
        $speaker = new PinshengSpeaker($conf['pinsheng_username'], $conf['pinsheng_password']);
        $result = $speaker->play($devid, $pay_type, $money, $out_trade_no);
        */
        
        // 模拟播报
        $message = generateSpeakerMessage($pay_type, $money);
        error_log("品胜音响播报: 设备ID=$devid, 消息=$message");
        
        // 这里应该调用实际的API
        // return $speaker->play($devid, $pay_type, $money, $out_trade_no);
        
        return true; // 临时返回成功
        
    } catch(Exception $e) {
        error_log("品胜音响播报失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 百利华音响播报
 * @param string $devid 设备ID
 * @param string $pay_type 支付类型
 * @param float $money 金额
 * @return bool
 */
function playBailihuaSpeaker($devid, $pay_type, $money) {
    global $conf;
    
    // 检查系统配置
    if(empty($conf['bailihua_agent_id']) || empty($conf['bailihua_agent_secret'])) {
        error_log("音响播报: 百利华音响系统配置不完整");
        return false;
    }
    
    try {
        // 这里需要根据实际的百利华音响SDK来实现
        // 示例代码：
        /*
        require_once '../includes/speaker/bailihua.php';
        $speaker = new BailihuaSpeaker($conf['bailihua_agent_id'], $conf['bailihua_agent_secret']);
        $result = $speaker->play($devid, $pay_type, $money);
        */
        
        // 模拟播报
        $message = generateSpeakerMessage($pay_type, $money);
        error_log("百利华音响播报: 设备ID=$devid, 消息=$message");
        
        // 这里应该调用实际的API
        // return $speaker->play($devid, $pay_type, $money);
        
        return true; // 临时返回成功
        
    } catch(Exception $e) {
        error_log("百利华音响播报失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 生成播报消息
 * @param string $pay_type 支付类型
 * @param float $money 金额
 * @return string
 */
function generateSpeakerMessage($pay_type, $money) {
    $money = number_format($money, 2);
    
    switch($pay_type) {
        case 'alipay':
            return "支付宝到账{$money}元";
        case 'wxpay':
            return "微信收款{$money}元";
        case 'qqpay':
            return "QQ钱包收款{$money}元";
        default:
            return "收款{$money}元";
    }
}

/**
 * 测试音响播报
 * @param string $brand 音响品牌
 * @param string $devid 设备ID
 * @return bool
 */
function testSpeakerNotification($brand, $devid) {
    $test_config = [
        'speaker_status' => 1,
        'speaker_brand' => $brand,
        'speaker_config' => json_encode(['devid' => $devid])
    ];
    
    $test_order = [
        'id' => 0,
        'money' => 1, // 0.01元
        'pay_type' => 'alipay',
        'out_trade_no' => 'TEST' . time()
    ];
    
    return playSpeakerNotification($test_config, $test_order);
}

/**
 * 在支付回调中调用此函数
 * 使用示例：
 * 
 * // 在支付成功回调中添加
 * if($order_status == 'success') {
 *     // 更新订单状态等操作...
 *     
 *     // 触发音响播报
 *     triggerSpeakerNotification($order_id);
 * }
 */
?>
