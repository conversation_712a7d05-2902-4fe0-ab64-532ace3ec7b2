// 导航栏混入
import { getPageTheme, getThemeStyle } from '@/config/navbar-themes.js'

export default {
  data() {
    return {
      // 导航栏配置
      navbarConfig: {
        title: '标题',
        subtitle: '',
        showBack: false,
        showNotification: false,
        badgeCount: 0,
        theme: 'default',
        fixed: true,
        shadow: true
      }
    }
  },
  
  computed: {
    // 当前页面主题
    currentPageTheme() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const route = currentPage.route;
      
      // 从路由中提取页面名称
      const pageName = route.split('/').pop();
      return getPageTheme(pageName);
    },
    
    // 导航栏样式
    navbarStyle() {
      return getThemeStyle(this.navbarConfig.theme);
    }
  },
  
  methods: {
    // 设置导航栏标题
    setNavbarTitle(title, subtitle = '') {
      this.navbarConfig.title = title;
      this.navbarConfig.subtitle = subtitle;
    },
    
    // 设置返回按钮
    setNavbarBack(show = true) {
      this.navbarConfig.showBack = show;
    },
    
    // 设置通知按钮
    setNavbarNotification(show = true, badgeCount = 0) {
      this.navbarConfig.showNotification = show;
      this.navbarConfig.badgeCount = badgeCount;
    },
    
    // 设置导航栏主题
    setNavbarTheme(theme = 'default') {
      this.navbarConfig.theme = theme;
    },
    
    // 导航栏返回事件
    onNavbarBack() {
      this.handleNavbarBack();
    },
    
    // 导航栏通知事件
    onNavbarNotification() {
      this.handleNavbarNotification();
    },
    
    // 处理返回按钮点击（可在页面中重写）
    handleNavbarBack() {
      if (getCurrentPages().length > 1) {
        uni.navigateBack();
      } else {
        uni.switchTab({
          url: '/pages/index/index'
        });
      }
    },
    
    // 处理通知按钮点击（可在页面中重写）
    handleNavbarNotification() {
      uni.navigateTo({
        url: '/pages/xiaoxi/index'
      });
    },
    
    // 更新通知徽章数量
    updateNotificationBadge(count) {
      this.navbarConfig.badgeCount = count;
    },
    
    // 获取系统信息
    getSystemInfo() {
      return uni.getSystemInfoSync();
    },
    
    // 设置页面标题（同时设置导航栏和页面标题）
    setPageTitle(title) {
      this.setNavbarTitle(title);
      uni.setNavigationBarTitle({
        title: title
      });
    }
  },
  
  // 页面生命周期
  onLoad() {
    // 自动应用页面主题
    try {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        const route = currentPage.route;
        const pageName = route.split('/').pop();

        // 根据页面名称设置主题
        const themeMap = {
          'index': 'default',
          'bill': 'business',
          'scan': 'success',
          'report': 'purple',
          'mine': 'dark'
        };

        const themeName = themeMap[pageName] || 'default';
        this.navbarConfig.theme = themeName;
      }
    } catch (error) {
      console.warn('设置页面主题失败:', error);
      this.navbarConfig.theme = 'default';
    }
  },
  
  onShow() {
    // 页面显示时可以刷新通知数量等
    this.refreshNavbarData();
  },
  
  // 刷新导航栏数据（可在页面中重写）
  refreshNavbarData() {
    // 默认实现：获取未读消息数量
    this.getUnreadMessageCount();
  },
  
  // 获取未读消息数量（示例）
  async getUnreadMessageCount() {
    try {
      // 这里应该调用实际的API
      // const response = await this.$api.getUnreadCount();
      // this.updateNotificationBadge(response.count);
      
      // 模拟数据
      const count = uni.getStorageSync('unread_count') || 0;
      this.updateNotificationBadge(count);
    } catch (error) {
      console.error('获取未读消息数量失败:', error);
    }
  }
};
