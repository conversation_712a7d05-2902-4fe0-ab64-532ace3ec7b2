<template>
  <view class="container">
    <!-- 加载状态 -->
    <view v-if="pageLoading" class="loading-container">
      <text>页面加载中...</text>
    </view>
    
    <!-- 页面内容 -->
    <view v-else class="content">
      <text>用户信息: {{ userInfo?.username || '未获取' }}</text>
      <text>登录状态: {{ isLogin ? '已登录' : '未登录' }}</text>
      
      <button @click="logout">退出登录</button>
    </view>
  </view>
</template>

<script>
import pageAuthMixin from '@/mixins/page-auth'

export default {
  mixins: [pageAuthMixin],
  
  data() {
    return {
      // 页面特定的数据
      pageData: null
    }
  },
  
  async onLoad() {
    console.log('📱 示例页面加载');
    // 使用混入提供的方法初始化页面
    await this.initPageWithAuth(this.loadExamplePageData);
  },
  
  onShow() {
    console.log('📱 示例页面显示');
    // 使用混入提供的方法检查登录状态
    this.checkLoginOnShow();
  },
  
  methods: {
    // 页面特定的数据加载方法
    async loadExamplePageData() {
      try {
        console.log('📊 加载示例页面数据');
        
        // 这里可以调用页面特定的接口
        // const response = await api.getPageData();
        // this.pageData = response.data;
        
        console.log('✅ 示例页面数据加载完成');
        
      } catch (error) {
        console.error('❌ 加载示例页面数据失败:', error);
        throw error;
      }
    }
  }
}
</script>

<style>
.container {
  padding: 20rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
</style>
