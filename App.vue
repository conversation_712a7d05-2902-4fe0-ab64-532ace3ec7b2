<template>
  <view id="app">
  </view>
</template>

<script>
	import globalWebSocketService from '@/utils/globalWebSocketService.js'
	import voiceManager from '@/utils/voiceManager.js'

	export default {

		onLaunch: function() {
			console.log('🚀 App Launch')

			// 🔧 暂时禁用全局WebSocket服务，改用语音设置页面管理
			// this.initGlobalServices()
		},

		onShow: function() {
			console.log('👁️ App Show')

			// 🔧 暂时禁用全局WebSocket服务恢复
			// 改用语音设置页面统一管理WebSocket连接
		},

		onHide: function() {
			console.log('🙈 App Hide')

			// 🔧 暂时禁用全局WebSocket服务暂停
			// globalWebSocketService.pause()
		},

		onError: function(error) {
			console.error('❌ App Error:', error)
			// 可以在这里添加错误上报逻辑
		},

		methods: {
			/**
			 * 初始化全局服务
			 */
			async initGlobalServices() {
				try {
					console.log('🎵 初始化全局语音播报服务')

					// 检查语音设置
					const voiceStatus = voiceManager.getStatus()
					console.log('🔊 当前语音设置:', voiceStatus)

					// 🔧 暂时禁用全局WebSocket服务启动
					// await globalWebSocketService.start()

					console.log('✅ 全局服务初始化完成')

				} catch (error) {
					console.error('❌ 全局服务初始化失败:', error)
				}
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	page {
		font-family: 'Segoe UI', sans-serif;
		background-color: #f5f5f5;
	}

	/* 为底部导航腾出空间 */
	.page-container {
		padding-bottom: 120rpx;
	}

	/* 全局应用容器 */
	#app {
		position: relative;
		width: 100%;
		height: 100%;
	}
</style>
