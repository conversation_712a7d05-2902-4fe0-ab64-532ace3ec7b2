import{x as e,y as a,a as t,d as s,e as l,w as o,i as n,o as c,f as d,h as i,t as u,z as m,j as p,k as y,l as r,I as h,p as f}from"./index-B1Q521gi.js";import{r as k,_ as A}from"./uni-app.es.DAfa8VxY.js";import{_ as C}from"./custom-navbar.DuzuSmPc.js";import{_,a as b}from"./shanfu.Cee0BMqH.js";import{_ as w}from"./alipay.TVRnAsOv.js";import{_ as I}from"./_plugin-vue_export-helper.BCo6x5W8.js";const N=I({components:{CustomNavbar:C},data:()=>({amount:"0.00",note:"",showNoteInput:!1,selectedPaymentMethod:"wechat",keySound:null}),onReady(){this.keySound=e(),this.keySound.src="/static/code/anjian.wav",this.keySound.autoplay=!1},computed:{displayAmount(){return this.amount}},methods:{goBack(){a()},showPaymentHistory(){t({title:"支付历史功能开发中",icon:"none"})},playKeySound(){this.keySound&&(this.keySound.stop(),setTimeout((()=>{this.keySound.play()}),10))},appendNumber(e){if(this.playKeySound(),"0.00"!==this.amount){if(("."!==e||!this.amount.includes("."))&&(this.amount+=String(e),-1!==this.amount.indexOf("."))){const e=this.amount.split(".");e[1].length>2&&(e[1]=e[1].substring(0,2),this.amount=e.join("."))}}else this.amount="."===e?"0.":String(e)},deleteNumber(){this.playKeySound(),this.amount.length>1?this.amount=this.amount.slice(0,-1):this.amount="0.00"},confirmNote(){this.playKeySound(),this.showNoteInput=!1},selectPaymentMethod(e){this.playKeySound(),this.selectedPaymentMethod=e},confirmPayment(){this.playKeySound(),t({title:`确认收款 ${this.amount}元，使用${this.selectedPaymentMethod}`,icon:"none"})}}},[["render",function(e,a,t,I,N,S){const g=k(s("uni-icons"),A),M=n,P=k(s("custom-navbar"),C),K=y,B=r,F=h,Y=f;return c(),l(M,{class:"payment-page"},{default:o((()=>[d(P,{title:"收款","show-back":!0,shadow:!0,onClickLeft:S.goBack},{right:o((()=>[d(M,{onClick:S.showPaymentHistory,style:{padding:"0 16rpx"}},{default:o((()=>[d(g,{type:"redo",size:"22",color:"#FFFFFF"})])),_:1},8,["onClick"])])),_:1},8,["onClickLeft"]),d(M,{class:"amount-section"},{default:o((()=>[d(M,{class:"amount-label"},{default:o((()=>[i("收款金额(元)")])),_:1}),d(M,{class:"amount-display"},{default:o((()=>[i(u(S.displayAmount),1)])),_:1}),d(M,{class:"note-section",onClick:a[0]||(a[0]=e=>N.showNoteInput=!0)},{default:o((()=>[d(K,{class:"note-icon",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAApCAYAAABHomvIAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKUSURBVHgBzZm/b9NAFMe/Zwq0EYhIZSsVZkKwkC4sHXClsJbCxJawI/oHMDQDOxJiT8vElCJWIiUMRYKl7QRMCULdilqBaMOvPt6zU8m17Na+c2x/pBcn57Pzyenu3eWsYAgRVfggcYGjPCze5fjC0VdKbSBLWKjMscjR4diheEjdGoeNUSE353iaQCqKZqqiwxZrpCAWpAFTyGu1Ho2OHum2Jl/oUPqtFoZ8x0JSuTplTy3MRYXIScpYR/ZIapoLpiXL/4G8/rCKfJAcukqBPmkFKi1x2MgPm6MZeianfheFc+ilfII95Nt6fmSKvCJvxuSF5eowlHv/+Q8+fv2LH3uEu7NncWnyFAyQ/OuwZFcNBTt8cKDJk5c/8aK9f6SsVh3H4/vnYECXBecs8kaNA02WWSwoJ6y0Bxz7MEAmirJlIueKvImWWGZJQ+6I4A0YsPXtIPrc9j8YUhHBCgyYmrSiz100GiiCI3e3YUDtdinyXJ0HiiFlY0GRqFUnQsonQssTYitJ20gByYHt9d84X1K4Pj2Gm1dPIw2MBL9zUm69G7jJuTpzBtem3bzvJu0PHCIr5SZJW+lOcc9e7+E5RxwezpfwaL4EDfrSB3eRkNbaILacIHWlpTVwBbtIiE4Cbq39ggYbIriJhHziAZEU6ZMauIKvUFzeWrxikD7YTXLVlMao1JhVZDXTP1xuOXzoxL1ya/sgcae/Nzt+7LQYQp0FVwq/ovb/pAcoDo3QUvJ2ofKmGalNo9+LOYkeBf4XR+0syIApI3tmYm14UoH2Zo6TXKDsdrduQQcq8v5gQHSJ0mVneM/0+jl5rdkkM0RM9rltjIqhqAyiTgIpqbuo02IKhpCXli7Dmyb9z0kkNk2fk/wHdkOt6sRBPGEAAAAASUVORK5CYII=",mode:"aspectFit"}),d(B,{class:"note-text"},{default:o((()=>[i(u(N.note||"添加备注"),1)])),_:1})])),_:1})])),_:1}),d(M,{class:"keypad"},{default:o((()=>[d(M,{class:"keypad-row"},{default:o((()=>[d(M,{class:"key",onClick:a[1]||(a[1]=e=>S.appendNumber(1))},{default:o((()=>[i("1")])),_:1}),d(M,{class:"key",onClick:a[2]||(a[2]=e=>S.appendNumber(2))},{default:o((()=>[i("2")])),_:1}),d(M,{class:"key",onClick:a[3]||(a[3]=e=>S.appendNumber(3))},{default:o((()=>[i("3")])),_:1})])),_:1}),d(M,{class:"keypad-row"},{default:o((()=>[d(M,{class:"key",onClick:a[4]||(a[4]=e=>S.appendNumber(4))},{default:o((()=>[i("4")])),_:1}),d(M,{class:"key",onClick:a[5]||(a[5]=e=>S.appendNumber(5))},{default:o((()=>[i("5")])),_:1}),d(M,{class:"key",onClick:a[6]||(a[6]=e=>S.appendNumber(6))},{default:o((()=>[i("6")])),_:1})])),_:1}),d(M,{class:"keypad-row"},{default:o((()=>[d(M,{class:"key",onClick:a[7]||(a[7]=e=>S.appendNumber(7))},{default:o((()=>[i("7")])),_:1}),d(M,{class:"key",onClick:a[8]||(a[8]=e=>S.appendNumber(8))},{default:o((()=>[i("8")])),_:1}),d(M,{class:"key",onClick:a[9]||(a[9]=e=>S.appendNumber(9))},{default:o((()=>[i("9")])),_:1})])),_:1}),d(M,{class:"keypad-row"},{default:o((()=>[d(M,{class:"key",onClick:a[10]||(a[10]=e=>S.appendNumber("."))},{default:o((()=>[i(".")])),_:1}),d(M,{class:"key",onClick:a[11]||(a[11]=e=>S.appendNumber(0))},{default:o((()=>[i("0")])),_:1}),d(M,{class:"key delete-key",onClick:S.deleteNumber},{default:o((()=>[d(K,{class:"delete-icon",src:"/h5/assets/delete-BK34JVBT.png",mode:"aspectFit"})])),_:1},8,["onClick"])])),_:1})])),_:1}),d(M,{class:"payment-methods"},{default:o((()=>[d(M,{class:m(["payment-method wechat",{selected:"wechat"===N.selectedPaymentMethod}]),onClick:a[12]||(a[12]=e=>S.selectPaymentMethod("wechat"))},{default:o((()=>[d(K,{class:"payment-icon",src:_,mode:"aspectFit"}),d(B,{class:"payment-label"},{default:o((()=>[i("微信支付")])),_:1})])),_:1},8,["class"]),d(M,{class:m(["payment-method alipay",{selected:"alipay"===N.selectedPaymentMethod}]),onClick:a[13]||(a[13]=e=>S.selectPaymentMethod("alipay"))},{default:o((()=>[d(K,{class:"payment-icon",src:w,mode:"aspectFit"}),d(B,{class:"payment-label"},{default:o((()=>[i("支付宝")])),_:1})])),_:1},8,["class"]),d(M,{class:m(["payment-method shanfu",{selected:"shanfu"===N.selectedPaymentMethod}]),onClick:a[14]||(a[14]=e=>S.selectPaymentMethod("shanfu"))},{default:o((()=>[d(K,{class:"payment-icon",src:b,mode:"aspectFit"}),d(B,{class:"payment-label"},{default:o((()=>[i("云闪付")])),_:1})])),_:1},8,["class"])])),_:1}),d(M,{class:"confirm-button",onClick:S.confirmPayment},{default:o((()=>[d(B,null,{default:o((()=>[i("确认收款")])),_:1})])),_:1},8,["onClick"]),N.showNoteInput?(c(),l(M,{key:0,class:"note-modal"},{default:o((()=>[d(M,{class:"note-modal-content"},{default:o((()=>[d(M,{class:"note-modal-header"},{default:o((()=>[i("添加备注")])),_:1}),d(F,{class:"note-input",modelValue:N.note,"onUpdate:modelValue":a[15]||(a[15]=e=>N.note=e),placeholder:"请输入备注信息"},null,8,["modelValue"]),d(M,{class:"note-modal-buttons"},{default:o((()=>[d(Y,{onClick:a[16]||(a[16]=e=>N.showNoteInput=!1)},{default:o((()=>[i("取消")])),_:1}),d(Y,{onClick:S.confirmNote},{default:o((()=>[i("确定")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})):p("",!0)])),_:1})}],["__scopeId","data-v-82d850ad"]]);export{N as default};
