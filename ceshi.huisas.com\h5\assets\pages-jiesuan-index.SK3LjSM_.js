import{y as t,a as e,d as a,e as s,w as l,i as r,o as n,f as c,C as u,D as o,F as i,h as d,t as m,j as f,l as _,z as p}from"./index-B1Q521gi.js";import{_ as b,r as g}from"./uni-app.es.DAfa8VxY.js";import{_ as k}from"./custom-navbar.DuzuSmPc.js";import{_ as h}from"./_plugin-vue_export-helper.BCo6x5W8.js";const v=h({components:{CustomNavbar:k,uniIcons:b},data:()=>({tabs:["全部","本月","结算中","已结算","结算失败"],currentTab:0,pendingAmount:"4,069.0",currentYear:2025,currentMonth:4,settlements:[{title:"自动结算",amount:"1,720.50",time:"04-13 23:00:00",bank:"招商银行(尾号3456)",status:"pending"},{title:"自动结算",amount:"2,835.60",time:"04-12 23:00:00",bank:"招商银行(尾号3456)",status:"completed",arrivalTime:"04-13 10:25:18"},{title:"自动结算",amount:"2,745.80",time:"04-11 23:00:00",bank:"招商银行(尾号3456)",status:"completed",arrivalTime:"04-12 09:36:42"},{title:"自动结算",amount:"3,180.20",time:"04-10 23:00:00",bank:"招商银行(尾号3456)",status:"failed",failReason:"银行账户信息有误"}],lastMonthSettlements:[{title:"自动结算",amount:"4,582.40",time:"03-31 23:00:00",bank:"招商银行(尾号3456)",status:"completed",arrivalTime:"04-01 11:28:35"},{title:"自动结算",amount:"3,960.80",time:"03-30 23:00:00",bank:"招商银行(尾号3456)",status:"completed",arrivalTime:"03-31 10:15:22"}]}),computed:{filteredSettlements(){let t=[...this.settlements];switch(0===this.currentTab&&(t=t.concat(this.lastMonthSettlements)),this.currentTab){case 0:default:return t;case 1:return this.settlements;case 2:return t.filter((t=>"pending"===t.status));case 3:return t.filter((t=>"completed"===t.status));case 4:return t.filter((t=>"failed"===t.status))}}},methods:{goBack(){t()},changeTab(t){this.currentTab=t},retrySettlement(t){e({title:"重新结算功能",icon:"none"})}}},[["render",function(t,e,h,v,y,T){const F=g(a("uni-icons"),b),w=r,C=g(a("custom-navbar"),k),x=_;return n(),s(w,{class:"page-container"},{default:l((()=>[c(C,{title:"结算记录","show-back":!0,shadow:!0,onClickLeft:T.goBack},{right:l((()=>[c(w,{style:{display:"flex",gap:"16rpx"}},{default:l((()=>[c(F,{type:"search",size:"22",color:"#FFFFFF"}),c(F,{type:"bars",size:"22",color:"#FFFFFF"})])),_:1})])),_:1},8,["onClickLeft"]),c(w,{class:"tab-section"},{default:l((()=>[(n(!0),u(i,null,o(y.tabs,((t,e)=>(n(),s(w,{key:e,class:p(["tab",{active:y.currentTab===e}]),onClick:t=>T.changeTab(e)},{default:l((()=>[c(x,null,{default:l((()=>[d(m(t),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1}),0===y.currentTab||2===y.currentTab?(n(),s(w,{key:0,class:"pending-section"},{default:l((()=>[c(w,{class:"pending-card"},{default:l((()=>[c(w,null,{default:l((()=>[c(x,{class:"pending-label"},{default:l((()=>[d("待结算金额(元)")])),_:1}),c(x,{class:"pending-amount"},{default:l((()=>[d("¥ "+m(y.pendingAmount),1)])),_:1}),c(x,{class:"pending-note"},{default:l((()=>[d("注：每日23:00系统自动发起结算，预计1-2个工作日到账")])),_:1})])),_:1}),c(w,{class:"settle-now-btn"},{default:l((()=>[d("立即结算")])),_:1})])),_:1})])),_:1})):f("",!0),c(w,{class:"month-title"},{default:l((()=>[d(m(y.currentYear)+"年"+m(y.currentMonth)+"月",1)])),_:1}),c(w,{class:"record-list"},{default:l((()=>[(n(!0),u(i,null,o(T.filteredSettlements,((t,e)=>(n(),s(w,{class:"record-item",key:e},{default:l((()=>[c(w,{class:"record-top"},{default:l((()=>[c(x,{class:"record-title"},{default:l((()=>[d(m(t.title),1)])),_:2},1024),c(x,{class:p(["record-amount","failed"===t.status?"failed-amount":""])},{default:l((()=>[d(" ¥ "+m(t.amount),1)])),_:2},1032,["class"])])),_:2},1024),c(x,{class:"record-time"},{default:l((()=>[d(m(t.time),1)])),_:2},1024),c(w,{class:"record-bank-row"},{default:l((()=>[c(x,{class:"record-label"},{default:l((()=>[d("入账银行")])),_:1}),c(x,{class:"record-value"},{default:l((()=>[d(m(t.bank),1)])),_:2},1024)])),_:2},1024),"completed"===t.status?(n(),u(i,{key:0},[c(w,{class:"record-row"},{default:l((()=>[c(x,{class:"record-label"},{default:l((()=>[d("到账时间")])),_:1}),c(x,{class:"record-value"},{default:l((()=>[d(m(t.arrivalTime),1)])),_:2},1024)])),_:2},1024),c(w,{class:"status-tag success-tag"},{default:l((()=>[d("已结算")])),_:1})],64)):"failed"===t.status?(n(),u(i,{key:1},[c(w,{class:"record-row"},{default:l((()=>[c(x,{class:"record-label"},{default:l((()=>[d("失败原因")])),_:1}),c(x,{class:"record-value error-text"},{default:l((()=>[d(m(t.failReason),1)])),_:2},1024)])),_:2},1024),c(w,{class:"retry-button",onClick:e=>T.retrySettlement(t)},{default:l((()=>[d(" 重新结算 ")])),_:2},1032,["onClick"]),c(w,{class:"status-tag error-tag"},{default:l((()=>[d("结算失败")])),_:1})],64)):(n(),s(w,{key:2,class:"status-tag pending-tag"},{default:l((()=>[d("结算中")])),_:1}))])),_:2},1024)))),128))])),_:1})])),_:1})}],["__scopeId","data-v-4676ada0"]]);export{v as default};
