<template>
  <view class="container">
    <custom-navbar title="反扫收银台" :shadow="true"></custom-navbar>

    <!-- 导航栏占位 -->
    <navbar-placeholder></navbar-placeholder>

    <!-- 商户信息展示 -->
    <view class="merchant-info">
      <view class="merchant-name">{{ merchantInfo.name || '商户收银台' }}</view>
      <view class="merchant-subtitle">扫描客户付款码完成收款</view>
    </view>

    <!-- 金额输入区域 -->
    <view class="amount-section">
      <view class="amount-label">收款金额</view>
      <view class="amount-input-wrapper">
        <text class="currency-symbol">¥</text>
        <input
          class="amount-input"
          type="digit"
          v-model="amount"
          placeholder="0.00"
          :focus="shouldFocusAmount"
          @focus="onAmountFocus"
          @blur="onAmountBlur"
        />
      </view>

      <!-- 快捷金额按钮 -->
      <view class="quick-amounts">
        <button
          v-for="quickAmount in quickAmounts"
          :key="quickAmount"
          class="quick-amount-btn"
          @tap="setQuickAmount(quickAmount)"
        >
          {{ quickAmount }}
        </button>
      </view>
    </view>

    <!-- 扫码区域 -->
    <view class="scan-section">
      <view class="scan-title">扫描客户付款码</view>

      <!-- 扫码按钮 -->
      <button class="scan-button" @tap="startScan">
        <view class="scan-icon">📷</view>
        <view class="scan-text">扫一扫付款码</view>
      </button>

      <!-- 手动输入选项 -->
      <view class="manual-input-option">
        <text class="manual-text" @tap="showManualInput">手动输入付款码</text>
      </view>
    </view>

    <!-- 支付方式显示 -->
    <view class="payment-info" v-if="detectedPayMethod">
      <view class="payment-method-display">
        <image class="payment-icon" :src="detectedPayMethod.icon"></image>
        <view class="payment-name">{{ detectedPayMethod.name }}</view>
        <view class="payment-status">已识别</view>
      </view>
    </view>

    <!-- 确认收款按钮 -->
    <view class="confirm-section" v-if="authCode && amount">
      <button class="confirm-button" @tap="confirmPayment" :disabled="isProcessing">
        <text v-if="!isProcessing">确认收款 ¥{{ amount }}</text>
        <text v-else>处理中...</text>
      </button>
    </view>

    <!-- 测试按钮 (开发环境) -->
    <view class="test-section" style="margin-top: 20px; padding: 0 30px;">
      <button class="test-button" @tap="testPaymentNotification"
              style="background: #ff6b35; color: white; border-radius: 8px; padding: 12px;">
        🧪 测试支付通知
      </button>
    </view>

    <!-- 手动输入弹窗 -->
    <view class="modal-overlay" v-if="showManualInputModal" @tap="hideManualInputModal">
      <view class="modal-content" @tap.stop>
        <view class="modal-header">
          <text class="modal-title">手动输入付款码</text>
          <text class="modal-close" @tap="hideManualInputModal">×</text>
        </view>
        <view class="modal-body">
          <input
            class="manual-input"
            type="text"
            v-model="manualAuthCode"
            placeholder="请输入18位付款码"
            maxlength="18"
          />
        </view>
        <view class="modal-footer">
          <button class="modal-btn cancel" @tap="hideManualInputModal">取消</button>
          <button class="modal-btn confirm" @tap="confirmManualInput">确认</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { checkLoginStatus } from '@/utils/auth'
import { post } from '@/utils/request'
import { websocketMixin } from '@/mixins/websocketMixin.js'
import NavbarPlaceholder from '@/components/navbar-placeholder/navbar-placeholder.vue'

export default {
  // 🔧 添加WebSocket Mixin，确保反扫收银台页面也能播报
  mixins: [websocketMixin],
  components: {
    NavbarPlaceholder
  },
  data() {
    return {
      merchantInfo: {
        name: '',
        uid: ''
      },
      amount: '',
      authCode: '', // 客户付款码
      manualAuthCode: '', // 手动输入的付款码
      shouldFocusAmount: true,
      isProcessing: false,
      showManualInputModal: false,

      // 快捷金额选项
      quickAmounts: [10, 20, 50, 100, 200, 500],

      // 支付方式配置
      paymentMethods: {
        'alipay': {
          id: 'alipay',
          name: '支付宝',
          icon: '/static/images/alipay.png'
        },
        'wxpay': {
          id: 'wxpay',
          name: '微信支付',
          icon: '/static/images/wxpay.png'
        },
        'qqpay': {
          id: 'qqpay',
          name: 'QQ钱包',
          icon: '/static/images/qqpay.png'
        },
        'bank': {
          id: 'bank',
          name: '银联支付',
          icon: '/static/images/bank.png'
        }
      },

      detectedPayMethod: null // 识别到的支付方式
    }
  },
  async onLoad(options) {
    // 检查登录状态
    await this.checkAuth()

    // 解析URL参数
    if (options.amount) {
      this.amount = options.amount
    }

    // 加载商户信息
    await this.loadMerchantInfo()

    // 设置金额输入框焦点
    this.$nextTick(() => {
      setTimeout(() => {
        this.shouldFocusAmount = true
      }, 300)
    })
  },

  onShow() {
    console.log('💳 反扫收银台页面显示');

    // 🔧 初始化WebSocket连接（来自websocketMixin）
    if (this.initWebSocket) {
      this.initWebSocket();
    }
  },

  onHide() {
    console.log('💳 反扫收银台页面隐藏');

    // 🔧 暂停WebSocket连接（来自websocketMixin）
    if (this.pauseWebSocket) {
      this.pauseWebSocket();
    }
  },

  onUnload() {
    console.log('💳 反扫收银台页面卸载');

    // 🔧 断开WebSocket连接（来自websocketMixin）
    if (this.disconnectWebSocket) {
      this.disconnectWebSocket();
    }
  },

  methods: {
    // 检查登录状态
    async checkAuth() {
      const isLoggedIn = await checkLoginStatus()
      if (!isLoggedIn) {
        uni.reLaunch({
          url: '/pages/login/index'
        })
        return false
      }
      return true
    },

    // 加载商户信息
    async loadMerchantInfo() {
      try {
        // 从本地存储获取商户信息
        let userInfo = uni.getStorageSync('user_info')

        // 如果本地没有用户信息，尝试从服务器获取
        if (!userInfo) {
          console.log('本地无用户信息，从服务器获取...')
          const response = await this.$http.post('/user/ajax2.php?act=getcount', {})
          if (response && response.code === 0) {
            userInfo = response
            // 保存到本地
            uni.setStorageSync('user_info', userInfo)
          }
        }

        if (userInfo) {
          this.merchantInfo = {
            name: userInfo.username || userInfo.name || '商户收银台',
            uid: userInfo.uid
          }
          console.log('商户信息加载成功:', this.merchantInfo)
        } else {
          console.warn('无法获取商户信息')
        }
      } catch (error) {
        console.error('加载商户信息失败:', error)
      }
    },

    // 金额输入框焦点事件
    onAmountFocus() {
      console.log('金额输入框获得焦点')
    },

    onAmountBlur() {
      console.log('金额输入框失去焦点')
    },

    // 设置快捷金额
    setQuickAmount(amount) {
      this.amount = amount.toString()
    },

    // 开始扫码
    startScan() {
      if (!this.amount || parseFloat(this.amount) <= 0) {
        uni.showToast({
          title: '请先输入收款金额',
          icon: 'none'
        })
        return
      }

      uni.scanCode({
        onlyFromCamera: true,
        success: (res) => {
          console.log('扫码结果:', res)
          this.handleScanResult(res.result)
        },
        fail: (err) => {
          console.error('扫码失败:', err)
          uni.showToast({
            title: '扫码失败，请重试',
            icon: 'none'
          })
        }
      })
    },

    // 处理扫码结果
    handleScanResult(scanData) {
      console.log('处理扫码数据:', scanData)

      // 验证付款码格式（通常是18位数字）
      if (!/^\d{18}$/.test(scanData)) {
        uni.showToast({
          title: '付款码格式不正确',
          icon: 'none'
        })
        return
      }

      this.authCode = scanData
      this.detectPaymentMethod(scanData)

      uni.showToast({
        title: '付款码识别成功',
        icon: 'success'
      })
    },

    // 识别支付方式
    detectPaymentMethod(authCode) {
      const prefix = authCode.substring(0, 2)

      // 根据付款码前缀识别支付方式
      if (['25', '26', '27', '28', '29', '30'].includes(prefix)) {
        this.detectedPayMethod = this.paymentMethods.alipay
      } else if (['10', '11', '12', '13', '14', '15'].includes(prefix)) {
        this.detectedPayMethod = this.paymentMethods.wxpay
      } else if (prefix === '91') {
        this.detectedPayMethod = this.paymentMethods.qqpay
      } else if (prefix === '62') {
        this.detectedPayMethod = this.paymentMethods.bank
      } else {
        this.detectedPayMethod = {
          id: 'unknown',
          name: '未知支付方式',
          icon: '/static/images/unknown.png'
        }
      }
    },

    // 显示手动输入
    showManualInput() {
      if (!this.amount || parseFloat(this.amount) <= 0) {
        uni.showToast({
          title: '请先输入收款金额',
          icon: 'none'
        })
        return
      }

      this.showManualInputModal = true
      this.manualAuthCode = ''
    },

    // 隐藏手动输入弹窗
    hideManualInputModal() {
      this.showManualInputModal = false
      this.manualAuthCode = ''
    },

    // 确认手动输入
    confirmManualInput() {
      if (!this.manualAuthCode || !/^\d{18}$/.test(this.manualAuthCode)) {
        uni.showToast({
          title: '请输入正确的18位付款码',
          icon: 'none'
        })
        return
      }

      this.authCode = this.manualAuthCode
      this.detectPaymentMethod(this.manualAuthCode)
      this.hideManualInputModal()

      uni.showToast({
        title: '付款码输入成功',
        icon: 'success'
      })
    },

    // 确认支付
    async confirmPayment() {
      if (!this.authCode || !this.amount) {
        uni.showToast({
          title: '请先扫描付款码并输入金额',
          icon: 'none'
        })
        return
      }

      if (this.isProcessing) return

      this.isProcessing = true
      uni.showLoading({ title: '处理中...' })

      try {
        // 调用反扫支付接口
        const result = await this.callScanPayAPI()

        if (result.code === 0) {
          // 订单创建成功，需要跳转到submit2.php处理支付
          uni.hideLoading()

          if (result.submit_url) {
            // 跳转到submit2.php处理支付
            uni.showToast({
              title: '正在处理支付...',
              icon: 'loading',
              duration: 2000
            })

            // 使用webview打开支付页面
            setTimeout(() => {
              uni.navigateTo({
                url: `/pages/webview/index?url=${encodeURIComponent(result.submit_url)}&title=付款码支付`
              })
            }, 1000)
          } else {
            // 直接支付成功（兼容旧格式）
            uni.showToast({
              title: '收款成功',
              icon: 'success'
            })

            // ⚡ 收款成功后立即检查新订单
            this.triggerImmediateCheck()

            // 跳转到结果页面
            setTimeout(() => {
              uni.redirectTo({
                url: `/pages/pay/result?status=success&amount=${this.amount}&payMethod=${this.detectedPayMethod.name}&tradeNo=${result.trade_no || ''}`
              })
            }, 1500)
          }
        } else {
          throw new Error(result.msg || '支付失败')
        }
      } catch (error) {
        console.error('支付失败:', error)
        uni.hideLoading()
        uni.showToast({
          title: error.message || '支付失败，请重试',
          icon: 'none'
        })
      } finally {
        this.isProcessing = false
      }
    },

    // 调用反扫支付API
    async callScanPayAPI() {
      let userInfo = uni.getStorageSync('user_info')

      // 如果本地没有用户信息，尝试从服务器获取
      if (!userInfo) {
        console.log('本地无用户信息，从服务器获取...')
        const response = await this.$http.post('/user/ajax2.php?act=getcount', {})
        if (response && response.code === 0) {
          userInfo = response
          // 保存到本地
          uni.setStorageSync('user_info', userInfo)
        }
      }

      if (!userInfo || !userInfo.uid) {
        throw new Error('用户信息不存在，请重新登录')
      }

      // 生成订单号
      const outTradeNo = 'SCAN' + Date.now() + Math.floor(Math.random() * 1000)

      // 构建请求参数
      const params = {
        amount: this.amount,
        auth_code: this.authCode,
        out_trade_no: outTradeNo,
        product_name: '商户收款'
      }

      console.log('反扫支付请求参数:', params)

      try {
        // 使用request工具调用后端接口
        const response = await post('/user/ajax2.php?act=scan_pay', params, {
          loading: false // 我们自己控制loading状态
        })

        console.log('反扫支付响应:', response)
        return response
      } catch (error) {
        console.error('反扫支付请求失败:', error)
        throw new Error(error.msg || error.message || '网络请求失败')
      }
    },

    // 重置表单
    resetForm() {
      this.amount = ''
      this.authCode = ''
      this.manualAuthCode = ''
      this.detectedPayMethod = null
      this.isProcessing = false
    },

    // 测试支付通知
    testPaymentNotification() {
      console.log('🧪 发送测试支付通知')

      // 模拟支付通知数据
      const testPaymentData = {
        type: 'payment_notification',
        data: {
          amount: '88.88',
          money: '88.88',
          trade_no: 'TEST_' + Date.now(),
          merchant_id: '1000',
          timestamp: Date.now(),
          pay_type: 'alipay'
        }
      }

      // 直接调用支付通知处理方法
      if (this.handlePaymentNotification) {
        this.handlePaymentNotification(testPaymentData.data)
      } else {
        console.warn('⚠️ handlePaymentNotification 方法不存在')
      }

      uni.showToast({
        title: '测试通知已发送',
        icon: 'success'
      })
    }
  }
}
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 商户信息样式 */
.merchant-info {
  text-align: center;
  padding: 40rpx 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.merchant-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.merchant-subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 金额输入区域 */
.amount-section {
  background-color: #fff;
  padding: 40rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.amount-label {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  border-bottom: 2px solid #5145F7;
  padding-bottom: 20rpx;
  margin-bottom: 30rpx;
}

.currency-symbol {
  font-size: 60rpx;
  color: #5145F7;
  font-weight: bold;
  margin-right: 10rpx;
}

.amount-input {
  flex: 1;
  font-size: 60rpx;
  color: #333;
  font-weight: bold;
  border: none;
  outline: none;
}

/* 快捷金额按钮 */
.quick-amounts {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.quick-amount-btn {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8rpx;
  padding: 15rpx 25rpx;
  font-size: 28rpx;
  color: #666;
}

.quick-amount-btn:active {
  background-color: #5145F7;
  color: #fff;
}

/* 扫码区域 */
.scan-section {
  background-color: #fff;
  padding: 40rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  text-align: center;
}

.scan-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 30rpx;
  font-weight: bold;
}

.scan-button {
  background: linear-gradient(135deg, #5145F7 0%, #7B68EE 100%);
  color: #fff;
  border-radius: 12rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  border: none;
  box-shadow: 0 4rpx 20rpx rgba(81, 69, 247, 0.3);
}

.scan-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

.scan-text {
  font-size: 32rpx;
  font-weight: bold;
}

.manual-input-option {
  padding: 20rpx;
}

.manual-text {
  color: #5145F7;
  font-size: 28rpx;
  text-decoration: underline;
}

/* 支付方式显示 */
.payment-info {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.payment-method-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.payment-icon {
  width: 60rpx;
  height: 60rpx;
}

.payment-name {
  flex: 1;
  margin-left: 20rpx;
  font-size: 30rpx;
  color: #333;
}

.payment-status {
  color: #52c41a;
  font-size: 26rpx;
  font-weight: bold;
}

/* 确认收款按钮 */
.confirm-section {
  padding: 30rpx;
}

.confirm-button {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  font-size: 36rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 4rpx 20rpx rgba(82, 196, 26, 0.3);
}

.confirm-button:disabled {
  background: #d9d9d9;
  box-shadow: none;
}

/* 手动输入弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 12rpx;
  width: 80%;
  max-width: 600rpx;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 40rpx 30rpx;
}

.manual-input {
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 32rpx;
  text-align: center;
}

.modal-footer {
  display: flex;
  border-top: 1px solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 30rpx;
  font-size: 30rpx;
  border: none;
  background-color: transparent;
}

.modal-btn.cancel {
  color: #666;
  border-right: 1px solid #f0f0f0;
}

.modal-btn.confirm {
  color: #5145F7;
  font-weight: bold;
}
</style>