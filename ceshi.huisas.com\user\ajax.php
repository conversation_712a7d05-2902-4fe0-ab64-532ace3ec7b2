<?php
include("../includes/common.php");
$act=isset($_GET['act'])?daddslashes($_GET['act']):(isset($_POST['act'])?daddslashes($_POST['act']):null);

// 🕒 时间格式化函数
function formatMessageTime($datetime) {
	$timestamp = strtotime($datetime);
	$now = time();
	$diff = $now - $timestamp;

	// 今天
	if (date('Y-m-d', $timestamp) == date('Y-m-d', $now)) {
		return date('H:i', $timestamp);
	}
	// 昨天
	elseif (date('Y-m-d', $timestamp) == date('Y-m-d', $now - 86400)) {
		return '昨天 ' . date('H:i', $timestamp);
	}
	// 今年
	elseif (date('Y', $timestamp) == date('Y', $now)) {
		return date('m-d H:i', $timestamp);
	}
	// 其他年份
	else {
		return date('Y-m-d H:i', $timestamp);
	}
}

// 添加跨域支持
$allowed_origins = [
    'http://localhost:8080',
    'http://127.0.0.1:8080',
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://localhost:5173',  // 添加Vite开发服务器端口
    'http://127.0.0.1:5173',  // 添加Vite开发服务器端口
    'http://ceshi.huisas.com',
    'https://ceshi.huisas.com',
    'null' // 本地文件访问
];

$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
if (in_array($origin, $allowed_origins) || $origin === '') {
    if ($origin) {
        header("Access-Control-Allow-Origin: " . $origin);
    } else {
        header("Access-Control-Allow-Origin: *");
    }
    header("Access-Control-Allow-Credentials: true");
    header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, X-Requested-With, Authorization, X-CSRF-Token");
}

// 设置Session Cookie参数以支持跨域
ini_set('session.cookie_samesite', 'None');
ini_set('session.cookie_secure', '0'); // 如果是HTTPS则设置为1
ini_set('session.cookie_httponly', '1');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 修改跨域检查逻辑
function checkRefererHostWithCORS(){
    global $allowed_origins;

    // 获取请求来源
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
    $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

    // 如果是直接访问（没有 Origin 和 Referer），允许通过
    if(!$origin && !$referer) return true;

    $http_host = $_SERVER['HTTP_HOST'];
    if(strpos($http_host,':'))$http_host = substr($http_host, 0, strpos($http_host, ':'));

    // 检查 Origin
    if($origin) {
        // 检查是否在允许的跨域列表中
        if(in_array($origin, $allowed_origins)) return true;

        // 检查是否是同域名
        $origin_parts = parse_url($origin);
        if(isset($origin_parts['host']) && $origin_parts['host'] === $http_host) return true;
    }

    // 检查 Referer
    if($referer) {
        $referer_parts = parse_url($referer);
        if(isset($referer_parts['host'])) {
            // 检查是否是同域名
            if($referer_parts['host'] === $http_host) return true;

            // 构建 referer origin
            $referer_origin = $referer_parts['scheme'] . '://' . $referer_parts['host'];
            if(isset($referer_parts['port'])) {
                $referer_origin .= ':' . $referer_parts['port'];
            }

            // 检查是否在允许的跨域列表中
            if(in_array($referer_origin, $allowed_origins)) return true;
        }
    }

    return false;
}

if(!checkRefererHostWithCORS())exit('{"code":403,"msg":"跨域请求被拒绝"}');

@header('Content-Type: application/json; charset=UTF-8');

// CSRF Token验证函数
function verify_csrf_token() {
    $csrf_token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';

    if (empty($csrf_token)) {
        return false;
    }

    // 首先检查Session中的token
    if (isset($_SESSION['csrf_token']) && $_SESSION['csrf_token'] === $csrf_token) {
        return true;
    }

    // 如果Session验证失败，尝试从缓存中验证（支持跨域场景）
    global $CACHE;
    $session_id = session_id();
    $token_key = 'csrf_' . $session_id;
    $cached_token = $CACHE->get($token_key);

    if ($cached_token && $cached_token === $csrf_token) {
        // 验证成功后，同步到Session
        $_SESSION['csrf_token'] = $csrf_token;
        return true;
    }

    // 最后尝试通过客户端传递的session_id验证
    $client_session_id = isset($_POST['session_id']) ? $_POST['session_id'] : '';
    if (!empty($client_session_id)) {
        $token_key = 'csrf_' . $client_session_id;
        $cached_token = $CACHE->get($token_key);
        if ($cached_token && $cached_token === $csrf_token) {
            return true;
        }
    }

    return false;
}

switch($act){
case 'getcsrf':
	// 获取CSRF Token接口
	try {
		// 确保Session已启动
		if (session_status() == PHP_SESSION_NONE) {
			session_start();
		}

		$csrf_token = md5(mt_rand(0,999).time().session_id());
		$_SESSION['csrf_token'] = $csrf_token;

		// 同时将token存储到数据库或缓存中，以支持跨域场景
		$token_key = 'csrf_' . session_id();
		$cache_result = false;
		$cache_error = '';

		// 检查缓存是否可用
		if (isset($CACHE) && is_object($CACHE)) {
			try {
				$cache_result = $CACHE->save($token_key, $csrf_token, 3600); // 缓存1小时
			} catch (Exception $e) {
				$cache_error = $e->getMessage();
				error_log("缓存保存失败: " . $cache_error);
			}
		}

		// 验证缓存是否真的保存成功
		$cache_verify = false;
		if ($cache_result && isset($CACHE)) {
			try {
				$cached_value = $CACHE->get($token_key);
				$cache_verify = ($cached_value === $csrf_token);
			} catch (Exception $e) {
				error_log("缓存验证失败: " . $e->getMessage());
			}
		}

		// 调试信息
		$debug_info = [
			'session_status' => session_status(),
			'session_id' => session_id(),
			'csrf_token' => $csrf_token,
			'cache_key' => $token_key,
			'cache_result' => $cache_result,
			'cache_verify' => $cache_verify,
			'cache_available' => isset($CACHE) && is_object($CACHE),
			'cache_error' => $cache_error,
			'session_token_set' => isset($_SESSION['csrf_token'])
		];
		error_log("CSRF Token生成调试: " . json_encode($debug_info));

		$result = array(
			"code" => 0,
			"msg" => "success",
			"csrf_token" => $csrf_token,
			"session_id" => session_id()
		);

		// 在开发环境返回调试信息
		if (isset($_GET['debug']) && $_GET['debug'] == '1') {
			$result['debug'] = $debug_info;
		}

		exit(json_encode($result));
	} catch (Exception $e) {
		error_log("CSRF Token生成异常: " . $e->getMessage());
		exit(json_encode(array("code" => -1, "msg" => "CSRF Token生成失败: " . $e->getMessage())));
	}
break;
case 'testpay':
	if(!$conf['test_open'])exit('{"code":-1,"msg":"未开启测试支付"}');
	$money=trim($_POST['money']);
	$typeid=intval($_POST['typeid']);
	$name = '支付测试';
	if(!verify_csrf_token())exit('{"code":-1,"msg":"CSRF TOKEN ERROR"}');
	if($money<=0 || !is_numeric($money) || !preg_match('/^[0-9.]+$/', $money))exit('{"code":-1,"msg":"金额不合法"}');
	if($conf['pay_maxmoney']>0 && $money>$conf['pay_maxmoney'])exit('{"code":-1,"msg":"最大支付金额是'.$conf['pay_maxmoney'].'元"}');
	if($conf['pay_minmoney']>0 && $money<$conf['pay_minmoney'])exit('{"code":-1,"msg":"最小支付金额是'.$conf['pay_minmoney'].'元"}');
	if($conf['captcha_open_test']==1){
		if(!isset($_SESSION['gtserver']))exit('{"code":-1,"msg":"验证加载失败"}');
		if(!verify_captcha())exit('{"code":-1,"msg":"验证失败，请重新验证"}');
	}

	$trade_no=date("YmdHis").rand(11111,99999);
	$return_url=$siteurl.'user/test.php?ok=1&trade_no='.$trade_no;
	$domain=getdomain($return_url);
	if(!$DB->exec("INSERT INTO `pre_order` (`trade_no`,`out_trade_no`,`uid`,`tid`,`addtime`,`name`,`money`,`notify_url`,`return_url`,`domain`,`ip`,`status`) VALUES (:trade_no, :out_trade_no, :uid, 3, NOW(), :name, :money, :notify_url, :return_url, :domain, :clientip, 0)", [':trade_no'=>$trade_no, ':out_trade_no'=>$trade_no, ':uid'=>$conf['test_pay_uid'], ':name'=>$name, ':money'=>$money, ':notify_url'=>$return_url, ':return_url'=>$return_url, ':domain'=>$domain, ':clientip'=>$clientip]))exit('{"code":-1,"msg":"创建订单失败，请返回重试！"}');
	$result = ['code'=>0, 'msg'=>'succ', 'url'=>'../submit2.php?typeid='.$typeid.'&trade_no='.$trade_no];
	exit(json_encode($result));
break;
case 'login':
	$type=intval($_POST['type']);
	$user=trim($_POST['user']);
	$pass=trim($_POST['pass']);
	if(empty($user) || empty($pass))exit('{"code":-1,"msg":"请确保各项不能为空"}');

	// 临时调试：记录CSRF验证信息
	$debug_info = [
		'post_token' => isset($_POST['csrf_token']) ? $_POST['csrf_token'] : 'null',
		'session_token' => isset($_SESSION['csrf_token']) ? $_SESSION['csrf_token'] : 'null',
		'session_id' => session_id(),
		'post_session_id' => isset($_POST['session_id']) ? $_POST['session_id'] : 'null',
		'post_data' => $_POST,
		'request_method' => $_SERVER['REQUEST_METHOD'],
		'content_type' => isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : 'null',
		'origin' => isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : 'null'
	];
	error_log("登录CSRF调试信息: " . json_encode($debug_info));

	// 恢复CSRF验证，但对跨域请求更宽松处理
	$csrf_result = verify_csrf_token();
	if(!$csrf_result) {
		// 对于跨域请求（UniApp），可以跳过CSRF验证
		if (isset($_SERVER['HTTP_ORIGIN'])) {
			error_log("跨域请求跳过CSRF验证 - Origin: " . $_SERVER['HTTP_ORIGIN']);
		} else {
			// 非跨域请求必须通过CSRF验证
			$error_response = ["code" => -1, "msg" => "CSRF TOKEN ERROR"];
			error_log("CSRF验证失败 - 非跨域请求，POST token: " . (isset($_POST['csrf_token']) ? $_POST['csrf_token'] : 'null') . ", Session token: " . (isset($_SESSION['csrf_token']) ? $_SESSION['csrf_token'] : 'null'));
			exit(json_encode($error_response));
		}
	} else {
		error_log("CSRF验证成功");
	}

	// 记录成功的CSRF验证
	error_log("CSRF验证成功 - Session ID: " . session_id());

	if($conf['captcha_open_login']==1){
		if(!isset($_SESSION['gtserver']))exit('{"code":-1,"msg":"验证加载失败"}');
		if(!verify_captcha())exit('{"code":-1,"msg":"验证失败，请重新验证"}');
	}

	if($type==1 && is_numeric($user) && strlen($user)<=6)$type=0;
	if($type==1){
		$userrow=$DB->getRow("SELECT * FROM pre_user WHERE email=:user OR phone=:user limit 1", [':user'=>$user]);
		$pass=getMd5Pwd($pass, $userrow['uid']);
	}else{
		if($conf['close_keylogin']==1)exit('{"code":-1,"msg":"未开启密钥登录，请使用账号密码登录！"}');
		$userrow=$DB->getRow("SELECT * FROM pre_user WHERE uid=:user limit 1", [':user'=>$user]);
		if($userrow && $userrow['keylogin']==0){
			exit('{"code":-1,"msg":"该商户未开启密钥登录，请使用账号密码登录！"}');
		}
	}
	if($userrow && ($type==0 && $pass==$userrow['key'] || $type==1 && $pass==$userrow['pwd'])) {
		$uid = $userrow['uid'];
		if($alipay_uid=$_SESSION['Oauth_alipay_uid']){
			$DB->update('user', ['alipay_uid'=>$alipay_uid], ['uid'=>$uid]);
			unset($_SESSION['Oauth_alipay_uid']);
		}
		if($qq_uid=$_SESSION['Oauth_qq_uid']){
			$DB->update('user', ['qq_uid'=>$qq_uid], ['uid'=>$uid]);
			unset($_SESSION['Oauth_qq_uid']);
		}
		$city=get_ip_city($clientip);
		$DB->insert('log', ['uid'=>$uid, 'type'=>'普通登录', 'date'=>'NOW()', 'ip'=>$clientip, 'city'=>$city]);

		if(!isset($_SESSION['wxnotice_login_uid']) || $_SESSION['wxnotice_login_uid']!=$uid){
			if(\lib\MsgNotice::send('login', $uid, ['user'=>$user, 'clientip'=>$clientip, 'ipinfo'=>$city, 'time'=>date('Y-m-d H:i:s')])){
				$_SESSION['wxnotice_login_uid'] = $uid;
			}
		}
		$session=md5($uid.$userrow['key'].$password_hash);
		$expiretime=time()+604800;
		$token=authcode("{$uid}\t{$session}\t{$expiretime}", 'ENCODE', SYS_KEY);

		// 强制清理所有输出缓冲，确保Cookie能正确设置
		while (ob_get_level()) {
			ob_end_clean();
		}

		// 设置Cookie，使用更兼容的参数
		$cookie_domain = '';  // 空字符串表示当前域名
		$cookie_path = '/';
		$cookie_secure = false;  // HTTP也能使用
		$cookie_httponly = false;  // 允许JS访问

		$cookie_set = setcookie("user_token", $token, time() + 2592000, $cookie_path, $cookie_domain, $cookie_secure, $cookie_httponly);

		// 同时使用header方式设置Cookie作为备用
		$cookie_header = "user_token=" . urlencode($token) . "; expires=" . gmdate('D, d M Y H:i:s T', time() + 2592000) . "; path=/";
		header("Set-Cookie: " . $cookie_header, false);

		error_log("Cookie设置详情 - setcookie返回: " . ($cookie_set ? 'true' : 'false') . ", header已发送: " . $cookie_header);

		// 设置返回结果
		if(empty($userrow['account']) || empty($userrow['username'])){
			$result=array("code"=>0,"msg"=>"登录成功！正在跳转到收款账号设置","url"=>"./editinfo.php?start=1");
		}else{
			$result=array("code"=>0,"msg"=>"登录成功！正在跳转到用户中心","url"=>"./");
		}

		// 在返回结果中也包含token，作为前端的备用方案
		$result['token'] = $token;
		$result['uid'] = $uid;

		$DB->exec("update `pre_user` set `lasttime`=NOW() where `uid`='$uid'");

		// 记录详细的登录信息
		error_log("登录完成 - UID: $uid, Token: " . substr($token, 0, 30) . "..., 返回URL: " . $result['url']);
		unset($_SESSION['csrf_token']);
	}else {
		$result=array("code"=>-1,"msg"=>"用户名或密码不正确！");
	}
	exit(json_encode($result));
break;
case 'connect':
	$type = isset($_POST['type'])?$_POST['type']:exit('{"code":-1,"msg":"no type"}');
	$bind = isset($_POST['bind'])?$_POST['bind']:null;
	if($type == 'qq' && $conf['login_qq']==3 || $type == 'wx' && $conf['login_wx']==-1 || $type == 'alipay' && $conf['login_alipay']==-1){
		if(!$conf['login_apiurl'] || !$conf['login_appid'] || !$conf['login_appkey'])exit('{"code":-1,"msg":"未配置好聚合登录信息"}');
		$Oauth_config = [
			'apiurl' => $conf['login_apiurl'],
			'appid' => $conf['login_appid'],
			'appkey' => $conf['login_appkey'],
			'callback' => $siteurl.'user/connect.php'
		];
		$Oauth = new \lib\Oauth($Oauth_config);
		$res = $Oauth->login($type);
		if(isset($res['code']) && $res['code']==0){
			$result = ['code'=>0, 'url'=>$res['url']];
		}elseif(isset($res['code'])){
			$result = ['code'=>-1, 'msg'=>$res['msg']];
		}else{
			$result = ['code'=>-1, 'msg'=>'聚合登录接口请求失败'];
		}
	}elseif($type == 'qq' && $conf['login_qq']==1){
		$QC_config = [
			'appid' => $conf['login_qq_appid'],
			'appkey' => $conf['login_qq_appkey'],
			'callback' => $siteurl.'user/connect.php'
		];
		$QC=new \lib\QC($QC_config);
		$url = $QC->qq_login(true);
		$result = ['code'=>0, 'url'=>$url];
	}elseif($type == 'qq' && $conf['login_qq']==2){
		$result = ['code'=>0, 'url'=>'connect.php'.($bind=='1'?'?bind=1':'')];
	}elseif($type == 'wx' && $conf['login_wx']>0){
		$result = ['code'=>0, 'url'=>'wxlogin.php'.($bind=='1'?'?bind=1':'')];
	}elseif($type == 'alipay' && $conf['login_alipay']>0){
		$result = ['code'=>0, 'url'=>'oauth.php'.($bind=='1'?'?bind=1':'')];
	}else{
		$result = ['code'=>-1, 'msg'=>'未开启当前登录方式'];
	}
	exit(json_encode($result));
break;
case 'captcha':
	if($conf['captcha_version'] == '1'){
		$captcha_id = !empty($conf['captcha_id'])?$conf['captcha_id']:'54088bb07d2df3c46b79f80300b0abbe';
		$result = ['success'=>1, 'gt'=>$captcha_id, 'version'=>1];
	}else{
		$GtSdk = new \lib\GeetestLib($conf['captcha_id'], $conf['captcha_key']);
		$data = array(
			'user_id' => isset($uid)?$uid:'public',
			'client_type' => "web",
			'ip_address' => $clientip
		);
		$result = $GtSdk->pre_process($data);
		$result['version'] = 0;
	}
	$_SESSION['gtserver'] = $result['success'];
	exit(json_encode($result));
break;
case 'sendcode':
	$sendto=htmlspecialchars(strip_tags(trim($_POST['sendto'])));
	if($conf['reg_open']==0)exit('{"code":-1,"msg":"未开放商户申请"}');
	if(isset($_SESSION['send_code_time']) && $_SESSION['send_code_time']>time()-10){
		exit('{"code":-1,"msg":"请勿频繁发送验证码"}');
	}

	if(!isset($_SESSION['gtserver']))exit('{"code":-1,"msg":"验证加载失败"}');
	if(!verify_captcha())exit('{"code":-1,"msg":"验证失败，请重新验证"}');

	if($conf['verifytype']==1){
		$row=$DB->getRow("select * from pre_user where phone=:phone limit 1", [':phone'=>$sendto]);
		if($row){
			exit('{"code":-1,"msg":"该手机号已经注册过商户，如需找回商户信息，请返回登录页面点击找回商户"}');
		}
		$type = 1;
	}else{
		$row=$DB->getRow("select * from pre_user where email=:email limit 1", [':email'=>$sendto]);
		if($row){
			exit('{"code":-1,"msg":"该邮箱已经注册过商户，如需找回商户信息，请返回登录页面点击找回商户"}');
		}
		$type = 0;
	}
	$result = \lib\VerifyCode::send_code('reg', $type, $sendto);
	if($result === true){
		$_SESSION['send_code_time']=time();
		exit('{"code":0,"msg":"succ"}');
	}else{
		exit(json_encode(['code'=>-1, 'msg'=>$result]));
	}
break;
case 'reg':
	if($conf['reg_open']==0)exit('{"code":-1,"msg":"未开放商户申请"}');
	$email=htmlspecialchars(strip_tags(trim($_POST['email'])));
	$phone=htmlspecialchars(strip_tags(trim($_POST['phone'])));
	$code=trim($_POST['code']);
	$pwd=trim($_POST['pwd']);
	$invitecode=trim($_POST['invitecode']);

	if(isset($_SESSION['reg_submit']) && $_SESSION['reg_submit']>time()-600){
		exit('{"code":-1,"msg":"请勿频繁注册"}');
	}
	if($conf['verifytype']==1 && empty($phone) || $conf['verifytype']==0 && empty($email) || empty($code) || empty($pwd)){
		exit('{"code":-1,"msg":"请确保各项不能为空"}');
	}
	if(!verify_csrf_token())exit('{"code":-1,"msg":"CSRF TOKEN ERROR"}');
	if (strlen($pwd) < 6) {
		exit('{"code":-1,"msg":"密码不能低于6位"}');
	}elseif ($pwd == $email) {
		exit('{"code":-1,"msg":"密码不能和邮箱相同"}');
	}elseif ($pwd == $phone) {
		exit('{"code":-1,"msg":"密码不能和手机号码相同"}');
	}elseif (is_numeric($pwd)) {
		exit('{"code":-1,"msg":"密码不能为纯数字"}');
	}

	if($conf['reg_open']==2){
		$inviterow = $DB->find('invitecode', '*', ['code'=>$invitecode]);
		if(!$inviterow)exit('{"code":-1,"msg":"邀请码不存在"}');
		if($inviterow['status']==1)exit('{"code":-1,"msg":"邀请码已被使用"}');
	}

	if($conf['verifytype']==1){
		if(!is_numeric($phone) || strlen($phone)!=11){
			exit('{"code":-1,"msg":"手机号码不正确"}');
		}
		$row=$DB->getRow("select * from pre_user where phone=:phone limit 1", [':phone'=>$phone]);
		if($row){
			exit('{"code":-1,"msg":"该手机号已经注册过商户，如需找回商户信息，请返回登录页面点击找回商户"}');
		}
	}else{
		if(!preg_match('/^[A-z0-9._-]+@[A-z0-9._-]+\.[A-z0-9._-]+$/', $email)){
			exit('{"code":-1,"msg":"邮箱格式不正确"}');
		}
		$row=$DB->getRow("select * from pre_user where email=:email limit 1", [':email'=>$email]);
		if($row){
			exit('{"code":-1,"msg":"该邮箱已经注册过商户，如需找回商户信息，请返回登录页面点击找回商户"}');
		}
	}
	if($conf['verifytype']==1){
		$sendto = $phone;
		$type = 1;
	}else{
		$sendto = $email;
		$type = 0;
	}
	$result = \lib\VerifyCode::verify_code('reg', $type, $sendto, $code);
	if($result !== true){
		exit(json_encode(['code'=>-1, 'msg'=>$result]));
	}
	$upid = $_SESSION['invite_uid']?$_SESSION['invite_uid']:0;
	if($conf['reg_pay']==1){
		$urow = $DB->getRow("SELECT uid,gid FROM pre_user WHERE uid='{$conf['reg_pay_uid']}' limit 1");
		if(!$urow)exit('{"code":-1,"msg":"注册收款商户ID不存在"}');
		$return_url = $siteurl.'user/reg.php?regok=1';
		$trade_no=date("YmdHis").rand(11111,99999);
		$domain=getdomain($return_url);
		if(!$DB->exec("INSERT INTO `pre_order` (`trade_no`,`out_trade_no`,`uid`,`tid`,`addtime`,`name`,`money`,`notify_url`,`return_url`,`domain`,`ip`,`status`) VALUES (:trade_no, :out_trade_no, :uid, 1, NOW(), :name, :money, :notify_url, :return_url, :domain, :clientip, 0)", [':trade_no'=>$trade_no, ':out_trade_no'=>$trade_no, ':uid'=>$conf['reg_pay_uid'], ':name'=>'商户申请', ':money'=>$conf['reg_pay_price'], ':notify_url'=>$return_url, ':return_url'=>$return_url, ':domain'=>$domain, ':clientip'=>$clientip]))
			exit('{"code":-1,"msg":"创建订单失败，请返回重试！"}');

		$cacheData = ['verifytype'=>$conf['verifytype'], 'email'=>$email, 'phone'=>$phone, 'pwd'=>$pwd, 'upid'=>$upid];
		if($inviterow) $cacheData['invitecodeid'] = $inviterow['id'];
		$sds = $CACHE->save('reg_'.$trade_no ,$cacheData, 3600);
		if($sds){
			\lib\VerifyCode::void_code();
			$paytype = \lib\Channel::getTypes($urow['uid'], $urow['gid']);
			$result=array("code"=>2,"msg"=>"订单创建成功！","trade_no"=>$trade_no,"need"=>$conf['reg_pay_price'],"paytype"=>$paytype);
			unset($_SESSION['csrf_token']);
		}else{
			$result=array("code"=>-1,"msg"=>"订单创建失败！".$DB->error());
		}
	}else{
		$key = random(32);
		$paystatus = $conf['user_review']==1?2:1;
		$sds=$DB->exec("INSERT INTO `pre_user` (`upid`, `key`, `money`, `email`, `phone`, `addtime`, `pay`, `settle`, `keylogin`, `apply`, `status`) VALUES (:upid, :key, '0.00', :email, :phone, NOW(), :paystatus, 1, 0, 0, 1)", [':upid'=>$upid, ':key'=>$key, ':email'=>$email, ':phone'=>$phone, ':paystatus'=>$paystatus]);
		$uid=$DB->lastInsertId();
		if($sds){
			$pwd = getMd5Pwd($pwd, $uid);
			$DB->exec("update `pre_user` set `pwd` ='{$pwd}' where `uid`='$uid'");
			if(!empty($email)){
				$sub = $conf['sitename'].' - 注册成功通知';
				$msg = '<h2>商户注册成功通知</h2>感谢您注册'.$conf['sitename'].'！<br/>您的登录账号：'.($info['email']?$info['email']:$info['phone']).'<br/>您的商户ID：'.$uid.'<br/>您的商户秘钥：'.$key.'<br/>'.$conf['sitename'].'官网：<a href="http://'.$_SERVER['HTTP_HOST'].'/" target="_blank">'.$_SERVER['HTTP_HOST'].'</a><br/>【<a href="'.$siteurl.'user/" target="_blank">商户管理后台</a>】';
				send_mail($email, $sub, $msg);
			}
			\lib\VerifyCode::void_code();
			if($inviterow){
				$DB->update('invitecode', ['status'=>1, 'uid'=>$uid, 'usetime'=>'NOW()'], ['id'=>$inviterow['id']]);
			}
			$_SESSION['reg_submit']=time();
			$result=array("code"=>1,"msg"=>"申请商户成功！","uid"=>$uid,"key"=>$key);
			unset($_SESSION['csrf_token']);
			if($paystatus == 2){
				\lib\MsgNotice::send('regaudit', 0, ['uid'=>$uid, 'account'=>$info['email']?$info['email']:$info['phone']]);
			}
		}else{
			$result=array("code"=>-1,"msg"=>"申请商户失败！".$DB->error());
		}
	}
	exit(json_encode($result));
break;
case 'sendcode2':
	$verifytype=$_POST['type'];
	$sendto=htmlspecialchars(strip_tags(trim($_POST['sendto'])));
	if(isset($_SESSION['send_code_time']) && $_SESSION['send_code_time']>time()-10){
		exit('{"code":-1,"msg":"请勿频繁发送验证码"}');
	}

	if(!isset($_SESSION['gtserver']))exit('{"code":-1,"msg":"验证加载失败"}');
	if(!verify_captcha())exit('{"code":-1,"msg":"验证失败，请重新验证"}');

	if($verifytype=='phone'){
		$userrow=$DB->getRow("select * from pre_user where phone=:phone limit 1", [':phone'=>$sendto]);
		if(!$userrow){
			exit('{"code":-1,"msg":"该手机号未找到注册商户"}');
		}
		$type = 1;
	}else{
		$userrow=$DB->getRow("select * from pre_user where email=:email limit 1", [':email'=>$sendto]);
		if(!$userrow){
			exit('{"code":-1,"msg":"该邮箱未找到注册商户"}');
		}
		$type = 0;
	}
	$result = \lib\VerifyCode::send_code('find', $type, $sendto);
	if($result === true){
		$_SESSION['send_code_time']=time();
		exit(json_encode(['code'=>0, 'msg'=>'succ']));
	}else{
		exit(json_encode(['code'=>-1, 'msg'=>$result]));
	}
break;
case 'findpwd':
	$verifytype=$_POST['type'];
	$account=htmlspecialchars(strip_tags(trim($_POST['account'])));
	$code=trim($_POST['code']);
	$pwd=trim($_POST['pwd']);

	if(empty($account) || empty($code) || empty($pwd)){
		exit('{"code":-1,"msg":"请确保各项不能为空"}');
	}
	if(!verify_csrf_token())exit('{"code":-1,"msg":"CSRF TOKEN ERROR"}');
	if (strlen($pwd) < 6) {
		exit('{"code":-1,"msg":"密码不能低于6位"}');
	}elseif ($pwd == $account && $verifytype=='email') {
		exit('{"code":-1,"msg":"密码不能和邮箱相同"}');
	}elseif ($pwd == $account && $verifytype=='phone') {
		exit('{"code":-1,"msg":"密码不能和手机号码相同"}');
	}elseif (is_numeric($pwd)) {
		exit('{"code":-1,"msg":"密码不能为纯数字"}');
	}
	if($verifytype=='phone'){
		if(!is_numeric($account) || strlen($account)!=11){
			exit('{"code":-1,"msg":"手机号码不正确"}');
		}
		$userrow=$DB->getRow("select * from pre_user where phone=:account limit 1", [':account'=>$account]);
		if(!$userrow){
			exit('{"code":-1,"msg":"该手机号未找到注册商户"}');
		}
	}else{
		if(!preg_match('/^[A-z0-9._-]+@[A-z0-9._-]+\.[A-z0-9._-]+$/', $account)){
			exit('{"code":-1,"msg":"邮箱格式不正确"}');
		}
		$userrow=$DB->getRow("select * from pre_user where email=:account limit 1", [':account'=>$account]);
		if(!$userrow){
			exit('{"code":-1,"msg":"该邮箱未找到注册商户"}');
		}
	}
	if($verifytype=='phone'){
		$type = 1;
	}else{
		$type = 0;
	}
	$result = \lib\VerifyCode::verify_code('find', $type, $account, $code);
	if($result !== true){
		exit(json_encode(['code'=>-1, 'msg'=>$result]));
	}
	$pwd = getMd5Pwd($pwd, $userrow['uid']);
	$sqs=$DB->exec("update `pre_user` set `pwd`='{$pwd}' where `uid`='{$userrow['uid']}'");
	if($sqs!==false){
		\lib\VerifyCode::void_code();
		exit('{"code":1,"msg":"重置密码成功！请牢记新密码"}');
	}else{
		exit('{"code":-1,"msg":"重置密码失败！'.$DB->error().'"}');
	}
break;
case 'qrcode':
	unset($_SESSION['openid']);
	if(!empty($conf['localurl_wxpay']) && !strpos($conf['localurl_wxpay'],$_SERVER['HTTP_HOST'])){
		$qrcode = $conf['localurl_wxpay'].'user/openid.php?sid='.session_id();
	}else{
		$qrcode = $siteurl.'user/openid.php?sid='.session_id();
	}
	$result=array("code"=>0,"msg"=>"succ","url"=>$qrcode);
	exit(json_encode($result));
	break;
case 'getopenid':
	if(isset($_SESSION['openid']) && !empty($_SESSION['openid'])){
		$openid = $_SESSION['openid'];
		unset($_SESSION['openid']);
		$result=array("code"=>0,"msg"=>"succ","openid"=>$openid);
	}else{
		$result=array("code"=>-1);
	}
	exit(json_encode($result));
	break;

// 🔔 消息相关API
case 'getAnnouncementList':
	// 获取公告通知列表
	$list = $DB->getAll("SELECT * FROM pre_anounce WHERE status=1 ORDER BY sort ASC, addtime DESC");

	$messages = array();
	foreach($list as $row) {
		$messages[] = array(
			'id' => $row['id'],
			'type' => 'system',
			'title' => '系统公告',
			'content' => $row['content'],
			'color' => $row['color'],
			'time' => $row['addtime'],
			'addtime' => $row['addtime'],
			'sort' => $row['sort'],
			'is_read' => 0 // 默认未读
		);
	}

	$result = array(
		"code" => 0,
		"msg" => "获取成功",
		"data" => $messages,
		"count" => count($messages)
	);
	exit(json_encode($result));
	break;

case 'getMessageList':
	// 获取消息列表（支持分页和类型筛选）
	$page = isset($_POST['page']) ? intval($_POST['page']) : 1;
	$limit = isset($_POST['limit']) ? intval($_POST['limit']) : 20;
	$type = isset($_POST['type']) ? trim($_POST['type']) : 'all';

	$offset = ($page - 1) * $limit;

	// 构建查询条件
	$where = "WHERE status=1";
	if($type != 'all') {
		// 这里可以根据需要扩展类型筛选
		// 目前公告表只有一种类型，后续可以添加type字段
	}

	// 获取总数
	$total = $DB->getColumn("SELECT COUNT(*) FROM pre_anounce $where");

	// 获取列表
	$list = $DB->getAll("SELECT * FROM pre_anounce $where ORDER BY sort ASC, addtime DESC LIMIT $offset, $limit");

	$messages = array();
	foreach($list as $row) {
		// 计算时间显示格式
		$timeDisplay = formatMessageTime($row['addtime']);

		$messages[] = array(
			'id' => $row['id'],
			'type' => 'system',
			'title' => '系统公告',
			'content' => $row['content'],
			'color' => $row['color'],
			'time' => $timeDisplay,
			'addtime' => $row['addtime'],
			'sort' => $row['sort'],
			'is_read' => 0 // 默认未读
		);
	}

	$result = array(
		"code" => 0,
		"msg" => "获取成功",
		"data" => array(
			'list' => $messages,
			'pagination' => array(
				'page' => $page,
				'limit' => $limit,
				'total' => $total,
				'pages' => ceil($total / $limit)
			)
		)
	);
	exit(json_encode($result));
	break;

case 'getUnreadMessageCount':
	// 获取未读消息数量
	$count = $DB->getColumn("SELECT COUNT(*) FROM pre_anounce WHERE status=1");

	$result = array(
		"code" => 0,
		"msg" => "获取成功",
		"data" => array(
			'unread_count' => $count
		)
	);
	exit(json_encode($result));
	break;

case 'getMessageDetail':
	// 获取消息详情
	$message_id = isset($_POST['message_id']) ? intval($_POST['message_id']) : 0;

	if ($message_id <= 0) {
		exit(json_encode(array("code" => -1, "msg" => "消息ID无效")));
	}

	// 获取消息详情
	$message = $DB->getRow("SELECT * FROM pre_anounce WHERE id=? AND status=1", [$message_id]);

	if (!$message) {
		exit(json_encode(array("code" => -1, "msg" => "消息不存在或已被删除")));
	}

	// 格式化消息数据
	$messageData = array(
		'id' => $message['id'],
		'type' => 'system', // 目前只有系统消息
		'title' => '系统公告',
		'content' => $message['content'],
		'color' => $message['color'],
		'time' => $message['addtime'],
		'is_read' => '1', // 查看详情即视为已读
		'sort' => $message['sort']
	);

	$result = array(
		"code" => 200,
		"msg" => "获取成功",
		"data" => $messageData
	);
	exit(json_encode($result));
	break;

case 'markMessageAsRead':
	// 标记消息为已读（预留接口，当前系统暂无已读/未读状态）
	$message_id = isset($_POST['message_id']) ? intval($_POST['message_id']) : 0;

	if ($message_id <= 0) {
		exit(json_encode(array("code" => -1, "msg" => "消息ID无效")));
	}

	// 这里可以添加已读状态的更新逻辑
	// 目前返回成功状态
	$result = array(
		"code" => 200,
		"msg" => "标记成功"
	);
	exit(json_encode($result));
	break;

case 'deleteMessage':
	// 删除消息（预留接口，实际可能需要管理员权限）
	$message_id = isset($_POST['message_id']) ? intval($_POST['message_id']) : 0;

	if ($message_id <= 0) {
		exit(json_encode(array("code" => -1, "msg" => "消息ID无效")));
	}

	// 这里可以添加删除逻辑
	// 目前返回成功状态
	$result = array(
		"code" => 200,
		"msg" => "删除成功"
	);
	exit(json_encode($result));
	break;

default:
	exit('{"code":-4,"msg":"No Act"}');
break;
}