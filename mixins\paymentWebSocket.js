/**
 * 支付WebSocket Mixin
 * 参考 CRMEB 的设计，为页面提供统一的 WebSocket 功能
 * 
 * 使用方法：
 * import paymentWebSocketMixin from '@/mixins/paymentWebSocket.js'
 * 
 * export default {
 *   mixins: [paymentWebSocketMixin],
 *   // 你的页面代码
 * }
 */

import { webSocketService } from '@/utils/websocketService.js'
import { getCurrentUserId, getCurrentUserChannel } from '@/utils/auth.js'

export default {
  data() {
    return {
      // WebSocket 连接状态
      websocketStatus: {
        connected: false,
        connecting: false,
        reconnectCount: 0,
        lastConnectTime: null,
        totalMessages: 0,
        paymentNotifications: 0
      },
      
      // 支付通知列表
      paymentNotifications: [],
      
      // WebSocket 配置
      websocketConfig: {
        autoConnect: true, // 页面加载时自动连接
        showNotifications: true, // 显示通知提示
        playSound: this.getVoiceSettings().enabled, // 播放语音提示（从设置读取）
        maxNotifications: 50 // 最大通知数量
      }
    }
  },

  computed: {
    /**
     * WebSocket 连接状态文本
     */
    websocketStatusText() {
      if (this.websocketStatus.connected) {
        return '✅ 已连接'
      } else if (this.websocketStatus.connecting) {
        return '🔄 连接中...'
      } else {
        return '❌ 未连接'
      }
    },

    /**
     * WebSocket 连接状态颜色
     */
    websocketStatusColor() {
      if (this.websocketStatus.connected) {
        return '#52c41a' // 绿色
      } else if (this.websocketStatus.connecting) {
        return '#1890ff' // 蓝色
      } else {
        return '#ff4d4f' // 红色
      }
    },

    /**
     * 最新的支付通知
     */
    latestPaymentNotification() {
      return this.paymentNotifications.length > 0 
        ? this.paymentNotifications[0] 
        : null
    },

    /**
     * 今日支付统计
     */
    todayPaymentStats() {
      const today = new Date().toDateString()
      const todayNotifications = this.paymentNotifications.filter(
        notification => new Date(notification.timestamp).toDateString() === today
      )
      
      const totalAmount = todayNotifications.reduce((sum, notification) => {
        const amount = parseFloat(notification.data?.amount || notification.data?.money || 0)
        return sum + amount
      }, 0)

      return {
        count: todayNotifications.length,
        amount: totalAmount.toFixed(2)
      }
    }
  },

  mounted() {
    if (this.websocketConfig.autoConnect) {
      this.initWebSocket()
    }
  },

  beforeDestroy() {
    this.disconnectWebSocket()
  },

  methods: {
    /**
     * 获取语音设置
     */
    getVoiceSettings() {
      try {
        const settings = uni.getStorageSync('voice_settings');
        return settings || {
          enabled: true,
          volume: 1.0,
          speed: 1.0
        };
      } catch (error) {
        console.error('获取语音设置失败:', error);
        return {
          enabled: true,
          volume: 1.0,
          speed: 1.0
        };
      }
    },

    /**
     * 保存语音设置
     */
    saveVoiceSettings(settings) {
      try {
        uni.setStorageSync('voice_settings', settings);
        // 更新当前配置
        this.websocketConfig.playSound = settings.enabled;
        console.log('🎵 语音设置已保存:', settings);
      } catch (error) {
        console.error('保存语音设置失败:', error);
      }
    },

    /**
     * 切换语音播报开关
     */
    toggleVoicePlayback() {
      const settings = this.getVoiceSettings();
      settings.enabled = !settings.enabled;
      this.saveVoiceSettings(settings);

      uni.showToast({
        title: settings.enabled ? '🎵 语音播报已开启' : '🔇 语音播报已关闭',
        icon: 'none',
        duration: 1500
      });

      return settings.enabled;
    },

    /**
     * 初始化 WebSocket 连接
     */
    initWebSocket() {
      console.log('🚀 初始化支付WebSocket连接')

      // 🔒 检查用户登录状态
      const userId = getCurrentUserId();
      const userChannel = getCurrentUserChannel();

      if (!userId || !userChannel) {
        console.error('❌ 用户未登录或无法获取用户频道，无法初始化WebSocket');
        uni.showToast({
          title: '请先登录',
          icon: 'error',
          duration: 2000
        });
        return;
      }

      console.log(`🔒 当前用户ID: ${userId}`);
      console.log(`🔒 用户专属频道: ${userChannel}`);

      webSocketService.connect({
        onConnect: this.onWebSocketConnect,
        onMessage: this.onWebSocketMessage,
        onDisconnect: this.onWebSocketDisconnect,
        onError: this.onWebSocketError
      })
    },

    /**
     * WebSocket 连接成功回调
     */
    onWebSocketConnect(data) {
      console.log('✅ WebSocket连接成功:', data)
      
      this.websocketStatus.connected = true
      this.websocketStatus.connecting = false
      this.websocketStatus.reconnectCount = data.reconnectCount || 0
      this.websocketStatus.lastConnectTime = new Date()

      // 显示连接成功提示
      if (this.websocketConfig.showNotifications) {
        uni.showToast({
          title: '💰 支付监听已开启',
          icon: 'success',
          duration: 2000
        })
      }

      // 触发自定义连接成功事件
      this.$emit('websocket-connected', data)
      
      // 如果页面有自定义的连接成功处理方法
      if (typeof this.onPaymentWebSocketConnected === 'function') {
        this.onPaymentWebSocketConnected(data)
      }
    },

    /**
     * WebSocket 消息接收回调
     */
    onWebSocketMessage(message) {
      console.log('📨 收到WebSocket消息:', message)

      this.websocketStatus.totalMessages++

      // 详细分析消息内容
      console.log('🔍 消息事件类型:', message.event)
      console.log('🔍 消息数据:', message.data)

      // 处理支付通知 - 扩展判断条件
      if (message.event === 'payment_success' ||
          message.event === 'payment' ||
          message.event === 'pay_success' ||
          (message.data && (message.data.amount || message.data.money))) {
        console.log('💰 检测到支付消息，触发处理')
        this.handlePaymentNotification(message)
      }

      // 触发自定义消息事件
      this.$emit('websocket-message', message)
      
      // 如果页面有自定义的消息处理方法
      if (typeof this.onPaymentWebSocketMessage === 'function') {
        this.onPaymentWebSocketMessage(message)
      }
    },

    /**
     * WebSocket 连接断开回调
     */
    onWebSocketDisconnect(data) {
      console.log('❌ WebSocket连接断开:', data)
      
      this.websocketStatus.connected = false
      this.websocketStatus.connecting = false

      // 显示断开提示
      if (this.websocketConfig.showNotifications) {
        uni.showToast({
          title: '⚠️ 支付监听已断开',
          icon: 'none',
          duration: 2000
        })
      }

      // 触发自定义断开事件
      this.$emit('websocket-disconnected', data)
      
      // 如果页面有自定义的断开处理方法
      if (typeof this.onPaymentWebSocketDisconnected === 'function') {
        this.onPaymentWebSocketDisconnected(data)
      }
    },

    /**
     * WebSocket 错误回调
     */
    onWebSocketError(error) {
      console.error('❌ WebSocket错误:', error)
      
      this.websocketStatus.connected = false
      this.websocketStatus.connecting = false

      // 触发自定义错误事件
      this.$emit('websocket-error', error)
      
      // 如果页面有自定义的错误处理方法
      if (typeof this.onPaymentWebSocketError === 'function') {
        this.onPaymentWebSocketError(error)
      }
    },

    /**
     * 处理支付通知
     */
    handlePaymentNotification(message) {
      console.log('💰 处理支付通知:', message)
      
      const notification = {
        id: Date.now(),
        timestamp: new Date(),
        type: 'payment_success',
        data: message.data || {},
        message: message
      }

      // 添加到通知列表（最新的在前面）
      this.paymentNotifications.unshift(notification)
      this.websocketStatus.paymentNotifications++

      // 限制通知数量
      if (this.paymentNotifications.length > this.websocketConfig.maxNotifications) {
        this.paymentNotifications = this.paymentNotifications.slice(0, this.websocketConfig.maxNotifications)
      }

      // 显示支付通知
      if (this.websocketConfig.showNotifications) {
        this.showPaymentNotification(notification)
      }

      // 播放语音提示
      if (this.websocketConfig.playSound) {
        this.playPaymentSound(notification)
      }

      // 触发支付通知事件
      this.$emit('payment-notification', notification)
      
      // 如果页面有自定义的支付通知处理方法
      if (typeof this.onPaymentNotification === 'function') {
        this.onPaymentNotification(notification)
      }
    },

    /**
     * 显示支付通知
     */
    showPaymentNotification(notification) {
      const amount = notification.data.amount || notification.data.money || '0.00'
      const tradeNo = notification.data.trade_no || notification.data.order_no || ''
      
      uni.showModal({
        title: '💰 收到新支付',
        content: `金额: ¥${amount}\n订单: ${tradeNo}`,
        showCancel: false,
        confirmText: '确定',
        success: () => {
          console.log('支付通知已确认')
        }
      })
    },

    /**
     * 播放支付语音提示 - 使用专业语音拼接
     */
    async playPaymentSound(notification) {
      const amount = notification.data.amount || notification.data.money || '0.00'

      try {
        // 检查语音设置
        const voiceSettings = this.getVoiceSettings();
        if (!voiceSettings.enabled) {
          console.log('🔇 语音播报已关闭，跳过播放');
          return;
        }

        console.log(`🎵 开始播放专业语音: 收款${amount}元`);

        // 直接导入AudioPlayer
        const AudioPlayer = require('@/utils/audioPlayer.js');
        const audioPlayer = new (AudioPlayer.default || AudioPlayer)();

        // 使用专业语音拼接播放
        await audioPlayer.playPaymentSuccess(amount);

        console.log(`✅ 专业语音播放完成: 收款${amount}元`);

      } catch (error) {
        console.error('❌ 专业语音播放失败，使用备用TTS:', error);

        // 备用方案：使用系统TTS
        this.playFallbackTTS(amount);
      }
    },

    /**
     * 备用TTS播放
     */
    playFallbackTTS(amount) {
      // #ifdef APP-PLUS
      if (plus.speech) {
        plus.speech.speak(`收到支付${amount}元`, {
          volume: 0.8,
          speed: 1.0
        })
      }
      // #endif

      // #ifdef H5
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(`收到支付${amount}元`)
        utterance.lang = 'zh-CN'
        utterance.volume = 0.8
        utterance.rate = 1.0
        speechSynthesis.speak(utterance)
      }
      // #endif

      console.log(`🎵 备用TTS播放: 收到支付${amount}元`)
    },

    /**
     * 手动连接 WebSocket
     */
    connectWebSocket() {
      this.websocketStatus.connecting = true
      this.initWebSocket()
    },

    /**
     * 断开 WebSocket 连接
     */
    disconnectWebSocket() {
      webSocketService.close()
      this.websocketStatus.connected = false
      this.websocketStatus.connecting = false
    },

    /**
     * 重新连接 WebSocket
     */
    reconnectWebSocket() {
      console.log('🔄 手动重连WebSocket')
      this.disconnectWebSocket()
      setTimeout(() => {
        this.connectWebSocket()
      }, 1000)
    },

    /**
     * 发送 WebSocket 消息
     */
    sendWebSocketMessage(message) {
      webSocketService.send(message)
    },

    /**
     * 获取 WebSocket 状态
     */
    getWebSocketStatus() {
      return {
        ...this.websocketStatus,
        service: webSocketService.getStatus()
      }
    },

    /**
     * 清空支付通知
     */
    clearPaymentNotifications() {
      this.paymentNotifications = []
      this.websocketStatus.paymentNotifications = 0
      
      uni.showToast({
        title: '通知已清空',
        icon: 'success'
      })
    },

    /**
     * 切换 WebSocket 连接状态
     */
    toggleWebSocketConnection() {
      if (this.websocketStatus.connected) {
        this.disconnectWebSocket()
      } else {
        this.connectWebSocket()
      }
    }
  }
}
