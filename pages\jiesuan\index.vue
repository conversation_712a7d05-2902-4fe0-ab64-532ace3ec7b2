<template>
  <view class="page-container">
    <!-- 优雅现代导航栏 -->
    <view class="elegant-navbar">
      <!-- 状态栏占位 -->
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

      <!-- 导航栏主体 -->
      <view class="navbar-main">
        <!-- 左侧返回按钮 -->
        <view class="navbar-left" @click="goBack">
          <view class="back-btn">
            <uni-icons type="left" size="20" color="#FFFFFF"></uni-icons>
          </view>
        </view>

        <!-- 中间标题 -->
        <view class="navbar-center">
          <text class="navbar-title">结算记录</text>
        </view>

        <!-- 右侧占位 -->
        <view class="navbar-right">
          <!-- 为了保持平衡，右侧留空 -->
        </view>
      </view>
    </view>

    <!-- 导航栏占位 - 自适应安全区域 -->
    <view class="navbar-placeholder" :style="{ height: navbarPlaceholderHeight + 'px' }"></view>

    <!-- 选项卡 -->
    <view class="tab-section">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab"
        :class="{ active: currentTab === index }"
        @click="changeTab(index)"
      >
        <text>{{ tab }}</text>
      </view>
    </view>
    
    <!-- 若为全部或结算中,显示待结算金额 -->
    <block v-if="currentTab === 0 || currentTab === 2">
      <view class="pending-section">
        <view class="pending-card">
          <view>
            <text class="pending-label">待结算金额(元)</text>
            <text class="pending-amount">¥ {{ pendingAmount }}</text>
            <text class="pending-note">注：每日23:00系统自动发起结算，预计1-2个工作日到账</text>
          </view>
          <view class="settle-now-btn">立即结算</view>
        </view>
      </view>
    </block>
    
    <!-- 月份标题 -->
    <view class="month-title">{{ currentYear }}年{{ currentMonth }}月</view>
    
    <!-- 结算记录列表 -->
    <view class="record-list">
      <!-- 加载状态 -->
      <view v-if="loading && settlements.length === 0" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空数据状态 -->
      <view v-else-if="!loading && filteredSettlements.length === 0" class="empty-container">
        <text class="empty-text">暂无结算记录</text>
      </view>

      <!-- 结算记录项 -->
      <view class="record-item" v-for="(item, index) in filteredSettlements" :key="item.id || index">
        <view class="record-top">
          <text class="record-title">{{ item.title }}</text>
          <text :class="['record-amount', item.status === 'failed' ? 'failed-amount' : '']">
            ¥ {{ item.amount }}
          </text>
        </view>
        
        <text class="record-time">{{ item.time }}</text>
        
        <view class="record-bank-row">
          <text class="record-label">入账银行</text>
          <text class="record-value">{{ item.bank }}</text>
        </view>
        
        <block v-if="item.status === 'completed'">
          <view class="record-row">
            <text class="record-label">到账时间</text>
            <text class="record-value">{{ item.arrivalTime }}</text>
          </view>

          <!-- 微信收款确认按钮 -->
          <view v-if="item.jumpurl" class="qr-button" @click="showQrcode(item)">
            确认收款
          </view>

          <view class="status-tag success-tag">已结算</view>
        </block>
        
        <block v-else-if="item.status === 'failed'">
          <view class="record-row">
            <text class="record-label">失败原因</text>
            <text class="record-value error-text" @click="showFailReason(item)">点击查看详情</text>
          </view>

          <view class="retry-button" @click="retrySettlement(item)">
            重新结算
          </view>

          <view class="status-tag error-tag">结算失败</view>
        </block>
        
        <block v-else>
          <view class="status-tag pending-tag">结算中</view>
        </block>
      </view>

      <!-- 底部加载提示 -->
      <view v-if="settlements.length > 0" class="load-more-container">
        <text v-if="loading" class="load-more-text">加载中...</text>
        <text v-else-if="!hasMore" class="load-more-text">没有更多数据了</text>
        <text v-else class="load-more-text">上拉加载更多</text>
      </view>
    </view>
  </view>
</template>

<script>
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons'
import { request } from '@/utils/request.js'
import { checkLoginStatus, redirectToLogin } from '@/utils/auth.js'

export default {
  components: {
    uniIcons
  },
  data() {
    return {
      statusBarHeight: 0, // 状态栏高度
      tabs: ['全部', '本月', '结算中', '已结算', '结算失败'],
      currentTab: 0,
      pendingAmount: '0.00',
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      settlements: [],
      loading: false,
      total: 0,
      pageSize: 20,
      currentPage: 1,
      hasMore: true
    };
  },
  computed: {
    // 导航栏占位高度
    navbarPlaceholderHeight() {
      // 状态栏高度(px) + 导航栏总高度
      // 增加一些额外高度确保完全不遮挡
      return this.statusBarHeight + 80;
    },

    filteredSettlements() {
      // 根据当前选中的tab过滤数据
      switch (this.currentTab) {
        case 0: // 全部
          return this.settlements;
        case 1: // 本月
          return this.settlements.filter(item => {
            const itemDate = new Date(item.addtime);
            return itemDate.getFullYear() === this.currentYear &&
                   itemDate.getMonth() + 1 === this.currentMonth;
          });
        case 2: // 结算中
          return this.settlements.filter(item => item.status === 'pending');
        case 3: // 已结算
          return this.settlements.filter(item => item.status === 'completed');
        case 4: // 结算失败
          return this.settlements.filter(item => item.status === 'failed');
        default:
          return this.settlements;
      }
    }
  },
  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight || 0;

    // 处理URL参数，支持直接跳转到特定选项卡
    if (options && options.tab) {
      const tabIndex = parseInt(options.tab);
      if (tabIndex >= 0 && tabIndex < this.tabs.length) {
        this.currentTab = tabIndex;
      }
    }
    this.checkLoginAndLoadData();
  },
  onShow() {
    this.checkLoginAndLoadData();
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMoreData();
    }
  },
  onPullDownRefresh() {
    this.refreshData();
  },
  methods: {
    // 检查登录状态并加载数据
    async checkLoginAndLoadData() {
      try {
        const isLoggedIn = await checkLoginStatus();
        if (!isLoggedIn) {
          console.log('用户未登录，显示空数据状态');
          // 不直接跳转，而是显示空数据状态，让用户可以看到页面结构
          this.settlements = [];
          this.pendingAmount = '0.00';

          // 显示登录提示
          setTimeout(() => {
            uni.showModal({
              title: '登录提示',
              content: '请先登录查看结算记录',
              confirmText: '去登录',
              cancelText: '稍后',
              success: (res) => {
                if (res.confirm) {
                  redirectToLogin();
                }
              }
            });
          }, 500);
          return;
        }
        await this.loadSettlements();
        await this.loadPendingAmount();
      } catch (error) {
        console.error('检查登录状态失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    },

    // 加载结算记录
    async loadSettlements(reset = true) {
      if (this.loading) return;

      this.loading = true;

      try {
        if (reset) {
          this.currentPage = 1;
          this.settlements = [];
          this.hasMore = true;
        }

        // 获取状态过滤参数
        let dstatus = -1; // 默认全部
        if (this.currentTab === 2) dstatus = 2; // 结算中
        else if (this.currentTab === 3) dstatus = 1; // 已结算
        else if (this.currentTab === 4) dstatus = 3; // 结算失败

        const offset = (this.currentPage - 1) * this.pageSize;

        const response = await request({
          url: '/user/ajax2.php',
          method: 'POST',
          data: {
            act: 'settleList',
            dstatus: dstatus,
            offset: offset,
            limit: this.pageSize
          }
        });

        if (response && response.rows) {
          const formattedData = response.rows.map(item => this.formatSettlementItem(item));

          if (reset) {
            this.settlements = formattedData;
          } else {
            this.settlements = this.settlements.concat(formattedData);
          }

          this.total = response.total || 0;
          this.hasMore = this.settlements.length < this.total;
          this.currentPage++;
        }
      } catch (error) {
        console.error('加载结算记录失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        uni.stopPullDownRefresh();
      }
    },

    // 格式化结算记录数据
    formatSettlementItem(item) {
      // 状态映射: 0=待结算, 1=已完成, 2=正在结算, 3=结算失败
      let status = 'pending';
      if (item.status == 1) status = 'completed';
      else if (item.status == 2) status = 'pending';
      else if (item.status == 3) status = 'failed';

      // 结算方式映射
      let typeText = '自动结算';
      if (item.type == 1) typeText = '支付宝结算';
      else if (item.type == 2) typeText = '微信结算';
      else if (item.type == 3) typeText = 'QQ钱包结算';
      else if (item.type == 4) typeText = '银行卡结算';
      else if (item.type == 5) typeText = '支付机构结算';

      if (item.auto != 1) typeText += '(手动)';

      // 格式化银行信息
      let bankInfo = item.account;
      if (item.account && item.account.length > 4) {
        const lastFour = item.account.slice(-4);
        bankInfo = `${item.username || ''}(尾号${lastFour})`;
      }

      return {
        id: item.id,
        title: typeText,
        amount: parseFloat(item.money || 0).toFixed(2),
        realmoney: parseFloat(item.realmoney || 0).toFixed(2),
        time: this.formatTime(item.addtime),
        bank: bankInfo,
        status: status,
        addtime: item.addtime,
        endtime: item.endtime,
        arrivalTime: item.endtime ? this.formatTime(item.endtime) : '',
        jumpurl: item.jumpurl || '',
        failReason: '',
        rawData: item
      };
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';
      const date = new Date(timeStr);
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 加载待结算金额
    async loadPendingAmount() {
      try {
        // 获取商户信息，包含待结算金额
        const response = await request({
          url: '/user/ajax2.php',
          method: 'POST',
          data: {
            act: 'getcount'
          }
        });

        if (response && response.money !== undefined) {
          this.pendingAmount = parseFloat(response.money || 0).toFixed(2);
        }
      } catch (error) {
        console.error('加载待结算金额失败:', error);
      }
    },

    // 加载更多数据
    async loadMoreData() {
      await this.loadSettlements(false);
    },

    // 刷新数据
    async refreshData() {
      await this.loadSettlements(true);
      await this.loadPendingAmount();
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 切换选项卡
    async changeTab(index) {
      if (this.currentTab === index) return;

      this.currentTab = index;
      await this.loadSettlements(true);
    },

    // 重新结算
    async retrySettlement(item) {
      uni.showModal({
        title: '确认重新结算',
        content: `确定要重新结算 ¥${item.amount} 吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({
                title: '处理中...'
              });

              // 这里可以调用重新结算的接口
              // const response = await request({
              //   url: '/user/ajax2.php',
              //   method: 'POST',
              //   data: {
              //     act: 'retrySettle',
              //     id: item.id
              //   }
              // });

              uni.hideLoading();
              uni.showToast({
                title: '重新结算请求已提交',
                icon: 'success'
              });

              // 刷新数据
              await this.refreshData();
            } catch (error) {
              uni.hideLoading();
              console.error('重新结算失败:', error);
              uni.showToast({
                title: '操作失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },

    // 查看失败原因
    async showFailReason(item) {
      try {
        uni.showLoading({
          title: '加载中...'
        });

        const response = await request({
          url: '/user/ajax2.php',
          method: 'POST',
          data: {
            act: 'settle_result',
            id: item.id
          }
        });

        uni.hideLoading();

        if (response && response.code === 0) {
          uni.showModal({
            title: '失败原因',
            content: response.msg || '未知原因',
            showCancel: false
          });
        } else {
          uni.showToast({
            title: response?.msg || '获取失败原因失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('获取失败原因失败:', error);
        uni.showToast({
          title: '获取失败',
          icon: 'none'
        });
      }
    },

    // 显示收款二维码
    showQrcode(item) {
      if (!item.jumpurl) return;

      uni.showModal({
        title: '收款确认',
        content: '请使用微信扫描二维码确认收款',
        confirmText: '打开链接',
        success: (res) => {
          if (res.confirm) {
            // 在小程序中可以复制链接让用户手动打开
            uni.setClipboardData({
              data: item.jumpurl,
              success: () => {
                uni.showToast({
                  title: '链接已复制',
                  icon: 'success'
                });
              }
            });
          }
        }
      });
    }
  }
};
</script>

<style>
/* 避免使用任何复杂的布局技术，确保所有元素都按照预期显示 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, Helvetica, Arial, sans-serif;
}

.page-container {
  width: 100%;
  background-color: #f5f5f5;
  min-height: 100vh;
  position: relative;
  padding-bottom: 120rpx; /* 为底部导航栏留出空间 */
}

/* 优雅现代导航栏样式 */
.elegant-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 50%, #8B5CF6 100%);
  box-shadow: 0 4rpx 20rpx rgba(81, 69, 247, 0.15);
}

.status-bar {
  width: 100%;
}

.navbar-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  min-height: 88rpx;
}

.navbar-left {
  width: 80rpx;
  display: flex;
  justify-content: flex-start;
}

.back-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.back-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}

.navbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.navbar-title {
  font-size: 36rpx;
  color: #fff;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.navbar-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}

/* 导航栏占位 - 动态计算高度 */
.navbar-placeholder {
  width: 100%;
  flex-shrink: 0;
  /* 高度由JavaScript动态计算设置 */
}

/* 简单的选项卡样式 */
.tab-section {
  background-color: #fff;
  display: flex;
  border-bottom: 1px solid #eee;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  font-size: 14px;
  color: #666;
  position: relative;
}

.tab.active {
  color: #5145F7;
  font-weight: bold;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 2px;
  background-color: #5145F7;
}

/* 待结算金额卡片 */
.pending-section {
  padding: 15px;
}

.pending-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  position: relative;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.pending-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.pending-amount {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.pending-note {
  display: block;
  font-size: 12px;
  color: #999;
  margin-bottom: 15px;
  width: 70%;
}

.settle-now-btn {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #5145F7;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
}

/* 月份标题 */
.month-title {
  padding: 15px;
  font-size: 15px;
  color: #666;
  font-weight: 500;
}

/* 结算记录列表 */
.record-list {
  padding: 0 15px;
}

.record-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  position: relative;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.record-top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.record-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.record-amount {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.failed-amount {
  color: #FF3B30;
}

.record-time {
  display: block;
  font-size: 13px;
  color: #999;
  margin-bottom: 10px;
}

.record-bank-row, .record-row {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  color: #666;
  margin-bottom: 5px;
}

.record-label {
  color: #666;
}

.record-value {
  color: #333;
}

.error-text {
  color: #FF3B30;
}

.retry-button {
  margin-top: 10px;
  padding: 8px 0;
  text-align: center;
  border: 1px solid #5145F7;
  border-radius: 4px;
  color: #5145F7;
  font-size: 14px;
}

/* 状态标签 - 关键是这里要放在靠下的位置 */
.status-tag {
  display: inline-block;
  font-size: 12px;
  padding: 3px 10px;
  border-radius: 100px;
  margin-top: 10px;
}

.pending-tag {
  color: #1890FF;
  background-color: rgba(24, 144, 255, 0.1);
}

.success-tag {
  color: #4CD964;
  background-color: rgba(76, 217, 100, 0.1);
}

.error-tag {
  color: #FF3B30;
  background-color: rgba(255, 59, 48, 0.1);
}

/* 确认收款按钮 */
.qr-button {
  margin-top: 10px;
  padding: 8px 0;
  text-align: center;
  border: 1px solid #4CD964;
  border-radius: 4px;
  color: #4CD964;
  font-size: 14px;
}

/* 加载和空数据状态 */
.loading-container, .empty-container {
  padding: 60px 20px;
  text-align: center;
}

.loading-text, .empty-text {
  color: #999;
  font-size: 14px;
}

/* 底部加载提示 */
.load-more-container {
  padding: 20px;
  text-align: center;
}

.load-more-text {
  color: #999;
  font-size: 12px;
}

/* 失败原因点击样式 */
.error-text {
  color: #FF3B30;
  text-decoration: underline;
}
</style>

