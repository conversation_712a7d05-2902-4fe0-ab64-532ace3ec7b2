import{y as e,d as t,e as a,w as l,i as s,o as n,f as c,h as o,t as d,C as u,D as i,F as h,j as f,l as m,k as r,aa as _,ab as p,z as y}from"./index-B1Q521gi.js";import{r as k,_ as g}from"./uni-app.es.DAfa8VxY.js";import{_ as w,a as M}from"./shanfu.Cee0BMqH.js";import{_ as F}from"./alipay.TVRnAsOv.js";import{_ as P}from"./_plugin-vue_export-helper.BCo6x5W8.js";const I=P({data(){const e=new Date;return e.getFullYear(),e.getMonth(),{dateValue:"",selectedMonth:"2025年4月",startDate:"2024-01",endDate:"2025-12",totalIncome:"42,58.90",totalCount:152,settledAmount:"38,62.00",pendingAmount:"4,06.90",showMonthPicker:!1,monthPickerValue:[1,3],years:[2024,2025,2026,2027,2028],months:Array.from({length:12},((e,t)=>t+1)),tempSelectedMonth:"",wechatInfo:{count:48,amount:"26,392.72",settled:"24,280.00",pending:"2,112.72"},alipayInfo:{count:36,amount:"12,583.40",settled:"11,382.00",pending:"1,201.40"},cloudpayInfo:{count:23,amount:"3,592.78",settled:"2,600.00",pending:"992.78"},dailyList:[{month:"4月",day:"14",weekday:"今天",count:23,amount:"2,586.50",status:"待结算"},{month:"4月",day:"13",weekday:"昨天",count:32,amount:"3,280.20",status:"已结算"},{month:"4月",day:"12",weekday:"周五",count:28,amount:"2,835.60",status:"已结算"},{month:"4月",day:"11",weekday:"周四",count:26,amount:"2,745.80",status:"已结算"}]}},onLoad(){const e=new Date,t=e.getFullYear(),a=e.getMonth()+1;this.dateValue=`${t}-${String(a).padStart(2,"0")}`,this.selectedMonth=`${t}年${a}月`;const l=this.years.findIndex((e=>e===t));this.monthPickerValue=[l>=0?l:0,a-1],this.loadData(t,a)},methods:{goBack(){e()},openDatePicker(){this.showMonthPicker=!0},onMonthPickerChange(e){this.monthPickerValue=e.detail.value,this.updateTempSelectedMonth()},updateTempSelectedMonth(){const e=this.years[this.monthPickerValue[0]],t=this.months[this.monthPickerValue[1]];this.tempSelectedMonth=`${e}-${t.toString().padStart(2,"0")}`},cancelMonthPicker(){this.showMonthPicker=!1,this.tempSelectedMonth=""},confirmMonthPicker(){if(this.tempSelectedMonth){const e=new Date(this.tempSelectedMonth),t=e.getFullYear(),a=e.getMonth()+1;this.selectedMonth=`${t}年${a}月`,this.dateValue=this.tempSelectedMonth,this.loadData(t,a)}this.showMonthPicker=!1,this.tempSelectedMonth=""},onDateChange(e){if(e){const t=new Date(e),a=t.getFullYear(),l=t.getMonth()+1;this.selectedMonth=`${a}年${l}月`,this.loadData(a,l)}},loadData(e,t){console.log(`加载${e}年${t}月的数据`)}}},[["render",function(e,P,I,C,D,S){const x=k(t("uni-icons"),g),$=m,v=s,V=r,b=_,j=p;return n(),a(v,{class:"container"},{default:l((()=>[c(v,{class:"header"},{default:l((()=>[c(v,{class:"header-left"},{default:l((()=>[c(x,{type:"left",color:"#FFFFFF",size:"24",onClick:S.goBack},null,8,["onClick"]),c($,{class:"header-title"},{default:l((()=>[o("对账单")])),_:1})])),_:1}),c(v,{class:"header-right"},{default:l((()=>[c(x,{type:"calendar",color:"#FFFFFF",size:"24"}),c(x,{type:"download",color:"#FFFFFF",size:"24",class:"margin-left"})])),_:1})])),_:1}),c(v,{class:"month-selector"},{default:l((()=>[c($,{class:"month-label"},{default:l((()=>[o("本月")])),_:1}),c(v,{class:"date-picker",onClick:S.openDatePicker},{default:l((()=>[c($,null,{default:l((()=>[o(d(D.selectedMonth),1)])),_:1}),c(x,{type:"bottom",size:"16",color:"#666666"})])),_:1},8,["onClick"])])),_:1}),c(v,{class:"income-summary"},{default:l((()=>[c(v,{class:"summary-row"},{default:l((()=>[c(v,{class:"summary-item"},{default:l((()=>[c($,{class:"item-label"},{default:l((()=>[o("总收入(元)")])),_:1}),c($,{class:"item-value"},{default:l((()=>[o("¥ "+d(D.totalIncome),1)])),_:1})])),_:1}),c(v,{class:"summary-item"},{default:l((()=>[c($,{class:"item-label"},{default:l((()=>[o("总笔数")])),_:1}),c($,{class:"item-value"},{default:l((()=>[o(d(D.totalCount),1)])),_:1})])),_:1})])),_:1}),c(v,{class:"summary-row"},{default:l((()=>[c(v,{class:"summary-item"},{default:l((()=>[c($,{class:"item-label"},{default:l((()=>[o("已结算(元)")])),_:1}),c($,{class:"item-value income"},{default:l((()=>[o("¥ "+d(D.settledAmount),1)])),_:1})])),_:1}),c(v,{class:"summary-item"},{default:l((()=>[c($,{class:"item-label"},{default:l((()=>[o("待结算(元)")])),_:1}),c($,{class:"item-value pending"},{default:l((()=>[o("¥ "+d(D.pendingAmount),1)])),_:1})])),_:1})])),_:1})])),_:1}),c(v,{class:"channel-section"},{default:l((()=>[c(v,{class:"section-header"},{default:l((()=>[c($,{class:"section-title"},{default:l((()=>[o("支付渠道对账")])),_:1}),c(v,{class:"section-actions"},{default:l((()=>[c($,{class:"action-text"},{default:l((()=>[o("筛选")])),_:1}),c($,{class:"action-text margin-left"},{default:l((()=>[o("导出")])),_:1})])),_:1})])),_:1}),c(v,{class:"payment-channel"},{default:l((()=>[c(v,{class:"channel-left"},{default:l((()=>[c(v,{class:"channel-icon wechat"},{default:l((()=>[c(V,{src:w,mode:"aspectFit",style:{width:"40rpx",height:"40rpx"}})])),_:1}),c(v,{class:"channel-info"},{default:l((()=>[c($,{class:"channel-name"},{default:l((()=>[o("微信支付")])),_:1}),c($,{class:"channel-count"},{default:l((()=>[o(d(D.wechatInfo.count)+"笔交易",1)])),_:1})])),_:1})])),_:1}),c(v,{class:"channel-right"},{default:l((()=>[c($,{class:"channel-amount"},{default:l((()=>[o("¥ "+d(D.wechatInfo.amount),1)])),_:1})])),_:1})])),_:1}),c(v,{class:"settlement-info wechat"},{default:l((()=>[c($,null,{default:l((()=>[o("已结算：¥ "+d(D.wechatInfo.settled),1)])),_:1}),c($,null,{default:l((()=>[o("待结算：¥ "+d(D.wechatInfo.pending),1)])),_:1})])),_:1}),c(v,{class:"payment-channel"},{default:l((()=>[c(v,{class:"channel-left"},{default:l((()=>[c(v,{class:"channel-icon alipay"},{default:l((()=>[c(V,{src:F,mode:"aspectFit",style:{width:"40rpx",height:"40rpx"}})])),_:1}),c(v,{class:"channel-info"},{default:l((()=>[c($,{class:"channel-name"},{default:l((()=>[o("支付宝")])),_:1}),c($,{class:"channel-count"},{default:l((()=>[o(d(D.alipayInfo.count)+"笔交易",1)])),_:1})])),_:1})])),_:1}),c(v,{class:"channel-right"},{default:l((()=>[c($,{class:"channel-amount"},{default:l((()=>[o("¥ "+d(D.alipayInfo.amount),1)])),_:1})])),_:1})])),_:1}),c(v,{class:"settlement-info alipay"},{default:l((()=>[c($,null,{default:l((()=>[o("已结算：¥ "+d(D.alipayInfo.settled),1)])),_:1}),c($,null,{default:l((()=>[o("待结算：¥ "+d(D.alipayInfo.pending),1)])),_:1})])),_:1}),c(v,{class:"payment-channel"},{default:l((()=>[c(v,{class:"channel-left"},{default:l((()=>[c(v,{class:"channel-icon cloudpay"},{default:l((()=>[c(V,{src:M,mode:"aspectFit",style:{width:"40rpx",height:"40rpx"}})])),_:1}),c(v,{class:"channel-info"},{default:l((()=>[c($,{class:"channel-name"},{default:l((()=>[o("云闪付")])),_:1}),c($,{class:"channel-count"},{default:l((()=>[o(d(D.cloudpayInfo.count)+"笔交易",1)])),_:1})])),_:1})])),_:1}),c(v,{class:"channel-right"},{default:l((()=>[c($,{class:"channel-amount"},{default:l((()=>[o("¥ "+d(D.cloudpayInfo.amount),1)])),_:1})])),_:1})])),_:1}),c(v,{class:"settlement-info cloudpay"},{default:l((()=>[c($,null,{default:l((()=>[o("已结算：¥ "+d(D.cloudpayInfo.settled),1)])),_:1}),c($,null,{default:l((()=>[o("待结算：¥ "+d(D.cloudpayInfo.pending),1)])),_:1})])),_:1})])),_:1}),c(v,{class:"daily-section"},{default:l((()=>[c($,{class:"daily-title"},{default:l((()=>[o("日对账单")])),_:1}),(n(!0),u(h,null,i(D.dailyList,((e,t)=>(n(),a(v,{key:t,class:y(["daily-item",{"last-item":t===D.dailyList.length-1}])},{default:l((()=>[c(v,{class:"daily-left"},{default:l((()=>[c(v,{class:"daily-date"},{default:l((()=>[c($,{class:"date-month"},{default:l((()=>[o(d(e.month),1)])),_:2},1024),c($,{class:"date-day"},{default:l((()=>[o(d(e.day),1)])),_:2},1024)])),_:2},1024),c(v,{class:"daily-info"},{default:l((()=>[c($,{class:"daily-weekday"},{default:l((()=>[o(d(e.weekday),1)])),_:2},1024),c($,{class:"daily-count"},{default:l((()=>[o(d(e.count)+"笔交易",1)])),_:2},1024)])),_:2},1024)])),_:2},1024),c(v,{class:"daily-right"},{default:l((()=>[c($,{class:"daily-amount"},{default:l((()=>[o("¥ "+d(e.amount),1)])),_:2},1024),c($,{class:y(["daily-status",{settled:"已结算"===e.status,pending:"待结算"===e.status}])},{default:l((()=>[o(d(e.status),1)])),_:2},1032,["class"])])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1}),c(v,{class:"view-all"},{default:l((()=>[c($,null,{default:l((()=>[o("查看全部")])),_:1})])),_:1}),D.showMonthPicker?(n(),a(v,{key:0,class:"month-picker-popup"},{default:l((()=>[c(v,{class:"month-picker-mask",onClick:S.cancelMonthPicker},null,8,["onClick"]),c(v,{class:"month-picker-container"},{default:l((()=>[c(v,{class:"month-picker-header"},{default:l((()=>[c(v,{class:"month-picker-action",onClick:S.cancelMonthPicker},{default:l((()=>[o("取消")])),_:1},8,["onClick"]),c(v,{class:"month-picker-title"},{default:l((()=>[o("选择月份")])),_:1}),c(v,{class:"month-picker-action confirm",onClick:S.confirmMonthPicker},{default:l((()=>[o("确定")])),_:1},8,["onClick"])])),_:1}),c(j,{class:"month-picker-view",value:D.monthPickerValue,onChange:S.onMonthPickerChange},{default:l((()=>[c(b,null,{default:l((()=>[(n(!0),u(h,null,i(D.years,((e,t)=>(n(),a(v,{class:"picker-item",key:"year-"+t},{default:l((()=>[o(d(e)+"年",1)])),_:2},1024)))),128))])),_:1}),c(b,null,{default:l((()=>[(n(!0),u(h,null,i(D.months,((e,t)=>(n(),a(v,{class:"picker-item",key:"month-"+t},{default:l((()=>[o(d(e)+"月",1)])),_:2},1024)))),128))])),_:1})])),_:1},8,["value","onChange"])])),_:1})])),_:1})):f("",!0)])),_:1})}],["__scopeId","data-v-1e8983e4"]]);export{I as default};
