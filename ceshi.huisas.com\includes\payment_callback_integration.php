<?php
/**
 * 支付回调集成示例
 * 展示如何在现有支付回调中集成WebSocket通知
 */

// 引入WebSocket通知模块 (基于AI客服版本)
require_once dirname(__DIR__) . '/websocket_notify.php';

/**
 * 支付宝回调集成示例
 */
function alipayCallbackWithWebSocket($params) {
    // 原有的支付宝回调处理逻辑
    $trade_no = $params['out_trade_no'] ?? '';
    $total_amount = $params['total_amount'] ?? '0.00';
    $trade_status = $params['trade_status'] ?? '';
    
    // 验证签名等原有逻辑...
    
    if ($trade_status === 'TRADE_SUCCESS') {
        // 更新数据库订单状态等原有逻辑...
        
        // 构建WebSocket通知数据
        $orderData = [
            'trade_no' => $trade_no,
            'uid' => getUserIdByTradeNo($trade_no), // 根据订单号获取用户ID
            'money' => $total_amount,
            'type' => 'alipay',
            'addtime' => date('Y-m-d H:i:s'),
            'api_trade_no' => $params['trade_no'] ?? '',
            'buyer' => $params['buyer_email'] ?? '',
            'status' => 'success'
        ];
        
        // 发送WebSocket通知
        $notifyResult = sendWebSocketPaymentNotification($orderData);
        
        // 记录通知结果
        if ($notifyResult) {
            error_log("WebSocket通知发送成功: {$trade_no}");
        } else {
            error_log("WebSocket通知发送失败: {$trade_no}");
        }
    }
    
    return 'success';
}

/**
 * 微信支付回调集成示例
 */
function wechatCallbackWithWebSocket($params) {
    // 原有的微信支付回调处理逻辑
    $out_trade_no = $params['out_trade_no'] ?? '';
    $total_fee = $params['total_fee'] ?? 0;
    $result_code = $params['result_code'] ?? '';
    
    // 验证签名等原有逻辑...
    
    if ($result_code === 'SUCCESS') {
        // 更新数据库订单状态等原有逻辑...
        
        // 构建WebSocket通知数据
        $orderData = [
            'trade_no' => $out_trade_no,
            'uid' => getUserIdByTradeNo($out_trade_no),
            'money' => number_format($total_fee / 100, 2), // 微信金额单位是分
            'type' => 'wxpay',
            'addtime' => date('Y-m-d H:i:s'),
            'api_trade_no' => $params['transaction_id'] ?? '',
            'buyer' => $params['openid'] ?? '',
            'status' => 'success'
        ];
        
        // 发送WebSocket通知
        sendWebSocketPaymentNotification($orderData);
    }
    
    return '<xml><return_code><![CDATA[SUCCESS]]></return_code></xml>';
}

/**
 * 通用支付回调集成函数
 */
function processPaymentCallbackWithWebSocket($orderInfo) {
    try {
        // 验证订单信息
        if (empty($orderInfo['trade_no']) || empty($orderInfo['money'])) {
            throw new Exception('订单信息不完整');
        }
        
        // 更新数据库订单状态
        $updateResult = updateOrderStatus($orderInfo);
        
        if (!$updateResult) {
            throw new Exception('更新订单状态失败');
        }
        
        // 发送WebSocket通知
        $notifyResult = sendWebSocketPaymentNotification($orderInfo);
        
        // 记录处理结果
        $logData = [
            'trade_no' => $orderInfo['trade_no'],
            'amount' => $orderInfo['money'],
            'type' => $orderInfo['type'] ?? 'unknown',
            'websocket_notify' => $notifyResult ? 'success' : 'failed',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        error_log('支付回调处理: ' . json_encode($logData, JSON_UNESCAPED_UNICODE));
        
        return true;
        
    } catch (Exception $e) {
        error_log('支付回调处理异常: ' . $e->getMessage());
        return false;
    }
}

/**
 * 批量处理支付通知（用于补发通知）
 */
function batchSendPaymentNotifications($orderList) {
    $successCount = 0;
    $failCount = 0;
    
    foreach ($orderList as $order) {
        $result = sendWebSocketPaymentNotification($order);
        
        if ($result) {
            $successCount++;
        } else {
            $failCount++;
        }
        
        // 避免请求过快
        usleep(100000); // 0.1秒
    }
    
    return [
        'total' => count($orderList),
        'success' => $successCount,
        'failed' => $failCount
    ];
}

/**
 * 🔒 获取用户ID（根据订单号查询商户ID）
 */
function getUserIdByTradeNo($trade_no) {
    try {
        // 引入数据库连接
        require_once __DIR__ . '/common.php';

        // 🔧 使用 pre_ 前缀，PdoHelper 会自动替换为 pay_
        $sql = "SELECT uid FROM pre_order WHERE trade_no = ? LIMIT 1";
        $result = $DB->getRow($sql, [$trade_no]);

        if ($result && isset($result['uid'])) {
            error_log("✅ 订单 {$trade_no} 对应商户ID: {$result['uid']}");
            return $result['uid'];
        }

        // 如果在pre_order表中没找到，尝试在其他可能的表中查找
        $tables = ['pre_orders', 'pre_record', 'pre_refundorder'];
        foreach ($tables as $table) {
            $sql = "SELECT uid FROM {$table} WHERE trade_no = ? LIMIT 1";
            $result = $DB->getRow($sql, [$trade_no]);
            if ($result && isset($result['uid'])) {
                error_log("✅ 在表 {$table} 中找到订单 {$trade_no}，商户ID: {$result['uid']}");
                return $result['uid'];
            }
        }

        error_log("❌ 未找到订单 {$trade_no} 对应的商户ID");
        return '';

    } catch (Exception $e) {
        error_log("❌ 查询商户ID失败: " . $e->getMessage());
        return '';
    }
}

/**
 * 更新订单状态（示例函数，需要根据实际数据库结构实现）
 */
function updateOrderStatus($orderInfo) {
    // 这里应该根据实际的数据库结构来更新订单状态
    // 示例代码：
    /*
    global $DB;
    $result = $DB->exec("UPDATE pay_order SET status = 1, endtime = NOW() WHERE trade_no = ?", [$orderInfo['trade_no']]);
    return $result !== false;
    */
    
    return true; // 临时返回true
}

/**
 * WebSocket服务健康检查和自动重启
 */
function checkAndRestartWebSocketService() {
    $isRunning = checkWebSocketServiceStatus();
    
    if (!$isRunning) {
        error_log('WebSocket服务未运行，尝试重启...');
        
        // 尝试重启服务
        $restartCommand = 'cd /www/wwwroot/ceshi.huisas.com/websocket_workerman && php start.php restart > /dev/null 2>&1 &';
        exec($restartCommand);
        
        // 等待几秒后再次检查
        sleep(3);
        $isRunningAfterRestart = checkWebSocketServiceStatus();
        
        if ($isRunningAfterRestart) {
            error_log('WebSocket服务重启成功');
            return true;
        } else {
            error_log('WebSocket服务重启失败');
            return false;
        }
    }
    
    return true;
}

/**
 * 定时任务：检查WebSocket服务状态
 * 可以添加到crontab中：每5分钟执行一次 php -f /path/to/this/file.php check_service
 */
if (isset($argv[1]) && $argv[1] === 'check_service') {
    checkAndRestartWebSocketService();
    
    // 获取服务统计信息
    $stats = (new WebSocketNotifyWorkerman())->getServiceStats();
    if ($stats) {
        echo "WebSocket服务统计:\n";
        echo "运行时间: {$stats['uptime_formatted']}\n";
        echo "总请求数: {$stats['total_requests']}\n";
        echo "支付通知数: {$stats['payment_notifications']}\n";
        echo "当前连接数: " . ($stats['websocket_clients'] ?? 0) . "\n";
    }
}
