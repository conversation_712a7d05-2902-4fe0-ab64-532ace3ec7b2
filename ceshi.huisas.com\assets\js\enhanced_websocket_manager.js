/**
 * 增强的WebSocket管理器
 * 专门用于解决长时间连接断开和重连问题
 * 
 * 特性：
 * 1. 🔄 智能重连机制 - 指数退避算法
 * 2. 💓 增强心跳检测 - 双向心跳验证
 * 3. 🔍 连接状态监控 - 实时状态检查
 * 4. ⚡ 快速故障恢复 - 自动故障检测
 * 5. 📊 连接质量统计 - 连接质量分析
 */

class EnhancedWebSocketManager {
    constructor(options = {}) {
        this.options = {
            url: 'ws://160.202.244.93:8080',
            heartbeatInterval: 15000,      // 心跳间隔15秒
            heartbeatTimeout: 5000,        // 心跳超时5秒
            reconnectInterval: 2000,       // 基础重连间隔2秒
            maxReconnectAttempts: 100,     // 最大重连次数
            connectionTimeout: 8000,       // 连接超时8秒
            pingPongTimeout: 10000,        // ping-pong超时10秒
            debug: true,
            ...options
        };
        
        // 连接状态
        this.ws = null;
        this.isConnected = false;
        this.isConnecting = false;
        this.connectionId = null;
        this.reconnectAttempts = 0;
        
        // 定时器
        this.heartbeatTimer = null;
        this.heartbeatTimeoutTimer = null;
        this.reconnectTimer = null;
        this.connectionTimer = null;
        this.statusCheckTimer = null;
        
        // 心跳状态
        this.lastPingTime = null;
        this.lastPongTime = null;
        this.pendingPings = new Set();
        
        // 统计信息
        this.stats = {
            connectTime: null,
            totalReconnects: 0,
            totalMessages: 0,
            heartbeatFailures: 0,
            lastDisconnectTime: null,
            connectionQuality: 'unknown'
        };
        
        // 事件回调
        this.callbacks = {
            onConnect: [],
            onDisconnect: [],
            onMessage: [],
            onPaymentNotification: [],
            onError: [],
            onStatusChange: []
        };
        
        this.init();
    }
    
    /**
     * 初始化
     */
    init() {
        this.log('🚀 初始化增强WebSocket管理器');
        this.startStatusMonitor();
        this.connect();
    }
    
    /**
     * 连接WebSocket
     */
    connect() {
        if (this.isConnected || this.isConnecting) {
            this.log('⚠️ WebSocket已连接或正在连接中');
            return;
        }
        
        this.isConnecting = true;
        this.log(`🔗 正在连接WebSocket... (第${this.reconnectAttempts + 1}次尝试)`);
        
        // 设置连接超时
        this.connectionTimer = setTimeout(() => {
            if (this.isConnecting) {
                this.log('⏰ WebSocket连接超时', 'error');
                this.handleConnectionFailure('连接超时');
            }
        }, this.options.connectionTimeout);
        
        try {
            this.ws = new WebSocket(this.options.url);
            
            this.ws.onopen = (event) => {
                this.handleConnectionSuccess(event);
            };
            
            this.ws.onmessage = (event) => {
                this.handleMessage(event);
            };
            
            this.ws.onclose = (event) => {
                this.handleConnectionClose(event);
            };
            
            this.ws.onerror = (event) => {
                this.handleConnectionError(event);
            };
            
        } catch (error) {
            this.log('❌ WebSocket连接异常:', error);
            this.handleConnectionFailure('连接异常: ' + error.message);
        }
    }
    
    /**
     * 处理连接成功
     */
    handleConnectionSuccess(event) {
        this.clearConnectionTimer();
        this.isConnected = true;
        this.isConnecting = false;
        this.stats.connectTime = Date.now();
        this.stats.totalReconnects = this.reconnectAttempts;
        this.reconnectAttempts = 0;
        
        this.log('✅ WebSocket连接成功');
        this.updateConnectionQuality('good');
        this.startHeartbeat();
        this.trigger('onConnect', event);
        this.trigger('onStatusChange', { status: 'connected', quality: 'good' });
    }
    
    /**
     * 处理消息
     */
    handleMessage(event) {
        try {
            const data = JSON.parse(event.data);
            this.stats.totalMessages++;
            
            // 处理不同类型的消息
            switch (data.type) {
                case 'welcome':
                    this.connectionId = data.data?.connection_id;
                    this.log('🎉 收到欢迎消息，连接ID:', this.connectionId);
                    break;
                    
                case 'pong':
                    this.handlePongMessage(data);
                    break;
                    
                case 'heartbeat':
                    // 服务器主动心跳，回复pong
                    this.send({ type: 'pong', timestamp: Date.now() });
                    break;
                    
                case 'payment_notification':
                    this.handlePaymentNotification(data.data);
                    break;
                    
                default:
                    this.log('📨 收到消息:', data);
            }
            
            this.trigger('onMessage', data);
            
        } catch (error) {
            this.log('❌ 解析消息失败:', error);
        }
    }
    
    /**
     * 处理pong消息
     */
    handlePongMessage(data) {
        const pingId = data.timestamp || data.ping_id;
        if (pingId && this.pendingPings.has(pingId)) {
            this.pendingPings.delete(pingId);
            this.lastPongTime = Date.now();
            const rtt = this.lastPongTime - pingId;
            this.log(`💓 心跳响应正常 (RTT: ${rtt}ms)`);
            
            // 更新连接质量
            if (rtt < 100) {
                this.updateConnectionQuality('excellent');
            } else if (rtt < 500) {
                this.updateConnectionQuality('good');
            } else {
                this.updateConnectionQuality('poor');
            }
            
            // 清除心跳超时定时器
            if (this.heartbeatTimeoutTimer) {
                clearTimeout(this.heartbeatTimeoutTimer);
                this.heartbeatTimeoutTimer = null;
            }
        }
    }
    
    /**
     * 处理支付通知
     */
    handlePaymentNotification(data) {
        this.log('💰 收到支付通知:', data);
        this.trigger('onPaymentNotification', data);
    }
    
    /**
     * 处理连接关闭
     */
    handleConnectionClose(event) {
        this.clearConnectionTimer();
        this.isConnected = false;
        this.isConnecting = false;
        this.connectionId = null;
        this.stats.lastDisconnectTime = Date.now();
        
        this.stopHeartbeat();
        this.updateConnectionQuality('disconnected');
        
        this.log(`🔌 WebSocket连接关闭: ${event.code} ${event.reason}`);
        this.trigger('onDisconnect', event);
        this.trigger('onStatusChange', { status: 'disconnected', code: event.code });
        
        // 自动重连（除非是正常关闭）
        if (event.code !== 1000) {
            this.scheduleReconnect();
        }
    }
    
    /**
     * 处理连接错误
     */
    handleConnectionError(event) {
        this.clearConnectionTimer();
        this.isConnecting = false;
        this.log('❌ WebSocket连接错误:', event);
        this.trigger('onError', event);
    }
    
    /**
     * 处理连接失败
     */
    handleConnectionFailure(reason) {
        this.clearConnectionTimer();
        this.isConnecting = false;
        this.log('💥 连接失败:', reason);
        this.scheduleReconnect();
    }
    
    /**
     * 启动心跳
     */
    startHeartbeat() {
        this.stopHeartbeat();
        
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected) {
                this.sendPing();
            }
        }, this.options.heartbeatInterval);
        
        this.log(`💓 心跳已启动，间隔: ${this.options.heartbeatInterval}ms`);
    }
    
    /**
     * 发送ping
     */
    sendPing() {
        const pingId = Date.now();
        this.lastPingTime = pingId;
        this.pendingPings.add(pingId);
        
        // 设置心跳超时
        this.heartbeatTimeoutTimer = setTimeout(() => {
            if (this.pendingPings.has(pingId)) {
                this.log('💔 心跳超时，连接可能已断开', 'warning');
                this.stats.heartbeatFailures++;
                this.handleHeartbeatFailure();
            }
        }, this.options.heartbeatTimeout);
        
        this.send({
            type: 'ping',
            timestamp: pingId,
            ping_id: pingId
        });
    }
    
    /**
     * 处理心跳失败
     */
    handleHeartbeatFailure() {
        this.updateConnectionQuality('poor');
        
        // 如果连续心跳失败，主动断开重连
        if (this.stats.heartbeatFailures >= 3) {
            this.log('💀 连续心跳失败，主动重连', 'error');
            this.disconnect();
            this.scheduleReconnect();
        }
    }
    
    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
        
        if (this.heartbeatTimeoutTimer) {
            clearTimeout(this.heartbeatTimeoutTimer);
            this.heartbeatTimeoutTimer = null;
        }
        
        this.pendingPings.clear();
    }
    
    /**
     * 计划重连
     */
    scheduleReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
            this.log('🛑 达到最大重连次数，停止重连', 'error');
            this.updateConnectionQuality('failed');
            return;
        }
        
        this.reconnectAttempts++;
        
        // 指数退避算法，但限制最大延迟
        const baseDelay = this.options.reconnectInterval;
        const delay = Math.min(baseDelay * Math.pow(1.5, this.reconnectAttempts - 1), 30000);
        
        this.log(`🔄 ${delay/1000}秒后尝试第${this.reconnectAttempts}次重连...`);
        this.updateConnectionQuality('reconnecting');
        
        this.reconnectTimer = setTimeout(() => {
            if (!this.isConnected && !this.isConnecting) {
                this.connect();
            }
        }, delay);
    }
    
    /**
     * 手动重连
     */
    reconnect() {
        this.log('🔄 手动重连...');
        this.reconnectAttempts = 0;
        this.stats.heartbeatFailures = 0;
        
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        this.disconnect();
        setTimeout(() => {
            this.connect();
        }, 1000);
    }
    
    /**
     * 发送消息
     */
    send(data) {
        if (this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN) {
            try {
                const message = JSON.stringify(data);
                this.ws.send(message);
                this.log('📤 发送消息:', data);
                return true;
            } catch (error) {
                this.log('❌ 发送消息失败:', error);
                return false;
            }
        } else {
            this.log('⚠️ WebSocket未连接，无法发送消息');
            return false;
        }
    }

    /**
     * 断开连接
     */
    disconnect() {
        this.log('🔌 断开WebSocket连接');

        // 清理所有定时器
        this.stopHeartbeat();
        this.clearConnectionTimer();
        this.stopStatusMonitor();

        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        // 重置状态
        this.isConnected = false;
        this.isConnecting = false;
        this.connectionId = null;
        this.reconnectAttempts = 0;
        this.stats.heartbeatFailures = 0;

        // 关闭WebSocket连接
        if (this.ws) {
            this.ws.close(1000, 'Client disconnect');
            this.ws = null;
        }

        this.updateConnectionQuality('disconnected');
    }

    /**
     * 清除连接定时器
     */
    clearConnectionTimer() {
        if (this.connectionTimer) {
            clearTimeout(this.connectionTimer);
            this.connectionTimer = null;
        }
    }

    /**
     * 启动状态监控
     */
    startStatusMonitor() {
        this.statusCheckTimer = setInterval(() => {
            this.checkConnectionHealth();
        }, 30000); // 每30秒检查一次连接健康状态
    }

    /**
     * 停止状态监控
     */
    stopStatusMonitor() {
        if (this.statusCheckTimer) {
            clearInterval(this.statusCheckTimer);
            this.statusCheckTimer = null;
        }
    }

    /**
     * 检查连接健康状态
     */
    checkConnectionHealth() {
        if (!this.isConnected) {
            return;
        }

        const now = Date.now();
        const timeSinceLastPong = this.lastPongTime ? now - this.lastPongTime : Infinity;

        // 如果超过2分钟没有收到pong响应，认为连接可能有问题
        if (timeSinceLastPong > 120000) {
            this.log('🔍 检测到连接可能异常，主动重连', 'warning');
            this.disconnect();
            this.scheduleReconnect();
        }
    }

    /**
     * 更新连接质量
     */
    updateConnectionQuality(quality) {
        if (this.stats.connectionQuality !== quality) {
            this.stats.connectionQuality = quality;
            this.log(`📊 连接质量更新: ${quality}`);
            this.trigger('onStatusChange', {
                status: this.isConnected ? 'connected' : 'disconnected',
                quality: quality
            });
        }
    }

    /**
     * 获取连接状态
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            isConnecting: this.isConnecting,
            connectionId: this.connectionId,
            reconnectAttempts: this.reconnectAttempts,
            stats: { ...this.stats },
            readyState: this.ws ? this.ws.readyState : -1,
            pendingPings: this.pendingPings.size,
            lastPingTime: this.lastPingTime,
            lastPongTime: this.lastPongTime
        };
    }

    /**
     * 添加事件监听
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * 移除事件监听
     */
    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     */
    trigger(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    this.log(`❌ 事件回调错误 (${event}):`, error);
                }
            });
        }
    }

    /**
     * 日志输出
     */
    log(message, type = 'info') {
        if (!this.options.debug) return;

        const timestamp = new Date().toLocaleTimeString();
        const prefix = `[${timestamp}] WebSocket`;

        switch (type) {
            case 'error':
                console.error(prefix, message);
                break;
            case 'warning':
                console.warn(prefix, message);
                break;
            case 'success':
                console.log(`%c${prefix} ${message}`, 'color: green');
                break;
            default:
                console.log(prefix, message);
        }
    }
}

// 导出类
window.EnhancedWebSocketManager = EnhancedWebSocketManager;

// 创建全局实例
let globalWebSocketManager = null;

/**
 * 初始化全局WebSocket管理器
 */
function initWebSocketManager(options = {}) {
    if (globalWebSocketManager) {
        globalWebSocketManager.disconnect();
    }

    globalWebSocketManager = new EnhancedWebSocketManager(options);
    return globalWebSocketManager;
}

/**
 * 获取全局WebSocket管理器
 */
function getWebSocketManager() {
    return globalWebSocketManager;
}

// 导出到全局
window.initWebSocketManager = initWebSocketManager;
window.getWebSocketManager = getWebSocketManager;
