<template>
  <view class="staff-stats-container">
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <view class="navbar-content">
        <view class="navbar-left" @tap="goBack">
          <text class="back-icon">←</text>
          <text class="navbar-title">收款统计</text>
        </view>
        
        <view class="navbar-right">
          <view class="action-btn" @tap="refreshStats">
            <text class="action-icon">🔄</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 时间筛选 -->
    <view class="time-filter">
      <view class="filter-tabs">
        <view 
          v-for="(tab, index) in timeTabs" 
          :key="index"
          class="filter-tab"
          :class="{ 'active': currentTimeTab === index }"
          @tap="switchTimeTab(index)"
        >
          {{ tab.name }}
        </view>
      </view>
      
      <view v-if="currentTimeTab === 3" class="custom-date">
        <picker mode="date" @change="onStartDateChange" :value="customDateRange.start">
          <view class="date-picker">
            <text class="date-text">{{ customDateRange.start || '开始日期' }}</text>
          </view>
        </picker>
        <text class="date-separator">至</text>
        <picker mode="date" @change="onEndDateChange" :value="customDateRange.end">
          <view class="date-picker">
            <text class="date-text">{{ customDateRange.end || '结束日期' }}</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 统计概览 -->
    <view class="stats-overview">
      <view class="overview-title">{{ getCurrentPeriodText() }}统计</view>
      <view class="stats-grid">
        <view class="stat-card primary">
          <view class="stat-icon">💰</view>
          <view class="stat-content">
            <text class="stat-value">¥{{ statsData.totalAmount || '0.00' }}</text>
            <text class="stat-label">总收款金额</text>
          </view>
        </view>
        
        <view class="stat-card success">
          <view class="stat-icon">📊</view>
          <view class="stat-content">
            <text class="stat-value">{{ statsData.totalCount || '0' }}</text>
            <text class="stat-label">总订单数量</text>
          </view>
        </view>
        
        <view class="stat-card warning">
          <view class="stat-icon">✅</view>
          <view class="stat-content">
            <text class="stat-value">{{ statsData.successCount || '0' }}</text>
            <text class="stat-label">成功订单</text>
          </view>
        </view>
        
        <view class="stat-card info">
          <view class="stat-icon">📈</view>
          <view class="stat-content">
            <text class="stat-value">{{ getSuccessRate() }}%</text>
            <text class="stat-label">成功率</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 支付方式统计 -->
    <view class="payment-stats">
      <view class="section-title">支付方式分布</view>
      <view class="payment-list">
        <view v-for="item in paymentStats" :key="item.type" class="payment-item">
          <view class="payment-info">
            <view class="payment-icon">{{ getPaymentIcon(item.type) }}</view>
            <view class="payment-details">
              <text class="payment-name">{{ getPaymentName(item.type) }}</text>
              <text class="payment-count">{{ item.count }}笔</text>
            </view>
          </view>
          <view class="payment-amount">¥{{ item.amount }}</view>
        </view>
      </view>
    </view>

    <!-- 每日统计趋势 -->
    <view class="daily-stats">
      <view class="section-title">每日收款趋势</view>
      <view v-if="loading" class="loading-state">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      
      <view v-else-if="dailyData.length === 0" class="empty-state">
        <text class="empty-text">暂无数据</text>
      </view>
      
      <view v-else class="daily-chart">
        <view v-for="item in dailyData" :key="item.date" class="chart-item">
          <view class="chart-bar">
            <view 
              class="bar-fill" 
              :style="{ height: getBarHeight(item.amount) + '%' }"
            ></view>
          </view>
          <view class="chart-info">
            <text class="chart-amount">¥{{ item.amount }}</text>
            <text class="chart-date">{{ formatChartDate(item.date) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 排名信息 -->
    <view class="ranking-info">
      <view class="section-title">我的排名</view>
      <view class="ranking-card">
        <view class="ranking-item">
          <text class="ranking-label">本月收款排名</text>
          <text class="ranking-value">第 {{ rankingData.monthRank || '-' }} 名</text>
        </view>
        <view class="ranking-item">
          <text class="ranking-label">今日收款排名</text>
          <text class="ranking-value">第 {{ rankingData.todayRank || '-' }} 名</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { request } from '@/utils/request.js'

export default {
  name: 'StaffStats',
  data() {
    return {
      staffInfo: null,
      currentTimeTab: 0,
      timeTabs: [
        { name: '今日', days: 1 },
        { name: '本周', days: 7 },
        { name: '本月', days: 30 },
        { name: '自定义', days: 0 }
      ],
      customDateRange: {
        start: '',
        end: ''
      },
      statsData: {
        totalAmount: '0.00',
        totalCount: 0,
        successCount: 0
      },
      paymentStats: [],
      dailyData: [],
      rankingData: {
        monthRank: 0,
        todayRank: 0
      },
      loading: false
    }
  },
  
  onLoad() {
    console.log('📱 员工统计页面加载')
    this.initPage()
  },
  
  onShow() {
    // 页面显示时刷新数据
    this.refreshStats()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      this.checkLoginStatus()
      this.loadStaffInfo()
      this.initCustomDateRange()
      this.loadStatsData()
    },
    
    // 检查登录状态
    checkLoginStatus() {
      const staffToken = uni.getStorageSync('staff_token')
      const staffInfo = uni.getStorageSync('staff_info')
      
      if (!staffToken || !staffInfo) {
        console.log('❌ 员工未登录，跳转到登录页')
        uni.reLaunch({
          url: '/pages/staff-login/index'
        })
        return false
      }
      
      return true
    },
    
    // 加载员工信息
    loadStaffInfo() {
      const staffInfo = uni.getStorageSync('staff_info')
      if (staffInfo) {
        this.staffInfo = staffInfo
        console.log('✅ 员工信息加载成功:', staffInfo)
      }
    },
    
    // 初始化自定义日期范围
    initCustomDateRange() {
      const today = new Date()
      const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      
      this.customDateRange.start = this.formatDate(lastWeek)
      this.customDateRange.end = this.formatDate(today)
    },
    
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    
    // 切换时间标签
    switchTimeTab(index) {
      this.currentTimeTab = index
      this.loadStatsData()
    },
    
    // 自定义开始日期变化
    onStartDateChange(e) {
      this.customDateRange.start = e.detail.value
      if (this.currentTimeTab === 3) {
        this.loadStatsData()
      }
    },
    
    // 自定义结束日期变化
    onEndDateChange(e) {
      this.customDateRange.end = e.detail.value
      if (this.currentTimeTab === 3) {
        this.loadStatsData()
      }
    },
    
    // 获取当前时间段文本
    getCurrentPeriodText() {
      return this.timeTabs[this.currentTimeTab].name
    },
    
    // 获取成功率
    getSuccessRate() {
      if (!this.statsData.totalCount) return '0'
      return Math.round((this.statsData.successCount / this.statsData.totalCount) * 100)
    },
    
    // 获取支付方式图标
    getPaymentIcon(type) {
      switch (type) {
        case 'alipay': return '💙'
        case 'wxpay': return '💚'
        case 'qqpay': return '💛'
        default: return '💳'
      }
    },
    
    // 获取支付方式名称
    getPaymentName(type) {
      switch (type) {
        case 'alipay': return '支付宝'
        case 'wxpay': return '微信支付'
        case 'qqpay': return 'QQ钱包'
        default: return '其他'
      }
    },
    
    // 获取柱状图高度
    getBarHeight(amount) {
      if (!this.dailyData.length) return 0
      
      const maxAmount = Math.max(...this.dailyData.map(item => parseFloat(item.amount)))
      if (maxAmount === 0) return 0
      
      return Math.max((parseFloat(amount) / maxAmount) * 100, 5)
    },
    
    // 格式化图表日期
    formatChartDate(dateStr) {
      const date = new Date(dateStr)
      return `${date.getMonth() + 1}/${date.getDate()}`
    },
    
    // 刷新统计数据
    refreshStats() {
      this.loadStatsData()
    },
    
    // 加载统计数据
    async loadStatsData() {
      if (!this.checkLoginStatus() || this.loading) return
      
      try {
        this.loading = true
        console.log('🔄 加载员工统计数据...')
        
        // 计算日期范围
        let startDate, endDate
        if (this.currentTimeTab === 3) {
          startDate = this.customDateRange.start
          endDate = this.customDateRange.end
        } else {
          const today = new Date()
          const days = this.timeTabs[this.currentTimeTab].days
          const start = new Date(today.getTime() - (days - 1) * 24 * 60 * 60 * 1000)
          startDate = this.formatDate(start)
          endDate = this.formatDate(today)
        }
        
        // 并行加载数据
        await Promise.all([
          this.loadOverviewStats(startDate, endDate),
          this.loadPaymentStats(startDate, endDate),
          this.loadDailyStats(startDate, endDate),
          this.loadRankingData()
        ])
        
        console.log('✅ 员工统计数据加载完成')
        
      } catch (error) {
        console.error('❌ 加载统计数据失败:', error)
        
        uni.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 加载概览统计
    async loadOverviewStats(startDate, endDate) {
      try {
        const response = await request({
          url: '/user/staff.php?act=getStaffOverviewStats',
          method: 'POST',
          data: {
            staff_id: this.staffInfo?.id,
            start_date: startDate,
            end_date: endDate
          }
        })
        
        if (response && response.code === 0) {
          this.statsData = response.data || this.statsData
        }
      } catch (error) {
        console.error('❌ 加载概览统计失败:', error)
      }
    },
    
    // 加载支付方式统计
    async loadPaymentStats(startDate, endDate) {
      try {
        const response = await request({
          url: '/user/staff.php?act=getStaffPaymentStats',
          method: 'POST',
          data: {
            staff_id: this.staffInfo?.id,
            start_date: startDate,
            end_date: endDate
          }
        })
        
        if (response && response.code === 0) {
          this.paymentStats = response.data || []
        }
      } catch (error) {
        console.error('❌ 加载支付方式统计失败:', error)
      }
    },
    
    // 加载每日统计
    async loadDailyStats(startDate, endDate) {
      try {
        const response = await request({
          url: '/user/staff.php?act=getStaffDailyStats',
          method: 'POST',
          data: {
            staff_id: this.staffInfo?.id,
            start_date: startDate,
            end_date: endDate
          }
        })
        
        if (response && response.code === 0) {
          this.dailyData = response.data || []
        }
      } catch (error) {
        console.error('❌ 加载每日统计失败:', error)
      }
    },
    
    // 加载排名数据
    async loadRankingData() {
      try {
        const response = await request({
          url: '/user/staff.php?act=getStaffRanking',
          method: 'POST',
          data: {
            staff_id: this.staffInfo?.id
          }
        })
        
        if (response && response.code === 0) {
          this.rankingData = response.data || this.rankingData
        }
      } catch (error) {
        console.error('❌ 加载排名数据失败:', error)
      }
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.staff-stats-container {
  min-height: 100vh;
  background: #f5f6fa;
}

.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  color: #ffffff;
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
}

.navbar-right {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 28rpx;
}

.time-filter {
  background: #ffffff;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-tabs {
  display: flex;
  margin-bottom: 20rpx;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666666;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s;
}

.filter-tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
  font-weight: bold;
}

.custom-date {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.date-picker {
  flex: 1;
  height: 60rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #e9ecef;
}

.date-text {
  font-size: 28rpx;
  color: #333333;
}

.date-separator {
  font-size: 28rpx;
  color: #666666;
}

.stats-overview {
  padding: 40rpx;
  margin-top: -20rpx;
}

.overview-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stat-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.stat-card.success {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: #ffffff;
}

.stat-card.warning {
  background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
  color: #ffffff;
}

.stat-card.info {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: #ffffff;
}

.stat-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

.payment-stats, .daily-stats, .ranking-info {
  margin: 40rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
}

.payment-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.payment-info {
  display: flex;
  align-items: center;
}

.payment-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.payment-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.payment-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.payment-count {
  font-size: 24rpx;
  color: #666666;
}

.payment-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #667eea;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

.empty-state {
  text-align: center;
  padding: 60rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}

.daily-chart {
  display: flex;
  align-items: end;
  gap: 20rpx;
  height: 200rpx;
  padding: 20rpx 0;
}

.chart-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.chart-bar {
  flex: 1;
  width: 100%;
  display: flex;
  align-items: end;
  margin-bottom: 20rpx;
}

.bar-fill {
  width: 100%;
  background: linear-gradient(to top, #667eea, #764ba2);
  border-radius: 4rpx 4rpx 0 0;
  min-height: 8rpx;
  transition: height 0.3s ease;
}

.chart-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.chart-amount {
  font-size: 20rpx;
  color: #333333;
  font-weight: bold;
}

.chart-date {
  font-size: 20rpx;
  color: #666666;
}

.ranking-card {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.ranking-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.ranking-label {
  font-size: 28rpx;
  color: #333333;
}

.ranking-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #667eea;
}
</style>
