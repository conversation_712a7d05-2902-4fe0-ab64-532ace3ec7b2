<template>
	<view class="container">
		<!-- 顶部标题栏 -->
		<view class="header">
			<text class="header-title">交易记录</text>
			<view class="header-icons">
				<image src="/static/home/<USER>" mode="aspectFit" class="icon-search"></image>
				<image src="/static/home/<USER>" mode="aspectFit" class="icon-filter"></image>
			</view>
		</view>
		
		<!-- 时间筛选 -->
		<view class="time-filter">
			<view class="filter-row">
				<view class="filter-item active">
					<text>全部</text>
				</view>
				<view class="filter-item">
					<text>今日</text>
				</view>
				<view class="filter-item">
					<text>昨日</text>
				</view>
			</view>
			<view class="filter-row">
				<view class="filter-item">
					<text>本周</text>
				</view>
				<view class="filter-item">
					<text>本月</text>
				</view>
				<view class="filter-item">
					<image src="/static/date.png" mode="aspectFit" class="icon-calendar"></image>
					<text>自定义</text>
				</view>
			</view>
		</view>
		
		<!-- 收款总览 -->
		<view class="bill-summary">
			<text class="summary-title">收款总览</text>
			<view class="summary-content">
				<view class="summary-item">
					<text class="item-title">收款总额</text>
					<text class="item-value">¥5,28.30</text>
				</view>
				<view class="summary-item">
					<text class="item-title">交易笔数</text>
					<text class="item-value">42</text>
				</view>
			</view>
		</view>
		
		<!-- 日期标题 -->
		<view class="date-header">
			<text class="date">今天 4月14日</text>
			<text class="count">共6笔</text>
		</view>
		
		<!-- 交易列表 -->
		<view class="transaction-list">
			<!-- 微信支付 -->
			<view class="transaction-item">
				<view class="transaction-left">
					<view class="transaction-left-top">
						<view class="payment-info-container">
							<view class="payment-icon wechat">
								<image src="/static/home/<USER>" mode="aspectFit"></image>
							</view>
							<view class="transaction-info">
								<text class="payment-name">微信支付</text>
								<text class="payment-time">15:23:45</text>
							</view>
						</view>
					</view>
					<view class="order-number">订单号: 202504140001</view>
				</view>
				<view class="transaction-right">
					<text class="amount">+ ¥ 128.00</text>
					<text class="details">详情</text>
				</view>
			</view>
			
			<!-- 云闪付 -->
			<view class="transaction-item">
				<view class="transaction-left">
					<view class="transaction-left-top">
						<view class="payment-info-container">
							<view class="payment-icon cloud">
								<image src="/static/home/<USER>" mode="aspectFit"></image>
							</view>
							<view class="transaction-info">
								<text class="payment-name">云闪付支付</text>
								<text class="payment-time">13:05:22</text>
							</view>
						</view>
					</view>
					<view class="order-number">订单号: 202504140002</view>
				</view>
				<view class="transaction-right">
					<text class="amount">+ ¥ 85.50</text>
					<text class="details">详情</text>
				</view>
			</view>
			
			<!-- 支付宝 -->
			<view class="transaction-item">
				<view class="transaction-left">
					<view class="transaction-left-top">
						<view class="payment-info-container">
							<view class="payment-icon alipay">
								<image src="/static/home/<USER>" mode="aspectFit"></image>
							</view>
							<view class="transaction-info">
								<text class="payment-name">支付宝</text>
								<text class="payment-time">10:18:36</text>
							</view>
						</view>
					</view>
					<view class="order-number">订单号: 202504140003</view>
				</view>
				<view class="transaction-right">
					<text class="amount">+ ¥ 299.00</text>
					<text class="details">详情</text>
				</view>
			</view>
		</view>
		
		<!-- 昨日日期标题 -->
		<view class="date-header">
			<text class="date">昨天 4月13日</text>
			<text class="count">共8笔</text>
		</view>
		
		<!-- 昨日交易列表 -->
		<view class="transaction-list">
			<!-- 微信支付 -->
			<view class="transaction-item">
				<view class="transaction-left">
					<view class="transaction-left-top">
						<view class="payment-info-container">
							<view class="payment-icon wechat">
								<image src="/static/home/<USER>" mode="aspectFit"></image>
							</view>
							<view class="transaction-info">
								<text class="payment-name">微信支付</text>
								<text class="payment-time">18:45:12</text>
							</view>
						</view>
					</view>
					<view class="order-number">订单号: 202504130001</view>
				</view>
				<view class="transaction-right">
					<text class="amount">+ ¥ 156.00</text>
					<text class="details">详情</text>
				</view>
			</view>
			
			<!-- 云闪付 -->
			<view class="transaction-item">
				<view class="transaction-left">
					<view class="transaction-left-top">
						<view class="payment-info-container">
							<view class="payment-icon cloud">
								<image src="/static/home/<USER>" mode="aspectFit"></image>
							</view>
							<view class="transaction-info">
								<text class="payment-name">云闪付支付</text>
								<text class="payment-time">14:23:50</text>
							</view>
						</view>
					</view>
					<view class="order-number">订单号: 202504130002</view>
				</view>
				<view class="transaction-right">
					<text class="amount">+ ¥ 95.80</text>
					<text class="details">详情</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	components: {
	},
	data() {
		return {
			
		}
	},
	methods: {
		
	}
}
</script>

<style>
	page {
		font-family: 'Segoe UI', sans-serif;
		background-color: #f5f5f5;
	}
	
	.container {
		width: 100%;
		position: relative;
		min-height: 100vh;
	}
	
	/* 顶部标题栏 */
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #5145F7;
		color: white;
		padding: 32rpx;
	}
	
	.header-title {
		font-size: 36rpx;
		font-weight: 500;
	}
	
	.header-icons {
		display: flex;
		gap: 32rpx;
	}
	
	.icon-search, .icon-filter {
		width: 48rpx;
		height: 48rpx;
	}
	
	/* 时间筛选 */
	.time-filter {
		display: flex;
		flex-direction: column;
		padding: 10rpx;
		background-color: #f9f9f9;
	}
	
	.filter-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10rpx;
	}
	
	.filter-row:last-child {
		margin-bottom: 0;
	}
	
	.filter-item {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 76rpx;
		background-color: #f1f1f1;
		font-size: 28rpx;
		color: #333;
		margin: 0 5rpx;
		border-radius: 10rpx;
	}
	
	.filter-item.active {
		background-color: #2b5bdd;
		color: white;
		border-radius: 10rpx;
	}
	
	.icon-calendar {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
	}
	
/* 收款总览 */
.bill-summary {
    margin: 15rpx;
    padding: 36rpx 30rpx;
    background-color: white;
    border-radius: 20rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.summary-title {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 35rpx;
    font-weight: 500;
}

.summary-content {
    display: flex;
    justify-content: flex-start;
    margin-top: 20rpx;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 4rpx;
}

.summary-item:first-child {
    margin-right: 120rpx;
}

.item-title {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 0;
    position: relative;
    top: 8rpx;
}

.item-value {
    font-size: 42rpx;
    font-weight: bold;
    color: #333;
}
	
	/* 日期标题 */
	.date-header {
		display: flex;
		justify-content: space-between;
		padding: 24rpx;
		font-size: 28rpx;
		color: #666;
	}
	
	/* 交易列表 */
	.transaction-list {
		margin: 0 24rpx;
	}
	
	.transaction-item {
		display: flex;
		justify-content: space-between;
		padding: 24rpx;
		background-color: white;
		border-radius: 16rpx;
		margin-bottom: 16rpx;
		position: relative;
		flex-wrap: wrap;
	}
	
	.transaction-left {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}
	
	.transaction-left-top {
		display: flex;
		gap: 16rpx;
	}
	
	.payment-info-container {
		display: flex;
		gap: 16rpx;
		align-items: center;
		margin-bottom: 8rpx;
	}
	
	.payment-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.payment-icon image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.payment-icon.wechat {
		background-color: #E4FFEF;
	}
	
	.payment-icon.alipay {
		background-color: #E9EFFF;
	}
	
	.payment-icon.cloud {
		background-color: #FFE4E4;
	}
	
	.transaction-info {
		display: flex;
		flex-direction: column;
		gap: 4rpx;
	}
	
	.payment-name {
		font-size: 30rpx;
		color: #333;
	}
	
	.payment-time {
		font-size: 24rpx;
		color: #999;
	}
	
	.order-number {
		font-size: 22rpx;
		color: #999;
		margin-top: 8rpx;
		margin-left: 0;
		padding-left: 0;
	}
	
	.transaction-right {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
	}
	
	.amount {
		font-size: 32rpx;
		font-weight: bold;
		color: #4CD964;
		margin-bottom: 16rpx;
	}
	
	.details {
		font-size: 24rpx;
		color: #5145F7;
	}
</style> 