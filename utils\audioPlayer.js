/**
 * 音频播放管理器
 * 用于支付语音播报功能
 */
class AudioPlayer {
    constructor() {
        this.audioContext = null;
        this.isPlaying = false;
        this.audioQueue = [];
        this.currentAudio = null;
        this.playingQueue = false;
    }

    /**
     * 播放支付成功语音
     * @param {string|number} amount 支付金额
     */
    async playPaymentSuccess(amount) {
        return this.playPaymentNotification(amount, 'alipay');
    }

    /**
     * 播放支付通知语音
     * @param {string|number} amount 支付金额
     * @param {string} payType 支付类型 (alipay, wechat, etc.)
     */
    async playPaymentNotification(amount, payType = 'alipay') {
        // 添加到播放队列
        return new Promise((resolve) => {
            const task = {
                amount,
                payType,
                resolve
            };

            this.audioQueue.push(task);
            console.log(`添加播放任务到队列: ${amount}元 (${payType}), 队列长度: ${this.audioQueue.length}`);

            // 如果没有正在播放，开始处理队列
            if (!this.playingQueue) {
                this.processQueue();
            }
        });
    }

    /**
     * 处理播放队列
     */
    async processQueue() {
        if (this.playingQueue || this.audioQueue.length === 0) {
            return;
        }

        this.playingQueue = true;
        console.log('开始处理播放队列');

        while (this.audioQueue.length > 0) {
            const task = this.audioQueue.shift();
            console.log(`处理播放任务: ${task.amount}元 (${task.payType}), 剩余队列: ${this.audioQueue.length}`);

            try {
                await this.playPaymentAudio(task.amount, task.payType);
                task.resolve();
            } catch (error) {
                console.error('播放音频失败:', error);
                task.resolve(); // 即使失败也要resolve，避免阻塞
            }
        }

        this.playingQueue = false;
        console.log('播放队列处理完成');
    }

    /**
     * 实际播放支付音频
     * @param {string|number} amount 支付金额
     * @param {string} payType 支付类型
     */
    async playPaymentAudio(amount, payType = 'alipay') {
        return new Promise((resolve) => {
            console.log(`开始播放支付语音: ${amount}元 (${payType})`);

            // 转换金额为音频文件数组
            const voiceString = this.convertAmountToVoice(amount);
            console.log(`语音字符串: ${voiceString}`);

            const audioFiles = this.parseVoiceString(voiceString);
            console.log(`音频文件列表:`, audioFiles);

            // 根据支付类型选择开头语音
            let prefixAudio = '/static/music/_shoukuan.mp3'; // 默认收款
            if (payType === 'wechat') {
                prefixAudio = '/static/music/_weixin.mp3'; // 微信支付
            } else if (payType === 'alipay') {
                prefixAudio = '/static/music/_zhifubao.mp3'; // 支付宝
            }

            // 添加开头语音
            const fullAudioList = [prefixAudio, ...audioFiles];

            this.playAudioSequence(fullAudioList).then(() => {
                console.log('支付语音播放完成');
                resolve();
            }).catch((error) => {
                console.error('支付语音播放失败:', error);
                resolve();
            });
        });
    }

    /**
     * 解析语音字符串为音频文件路径数组
     * @param {string} voiceString 语音字符串
     * @returns {Array} 音频文件路径数组
     */
    parseVoiceString(voiceString) {
        if (!voiceString) return [];
        
        const voiceArray = voiceString.split(',').filter(item => item.trim());
        return voiceArray.map(item => `/static/music/${item.trim()}.mp3`);
    }

    /**
     * 按序播放音频文件
     * @param {Array} audioFiles 音频文件路径数组
     */
    async playAudioSequence(audioFiles) {
        return new Promise((resolve, reject) => {
            if (audioFiles.length === 0) {
                resolve();
                return;
            }

            this.isPlaying = true;
            let currentIndex = 0;

            const playNext = () => {
                if (currentIndex >= audioFiles.length) {
                    this.cleanup();
                    resolve();
                    return;
                }

                const audioFile = audioFiles[currentIndex];
                console.log(`播放音频 [${currentIndex + 1}/${audioFiles.length}]: ${audioFile}`);
                
                // 创建新的音频实例
                this.audioContext = uni.createInnerAudioContext();
                this.audioContext.src = audioFile;
                
                // 监听播放结束事件
                this.audioContext.onEnded(() => {
                    console.log(`音频播放完成: ${audioFile}`);
                    this.destroyCurrentAudio();
                    currentIndex++;

                    // 短暂延迟后播放下一个
                    setTimeout(() => {
                        playNext();
                    }, 100);
                });

                // 监听播放错误事件
                this.audioContext.onError((error) => {
                    console.error(`音频播放错误: ${audioFile}`, error);
                    this.destroyCurrentAudio();
                    currentIndex++;

                    // 继续播放下一个
                    setTimeout(() => {
                        playNext();
                    }, 100);
                });

                // 开始播放
                this.audioContext.play();
            };

            // 开始播放第一个音频
            playNext();
        });
    }

    /**
     * 转换金额为语音字符串
     * @param {string|number} amount 金额
     * @returns {string} 语音字符串
     */
    convertAmountToVoice(amount) {
        // 汉字数字对应的音频文件标识
        const cnNums = ["_0,", "_1,", "_2,", "_3,", "_4,", "_5,", "_6,", "_7,", "_8,", "_9,"];
        // 基本单位
        const cnIntRadice = ["", "_shi,", "_bai,", "_qian,"];
        // 扩展单位
        const cnIntUnits = ["", "_wan,", "_yi,"];
        // 整数后缀
        const cnInteger = "_yuan";
        // 小数点
        const cnDecimal = "_dian,";
        // 最大处理数字
        const maxNum = 9999999999999999.99;

        if (amount === "" || amount === null || amount === undefined) {
            return "";
        }

        amount = parseFloat(amount);
        if (amount >= maxNum) {
            return "";
        }

        if (amount === 0) {
            return cnNums[0] + cnInteger;
        }

        // 转换为字符串并分离整数和小数部分
        const amountStr = amount.toString();
        let integerNum, decimalNum;

        if (amountStr.indexOf(".") === -1) {
            integerNum = amountStr;
            decimalNum = "";
        } else {
            const parts = amountStr.split(".");
            integerNum = parts[0];
            decimalNum = parts[1].substr(0, 2); // 只取两位小数
        }

        let chineseStr = "";

        // 处理整数部分
        if (parseInt(integerNum, 10) > 0) {
            let zeroCount = 0;
            const intLen = integerNum.length;
            
            for (let i = 0; i < intLen; i++) {
                const n = integerNum.substr(i, 1);
                const p = intLen - i - 1;
                const q = Math.floor(p / 4);
                const m = p % 4;
                
                if (n === "0") {
                    zeroCount++;
                } else {
                    if (zeroCount > 0) {
                        chineseStr += cnNums[0];
                    }
                    zeroCount = 0;
                    chineseStr += cnNums[parseInt(n, 10)] + cnIntRadice[m];
                }
                
                if (m === 0 && zeroCount < 4) {
                    chineseStr += cnIntUnits[q];
                }
            }
        } else {
            chineseStr = cnNums[0];
        }

        // 处理小数部分
        if (decimalNum !== "" && decimalNum !== "00") {
            chineseStr += cnDecimal;
            for (let i = 0; i < decimalNum.length; i++) {
                const n = decimalNum.substr(i, 1);
                chineseStr += cnNums[parseInt(n, 10)];
            }
        }

        return chineseStr + cnInteger;
    }

    /**
     * 停止播放并清理资源
     */
    stop() {
        this.isPlaying = false;
        this.cleanup();
    }

    /**
     * 清理音频资源
     */
    cleanup() {
        if (this.audioContext) {
            this.audioContext.destroy();
            this.audioContext = null;
        }
        this.isPlaying = false;
    }

    /**
     * 检查是否正在播放
     */
    getPlayingStatus() {
        return this.isPlaying;
    }

    /**
     * 销毁当前音频实例
     */
    destroyCurrentAudio() {
        if (this.audioContext) {
            try {
                this.audioContext.destroy();
                console.log('音频实例已销毁');
            } catch (error) {
                console.warn('销毁音频实例时出错:', error);
            }
            this.audioContext = null;
        }

        if (this.currentAudio) {
            try {
                this.currentAudio.pause();
                this.currentAudio.src = '';
                this.currentAudio = null;
                console.log('HTML5音频实例已销毁');
            } catch (error) {
                console.warn('销毁HTML5音频实例时出错:', error);
            }
        }
    }

    /**
     * 清空播放队列
     */
    clearQueue() {
        this.audioQueue = [];
        this.playingQueue = false;
        this.destroyCurrentAudio();
        console.log('播放队列已清空');
    }

    /**
     * 获取队列状态
     */
    getQueueStatus() {
        return {
            queueLength: this.audioQueue.length,
            isPlaying: this.playingQueue,
            hasAudio: !!this.audioContext || !!this.currentAudio
        };
    }
}

export default AudioPlayer;
