/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 🎨 苹果风格设计系统 - Apple Design System */

/* 🎨 iOS系统色彩 - iOS System Colors */
$uni-color-primary: #007AFF;        // iOS蓝色
$uni-color-success: #34C759;        // iOS绿色
$uni-color-warning: #FF9500;        // iOS橙色
$uni-color-error: #FF3B30;          // iOS红色
$uni-color-purple: #AF52DE;         // iOS紫色
$uni-color-pink: #FF2D92;           // iOS粉色
$uni-color-indigo: #5856D6;         // iOS靛蓝色
$uni-color-teal: #5AC8FA;           // iOS青色

/* 🎨 优雅渐变色 - Elegant Gradients */
$uni-gradient-primary: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
$uni-gradient-success: linear-gradient(135deg, #34C759 0%, #30D158 100%);
$uni-gradient-warm: linear-gradient(135deg, #FF9500 0%, #FF2D92 100%);
$uni-gradient-cool: linear-gradient(135deg, #5AC8FA 0%, #007AFF 100%);
$uni-gradient-navbar: linear-gradient(135deg, #007AFF 0%, #AF52DE 100%);

/* 🎨 中性色彩 - Neutral Colors */
$uni-color-gray-50: #F9FAFB;
$uni-color-gray-100: #F3F4F6;
$uni-color-gray-200: #E5E7EB;
$uni-color-gray-300: #D1D5DB;
$uni-color-gray-400: #9CA3AF;
$uni-color-gray-500: #6B7280;
$uni-color-gray-600: #4B5563;
$uni-color-gray-700: #374151;
$uni-color-gray-800: #1F2937;
$uni-color-gray-900: #111827;

/* 🎨 文字颜色系统 - Typography Colors */
$uni-text-color: $uni-color-gray-900;           // 主要文字色
$uni-text-color-inverse: #FFFFFF;               // 反色文字
$uni-text-color-secondary: $uni-color-gray-600; // 次要文字色
$uni-text-color-tertiary: $uni-color-gray-400;  // 三级文字色
$uni-text-color-placeholder: $uni-color-gray-400; // 占位符颜色
$uni-text-color-disable: $uni-color-gray-300;   // 禁用文字色
$uni-text-color-link: $uni-color-primary;       // 链接颜色

/* 背景颜色 */
$uni-bg-color:#ffffff;
$uni-bg-color-grey:#f8f8f8;
$uni-bg-color-hover:#f1f1f1;//点击状态颜色
$uni-bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

/* 边框颜色 */
$uni-border-color:#c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm:12px;
$uni-font-size-base:14px;
$uni-font-size-lg:16px;

/* 图片尺寸 */
$uni-img-size-sm:20px;
$uni-img-size-base:26px;
$uni-img-size-lg:40px;

/* Border Radius */
$uni-border-radius-sm: 2px;
$uni-border-radius-base: 3px;
$uni-border-radius-lg: 6px;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 5px;
$uni-spacing-row-base: 10px;
$uni-spacing-row-lg: 15px;

/* 垂直间距 */
$uni-spacing-col-sm: 4px;
$uni-spacing-col-base: 8px;
$uni-spacing-col-lg: 12px;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title:20px;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle:26px;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph:15px;
