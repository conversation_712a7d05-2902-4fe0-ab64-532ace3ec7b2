import{y as t,S as e,e as a,w as s,i,o as l,f as n,h as r,t as c,z as d,C as o,D as u,F as h,j as D,k,l as f,aa as m,ab as p}from"./index-B1Q521gi.js";import{_ as g}from"./back.CcwIJs7W.js";import{_}from"./date.5CEjHFwh.js";import{_ as P}from"./_plugin-vue_export-helper.BCo6x5W8.js";const S=P({data(){const t=new Date,e=t.getFullYear();return{startDate:"",endDate:"",showDatePicker:!1,currentPickerType:"start",datePickerValue:[5,t.getMonth()+1-1,t.getDate()-1],years:Array.from({length:10},((t,a)=>e-5+a)),months:Array.from({length:12},((t,e)=>e+1)),days:Array.from({length:31},((t,e)=>e+1)),tempSelectedDate:"",isSubmitting:!1}},computed:{startDateFormatted(){return this.formatDisplayDate(this.startDate)},endDateFormatted(){return this.formatDisplayDate(this.endDate)},isDateRangeValid(){return this.startDate&&this.endDate}},methods:{goBack(){t()},openStartDatePicker(){this.currentPickerType="start",this.initDatePicker(this.startDate),this.showDatePicker=!0},openEndDatePicker(){this.currentPickerType="end",this.initDatePicker(this.endDate),this.showDatePicker=!0},initDatePicker(t){let e;e=t?new Date(t):new Date;const a=this.years.findIndex((t=>t===e.getFullYear())),s=e.getMonth(),i=e.getDate()-1;this.datePickerValue=[a>=0?a:5,s,i],this.updateTempSelectedDate()},onDatePickerChange(t){this.datePickerValue=t.detail.value,this.updateTempSelectedDate()},updateTempSelectedDate(){const t=this.years[this.datePickerValue[0]],e=this.months[this.datePickerValue[1]],a=this.days[this.datePickerValue[2]],s=new Date(t,e,0).getDate(),i=Math.min(a,s);a!==i&&(this.datePickerValue[2]=i-1),this.tempSelectedDate=`${t}-${e.toString().padStart(2,"0")}-${i.toString().padStart(2,"0")}`},cancelDatePicker(){this.showDatePicker=!1,this.tempSelectedDate=""},confirmDatePicker(){"start"===this.currentPickerType?(this.startDate=this.tempSelectedDate,this.endDate&&this.endDate<this.startDate&&(this.endDate="")):(this.endDate=this.tempSelectedDate,this.startDate&&this.startDate>this.endDate&&(this.startDate=this.endDate)),this.showDatePicker=!1,this.tempSelectedDate=""},formatDisplayDate(t){if(!t)return"";const e=new Date(t);return`${e.getFullYear()}年${e.getMonth()+1}月${e.getDate()}日`},selectLastWeek(){const t=new Date,e=this.formatDateToString(t),a=new Date;a.setDate(a.getDate()-7);const s=this.formatDateToString(a);this.startDate=s,this.endDate=e},selectLastMonth(){const t=new Date,e=this.formatDateToString(t),a=new Date;a.setMonth(a.getMonth()-1);const s=this.formatDateToString(a);this.startDate=s,this.endDate=e},selectLastThreeMonths(){const t=new Date,e=this.formatDateToString(t),a=new Date;a.setMonth(a.getMonth()-3);const s=this.formatDateToString(a);this.startDate=s,this.endDate=e},formatDateToString:t=>`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")}`,getCurrentDate(){return this.formatDateToString(new Date)},confirmDateRange(){this.isDateRangeValid&&!this.isSubmitting&&(this.isSubmitting=!0,e({url:`/pages/bill/index?startDate=${this.startDate}&endDate=${this.endDate}`,complete:()=>{this.isSubmitting=!1}}))}}},[["render",function(t,e,P,S,y,C){const w=k,T=i,b=f,M=m,F=p;return l(),a(T,{class:"container"},{default:s((()=>[n(T,{class:"header"},{default:s((()=>[n(T,{class:"back-button",onClick:C.goBack},{default:s((()=>[n(w,{src:g,mode:"aspectFit",class:"icon-back"})])),_:1},8,["onClick"]),n(b,{class:"header-title"},{default:s((()=>[r("选择日期范围")])),_:1}),n(T,{class:"header-right"})])),_:1}),n(T,{class:"date-selection"},{default:s((()=>[n(T,{class:"date-range-display"},{default:s((()=>[n(T,{class:"date-item"},{default:s((()=>[n(b,{class:"date-label"},{default:s((()=>[r("开始日期")])),_:1}),n(T,{class:"date-value",onClick:C.openStartDatePicker},{default:s((()=>[n(b,null,{default:s((()=>[r(c(C.startDateFormatted||"请选择"),1)])),_:1}),n(w,{src:_,mode:"aspectFit",class:"icon-calendar"})])),_:1},8,["onClick"])])),_:1}),n(T,{class:"date-separator"},{default:s((()=>[n(b,null,{default:s((()=>[r("至")])),_:1})])),_:1}),n(T,{class:"date-item"},{default:s((()=>[n(b,{class:"date-label"},{default:s((()=>[r("结束日期")])),_:1}),n(T,{class:"date-value",onClick:C.openEndDatePicker},{default:s((()=>[n(b,null,{default:s((()=>[r(c(C.endDateFormatted||"请选择"),1)])),_:1}),n(w,{src:_,mode:"aspectFit",class:"icon-calendar"})])),_:1},8,["onClick"])])),_:1})])),_:1}),n(T,{class:"quick-dates"},{default:s((()=>[n(b,{class:"quick-dates-title"},{default:s((()=>[r("快捷选择")])),_:1}),n(T,{class:"quick-date-buttons"},{default:s((()=>[n(T,{class:"quick-date-btn",onClick:C.selectLastWeek},{default:s((()=>[n(b,null,{default:s((()=>[r("最近一周")])),_:1})])),_:1},8,["onClick"]),n(T,{class:"quick-date-btn",onClick:C.selectLastMonth},{default:s((()=>[n(b,null,{default:s((()=>[r("最近一月")])),_:1})])),_:1},8,["onClick"]),n(T,{class:"quick-date-btn",onClick:C.selectLastThreeMonths},{default:s((()=>[n(b,null,{default:s((()=>[r("最近三月")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1}),n(T,{class:d(["confirm-button",{"confirm-active":C.isDateRangeValid}]),onClick:C.confirmDateRange},{default:s((()=>[n(b,null,{default:s((()=>[r("确认")])),_:1})])),_:1},8,["class","onClick"]),y.showDatePicker?(l(),a(T,{key:0,class:"date-picker-popup"},{default:s((()=>[n(T,{class:"date-picker-mask",onClick:C.cancelDatePicker},null,8,["onClick"]),n(T,{class:"date-picker-container"},{default:s((()=>[n(T,{class:"date-picker-header"},{default:s((()=>[n(T,{class:"date-picker-action",onClick:C.cancelDatePicker},{default:s((()=>[r("取消")])),_:1},8,["onClick"]),n(T,{class:"date-picker-title"},{default:s((()=>[r(c("start"===y.currentPickerType?"选择开始日期":"选择结束日期"),1)])),_:1}),n(T,{class:"date-picker-action confirm",onClick:C.confirmDatePicker},{default:s((()=>[r("确定")])),_:1},8,["onClick"])])),_:1}),n(F,{class:"date-picker-view",value:y.datePickerValue,onChange:C.onDatePickerChange},{default:s((()=>[n(M,null,{default:s((()=>[(l(!0),o(h,null,u(y.years,((t,e)=>(l(),a(T,{class:"picker-item",key:"year-"+e},{default:s((()=>[r(c(t)+"年",1)])),_:2},1024)))),128))])),_:1}),n(M,null,{default:s((()=>[(l(!0),o(h,null,u(y.months,((t,e)=>(l(),a(T,{class:"picker-item",key:"month-"+e},{default:s((()=>[r(c(t)+"月",1)])),_:2},1024)))),128))])),_:1}),n(M,null,{default:s((()=>[(l(!0),o(h,null,u(y.days,((t,e)=>(l(),a(T,{class:"picker-item",key:"day-"+e},{default:s((()=>[r(c(t)+"日",1)])),_:2},1024)))),128))])),_:1})])),_:1},8,["value","onChange"])])),_:1})])),_:1})):D("",!0)])),_:1})}],["__scopeId","data-v-273d1235"]]);export{S as default};
