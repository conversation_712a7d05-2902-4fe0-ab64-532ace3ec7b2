import{y as e,a as o,E as s,d as a,e as t,w as l,i,o as n,f as d,h as c,t as r,j as m,C as p,D as u,F as f,l as h,I as w,a4 as _,p as y,z as F}from"./index-B1Q521gi.js";import{r as C,_ as E}from"./uni-app.es.DAfa8VxY.js";import{_ as g}from"./custom-navbar.DuzuSmPc.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";const v=k({components:{CustomNavbar:g},data:()=>({showEditForm:!1,showAddForm:!1,employeeList:[{name:"李店长",position:"店长",status:"在线",account:"manager",password:"123456",icon:"person",avatarClass:"avatar-blue"},{name:"王收银",position:"收银员",status:"在线",account:"cashier1",password:"123456",icon:"person",avatarClass:"avatar-green"},{name:"张收银",position:"收银员",status:"离线",account:"cashier2",password:"123456",icon:"person",avatarClass:"avatar-purple"},{name:"赵服务",position:"服务员",status:"在线",account:"waiter",password:"123456",icon:"person",avatarClass:"avatar-orange"}],editForm:{name:"",position:"",account:"",password:""},newEmployee:{name:"",position:"店长",account:"",password:"",status:"在线",icon:"person",avatarClass:"avatar-blue"},editIndex:-1,positionOptions:["店长","收银员","服务员"],positionIndex:0,newPositionIndex:0}),methods:{goBack(){e()},searchEmployee(){o({title:"搜索员工功能",icon:"none"})},editEmployee(e){this.showEditForm=!0,this.editIndex=e;const o=this.employeeList[e];this.editForm.name=o.name,this.editForm.position=o.position,this.editForm.account=o.account,this.editForm.password=o.password,this.positionIndex=this.positionOptions.indexOf(o.position)},cancelEdit(){this.showEditForm=!1},confirmEdit(){if(!this.editForm.name||!this.editForm.account||!this.editForm.password)return void o({title:"请填写完整信息",icon:"none"});const e=this.employeeList[this.editIndex];e.name=this.editForm.name,e.position=this.editForm.position,e.account=this.editForm.account,e.password=this.editForm.password,this.showEditForm=!1,o({title:"员工信息已更新",icon:"success"})},onPositionChange(e){const o=e.detail.value;this.positionIndex=o,this.editForm.position=this.positionOptions[o]},onNewPositionChange(e){const o=e.detail.value;this.newPositionIndex=o,this.newEmployee.position=this.positionOptions[o]},deleteEmployee(e){s({title:"确认删除",content:`确定要删除员工"${this.employeeList[e].name}"吗？`,success:s=>{s.confirm&&(this.employeeList.splice(e,1),o({title:"员工已删除",icon:"success"}))}})},showAddEmployeeForm(){this.showAddForm=!0,this.newEmployee={name:"",position:"店长",account:"",password:"",status:"在线",icon:"person",avatarClass:"avatar-blue"},this.newPositionIndex=0},cancelAdd(){this.showAddForm=!1},confirmAdd(){this.newEmployee.name&&this.newEmployee.account&&this.newEmployee.password?(this.employeeList.push({...this.newEmployee}),this.showAddForm=!1,o({title:"员工添加成功",icon:"success"})):o({title:"请填写完整信息",icon:"none"})}}},[["render",function(e,o,s,k,v,b){const x=C(a("uni-icons"),E),V=i,A=C(a("custom-navbar"),g),I=h,L=w,z=_,P=y;return n(),t(V,{class:"container"},{default:l((()=>[d(A,{title:"员工管理","show-back":!0,shadow:!0,onClickLeft:b.goBack},{right:l((()=>[d(V,{onClick:b.searchEmployee,style:{padding:"0 16rpx"}},{default:l((()=>[d(x,{type:"search",size:"22",color:"#FFFFFF"})])),_:1},8,["onClick"])])),_:1},8,["onClickLeft"]),v.showEditForm?(n(),t(V,{key:0,class:"edit-form"},{default:l((()=>[d(V,{class:"edit-header"},{default:l((()=>[d(I,{class:"edit-title"},{default:l((()=>[c("编辑员工信息")])),_:1})])),_:1}),d(V,{class:"edit-body"},{default:l((()=>[d(V,{class:"form-group"},{default:l((()=>[d(I,{class:"form-label"},{default:l((()=>[c("员工姓名")])),_:1}),d(L,{class:"form-input",modelValue:v.editForm.name,"onUpdate:modelValue":o[0]||(o[0]=e=>v.editForm.name=e),placeholder:"请输入员工姓名"},null,8,["modelValue"])])),_:1}),d(V,{class:"form-group"},{default:l((()=>[d(I,{class:"form-label"},{default:l((()=>[c("登录账号")])),_:1}),d(L,{class:"form-input",modelValue:v.editForm.account,"onUpdate:modelValue":o[1]||(o[1]=e=>v.editForm.account=e),placeholder:"请输入登录账号"},null,8,["modelValue"])])),_:1}),d(V,{class:"form-group"},{default:l((()=>[d(I,{class:"form-label"},{default:l((()=>[c("登录密码")])),_:1}),d(L,{class:"form-input",type:"password",modelValue:v.editForm.password,"onUpdate:modelValue":o[2]||(o[2]=e=>v.editForm.password=e),placeholder:"请输入登录密码"},null,8,["modelValue"])])),_:1}),d(V,{class:"form-group"},{default:l((()=>[d(I,{class:"form-label"},{default:l((()=>[c("职位")])),_:1}),d(z,{value:v.positionIndex,range:v.positionOptions,onChange:b.onPositionChange},{default:l((()=>[d(V,{class:"form-input picker-input"},{default:l((()=>[d(I,null,{default:l((()=>[c(r(v.editForm.position||"请选择职位"),1)])),_:1}),d(x,{type:"arrowdown",size:"14",color:"#666"})])),_:1})])),_:1},8,["value","range","onChange"])])),_:1}),d(V,{class:"form-actions"},{default:l((()=>[d(P,{class:"btn-cancel",onClick:b.cancelEdit},{default:l((()=>[c("取消")])),_:1},8,["onClick"]),d(P,{class:"btn-confirm",onClick:b.confirmEdit},{default:l((()=>[c("确定")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})):m("",!0),v.showAddForm?(n(),t(V,{key:1,class:"edit-form"},{default:l((()=>[d(V,{class:"edit-header"},{default:l((()=>[d(I,{class:"edit-title"},{default:l((()=>[c("添加新员工")])),_:1})])),_:1}),d(V,{class:"edit-body"},{default:l((()=>[d(V,{class:"form-group"},{default:l((()=>[d(I,{class:"form-label"},{default:l((()=>[c("员工姓名")])),_:1}),d(L,{class:"form-input",modelValue:v.newEmployee.name,"onUpdate:modelValue":o[3]||(o[3]=e=>v.newEmployee.name=e),placeholder:"请输入员工姓名"},null,8,["modelValue"])])),_:1}),d(V,{class:"form-group"},{default:l((()=>[d(I,{class:"form-label"},{default:l((()=>[c("登录账号")])),_:1}),d(L,{class:"form-input",modelValue:v.newEmployee.account,"onUpdate:modelValue":o[4]||(o[4]=e=>v.newEmployee.account=e),placeholder:"请输入登录账号"},null,8,["modelValue"])])),_:1}),d(V,{class:"form-group"},{default:l((()=>[d(I,{class:"form-label"},{default:l((()=>[c("登录密码")])),_:1}),d(L,{class:"form-input",type:"password",modelValue:v.newEmployee.password,"onUpdate:modelValue":o[5]||(o[5]=e=>v.newEmployee.password=e),placeholder:"请输入登录密码"},null,8,["modelValue"])])),_:1}),d(V,{class:"form-group"},{default:l((()=>[d(I,{class:"form-label"},{default:l((()=>[c("职位")])),_:1}),d(z,{value:v.newPositionIndex,range:v.positionOptions,onChange:b.onNewPositionChange},{default:l((()=>[d(V,{class:"form-input picker-input"},{default:l((()=>[d(I,null,{default:l((()=>[c(r(v.newEmployee.position||"请选择职位"),1)])),_:1}),d(x,{type:"arrowdown",size:"14",color:"#666"})])),_:1})])),_:1},8,["value","range","onChange"])])),_:1}),d(V,{class:"form-actions"},{default:l((()=>[d(P,{class:"btn-cancel",onClick:b.cancelAdd},{default:l((()=>[c("取消")])),_:1},8,["onClick"]),d(P,{class:"btn-confirm",onClick:b.confirmAdd},{default:l((()=>[c("确定")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})):m("",!0),v.showEditForm||v.showAddForm?m("",!0):(n(),t(V,{key:2,class:"employee-list"},{default:l((()=>[(n(!0),p(f,null,u(v.employeeList,((e,o)=>(n(),t(V,{key:o,class:"employee-item"},{default:l((()=>[d(V,{class:"employee-left"},{default:l((()=>[d(V,{class:F(["employee-avatar",e.avatarClass])},{default:l((()=>[d(x,{type:e.icon,color:"#FFFFFF",size:"20"},null,8,["type"])])),_:2},1032,["class"]),d(V,{class:"employee-info"},{default:l((()=>[d(I,{class:"employee-name"},{default:l((()=>[c(r(e.name),1)])),_:2},1024),d(V,{class:"employee-position"},{default:l((()=>[d(I,{class:"position-text"},{default:l((()=>[c(r(e.position),1)])),_:2},1024),d(V,{class:F(["status-indicator",{online:"在线"===e.status,offline:"离线"===e.status}])},null,8,["class"]),d(I,{class:"status-text"},{default:l((()=>[c(r(e.status),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),d(V,{class:"employee-actions"},{default:l((()=>[d(V,{class:"action-icon",onClick:e=>b.editEmployee(o)},{default:l((()=>[d(x,{type:"compose",color:"#666",size:"18"})])),_:2},1032,["onClick"]),d(V,{class:"action-icon",onClick:e=>b.deleteEmployee(o)},{default:l((()=>[d(x,{type:"trash",color:"#666",size:"18"})])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)))),128))])),_:1})),v.showEditForm||v.showAddForm?m("",!0):(n(),t(V,{key:3,class:"create-button",onClick:b.showAddEmployeeForm},{default:l((()=>[d(x,{type:"plus",color:"#FFFFFF",size:"18"}),d(I,{class:"create-text"},{default:l((()=>[c("创建员工")])),_:1})])),_:1},8,["onClick"]))])),_:1})}],["__scopeId","data-v-0c65e075"]]);export{v as default};
