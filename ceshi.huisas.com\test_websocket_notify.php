<?php
/**
 * 🔧 增强版WebSocket通知测试页面
 * 专门解决连接延迟和语音播报问题
 */

// 引入必要的文件
require_once __DIR__ . '/includes/common.php';
require_once __DIR__ . '/includes/functions.php';

echo "<h1>🔧 增强版WebSocket通知测试</h1>\n";
echo "<p style='color: #666;'>专门解决前端连接后需要等待4分钟才能接收语音播报的问题</p>\n";

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');

    $action = $_POST['action'];
    $response = ['success' => false, 'message' => '', 'data' => []];

    switch ($action) {
        case 'send_test_notification':
            $response = handleTestNotification();
            break;
        case 'check_websocket_status':
            $response = checkWebSocketStatus();
            break;
        case 'force_broadcast':
            $response = forceBroadcastTest();
            break;
    }

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 1. 检查函数是否存在
echo "<h2>1. 🔍 系统检查</h2>\n";
$functionExists = function_exists('sendWebSocketPaymentNotification');
echo "<p><strong>WebSocket通知函数:</strong> " . ($functionExists ? '✅ 存在' : '❌ 不存在') . "</p>\n";

if (!$functionExists) {
    echo "<p>🔄 尝试手动引入websocket_notify_workerman.php...</p>\n";
    $websocketFile = __DIR__ . '/includes/websocket_notify_workerman.php';
    if (file_exists($websocketFile)) {
        require_once $websocketFile;
        $functionExists = function_exists('sendWebSocketPaymentNotification');
        echo "<p><strong>重新检查:</strong> " . ($functionExists ? '✅ 成功' : '❌ 失败') . "</p>\n";
    } else {
        echo "<p>❌ WebSocket通知文件不存在</p>\n";
    }
}

// 2. WebSocket服务状态检查
echo "<h2>2. 🌐 WebSocket服务状态</h2>\n";
$websocketHost = 'ceshi.huisas.com';
$websocketPort = 8080;

$connection = @fsockopen($websocketHost, $websocketPort, $errno, $errstr, 5);
if ($connection) {
    echo "<p>✅ <strong>WebSocket服务运行正常</strong> ({$websocketHost}:{$websocketPort})</p>\n";
    fclose($connection);
} else {
    echo "<p>❌ <strong>WebSocket服务无法连接:</strong> {$errstr} ({$errno})</p>\n";
}

// 3. 实时测试控制面板
echo "<h2>3. 🎮 实时测试控制面板</h2>\n";
?>

<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 20px;">
        <button onclick="sendTestNotification()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
            🧪 发送测试通知
        </button>
        <button onclick="checkWebSocketStatus()" style="background: #17a2b8; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
            🔍 检查服务状态
        </button>
        <button onclick="forceBroadcast()" style="background: #ffc107; color: black; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
            📢 强制广播测试
        </button>
        <button onclick="clearResults()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
            🗑️ 清空结果
        </button>
    </div>

    <div id="test-results" style="background: #000; color: #00ff00; padding: 15px; border-radius: 5px; font-family: monospace; min-height: 200px; max-height: 400px; overflow-y: auto;">
        <div>🚀 WebSocket通知测试控制台已就绪...</div>
        <div>💡 提示：点击上方按钮开始测试</div>
        <div>⚠️ 如果前端连接后无法立即接收通知，这个工具可以帮助诊断问题</div>
    </div>
</div>

<?php
// 4. 连接状态诊断
echo "<h2>4. 🔧 连接状态诊断</h2>\n";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px;'>\n";
echo "<p><strong>常见问题分析：</strong></p>\n";
echo "<ul>\n";
echo "<li><strong>连接延迟问题：</strong> 前端连接后需要完成认证和频道订阅</li>\n";
echo "<li><strong>频道同步问题：</strong> 服务器需要时间识别新连接的频道订阅</li>\n";
echo "<li><strong>消息路由延迟：</strong> 消息可能需要等待连接完全就绪</li>\n";
echo "</ul>\n";
echo "<p><strong>解决方案：</strong></p>\n";
echo "<ul>\n";
echo "<li>使用上方的实时测试工具检查连接状态</li>\n";
echo "<li>确保前端已完成认证和频道订阅</li>\n";
echo "<li>使用强制广播测试验证消息路由</li>\n";
echo "</ul>\n";
echo "</div>\n";

// 4. 检查日志目录
echo "<h2>4. 日志目录检查</h2>\n";
$logDir = __DIR__ . '/includes/logs';
if (is_dir($logDir)) {
    echo "✅ 日志目录存在: $logDir<br>\n";
    if (is_writable($logDir)) {
        echo "✅ 日志目录可写<br>\n";
    } else {
        echo "❌ 日志目录不可写<br>\n";
    }
} else {
    echo "❌ 日志目录不存在: $logDir<br>\n";
    if (mkdir($logDir, 0755, true)) {
        echo "✅ 已创建日志目录<br>\n";
    } else {
        echo "❌ 无法创建日志目录<br>\n";
    }
}

// 5. 测试日志写入
echo "<h2>5. 测试日志写入</h2>\n";
try {
    \lib\Payment::writePaymentLog('websocket_test', [
        'message' => '这是一个测试日志',
        'timestamp' => date('Y-m-d H:i:s'),
        'test_data' => $testData ?? []
    ]);
    echo "✅ 日志写入成功<br>\n";
} catch (Exception $e) {
    echo "❌ 日志写入失败: " . $e->getMessage() . "<br>\n";
}

// 6. 检查WebSocket服务状态
echo "<h2>6. WebSocket服务检查</h2>\n";
$websocketHost = 'ceshi.huisas.com';
$websocketPort = 8080;

$connection = @fsockopen($websocketHost, $websocketPort, $errno, $errstr, 5);
if ($connection) {
    echo "✅ WebSocket服务运行正常 ($websocketHost:$websocketPort)<br>\n";
    fclose($connection);
} else {
    echo "❌ WebSocket服务无法连接: $errstr ($errno)<br>\n";
}

// PHP处理函数
function handleTestNotification() {
    try {
        $testData = [
            'trade_no' => 'ENHANCED_TEST_' . date('YmdHis'),
            'uid' => '1000',  // 商户1000
            'money' => (rand(1, 999) / 100), // 随机金额
            'type' => 'alipay',
            'typename' => '支付宝',
            'addtime' => date('Y-m-d H:i:s'),
            'api_trade_no' => 'ENHANCED_API_' . time(),
            'buyer' => '<EMAIL>',
            'status' => 'success'
        ];

        if (function_exists('sendWebSocketPaymentNotification')) {
            $result = sendWebSocketPaymentNotification($testData);
            return [
                'success' => true,
                'message' => '测试通知发送' . ($result ? '成功' : '失败'),
                'data' => $testData,
                'result' => $result
            ];
        } else {
            return [
                'success' => false,
                'message' => 'WebSocket通知函数不存在',
                'data' => []
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '发送异常: ' . $e->getMessage(),
            'data' => []
        ];
    }
}

function checkWebSocketStatus() {
    $host = 'ceshi.huisas.com';
    $port = 8080;

    // 检查服务连接
    $connection = @fsockopen($host, $port, $errno, $errstr, 3);
    $serviceRunning = $connection !== false;
    if ($connection) fclose($connection);

    // 检查健康状态
    $healthUrl = "http://{$host}:{$port}/health";
    $healthStatus = false;
    $healthData = null;

    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 3
        ]
    ]);

    $healthResponse = @file_get_contents($healthUrl, false, $context);
    if ($healthResponse) {
        $healthData = json_decode($healthResponse, true);
        $healthStatus = $healthData && isset($healthData['status']) && $healthData['status'] === 'healthy';
    }

    // 检查统计信息
    $statsUrl = "http://{$host}:{$port}/stats";
    $statsData = null;

    $statsResponse = @file_get_contents($statsUrl, false, $context);
    if ($statsResponse) {
        $statsData = json_decode($statsResponse, true);
    }

    return [
        'success' => true,
        'message' => '状态检查完成',
        'data' => [
            'service_running' => $serviceRunning,
            'health_status' => $healthStatus,
            'health_data' => $healthData,
            'stats_data' => $statsData,
            'error_info' => $serviceRunning ? null : "{$errstr} ({$errno})"
        ]
    ];
}

function forceBroadcastTest() {
    try {
        $testData = [
            'merchant_id' => '1000',
            'order_id' => 'FORCE_TEST_' . time(),
            'amount' => '88.88',
            'status' => 'success',
            'extra_data' => [
                'pay_type' => 'alipay',
                'pay_time' => date('Y-m-d H:i:s'),
                'test_type' => 'force_broadcast'
            ]
        ];

        // 直接调用Swoole WebSocket服务器的支付通知接口
        $notifyUrl = 'http://ceshi.huisas.com:8080/payment/notify';

        $postData = json_encode($testData);
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($postData)
                ],
                'content' => $postData,
                'timeout' => 5
            ]
        ]);

        $result = @file_get_contents($notifyUrl, false, $context);

        if ($result !== false) {
            $response = json_decode($result, true);
            return [
                'success' => true,
                'message' => '强制广播测试完成',
                'data' => [
                    'test_data' => $testData,
                    'server_response' => $response
                ]
            ];
        } else {
            return [
                'success' => false,
                'message' => '强制广播失败：无法连接到WebSocket服务器',
                'data' => ['test_data' => $testData]
            ];
        }

    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '强制广播异常: ' . $e->getMessage(),
            'data' => []
        ];
    }
}

echo "<h2>5. 📊 使用说明</h2>\n";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;'>\n";
echo "<p><strong>🎯 解决连接延迟问题的步骤：</strong></p>\n";
echo "<ol>\n";
echo "<li><strong>首先</strong> - 确保前端已连接并完成认证</li>\n";
echo "<li><strong>然后</strong> - 点击\"检查服务状态\"确认WebSocket服务正常</li>\n";
echo "<li><strong>接着</strong> - 使用\"发送测试通知\"测试正常流程</li>\n";
echo "<li><strong>如果失败</strong> - 使用\"强制广播测试\"直接测试服务器</li>\n";
echo "<li><strong>观察结果</strong> - 查看控制台输出分析问题</li>\n";
echo "</ol>\n";
echo "<p><strong>💡 提示：</strong> 如果强制广播能成功但正常通知失败，说明问题在于连接认证或频道订阅</p>\n";
echo "</div>\n";
?>

<script>
// JavaScript函数
function addLog(message, type = 'info') {
    const results = document.getElementById('test-results');
    const timestamp = new Date().toLocaleTimeString();
    const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';

    const logEntry = document.createElement('div');
    logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
    logEntry.style.marginBottom = '5px';

    if (type === 'error') {
        logEntry.style.color = '#ff6b6b';
    } else if (type === 'success') {
        logEntry.style.color = '#51cf66';
    } else if (type === 'warning') {
        logEntry.style.color = '#ffd43b';
    }

    results.appendChild(logEntry);
    results.scrollTop = results.scrollHeight;
}

function sendTestNotification() {
    addLog('🧪 正在发送测试通知...');

    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=send_test_notification'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLog(`✅ ${data.message}`, 'success');
            addLog(`📊 测试数据: 订单${data.data.trade_no}, 金额¥${data.data.money}`);
        } else {
            addLog(`❌ ${data.message}`, 'error');
        }
    })
    .catch(error => {
        addLog(`❌ 请求失败: ${error.message}`, 'error');
    });
}

function checkWebSocketStatus() {
    addLog('🔍 正在检查WebSocket服务状态...');

    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=check_websocket_status'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const status = data.data;
            addLog(`🌐 服务运行: ${status.service_running ? '✅ 正常' : '❌ 异常'}`, status.service_running ? 'success' : 'error');
            addLog(`💚 健康状态: ${status.health_status ? '✅ 健康' : '❌ 异常'}`, status.health_status ? 'success' : 'error');

            if (status.stats_data) {
                addLog(`📊 当前连接: ${status.stats_data.current_connections || 0}个`);
                addLog(`📈 总消息数: ${status.stats_data.total_messages || 0}条`);
                addLog(`💰 支付通知: ${status.stats_data.payment_notifications || 0}次`);
            }

            if (status.error_info) {
                addLog(`⚠️ 错误信息: ${status.error_info}`, 'warning');
            }
        } else {
            addLog(`❌ ${data.message}`, 'error');
        }
    })
    .catch(error => {
        addLog(`❌ 状态检查失败: ${error.message}`, 'error');
    });
}

function forceBroadcast() {
    addLog('📢 正在执行强制广播测试...');

    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=force_broadcast'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLog(`✅ ${data.message}`, 'success');
            if (data.data.server_response) {
                const resp = data.data.server_response;
                addLog(`📡 服务器响应: ${resp.message || '成功'}`);
                addLog(`📊 广播数量: ${resp.broadcast_count || 0}个连接`);
                addLog(`📺 目标频道: ${resp.channel || '未知'}`);
            }
            addLog(`💰 测试金额: ¥${data.data.test_data.amount}`);
        } else {
            addLog(`❌ ${data.message}`, 'error');
        }
    })
    .catch(error => {
        addLog(`❌ 强制广播失败: ${error.message}`, 'error');
    });
}

function clearResults() {
    const results = document.getElementById('test-results');
    results.innerHTML = '<div>🗑️ 结果已清空，准备新的测试...</div>';
}

// 页面加载完成后自动检查状态
document.addEventListener('DOMContentLoaded', function() {
    addLog('🚀 增强版WebSocket测试工具已加载');
    addLog('💡 建议先点击"检查服务状态"确认环境正常');
});
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
p { margin: 8px 0; }
ol, ul { margin: 10px 0; padding-left: 30px; }
li { margin: 5px 0; }
button:hover { opacity: 0.8; transform: translateY(-1px); transition: all 0.2s; }
</style>
