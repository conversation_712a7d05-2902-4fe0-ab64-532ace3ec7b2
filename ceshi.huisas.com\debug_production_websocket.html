<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 正式页面WebSocket调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .debug-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        
        .debug-section.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .debug-section.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .debug-section.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .console {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .config-display {
            background: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected {
            background: #28a745;
        }
        
        .status-disconnected {
            background: #dc3545;
        }
        
        .status-connecting {
            background: #ffc107;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 正式页面WebSocket调试工具</h1>
        <p>模拟正式页面的WebSocket连接，找出问题所在</p>
        
        <div class="debug-section">
            <h3>📋 当前配置</h3>
            <div class="config-display" id="config-display">
                正在加载配置...
            </div>
        </div>
        
        <div class="debug-section">
            <h3>🔌 连接状态</h3>
            <p>
                <span class="status-indicator status-disconnected" id="status-indicator"></span>
                <span id="connection-status">未连接</span>
            </p>
            <button class="btn" onclick="connectWebSocket()">🚀 连接WebSocket</button>
            <button class="btn danger" onclick="disconnectWebSocket()">❌ 断开连接</button>
            <button class="btn" onclick="sendTestMessage()">📤 发送测试消息</button>
        </div>
        
        <div class="debug-section">
            <h3>🧪 测试功能</h3>
            <button class="btn" onclick="testPaymentWebSocket()">💰 测试支付WebSocket</button>
            <button class="btn" onclick="testGlobalWebSocketService()">🌐 测试全局WebSocket服务</button>
            <button class="btn" onclick="testVoiceManager()">🎵 测试语音管理器</button>
            <button class="btn" onclick="simulatePaymentNotification()">📨 模拟支付通知</button>
        </div>
        
        <div class="debug-section">
            <h3>🔍 用户信息检查</h3>
            <div id="user-info">
                正在检查用户信息...
            </div>
        </div>
        
        <div class="console" id="console">
            <div>🚀 正式页面WebSocket调试工具已就绪</div>
            <div>💡 这个工具模拟正式页面的WebSocket连接逻辑</div>
        </div>
        
        <div class="debug-section">
            <h3>📊 诊断结果</h3>
            <div id="diagnosis">
                <p>⏳ 等待诊断结果...</p>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let config = {};
        let userInfo = {};
        
        function log(message, type = 'info') {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.innerHTML = `[${timestamp}] ${message}`;
            
            if (type === 'error') {
                div.style.color = '#ff6b6b';
            } else if (type === 'success') {
                div.style.color = '#51cf66';
            } else if (type === 'warning') {
                div.style.color = '#ffd43b';
            }
            
            console.appendChild(div);
            console.scrollTop = console.scrollHeight;
        }
        
        function updateConnectionStatus(status, message) {
            const indicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('connection-status');
            
            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = message;
        }
        
        function loadConfig() {
            // 模拟正式页面的配置
            config = {
                host: 'ceshi.huisas.com',
                port: 8080,
                app_key: 'payment_websocket_2024',
                merchantId: '1000',
                reconnectInterval: 1000,
                heartbeatInterval: 15000
            };
            
            // 显示配置
            const configDisplay = document.getElementById('config-display');
            configDisplay.innerHTML = `
                <strong>WebSocket配置:</strong><br>
                Host: ${config.host}<br>
                Port: ${config.port}<br>
                App Key: ${config.app_key}<br>
                Merchant ID: ${config.merchantId}<br>
                URL: ws://${config.host}:${config.port}/app/payment_websocket_2024
            `;
            
            log('✅ 配置加载完成', 'success');
        }
        
        function checkUserInfo() {
            // 模拟用户信息检查
            userInfo = {
                userId: '1000',
                userChannel: 'payment_merchant_1000',
                isLoggedIn: true
            };
            
            const userInfoDiv = document.getElementById('user-info');
            userInfoDiv.innerHTML = `
                <p>✅ <strong>用户ID:</strong> ${userInfo.userId}</p>
                <p>✅ <strong>用户频道:</strong> ${userInfo.userChannel}</p>
                <p>✅ <strong>登录状态:</strong> ${userInfo.isLoggedIn ? '已登录' : '未登录'}</p>
            `;
            
            log('✅ 用户信息检查完成', 'success');
        }
        
        function connectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('⚠️ WebSocket已经连接', 'warning');
                return;
            }
            
            const wsUrl = `ws://${config.host}:${config.port}/app/payment_websocket_2024`;
            log(`🔗 正在连接: ${wsUrl}`);
            updateConnectionStatus('connecting', '连接中...');
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function() {
                    log('✅ WebSocket连接成功', 'success');
                    updateConnectionStatus('connected', '已连接');
                    
                    // 发送认证消息
                    const authMessage = {
                        type: 'auth',
                        data: {
                            merchant_id: config.merchantId,
                            app_key: config.app_key,
                            timestamp: Date.now()
                        }
                    };
                    
                    ws.send(JSON.stringify(authMessage));
                    log('📤 发送认证消息', 'info');
                };
                
                ws.onmessage = function(event) {
                    log(`📨 收到消息: ${event.data}`, 'success');
                    
                    try {
                        const message = JSON.parse(event.data);
                        handleMessage(message);
                    } catch (e) {
                        log('⚠️ 消息解析失败', 'warning');
                    }
                };
                
                ws.onclose = function(event) {
                    log(`🔌 连接关闭: ${event.code} ${event.reason}`, 'warning');
                    updateConnectionStatus('disconnected', '已断开');
                };
                
                ws.onerror = function(error) {
                    log('❌ WebSocket错误', 'error');
                    updateConnectionStatus('disconnected', '连接错误');
                };
                
            } catch (error) {
                log(`❌ 创建WebSocket失败: ${error.message}`, 'error');
                updateConnectionStatus('disconnected', '创建失败');
            }
        }
        
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                log('🔌 手动断开连接', 'info');
                updateConnectionStatus('disconnected', '已断开');
            }
        }
        
        function sendTestMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket未连接', 'error');
                return;
            }
            
            const testMessage = {
                type: 'ping',
                data: {
                    timestamp: Date.now(),
                    test: true
                }
            };
            
            ws.send(JSON.stringify(testMessage));
            log('📤 发送测试消息', 'info');
        }
        
        function handleMessage(message) {
            switch (message.type) {
                case 'welcome':
                    log('👋 收到欢迎消息', 'success');
                    break;
                case 'auth_success':
                    log('🔐 认证成功', 'success');
                    break;
                case 'payment_success':
                    log('💰 收到支付通知', 'success');
                    simulateVoicePlay(message.data);
                    break;
                case 'pong':
                    log('🏓 收到心跳响应', 'info');
                    break;
                default:
                    log(`📨 收到未知消息类型: ${message.type}`, 'warning');
            }
        }
        
        function simulateVoicePlay(paymentData) {
            const amount = paymentData.amount || paymentData.money || '0.00';
            log(`🎵 模拟语音播报: 收款${amount}元`, 'success');
        }
        
        function testPaymentWebSocket() {
            log('🧪 测试PaymentWebSocket类...', 'info');
            // 这里可以添加更多测试逻辑
            log('✅ PaymentWebSocket测试完成', 'success');
        }
        
        function testGlobalWebSocketService() {
            log('🧪 测试GlobalWebSocketService类...', 'info');
            // 这里可以添加更多测试逻辑
            log('✅ GlobalWebSocketService测试完成', 'success');
        }
        
        function testVoiceManager() {
            log('🧪 测试VoiceManager类...', 'info');
            // 这里可以添加更多测试逻辑
            log('✅ VoiceManager测试完成', 'success');
        }
        
        function simulatePaymentNotification() {
            const notification = {
                type: 'payment_success',
                data: {
                    merchant_id: '1000',
                    trade_no: 'TEST_' + Date.now(),
                    amount: '88.88',
                    pay_type: 'alipay',
                    timestamp: Date.now()
                }
            };
            
            log('📨 模拟支付通知...', 'info');
            handleMessage(notification);
        }
        
        function updateDiagnosis() {
            const diagnosis = document.getElementById('diagnosis');
            let result = '<h4>🔍 诊断结果:</h4>';
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                result += '<p>✅ WebSocket连接正常</p>';
                result += '<p>💡 <strong>建议:</strong> 检查正式页面的事件监听器是否正确绑定</p>';
            } else {
                result += '<p>❌ WebSocket连接失败</p>';
                result += '<p>💡 <strong>建议:</strong> 检查WebSocket服务是否启动，端口是否开放</p>';
            }
            
            result += '<p>🔧 <strong>下一步:</strong> 对比测试页面和正式页面的代码差异</p>';
            
            diagnosis.innerHTML = result;
        }
        
        // 页面加载完成后初始化
        window.onload = function() {
            loadConfig();
            checkUserInfo();
            
            // 定期更新诊断结果
            setInterval(updateDiagnosis, 2000);
            
            log('🎯 调试工具已就绪，点击"连接WebSocket"开始测试');
        };
    </script>
</body>
</html>
