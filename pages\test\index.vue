<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">系统测试页面</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">登录状态测试</text>
      <view class="test-item">
        <text class="test-label">Token状态:</text>
        <text class="test-value" :class="tokenStatus.class">{{ tokenStatus.text }}</text>
      </view>
      <view class="test-item">
        <text class="test-label">用户信息:</text>
        <text class="test-value">{{ userInfo || '未获取' }}</text>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">API测试</text>
      <view class="test-item">
        <text class="test-label">API状态:</text>
        <text class="test-value" :class="apiStatus.class">{{ apiStatus.text }}</text>
      </view>
      <button class="test-button" @click="testAPI">测试API连接</button>
    </view>

    <view class="test-section">
      <text class="section-title">页面跳转测试</text>
      <button class="test-button" @click="goToLogin">跳转登录页</button>
      <button class="test-button" @click="goToHome">跳转首页</button>
    </view>

    <view class="test-section">
      <text class="section-title">系统信息</text>
      <view class="test-item">
        <text class="test-label">环境:</text>
        <text class="test-value">{{ systemInfo.env }}</text>
      </view>
      <view class="test-item">
        <text class="test-label">平台:</text>
        <text class="test-value">{{ systemInfo.platform }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      tokenStatus: {
        text: '检查中...',
        class: 'status-checking'
      },
      apiStatus: {
        text: '未测试',
        class: 'status-default'
      },
      userInfo: '',
      systemInfo: {
        env: process.env.NODE_ENV || 'unknown',
        platform: ''
      }
    }
  },
  onLoad() {
    this.checkTokenStatus();
    this.getSystemInfo();
  },
  methods: {
    // 检查Token状态
    checkTokenStatus() {
      const token = uni.getStorageSync('user_token');
      const userInfo = uni.getStorageSync('user_info');
      
      if (token) {
        this.tokenStatus = {
          text: '已登录',
          class: 'status-success'
        };
        this.userInfo = userInfo ? JSON.stringify(userInfo) : '有Token但无用户信息';
      } else {
        this.tokenStatus = {
          text: '未登录',
          class: 'status-error'
        };
        this.userInfo = '无';
      }
    },

    // 测试API连接
    async testAPI() {
      this.apiStatus = {
        text: '测试中...',
        class: 'status-checking'
      };

      try {
        const baseUrl = process.env.NODE_ENV === 'development' ? '' : 'http://ceshi.huisas.com';
        
        const result = await new Promise((resolve, reject) => {
          uni.request({
            url: baseUrl + '/user/ajax2.php?act=getcount',
            method: 'POST',
            data: {},
            header: {
              'content-type': 'application/x-www-form-urlencoded'
            },
            success: (res) => {
              console.log('API测试结果:', res);
              resolve(res);
            },
            fail: (err) => {
              console.error('API测试失败:', err);
              reject(err);
            }
          });
        });

        if (result.statusCode === 200) {
          this.apiStatus = {
            text: `连接成功 (${result.data?.code || 'unknown'})`,
            class: 'status-success'
          };
        } else {
          this.apiStatus = {
            text: `连接失败 (${result.statusCode})`,
            class: 'status-error'
          };
        }
      } catch (error) {
        this.apiStatus = {
          text: `连接异常: ${error.message || 'unknown'}`,
          class: 'status-error'
        };
      }
    },

    // 获取系统信息
    getSystemInfo() {
      uni.getSystemInfo({
        success: (res) => {
          this.systemInfo.platform = res.platform;
        }
      });
    },

    // 跳转到登录页
    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/index'
      });
    },

    // 跳转到首页
    goToHome() {
      uni.navigateTo({
        url: '/pages/index/index'
      });
    }
  }
}
</script>

<style>
.test-container {
  padding: 32rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.test-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.test-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.test-label {
  font-size: 28rpx;
  color: #666;
}

.test-value {
  font-size: 28rpx;
  max-width: 400rpx;
  text-align: right;
}

.status-success {
  color: #4CD964;
}

.status-error {
  color: #FF3B30;
}

.status-checking {
  color: #FF9500;
}

.status-default {
  color: #999;
}

.test-button {
  width: 100%;
  height: 80rpx;
  background-color: #5145F7;
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  margin-bottom: 16rpx;
  border: none;
}

.test-button:last-child {
  margin-bottom: 0;
}
</style>
