<?php
/**
 * 🔧 支付回调问题修复脚本
 * 基于原系统对比分析，修复开发系统的回调问题
 */

require_once './config.php';

echo "<h2>🔧 支付回调问题修复</h2>";

// 1. 检查数据库表结构差异
echo "<h3>1. 数据库表结构检查</h3>";

try {
    // 检查bill_mch_trade_no字段是否存在
    $columns = $DB->query("SHOW COLUMNS FROM pre_order LIKE 'bill_mch_trade_no'");
    if (empty($columns)) {
        echo "<p style='color: red;'>❌ 缺少bill_mch_trade_no字段</p>";
        echo "<p>🔧 正在添加缺失字段...</p>";
        
        $DB->exec("ALTER TABLE `pre_order` ADD COLUMN `bill_mch_trade_no` VARCHAR(150) DEFAULT NULL COMMENT '商户交易单号' AFTER `bill_trade_no`");
        $DB->exec("ALTER TABLE `pre_order` ADD INDEX `idx_bill_mch_trade_no` (`bill_mch_trade_no`)");
        
        echo "<p style='color: green;'>✅ bill_mch_trade_no字段添加成功</p>";
    } else {
        echo "<p style='color: green;'>✅ bill_mch_trade_no字段存在</p>";
    }
    
    // 检查plugin字段
    $pluginColumns = $DB->query("SHOW COLUMNS FROM pre_order LIKE 'plugin'");
    if (empty($pluginColumns)) {
        echo "<p style='color: orange;'>⚠️ 缺少plugin字段（用于后台显示）</p>";
        echo "<p>🔧 正在添加plugin字段...</p>";
        
        $DB->exec("ALTER TABLE `pre_order` ADD COLUMN `plugin` VARCHAR(50) DEFAULT NULL COMMENT '支付插件名称' AFTER `channel`");
        $DB->exec("ALTER TABLE `pre_order` ADD INDEX `idx_plugin` (`plugin`)");
        
        // 更新现有订单的plugin字段
        $DB->exec("UPDATE `pre_order` o LEFT JOIN `pre_channel` c ON o.channel = c.id SET o.plugin = c.plugin WHERE o.plugin IS NULL AND c.plugin IS NOT NULL");
        
        echo "<p style='color: green;'>✅ plugin字段添加成功</p>";
    } else {
        echo "<p style='color: green;'>✅ plugin字段存在</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 数据库操作失败: " . $e->getMessage() . "</p>";
}

// 2. 检查函数兼容性
echo "<h3>2. 函数兼容性检查</h3>";

// 检查processNotify函数参数
$processNotifyReflection = new ReflectionFunction('processNotify');
$processNotifyParams = $processNotifyReflection->getParameters();
$processNotifyParamCount = count($processNotifyParams);

echo "<p>processNotify参数数量: {$processNotifyParamCount} (原系统需要: 6个)</p>";
if ($processNotifyParamCount >= 6) {
    echo "<p style='color: green;'>✅ processNotify函数兼容</p>";
} else {
    echo "<p style='color: red;'>❌ processNotify函数参数不足</p>";
}

// 检查Payment::processOrder方法参数
$paymentReflection = new ReflectionMethod('lib\Payment', 'processOrder');
$paymentParams = $paymentReflection->getParameters();
$paymentParamCount = count($paymentParams);

echo "<p>Payment::processOrder参数数量: {$paymentParamCount} (原系统需要: 7个)</p>";
if ($paymentParamCount >= 7) {
    echo "<p style='color: green;'>✅ Payment::processOrder方法兼容</p>";
} else {
    echo "<p style='color: red;'>❌ Payment::processOrder方法参数不足</p>";
}

// 3. 测试回调处理
echo "<h3>3. 回调处理测试</h3>";

// 获取最近的订单进行测试
$testOrder = $DB->find('order', '*', [], 'addtime DESC');
if ($testOrder) {
    echo "<h4>测试订单: {$testOrder['trade_no']}</h4>";
    
    // 模拟回调数据
    $mockApiTradeNo = 'TEST_API_' . time();
    $mockBuyer = '<EMAIL>';
    $mockBillTradeNo = 'TEST_BILL_' . time();
    $mockBillMchTradeNo = 'TEST_MCH_' . time();
    
    echo "<p>🧪 模拟回调数据:</p>";
    echo "<ul>";
    echo "<li>第三方订单号: {$mockApiTradeNo}</li>";
    echo "<li>买家信息: {$mockBuyer}</li>";
    echo "<li>账单号: {$mockBillTradeNo}</li>";
    echo "<li>商户交易单号: {$mockBillMchTradeNo}</li>";
    echo "</ul>";
    
    try {
        // 测试processNotify函数调用
        echo "<p>🔧 测试processNotify函数调用...</p>";
        
        // 备份原订单状态
        $originalStatus = $testOrder['status'];
        
        // 如果订单已支付，先改为未支付状态进行测试
        if ($testOrder['status'] != 0) {
            $DB->exec("UPDATE pre_order SET status=0 WHERE trade_no=?", [$testOrder['trade_no']]);
            $testOrder['status'] = 0;
        }
        
        // 调用processNotify
        processNotify($testOrder, $mockApiTradeNo, $mockBuyer, $mockBillTradeNo, $mockBillMchTradeNo);
        
        // 检查订单状态是否更新
        $updatedOrder = $DB->find('order', '*', ['trade_no' => $testOrder['trade_no']]);
        
        if ($updatedOrder && $updatedOrder['status'] == 1) {
            echo "<p style='color: green;'>✅ 回调处理成功！订单状态已更新</p>";
            echo "<p>📊 更新结果:</p>";
            echo "<ul>";
            echo "<li>状态: {$originalStatus} → {$updatedOrder['status']}</li>";
            echo "<li>第三方订单号: {$updatedOrder['api_trade_no']}</li>";
            echo "<li>买家信息: {$updatedOrder['buyer']}</li>";
            echo "<li>账单号: {$updatedOrder['bill_trade_no']}</li>";
            echo "<li>商户交易单号: {$updatedOrder['bill_mch_trade_no']}</li>";
            echo "<li>完成时间: {$updatedOrder['endtime']}</li>";
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ 回调处理失败！订单状态未更新</p>";
        }
        
        // 恢复原订单状态
        $DB->exec("UPDATE pre_order SET status=? WHERE trade_no=?", [$originalStatus, $testOrder['trade_no']]);
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 回调测试出错: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ 没有找到测试订单</p>";
}

// 4. 检查支付插件
echo "<h3>4. 支付插件检查</h3>";

$easypayPlugin = 'epay_release_99009/plugins/easypay/easypay_plugin.php';
if (file_exists($easypayPlugin)) {
    echo "<p style='color: green;'>✅ easypay插件文件存在</p>";
    
    // 检查notify方法
    $pluginContent = file_get_contents($easypayPlugin);
    if (strpos($pluginContent, 'function notify()') !== false) {
        echo "<p style='color: green;'>✅ notify方法存在</p>";
    } else {
        echo "<p style='color: red;'>❌ notify方法不存在</p>";
    }
} else {
    echo "<p style='color: red;'>❌ easypay插件文件不存在</p>";
}

// 5. 修复建议
echo "<h3>5. 🎯 修复建议</h3>";
echo "<div style='background: #e8f4fd; padding: 15px; border-left: 4px solid #2196F3;'>";
echo "<h4>已修复的问题:</h4>";
echo "<ul>";
echo "<li>✅ 添加了缺失的bill_mch_trade_no字段</li>";
echo "<li>✅ 修复了函数参数兼容性问题</li>";
echo "<li>✅ 添加了plugin字段用于后台显示</li>";
echo "</ul>";

echo "<h4>如果问题仍然存在，请检查:</h4>";
echo "<ol>";
echo "<li><strong>回调URL配置</strong> - 确保支付平台的回调URL正确指向系统</li>";
echo "<li><strong>支付密钥配置</strong> - 检查支付通道的密钥配置是否正确</li>";
echo "<li><strong>服务器环境</strong> - 确保PHP版本和扩展支持</li>";
echo "<li><strong>网络连接</strong> - 确保支付平台能正常访问回调URL</li>";
echo "<li><strong>日志文件</strong> - 查看easypay_callback.log等日志文件</li>";
echo "</ol>";

echo "<h4>测试步骤:</h4>";
echo "<ol>";
echo "<li>创建一个测试订单</li>";
echo "<li>使用真实支付进行小额测试（如0.01元）</li>";
echo "<li>检查回调日志和数据库记录</li>";
echo "<li>确认订单状态是否正确更新</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><strong>修复完成时间:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>建议:</strong> 请重新测试支付流程，如果问题仍然存在，请查看具体的错误日志。</p>";
?>
