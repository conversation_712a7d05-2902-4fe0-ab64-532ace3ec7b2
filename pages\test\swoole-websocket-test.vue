<template>
  <view class="container">
    <!-- 页面标题 -->
    <view class="header">
      <text class="title">🚀 Swoole WebSocket 测试</text>
      <text class="subtitle">专门测试Swoole WebSocket服务连接</text>
    </view>

    <!-- 连接状态 -->
    <view class="status-card">
      <view class="status-header">
        <text class="status-title">连接状态</text>
        <text :class="['status-indicator', connectionStatus]">
          {{ getStatusText() }}
        </text>
      </view>
      <view class="status-details">
        <text class="detail-item">服务器: ceshi.huisas.com:8080</text>
        <text class="detail-item">协议: Swoole WebSocket</text>
        <text class="detail-item">重连次数: {{ reconnectCount }}</text>
        <text class="detail-item">消息数量: {{ messageCount }}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="button-group">
      <button @click="connectWebSocket" :disabled="isConnecting" class="btn primary">
        {{ isConnecting ? '连接中...' : '连接WebSocket' }}
      </button>
      <button @click="disconnectWebSocket" :disabled="!isConnected" class="btn secondary">
        断开连接
      </button>
      <button @click="sendTestMessage" :disabled="!isConnected" class="btn success">
        发送测试消息
      </button>
      <button @click="testPaymentNotify" :disabled="!isConnected" class="btn warning">
        测试支付通知
      </button>
    </view>

    <!-- 认证信息 -->
    <view class="auth-section">
      <text class="section-title">认证信息</text>
      <view class="auth-form">
        <view class="form-item">
          <text class="label">商户ID:</text>
          <input v-model="authInfo.merchant_id" placeholder="请输入商户ID" class="input" />
        </view>
        <view class="form-item">
          <text class="label">员工ID:</text>
          <input v-model="authInfo.staff_id" placeholder="请输入员工ID（可选）" class="input" />
        </view>
        <view class="form-item">
          <text class="label">Token:</text>
          <input v-model="authInfo.token" placeholder="请输入认证Token" class="input" />
        </view>
        <button @click="sendAuth" :disabled="!isConnected" class="btn auth-btn">
          发送认证
        </button>
      </view>
    </view>

    <!-- 消息日志 -->
    <view class="log-section">
      <view class="log-header">
        <text class="section-title">消息日志</text>
        <button @click="clearLogs" class="btn small">清空</button>
      </view>
      <scroll-view class="log-content" scroll-y>
        <view v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          <text class="log-time">{{ formatTime(log.timestamp) }}</text>
          <text class="log-message">{{ log.message }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // WebSocket相关
      socketTask: null,
      isConnected: false,
      isConnecting: false,
      connectionStatus: 'disconnected', // disconnected, connecting, connected, error
      autoReconnect: true,
      reconnectCount: 0,
      messageCount: 0,
      heartbeatTimer: null,
      
      // 认证信息
      authInfo: {
        merchant_id: '1000', // 默认测试商户ID
        staff_id: '',
        token: 'test_token_1234567890' // 默认测试token
      },
      
      // 日志
      logs: []
    }
  },

  onLoad() {
    this.addLog('info', 'Swoole WebSocket测试页面加载完成')
  },

  onUnload() {
    this.disconnectWebSocket()
  },

  methods: {
    // 连接WebSocket
    connectWebSocket() {
      if (this.isConnected || this.isConnecting) {
        this.addLog('warning', '已经连接或正在连接中')
        return
      }

      this.isConnecting = true
      this.connectionStatus = 'connecting'
      this.autoReconnect = true // 重新启用自动重连

      // 使用Swoole WebSocket的标准连接格式
      const wsUrl = 'ws://ceshi.huisas.com:8080'
      this.addLog('info', `正在连接: ${wsUrl}`)

      this.socketTask = uni.connectSocket({
        url: wsUrl,
        success: () => {
          this.addLog('info', 'WebSocket连接请求发送成功')
        },
        fail: (error) => {
          this.addLog('error', `连接失败: ${JSON.stringify(error)}`)
          this.isConnecting = false
          this.connectionStatus = 'error'
        }
      })

      // 连接成功
      this.socketTask.onOpen(() => {
        this.isConnected = true
        this.isConnecting = false
        this.connectionStatus = 'connected'
        this.addLog('success', 'WebSocket连接成功！')

        // 启动心跳
        this.startHeartbeat()

        // 发送欢迎消息
        this.sendMessage({
          type: 'ping',
          data: {
            timestamp: Date.now(),
            message: 'Hello Swoole WebSocket!'
          }
        })
      })

      // 接收消息
      this.socketTask.onMessage((res) => {
        this.messageCount++
        try {
          const data = JSON.parse(res.data)
          this.addLog('receive', `收到消息: ${JSON.stringify(data, null, 2)}`)
          this.handleMessage(data)
        } catch (error) {
          this.addLog('receive', `收到原始消息: ${res.data}`)
        }
      })

      // 连接关闭
      this.socketTask.onClose((res) => {
        this.isConnected = false
        this.isConnecting = false
        this.connectionStatus = 'disconnected'
        this.addLog('warning', `WebSocket连接已关闭: ${res.code || 'unknown'} ${res.reason || ''}`)

        // 清理心跳定时器
        if (this.heartbeatTimer) {
          clearInterval(this.heartbeatTimer)
          this.heartbeatTimer = null
        }

        // 自动重连（如果不是主动断开）
        if (res.code !== 1000 && this.autoReconnect) {
          this.addLog('info', '5秒后尝试自动重连...')
          setTimeout(() => {
            if (!this.isConnected && this.autoReconnect) {
              this.addLog('info', '开始自动重连')
              this.connectWebSocket()
            }
          }, 5000)
        }
      })

      // 连接错误
      this.socketTask.onError((error) => {
        this.isConnected = false
        this.isConnecting = false
        this.connectionStatus = 'error'
        this.addLog('error', `WebSocket连接错误: ${JSON.stringify(error)}`)
      })
    },

    // 断开连接
    disconnectWebSocket() {
      this.autoReconnect = false // 禁用自动重连
      if (this.socketTask) {
        this.socketTask.close()
        this.socketTask = null
      }
      this.isConnected = false
      this.isConnecting = false
      this.connectionStatus = 'disconnected'

      // 清理心跳定时器
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer)
        this.heartbeatTimer = null
      }

      this.addLog('info', '主动断开WebSocket连接')
    },

    // 发送消息
    sendMessage(message) {
      if (!this.isConnected || !this.socketTask) {
        this.addLog('error', '未连接，无法发送消息')
        return
      }

      const messageStr = JSON.stringify(message)
      this.socketTask.send({
        data: messageStr,
        success: () => {
          this.addLog('send', `发送消息: ${messageStr}`)
        },
        fail: (error) => {
          this.addLog('error', `发送失败: ${JSON.stringify(error)}`)
        }
      })
    },

    // 发送认证
    sendAuth() {
      if (!this.authInfo.merchant_id || !this.authInfo.token) {
        this.addLog('error', '请填写商户ID和Token')
        return
      }

      const authMessage = {
        type: 'auth',
        data: {
          merchant_id: this.authInfo.merchant_id,
          staff_id: this.authInfo.staff_id || null,
          token: this.authInfo.token
        }
      }

      this.sendMessage(authMessage)
    },

    // 发送测试消息
    sendTestMessage() {
      const testMessage = {
        type: 'ping',
        data: {
          timestamp: Date.now(),
          test_data: 'Hello from UniApp!'
        }
      }
      this.sendMessage(testMessage)
    },

    // 测试支付通知
    testPaymentNotify() {
      // 这里应该调用后端API来发送支付通知
      this.addLog('info', '测试支付通知功能需要后端配合')
      uni.showModal({
        title: '提示',
        content: '请使用后端测试页面发送支付通知，观察此页面是否能收到消息',
        showCancel: false
      })
    },

    // 处理收到的消息
    handleMessage(data) {
      switch (data.type) {
        case 'welcome':
          this.addLog('info', '收到欢迎消息')
          break
        case 'auth_result':
          if (data.data.success) {
            this.addLog('success', '认证成功')
          } else {
            this.addLog('error', `认证失败: ${data.data.message}`)
          }
          break
        case 'payment_notification':
          const paymentData = data.data
          const logMessage = `💰 收到支付通知: ${paymentData.amount}元 (订单: ${paymentData.order_id})`
          this.addLog('payment', logMessage)

          // 显示支付通知弹窗
          uni.showToast({
            title: `收款 ${paymentData.amount}元`,
            icon: 'success',
            duration: 3000
          })

          // 可选：播放提示音
          uni.vibrateShort()
          break
        case 'pong':
          this.addLog('info', '收到心跳响应')
          break
        default:
          this.addLog('info', `收到未知类型消息: ${data.type}`)
      }
    },

    // 获取状态文本
    getStatusText() {
      switch (this.connectionStatus) {
        case 'connected': return '🟢 已连接'
        case 'connecting': return '🟡 连接中'
        case 'error': return '🔴 连接错误'
        default: return '⚪ 未连接'
      }
    },

    // 启动心跳
    startHeartbeat() {
      // 清理现有心跳
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer)
      }

      // 每30秒发送一次心跳
      this.heartbeatTimer = setInterval(() => {
        if (this.isConnected && this.socketTask) {
          this.sendMessage({
            type: 'ping',
            data: {
              timestamp: Date.now()
            }
          })
        }
      }, 30000)
    },

    // 添加日志
    addLog(type, message) {
      this.logs.unshift({
        type,
        message,
        timestamp: Date.now()
      })

      // 限制日志数量
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(0, 100)
      }
    },

    // 清空日志
    clearLogs() {
      this.logs = []
      this.addLog('info', '日志已清空')
    },

    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
    }
  }
}
</script>

<style scoped>
.container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 10rpx rgba(0,0,0,0.3);
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255,255,255,0.8);
}

.status-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.status-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.status-indicator {
  font-size: 28rpx;
  font-weight: bold;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
}

.status-indicator.connected {
  background: #d4edda;
  color: #155724;
}

.status-indicator.connecting {
  background: #fff3cd;
  color: #856404;
}

.status-indicator.disconnected {
  background: #f8d7da;
  color: #721c24;
}

.status-indicator.error {
  background: #f8d7da;
  color: #721c24;
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.detail-item {
  font-size: 28rpx;
  color: #666;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  border: none;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn.primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.btn.secondary {
  background: linear-gradient(45deg, #f093fb, #f5576c);
}

.btn.success {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
}

.btn.warning {
  background: linear-gradient(45deg, #43e97b, #38f9d7);
}

.btn.small {
  flex: none;
  min-width: 120rpx;
  height: 60rpx;
  font-size: 24rpx;
}

.btn:disabled {
  opacity: 0.5;
}

.auth-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.input {
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.auth-btn {
  margin-top: 20rpx;
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.log-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.log-content {
  height: 400rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 10rpx;
  padding: 20rpx;
}

.log-item {
  display: flex;
  margin-bottom: 15rpx;
  padding: 10rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.log-item.info {
  background: #e3f2fd;
  color: #1976d2;
}

.log-item.success {
  background: #e8f5e8;
  color: #2e7d32;
}

.log-item.error {
  background: #ffebee;
  color: #c62828;
}

.log-item.warning {
  background: #fff3e0;
  color: #ef6c00;
}

.log-item.send {
  background: #f3e5f5;
  color: #7b1fa2;
}

.log-item.receive {
  background: #e0f2f1;
  color: #00695c;
}

.log-item.payment {
  background: #fff8e1;
  color: #f57f17;
}

.log-time {
  min-width: 120rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.log-message {
  flex: 1;
  word-break: break-all;
}
</style>
