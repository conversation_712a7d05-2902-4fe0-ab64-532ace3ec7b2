import{Z as t,R as a,o as e,e as s,w as i,f as l,C as c,D as o,F as n,j as r,a3 as h,i as d,a1 as u,h as g,t as m,l as f,y as D,a as p,v as x,z as F,k as _}from"./index-B1Q521gi.js";import{_ as S}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as C}from"./share.DVl4BbzX.js";const y=S({components:{DiamondChart:S({name:"Diamond<PERSON><PERSON>",props:{chartData:{type:Object,default:()=>({categories:[],series:[]})},options:{type:Object,default:()=>({})}},data:()=>({ctx:null,canvasWidth:300,canvasHeight:300,centerX:150,centerY:150,maxRadius:120,showLegend:!0,legendData:[],defaultColors:["#5145F7","#1890FF","#4CD964","#FAAD14","#FF4D4F","#9013FE"],fontColor:"#666666",fontSize:12,animation:!0,animationDuration:1e3,currentStep:0,totalSteps:30,touchStartX:0,touchStartY:0,rotating:!1,rotationAngle:0,lastAngle:0}),mounted(){this.$nextTick((()=>{t().in(this).select("#diamondChart").fields({node:!0,size:!0}).exec((t=>{const e=t[0].node;this.ctx=e.getContext("2d");const s=a().pixelRatio,i=t[0].width||300,l=t[0].height||300;this.canvasWidth=i*s,this.canvasHeight=l*s,this.centerX=this.canvasWidth/2,this.centerY=this.canvasHeight/2,this.maxRadius=.85*Math.min(this.centerX,this.centerY),e.width=this.canvasWidth,e.height=this.canvasHeight,this.processLegendData(),this.animation?this.animateChart():this.drawChart(1)}))}))},watch:{chartData:{deep:!0,handler(){this.processLegendData(),this.ctx&&(this.animation?(this.currentStep=0,this.animateChart()):this.drawChart(1))}}},methods:{processLegendData(){this.chartData&&this.chartData.series&&this.chartData.series.length>0?(this.legendData=this.chartData.series.map(((t,a)=>({name:t.name,color:t.color||this.defaultColors[a%this.defaultColors.length]}))),this.showLegend=this.legendData.length>0):this.showLegend=!1},animateChart(){if(this.currentStep<=this.totalSteps){const t=this.currentStep/this.totalSteps;this.drawChart(t),this.currentStep++,requestAnimationFrame(this.animateChart)}},drawChart(t){const{ctx:a,centerX:e,centerY:s,maxRadius:i,rotationAngle:l}=this,{categories:c,series:o}=this.chartData;if(!c||!o||c.length<3||0===o.length)return;a.clearRect(0,0,this.canvasWidth,this.canvasHeight);const n=c.length,r=2*Math.PI/n;this.drawPolygonGrid(n,r,t,l),this.drawAxes(c,n,r,l),this.drawDataPolygons(o,c,n,r,t,l)},drawPolygonGrid(t,a,e,s){const{ctx:i,centerX:l,centerY:c,maxRadius:o}=this;i.lineWidth=1,i.strokeStyle="#EEEEEE";for(let n=1;n<=5;n++){const r=o*n/5*e;i.beginPath();for(let e=0;e<t;e++){const t=e*a+s,o=l+r*Math.cos(t),n=c+r*Math.sin(t);0===e?i.moveTo(o,n):i.lineTo(o,n)}i.closePath(),i.stroke()}},drawAxes(t,a,e,s){const{ctx:i,centerX:l,centerY:c,maxRadius:o,fontColor:n,fontSize:r}=this;i.lineWidth=1,i.strokeStyle="#DDDDDD",i.fillStyle=n,i.font=1.2*r+"px Arial",i.textAlign="center",i.textBaseline="middle";for(let h=0;h<a;h++){const a=h*e+s,d=l+o*Math.cos(a),u=c+o*Math.sin(a);i.beginPath(),i.moveTo(l,c),i.lineTo(d,u),i.stroke();const g=1.15*o,m=l+g*Math.cos(a),f=c+g*Math.sin(a),D=i.measureText(t[h]).width;i.fillStyle="rgba(255, 255, 255, 0.7)",i.fillRect(m-D/2-5,f-r/2-2,D+10,r+4),i.fillStyle=n,i.fillText(t[h],m,f)}},drawDataPolygons(t,a,e,s,i,l){const{ctx:c,centerX:o,centerY:n,maxRadius:r,defaultColors:h}=this,d=a.map(((a,e)=>Math.max(...t.map((t=>t.data[e]||0)))));for(let u=t.length-1;u>=0;u--){const a=t[u],g=a.color||h[u%h.length],m=[];c.fillStyle=this.hexToRgba(g,.25),c.strokeStyle=g,c.lineWidth=3,c.beginPath();for(let t=0;t<e;t++){const e=a.data[t]||0,h=d[t]||1,u=Math.min(e/h,1)*i,g=t*s+l,f=o+r*u*Math.cos(g),D=n+r*u*Math.sin(g);m.push({x:f,y:D}),0===t?c.moveTo(f,D):c.lineTo(f,D)}c.closePath(),c.fill(),c.stroke(),c.fillStyle="#FFFFFF",c.strokeStyle=g,c.lineWidth=2,m.forEach((t=>{c.beginPath(),c.arc(t.x,t.y,6,0,2*Math.PI),c.fill(),c.stroke(),c.fillStyle=g,c.beginPath(),c.arc(t.x,t.y,3,0,2*Math.PI),c.fill()}))}},hexToRgba:(t,a)=>`rgba(${parseInt(t.slice(1,3),16)}, ${parseInt(t.slice(3,5),16)}, ${parseInt(t.slice(5,7),16)}, ${a})`,touchStart(t){const a=t.touches[0];this.touchStartX=a.x,this.touchStartY=a.y,this.rotating=!0,this.lastAngle=this.calculateAngle(a.x,a.y)},touchMove(t){if(!this.rotating)return;const a=t.touches[0],e=this.calculateAngle(a.x,a.y),s=e-this.lastAngle;this.rotationAngle+=s,this.lastAngle=e,this.drawChart(1)},touchEnd(){this.rotating=!1},calculateAngle(t,a){const{centerX:e,centerY:s}=this;return Math.atan2(a-s,t-e)}}},[["render",function(t,a,D,p,x,F){const _=h,S=d,C=f;return e(),s(S,{class:"diamond-chart-container"},{default:i((()=>[l(_,{"canvas-id":"diamondChart",id:"diamondChart",class:"diamond-chart",onTouchstart:F.touchStart,onTouchmove:F.touchMove,onTouchend:F.touchEnd},null,8,["onTouchstart","onTouchmove","onTouchend"]),x.showLegend?(e(),s(S,{key:0,class:"legend"},{default:i((()=>[(e(!0),c(n,null,o(x.legendData,((t,a)=>(e(),s(S,{class:"legend-item",key:a},{default:i((()=>[l(S,{class:"legend-color",style:u({backgroundColor:t.color})},null,8,["style"]),l(C,{class:"legend-name"},{default:i((()=>[g(m(t.name),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):r("",!0)])),_:1})}],["__scopeId","data-v-fda4e0e2"]])},data:()=>({activeDataSet:"business",chartTitle:"经营多维度分析",chartSubtitle:"旋转图表可查看不同维度数据",chartData:{},chartOptions:{},businessData:{categories:["收入增长","客户满意度","交易频次","客单价","成本控制","员工效率"],series:[{name:"本月表现",color:"#5145F7",data:[85,92,78,80,65,88]},{name:"行业平均",color:"#FAAD14",data:[70,75,65,72,75,68]}]},productData:{categories:["销量","利润率","客户评分","复购率","增长趋势","市场份额"],series:[{name:"主打产品",color:"#1890FF",data:[90,85,95,82,78,75]},{name:"次要产品",color:"#4CD964",data:[75,70,80,65,60,55]},{name:"新品",color:"#9013FE",data:[60,75,85,45,90,40]}]},customerData:{categories:["消费能力","忠诚度","活跃度","影响力","增长潜力","价格敏感度"],series:[{name:"核心客户",color:"#FF4D4F",data:[92,95,88,80,85,60]},{name:"普通客户",color:"#FAAD14",data:[70,65,60,55,75,80]},{name:"潜在客户",color:"#1890FF",data:[50,30,45,75,95,85]}]},insights:[{color:"#5145F7",title:"核心优势",text:"客户满意度表现优异，高于行业平均17个百分点，是您的核心竞争力。"},{color:"#FF4D4F",title:"需改进领域",text:"成本控制低于行业平均，可能影响长期盈利能力，建议优化供应链。"},{color:"#FAAD14",title:"潜在机会",text:"通过提高员工效率，进一步扩大与行业平均的差距，降低人力成本。"}],actions:[{icon:"/static/home/<USER>",text:"导出详细报告"},{icon:"/static/home/<USER>",text:"制定优化计划"},{icon:"/static/home/<USER>",text:"查看历史趋势"}]}),onReady(){this.setDataSet("business")},methods:{goBack(){D()},setDataSet(t){switch(this.activeDataSet=t,t){case"business":this.chartData=this.businessData,this.chartTitle="经营多维度分析",this.chartSubtitle="旋转图表可查看不同维度数据",this.insights=[{color:"#5145F7",title:"核心优势",text:"客户满意度表现优异，高于行业平均17个百分点，是您的核心竞争力。"},{color:"#FF4D4F",title:"需改进领域",text:"成本控制低于行业平均，可能影响长期盈利能力，建议优化供应链。"},{color:"#FAAD14",title:"潜在机会",text:"通过提高员工效率，进一步扩大与行业平均的差距，降低人力成本。"}];break;case"product":this.chartData=this.productData,this.chartTitle="产品表现分析",this.chartSubtitle="对比不同产品线的多维度表现",this.insights=[{color:"#1890FF",title:"主打产品优势",text:"主打产品在客户评分方面表现突出，是产品口碑的核心支撑。"},{color:"#9013FE",title:"新品增长亮点",text:"新品在增长趋势方面表现优异，但市场份额仍有提升空间。"},{color:"#4CD964",title:"产品矩阵建议",text:"次要产品的复购率偏低，建议提升产品体验或调整定价策略。"}];break;case"customer":this.chartData=this.customerData,this.chartTitle="客户画像分析",this.chartSubtitle="了解不同客户群体的特征分布",this.insights=[{color:"#FF4D4F",title:"核心客户价值",text:"核心客户忠诚度极高，建议推出会员回馈计划，进一步增强客户粘性。"},{color:"#1890FF",title:"潜在客户转化",text:"潜在客户增长潜力高，但忠诚度低，可设计新客优惠活动促进转化。"},{color:"#FAAD14",title:"普通客户特点",text:"普通客户对价格较为敏感，可通过限时促销或套餐优惠提升消费频次。"}]}},handleAction(t){p({title:`点击了${["导出报告","制定计划","查看趋势"][t]}`,icon:"none"})}}},[["render",function(t,a,r,h,D,p){const S=_,y=d,b=f,v=x("diamond-chart");return e(),s(y,{class:"container"},{default:i((()=>[l(y,{class:"header"},{default:i((()=>[l(y,{class:"header-left",onClick:p.goBack},{default:i((()=>[l(S,{src:"/h5/static/home/<USER>",mode:"aspectFit",class:"icon-back"})])),_:1},8,["onClick"]),l(b,{class:"header-title"},{default:i((()=>[g("多维数据分析")])),_:1}),l(y,{class:"header-right"},{default:i((()=>[l(S,{src:C,mode:"aspectFit",class:"icon-share"})])),_:1})])),_:1}),l(y,{class:"selector"},{default:i((()=>[l(y,{class:F(["selector-item",{active:"business"===D.activeDataSet}]),onClick:a[0]||(a[0]=t=>p.setDataSet("business"))},{default:i((()=>[l(b,null,{default:i((()=>[g("经营概览")])),_:1})])),_:1},8,["class"]),l(y,{class:F(["selector-item",{active:"product"===D.activeDataSet}]),onClick:a[1]||(a[1]=t=>p.setDataSet("product"))},{default:i((()=>[l(b,null,{default:i((()=>[g("产品表现")])),_:1})])),_:1},8,["class"]),l(y,{class:F(["selector-item",{active:"customer"===D.activeDataSet}]),onClick:a[2]||(a[2]=t=>p.setDataSet("customer"))},{default:i((()=>[l(b,null,{default:i((()=>[g("客户画像")])),_:1})])),_:1},8,["class"])])),_:1}),l(y,{class:"chart-title"},{default:i((()=>[l(b,{class:"title"},{default:i((()=>[g(m(D.chartTitle),1)])),_:1}),l(b,{class:"subtitle"},{default:i((()=>[g(m(D.chartSubtitle),1)])),_:1})])),_:1}),l(y,{class:"chart-container"},{default:i((()=>[l(v,{chartData:D.chartData,options:D.chartOptions},null,8,["chartData","options"])])),_:1}),l(y,{class:"analysis-card"},{default:i((()=>[l(y,{class:"card-title"},{default:i((()=>[l(b,null,{default:i((()=>[g("数据解析")])),_:1})])),_:1}),l(y,{class:"insight-list"},{default:i((()=>[(e(!0),c(n,null,o(D.insights,((t,a)=>(e(),s(y,{class:"insight-item",key:a},{default:i((()=>[l(y,{class:"insight-icon",style:u({backgroundColor:t.color})},{default:i((()=>[l(b,null,{default:i((()=>[g(m(a+1),1)])),_:2},1024)])),_:2},1032,["style"]),l(y,{class:"insight-content"},{default:i((()=>[l(b,{class:"insight-title"},{default:i((()=>[g(m(t.title),1)])),_:2},1024),l(b,{class:"insight-text"},{default:i((()=>[g(m(t.text),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1}),l(y,{class:"action-card"},{default:i((()=>[l(y,{class:"card-title"},{default:i((()=>[l(b,null,{default:i((()=>[g("建议行动")])),_:1})])),_:1}),l(y,{class:"action-list"},{default:i((()=>[(e(!0),c(n,null,o(D.actions,((t,a)=>(e(),s(y,{class:"action-item",key:a,onClick:t=>p.handleAction(a)},{default:i((()=>[l(S,{src:t.icon,mode:"aspectFit",class:"action-icon"},null,8,["src"]),l(b,{class:"action-text"},{default:i((()=>[g(m(t.text),1)])),_:2},1024),l(S,{src:"/h5/static/home/<USER>",mode:"aspectFit",class:"icon-arrow-right"})])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-5ba419e8"]]);export{y as default};
