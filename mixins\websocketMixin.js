/**
 * WebSocket Mixin - 参考CSDN文章实现
 * 为指定页面注入WebSocket功能
 */
import AudioPlayer from '@/utils/audioPlayer.js'

let isSocketClose = false // 是否关闭socket
let heartbeatInterval = null // 心跳定时器
let socketTask = null // websocket对象
let againTimer = null // 断线重连定时器
let onReFn = null
let onSucFn = null
let onErrFn = null

export const websocketMixin = {
  data() {
    return {
      // WebSocket状态
      wsConnected: false,
      wsConnecting: false,
      wsError: false,
      wsErrorMessage: '',
      
      // 音频播放器
      audioPlayer: null,

      // 语音播报状态（从存储中读取）
      voiceEnabled: uni.getStorageSync('voice_enabled') !== false,

      // 消息去重（防止重复播放）
      processedMessages: new Set(),
      messageCleanupTimer: null
    }
  },

  onLoad() {
    console.log('🎵 WebSocket Mixin: 页面加载，初始化音频播放器')
    this.audioPlayer = new AudioPlayer()
    this.updateVoiceSettings()
  },

  onShow() {
    console.log('🔄 WebSocket Mixin: 页面显示')

    // 🔧 参考CSDN文章：每个页面显示时重新连接WebSocket
    setTimeout(() => {
      this.initWebSocket()
    }, 500)
  },

  onUnload() {
    console.log('🔌 WebSocket Mixin: 页面卸载，关闭WebSocket连接')
    this.closeWebSocket()
  },

  onHide() {
    console.log('👁️ WebSocket Mixin: 页面隐藏，关闭WebSocket连接')
    this.closeWebSocket()
  },

  beforeDestroy() {
    console.log('💀 WebSocket Mixin: 组件销毁，关闭WebSocket连接')
    this.closeWebSocket()
  },

  methods: {


    /**
     * 初始化音频播放权限
     */
    async initAudioPermission() {
      try {
        console.log('🔊 WebSocket Mixin: 初始化音频播放权限')

        // #ifdef H5
        // 创建一个静音的音频上下文来获取播放权限
        if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
          const AudioContextClass = AudioContext || webkitAudioContext
          const audioContext = new AudioContextClass()

          // 创建一个极短的静音音频
          const buffer = audioContext.createBuffer(1, 1, 22050)
          const source = audioContext.createBufferSource()
          source.buffer = buffer
          source.connect(audioContext.destination)
          source.start(0)

          console.log('✅ WebSocket Mixin: 音频上下文初始化成功')
        }

        // 预加载Web Speech API
        if ('speechSynthesis' in window) {
          // 创建一个静音的语音来激活API
          const utterance = new SpeechSynthesisUtterance('')
          utterance.volume = 0
          speechSynthesis.speak(utterance)
          console.log('✅ WebSocket Mixin: Web Speech API预加载成功')
        }
        // #endif

        // 标记音频权限已初始化
        getApp().globalData.audioPermissionInitialized = true

      } catch (error) {
        console.error('❌ WebSocket Mixin: 音频权限初始化失败:', error)
      }
    },

    /**
     * 绑定全局WebSocket事件
     */
    bindGlobalEvents() {
      // 🔧 智能音频处理分配
      const shouldHandleAudio = this.shouldHandleAudio()

      if (shouldHandleAudio) {
        // 如果有其他音频处理器，先解绑它
        const currentHandler = getApp().globalData.audioHandler
        if (currentHandler && currentHandler !== this) {
          console.log('🔄 WebSocket Mixin: 切换音频处理器')
          try {
            uni.$off('websocket-message', currentHandler.handleWebSocketMessage)
            uni.$on('websocket-message', currentHandler.handleWebSocketMessageSilent)
          } catch (error) {
            console.error('切换音频处理器失败:', error)
          }
        }

        console.log('🎵 WebSocket Mixin: 当前页面负责音频播放')
        getApp().globalData.audioHandler = this
        uni.$off('websocket-message', this.handleWebSocketMessageSilent) // 先解绑静默处理
        uni.$on('websocket-message', this.handleWebSocketMessage)
      } else {
        console.log('🔇 WebSocket Mixin: 当前页面只监听状态，不处理音频')
        uni.$off('websocket-message', this.handleWebSocketMessage) // 先解绑音频处理
        uni.$on('websocket-message', this.handleWebSocketMessageSilent)
      }

      uni.$on('websocket-error', this.handleWebSocketError)
      uni.$on('websocket-close', this.handleWebSocketClose)
    },

    /**
     * 判断当前页面是否应该处理音频播放
     */
    shouldHandleAudio() {
      // 🔧 优先级策略：首页 > 支付页面 > 其他页面
      const currentPage = this.getCurrentPagePath()
      const currentHandler = getApp().globalData.audioHandler

      console.log(`🎵 WebSocket Mixin: 检查音频处理权 - 当前页面: ${currentPage}`)

      // 如果没有其他页面在处理音频，当前页面接管
      if (!currentHandler) {
        console.log('🎵 WebSocket Mixin: 没有音频处理器，当前页面接管')
        return true
      }

      // 如果当前音频处理器已失效，当前页面接管
      try {
        if (!currentHandler.wsConnected) {
          console.log('🎵 WebSocket Mixin: 当前音频处理器已失效，当前页面接管')
          return true
        }
      } catch (error) {
        console.log('🎵 WebSocket Mixin: 音频处理器异常，当前页面接管')
        return true
      }

      // 页面优先级判断
      const currentHandlerPage = this.getPagePath(currentHandler)
      const priority = this.getPagePriority(currentPage)
      const currentHandlerPriority = this.getPagePriority(currentHandlerPage)

      if (priority > currentHandlerPriority) {
        console.log(`🎵 WebSocket Mixin: 当前页面优先级更高 (${priority} > ${currentHandlerPriority})，接管音频处理`)
        return true
      }

      console.log(`🔇 WebSocket Mixin: 保持现有音频处理器 (${currentHandlerPage})`)
      return false
    },

    /**
     * 获取当前页面路径
     */
    getCurrentPagePath() {
      try {
        const pages = getCurrentPages()
        if (pages.length > 0) {
          return pages[pages.length - 1].route
        }
      } catch (error) {
        console.error('获取当前页面路径失败:', error)
      }
      return 'unknown'
    },

    /**
     * 获取页面路径（从页面实例）
     */
    getPagePath(pageInstance) {
      try {
        if (pageInstance && pageInstance.$scope) {
          return pageInstance.$scope.route
        }
        if (pageInstance && pageInstance.route) {
          return pageInstance.route
        }
      } catch (error) {
        console.error('获取页面路径失败:', error)
      }
      return 'unknown'
    },

    /**
     * 获取页面优先级
     */
    getPagePriority(pagePath) {
      const priorities = {
        'pages/index/index': 100,           // 首页最高优先级
        'pages/pay/mini-payment': 90,       // 支付页面高优先级
        'pages/dynamic-code/index': 80,     // 动态码页面
        'pages/scan/index': 70,             // 扫码页面
        'pages/settings/voice': 60,         // 语音设置页面
        'pages/bill/index': 50              // 账单页面低优先级
      }

      return priorities[pagePath] || 10 // 默认低优先级
    },

    /**
     * 重新评估音频处理器
     */
    reevaluateAudioHandler() {
      if (!this.wsConnected) {
        console.log('🔇 WebSocket Mixin: 连接未建立，跳过音频处理器评估')
        return
      }

      const shouldHandleAudio = this.shouldHandleAudio()
      const currentHandler = getApp().globalData.audioHandler

      // 如果当前页面应该处理音频，但不是当前处理器
      if (shouldHandleAudio && currentHandler !== this) {
        console.log('🔄 WebSocket Mixin: 重新绑定音频处理器')
        this.bindGlobalEvents()
      }
      // 如果当前页面不应该处理音频，但是当前处理器
      else if (!shouldHandleAudio && currentHandler === this) {
        console.log('🔄 WebSocket Mixin: 释放音频处理权')
        this.bindGlobalEvents()
      }
    },

    /**
     * 解绑全局WebSocket事件
     */
    unbindGlobalEvents() {
      // 解绑全局WebSocket消息
      uni.$off('websocket-message', this.handleWebSocketMessage)
      uni.$off('websocket-message', this.handleWebSocketMessageSilent)
      uni.$off('websocket-error', this.handleWebSocketError)
      uni.$off('websocket-close', this.handleWebSocketClose)

      // 🔧 如果当前页面是音频处理器，清除标记
      if (getApp().globalData.audioHandler === this) {
        getApp().globalData.audioHandler = null
        console.log('🔇 WebSocket Mixin: 清除音频处理器标记')
      }
    },

    /**
     * 初始化WebSocket连接
     */
    initWebSocket() {
      console.log('🔧 WebSocket Mixin: 初始化WebSocket连接')

      // 检查用户登录状态
      const token = uni.getStorageSync('user_token')
      const uid = uni.getStorageSync('user_uid')

      if (!token || !uid) {
        console.log('⚠️ WebSocket Mixin: 用户未登录，跳过连接')
        return
      }

      // 🔧 参考CSDN文章：设置全局回调函数
      onReFn = this.handleWebSocketMessage.bind(this)
      onErrFn = this.handleWebSocketError.bind(this)
      onSucFn = this.handleWebSocketSuccess.bind(this)

      console.log('🚀 WebSocket Mixin: 初始化WebSocket连接')
      this.connectWebSocket()
    },

    /**
     * 连接WebSocket（参考CSDN文章实现）
     */
    connectWebSocket() {
      const uid = uni.getStorageSync('user_uid')
      const wsUrl = `ws://ceshi.huisas.com:8080?id=${uid}`

      console.log('🚀 WebSocket Mixin: 连接到:', wsUrl)

      isSocketClose = false

      // 判断是否有websocket对象，有的话清空
      if (socketTask) {
        socketTask.close()
        socketTask = null
        clearInterval(heartbeatInterval)
      }

      console.log(`🔗 WebSocket Mixin: 正在连接 ${wsUrl}`)
      this.wsConnecting = true

      // 连接WebSocket
      socketTask = uni.connectSocket({
        url: wsUrl,
        success: (data) => {
          console.log('✅ WebSocket Mixin: 连接请求发送成功')
          clearInterval(againTimer) // 清除断线重连定时器
        },
        fail: (err) => {
          console.error('❌ WebSocket Mixin: 连接失败', err)
          this.wsConnecting = false
        }
      })

      // 连接打开
      socketTask.onOpen((res) => {
        console.log('🎉 WebSocket Mixin: 连接已打开', res)
        this.wsConnected = true
        this.wsConnecting = false
        this.wsError = false

        clearInterval(againTimer) // 清除断线重连定时器

        // 🔧 参考CSDN文章：调用成功回调
        if (onSucFn) {
          onSucFn({ isShow: false })
        }

        // 启动心跳
        this.startHeartbeat()

        // 发送认证
        this.sendAuth()
      })

      // 监听连接失败
      socketTask.onError((err) => {
        console.error('❌ WebSocket Mixin: 连接错误', err)
        this.wsConnected = false
        this.wsConnecting = false
        this.wsError = true

        // 停止发送心跳
        clearInterval(heartbeatInterval)

        // 如果不是人为关闭的话，进行重连
        if (!isSocketClose) {
          this.reconnectWebSocket()
        }
      })

      // 监听连接关闭
      socketTask.onClose((e) => {
        console.log('🔌 WebSocket Mixin: 连接已关闭')
        this.wsConnected = false
        this.wsConnecting = false

        clearInterval(heartbeatInterval)

        if (!isSocketClose) {
          this.reconnectWebSocket()
        }
      })

      // 监听收到信息
      socketTask.onMessage((res) => {
        console.log('📨 WebSocket Mixin: 收到原始消息', res.data)

        try {
          // 尝试解析JSON
          const serverData = JSON.parse(res.data)
          console.log('✅ WebSocket Mixin: JSON解析成功', serverData)

          // 🔧 参考CSDN文章：直接调用回调函数处理消息
          if (onReFn && serverData) {
            onReFn(serverData)
          }
        } catch (error) {
          console.error('❌ WebSocket Mixin: JSON解析失败', error)
          console.log('🔍 WebSocket Mixin: 原始消息内容:', res.data)

          // 🔧 尝试处理非JSON格式的消息
          this.handleRawMessage(res.data)
        }
      })
    },

    /**
     * 断线重连
     */
    reconnectWebSocket() {
      console.log('🔄 WebSocket Mixin: 开始断线重连')
      clearInterval(againTimer)
      clearInterval(heartbeatInterval)
      
      if (socketTask) {
        socketTask.close()
        socketTask = null
      }
      
      this.handleWebSocketError({
        isShow: true,
        message: 'WebSocket服务正在重连...'
      })
      
      // 5秒后重连
      againTimer = setInterval(() => {
        console.log('🔄 WebSocket Mixin: 正在重新连接...')
        this.connectWebSocket()
      }, 5000)
    },

    /**
     * 启动心跳
     */
    startHeartbeat() {
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval)
      }
      
      // 10秒发送一次心跳
      heartbeatInterval = setInterval(() => {
        if (socketTask && this.wsConnected) {
          this.sendMessage('心跳ing')
        }
      }, 10000)
    },

    /**
     * 发送消息
     */
    sendMessage(msg) {
      if (!socketTask || !this.wsConnected) {
        console.log('⚠️ WebSocket Mixin: 连接未建立，无法发送消息')
        return
      }
      
      try {
        const message = typeof msg === 'string' ? msg : JSON.stringify(msg)
        socketTask.send({
          data: message
        })
      } catch (e) {
        console.error('❌ WebSocket Mixin: 发送消息失败', e)
        if (!isSocketClose) {
          this.reconnectWebSocket()
        }
      }
    },

    /**
     * 发送认证消息（参考测试页面实现）
     */
    sendAuth() {
      // 🔧 使用与测试页面相同的认证方式
      const merchantId = uni.getStorageSync('user_uid') || '1000'
      const token = uni.getStorageSync('user_token') || 'test_token_1234567890'

      console.log('🔐 WebSocket Mixin: 发送认证消息，商户ID:', merchantId, '令牌:', token)

      const authMessage = {
        type: 'auth',
        data: {
          merchant_id: merchantId,
          staff_id: '',
          token: token
        }
      }

      this.sendMessage(authMessage)

      // 认证后立即订阅支付频道（参考测试页面）
      setTimeout(() => {
        this.subscribeToMerchantChannel(merchantId)
      }, 100) // 缩短延迟时间
    },

    /**
     * 订阅商户专属频道（参考测试页面实现）
     */
    subscribeToMerchantChannel(merchantId) {
      const channel = `merchant_${merchantId}_payment`

      console.log('📡 WebSocket Mixin: 订阅商户专属频道:', channel)

      const subscribeMessage = {
        type: 'subscribe',
        data: {
          channel: channel
        }
      }

      this.sendMessage(subscribeMessage)
    },

    /**
     * 关闭WebSocket连接
     */
    closeWebSocket() {
      console.log('🔌 WebSocket Mixin: 关闭WebSocket连接', {
        wsConnected: this.wsConnected,
        wsConnecting: this.wsConnecting,
        hasSocketTask: !!socketTask
      })

      isSocketClose = true

      // 清理定时器
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval)
        heartbeatInterval = null
        console.log('🔌 WebSocket Mixin: 清理心跳定时器')
      }

      if (againTimer) {
        clearInterval(againTimer)
        againTimer = null
        console.log('🔌 WebSocket Mixin: 清理重连定时器')
      }

      // 关闭连接
      if (socketTask) {
        socketTask.close()
        socketTask = null
        console.log('🔌 WebSocket Mixin: 连接已关闭')
      }

      this.wsConnected = false
      this.wsConnecting = false
    },

    /**
     * 静默处理WebSocket消息（不播放音频）
     */
    handleWebSocketMessageSilent(data) {
      console.log('🔇 WebSocket Mixin: 静默处理消息（不播放音频）', data)

      // 只更新连接状态，不播放音频
      switch (data.type) {
        case 'welcome':
        case 'auth_result':
        case 'pong':
        case 'heartbeat':
          // 这些消息只记录日志，不做其他处理
          break
        case 'payment_notification':
          console.log('💰 WebSocket Mixin: 收到支付通知（静默模式）:', data.data)
          // 不播放音频，只记录
          break
        default:
          console.log(`ℹ️ WebSocket Mixin: 收到未知类型消息（静默模式）: ${data.type}`, data)
      }
    },

    /**
     * 处理原始消息（非JSON格式）
     */
    handleRawMessage(rawData) {
      console.log('🔍 WebSocket Mixin: 处理原始消息', rawData)

      // 尝试从原始消息中提取支付信息
      try {
        // 检查是否包含支付相关关键词
        if (rawData.includes('money') || rawData.includes('amount') || rawData.includes('payment')) {
          console.log('💰 WebSocket Mixin: 检测到支付相关原始消息')

          // 尝试提取金额
          const moneyMatch = rawData.match(/(?:money|amount)["\s]*[:=]["\s]*([0-9.]+)/i)
          if (moneyMatch) {
            const amount = moneyMatch[1]
            console.log('💰 WebSocket Mixin: 从原始消息提取金额:', amount)

            // 构造支付通知对象
            const paymentData = {
              amount: amount,
              money: amount,
              typename: '未知支付方式',
              order_id: 'RAW_' + Date.now(),
              raw_message: rawData
            }

            this.handlePaymentNotification(paymentData)
            return
          }
        }

        console.log('ℹ️ WebSocket Mixin: 原始消息不包含支付信息，忽略')
      } catch (error) {
        console.error('❌ WebSocket Mixin: 处理原始消息失败', error)
      }
    },

    /**
     * 处理WebSocket消息（子类需要重写）
     */
    handleWebSocketMessage(data) {
      console.log('📨 WebSocket Mixin: 处理消息（默认实现）', data)

      // 🔧 增强消息类型检测
      const messageType = data.type || data.event || data.action || 'unknown'
      console.log('🔍 WebSocket Mixin: 消息类型:', messageType)

      switch (messageType) {
        case 'welcome':
          console.log('🎉 WebSocket Mixin: 收到服务器欢迎消息')
          break
        case 'auth_result':
          if (data.data && data.data.success) {
            console.log('✅ WebSocket Mixin: 认证成功', data.data)
          } else {
            console.error(`❌ WebSocket Mixin: 认证失败: ${data.data?.message}`, data.data)
          }
          break
        case 'payment_notification':
        case 'payment_success':
        case 'payment':
          console.log('💰 WebSocket Mixin: 收到支付通知:', data.data || data)
          this.handlePaymentNotification(data.data || data)
          break
        case 'pong':
          console.log('💓 WebSocket Mixin: 收到心跳响应')
          break
        case 'heartbeat':
          console.log('💓 WebSocket Mixin: 收到服务器心跳')
          break
        default:
          console.log(`ℹ️ WebSocket Mixin: 收到未知类型消息: ${messageType}`, data)

          // 🔧 尝试检测是否为支付消息（兼容不同格式）
          if (this.isPaymentMessage(data)) {
            console.log('💰 WebSocket Mixin: 检测到支付消息（通用格式）')
            this.handlePaymentNotification(data)
          }
      }
    },

    /**
     * 检测是否为支付消息
     */
    isPaymentMessage(data) {
      // 检查是否包含支付相关字段
      return !!(
        data.amount ||
        data.money ||
        data.realmoney ||
        (data.data && (data.data.amount || data.data.money)) ||
        (data.extra_data && (data.extra_data.amount || data.extra_data.money))
      )
    },

    /**
     * 处理支付通知
     */
    async handlePaymentNotification(paymentData) {
      console.log('🎯 WebSocket Mixin: 开始处理支付通知')
      console.log('📊 WebSocket Mixin: 原始支付数据:', JSON.stringify(paymentData, null, 2))

      try {
        // 🔧 支持多种金额字段格式，包括嵌套的extra_data
        const amount = paymentData.amount ||
                      paymentData.money ||
                      paymentData.extra_data?.money ||
                      paymentData.extra_data?.amount ||
                      paymentData.realmoney || '0.00'

        console.log('💰 WebSocket Mixin: 提取的金额:', amount)

        // 验证金额有效性
        if (!amount || amount === '0.00' || isNaN(parseFloat(amount))) {
          console.warn('⚠️ WebSocket Mixin: 无效的支付金额，跳过处理:', amount)
          return
        }

        // 生成消息唯一标识（用于去重）
        const messageId = this.generateMessageId(paymentData)
        console.log('🔑 WebSocket Mixin: 消息ID:', messageId)

        // 检查是否已处理过此消息
        if (this.processedMessages.has(messageId)) {
          console.log(`⚠️ WebSocket Mixin: 消息已处理过，跳过重复播放: ${messageId}`)
          return
        }

        // 标记消息已处理
        this.processedMessages.add(messageId)
        console.log('✅ WebSocket Mixin: 消息已标记为已处理')

        // 定时清理已处理消息（防止内存泄漏）
        this.scheduleMessageCleanup()

        console.log(`🎵 WebSocket Mixin: 准备播放支付语音: 收款${amount}元 (ID: ${messageId})`)

        // 检查语音设置
        const voiceSettings = this.getVoiceSettings()
        console.log('🔊 WebSocket Mixin: 语音设置:', voiceSettings)

        if (!voiceSettings.enabled) {
          console.log('🔇 WebSocket Mixin: 语音播报已关闭，跳过播放')
          return
        }

        // 检查音频播放器
        if (!this.audioPlayer) {
          console.warn('⚠️ WebSocket Mixin: 音频播放器未初始化')
          await this.initAudioPlayer()
        }

        console.log('🎵 WebSocket Mixin: 开始播放语音...')

        // 播放语音
        if (this.audioPlayer) {
          await this.audioPlayer.playPaymentSuccess(amount)
          console.log('✅ WebSocket Mixin: 语音播放完成')
        } else {
          console.error('❌ WebSocket Mixin: 音频播放器不可用')
        }

        // 显示Toast提示
        uni.showToast({
          title: `收款 ¥${amount}`,
          icon: 'success',
          duration: 3000
        })

        console.log(`🎉 WebSocket Mixin: 支付通知处理完成: 收款${amount}元`)

      } catch (error) {
        console.error('❌ WebSocket Mixin: 处理支付通知失败:', error)
        console.error('❌ WebSocket Mixin: 错误堆栈:', error.stack)
      }
    },

    /**
     * 生成消息唯一标识
     */
    generateMessageId(paymentData) {
      const timestamp = paymentData.timestamp ||
                       paymentData.time ||
                       paymentData.created_at ||
                       Date.now()
      const amount = paymentData.amount ||
                    paymentData.money ||
                    paymentData.realmoney || '0'
      const orderId = paymentData.order_id ||
                     paymentData.merchant_id ||
                     paymentData.id ||
                     Math.random().toString(36).substr(2, 9)

      return `${timestamp}_${amount}_${orderId}`
    },

    /**
     * 定时清理已处理消息
     */
    scheduleMessageCleanup() {
      if (this.messageCleanupTimer) {
        clearTimeout(this.messageCleanupTimer)
      }

      // 30秒后清理消息记录
      this.messageCleanupTimer = setTimeout(() => {
        const oldSize = this.processedMessages.size
        this.processedMessages.clear()
        console.log(`🧹 WebSocket Mixin: 清理消息记录 (${oldSize} -> 0)`)
      }, 30000)
    },

    /**
     * 更新语音设置
     */
    updateVoiceSettings() {
      const voiceEnabled = uni.getStorageSync('voice_enabled')
      this.voiceEnabled = voiceEnabled !== false // 默认启用
      console.log(`🔊 WebSocket Mixin: 语音设置更新: ${this.voiceEnabled ? '启用' : '禁用'}`)
    },

    /**
     * 更新存储中的WebSocket状态
     */
    updateStorageStatus() {
      try {
        const status = {
          connected: this.wsConnected,
          connecting: this.wsConnecting,
          timestamp: Date.now()
        }
        uni.setStorageSync('websocket_status', status)
        console.log('💾 WebSocket状态已保存到存储:', status)
      } catch (error) {
        console.error('❌ 保存WebSocket状态失败:', error)
      }
    },

    /**
     * 处理WebSocket错误
     */
    handleWebSocketError(error) {
      console.log('❌ WebSocket Mixin: 连接错误', error)
      this.wsError = true
      this.wsErrorMessage = error.message || '连接失败'
      
      if (error.isShow) {
        // 可以在这里显示错误提示
        // uni.showToast({
        //   title: error.message || '连接失败',
        //   icon: 'none'
        // })
      }
    },

    /**
     * 处理WebSocket成功
     */
    handleWebSocketSuccess(success) {
      console.log('✅ WebSocket Mixin: 连接成功', success)
      this.wsError = false
      this.wsErrorMessage = ''
    },

    /**
     * 清理资源
     */
    cleanup() {
      // 清理消息记录
      if (this.processedMessages) {
        this.processedMessages.clear()
      }

      // 清理定时器
      if (this.messageCleanupTimer) {
        clearTimeout(this.messageCleanupTimer)
        this.messageCleanupTimer = null
      }

      // 清理音频播放器
      if (this.audioPlayer) {
        this.audioPlayer.clearQueue()
      }

      console.log('🧹 WebSocket Mixin: 资源清理完成')
    }
  }
}
