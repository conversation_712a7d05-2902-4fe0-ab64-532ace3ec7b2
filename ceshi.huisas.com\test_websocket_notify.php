<?php
/**
 * 测试WebSocket通知功能
 */

// 引入必要的文件
require_once __DIR__ . '/includes/common.php';
require_once __DIR__ . '/includes/functions.php';

echo "<h1>WebSocket通知功能测试</h1>\n";

// 1. 检查函数是否存在
echo "<h2>1. 函数检查</h2>\n";
$functionExists = function_exists('sendWebSocketPaymentNotification');
echo "sendWebSocketPaymentNotification函数存在: " . ($functionExists ? '✅ 是' : '❌ 否') . "<br>\n";

if (!$functionExists) {
    echo "尝试手动引入websocket_notify.php...<br>\n";
    if (file_exists(__DIR__ . '/websocket_notify.php')) {
        require_once __DIR__ . '/websocket_notify.php';
        $functionExists = function_exists('sendWebSocketPaymentNotification');
        echo "重新检查函数存在: " . ($functionExists ? '✅ 是' : '❌ 否') . "<br>\n";
    } else {
        echo "❌ websocket_notify.php文件不存在<br>\n";
    }
}

// 2. 检查包含的文件
echo "<h2>2. 已包含的文件</h2>\n";
$includedFiles = get_included_files();
foreach ($includedFiles as $file) {
    if (strpos($file, 'websocket') !== false) {
        echo "✅ " . basename($file) . "<br>\n";
    }
}

// 3. 测试WebSocket通知
if ($functionExists) {
    echo "<h2>3. 测试WebSocket通知</h2>\n";
    
    $testData = [
        'trade_no' => 'TEST_' . date('YmdHis'),
        'uid' => '1000',  // 商户1000
        'money' => '0.01',
        'type' => 'alipay',
        'typename' => '支付宝',
        'addtime' => date('Y-m-d H:i:s'),
        'api_trade_no' => 'TEST_API_' . time(),
        'buyer' => '<EMAIL>',
        'status' => 'success'
    ];
    
    echo "测试数据: <pre>" . json_encode($testData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";
    
    try {
        $result = sendWebSocketPaymentNotification($testData);
        echo "通知结果: " . ($result ? '✅ 成功' : '❌ 失败') . "<br>\n";
    } catch (Exception $e) {
        echo "❌ 异常: " . $e->getMessage() . "<br>\n";
    }
} else {
    echo "<h2>3. 无法测试</h2>\n";
    echo "❌ sendWebSocketPaymentNotification函数不存在，无法测试<br>\n";
}

// 4. 检查日志目录
echo "<h2>4. 日志目录检查</h2>\n";
$logDir = __DIR__ . '/includes/logs';
if (is_dir($logDir)) {
    echo "✅ 日志目录存在: $logDir<br>\n";
    if (is_writable($logDir)) {
        echo "✅ 日志目录可写<br>\n";
    } else {
        echo "❌ 日志目录不可写<br>\n";
    }
} else {
    echo "❌ 日志目录不存在: $logDir<br>\n";
    if (mkdir($logDir, 0755, true)) {
        echo "✅ 已创建日志目录<br>\n";
    } else {
        echo "❌ 无法创建日志目录<br>\n";
    }
}

// 5. 测试日志写入
echo "<h2>5. 测试日志写入</h2>\n";
try {
    \lib\Payment::writePaymentLog('websocket_test', [
        'message' => '这是一个测试日志',
        'timestamp' => date('Y-m-d H:i:s'),
        'test_data' => $testData ?? []
    ]);
    echo "✅ 日志写入成功<br>\n";
} catch (Exception $e) {
    echo "❌ 日志写入失败: " . $e->getMessage() . "<br>\n";
}

// 6. 检查WebSocket服务状态
echo "<h2>6. WebSocket服务检查</h2>\n";
$websocketHost = 'ceshi.huisas.com';
$websocketPort = 8080;

$connection = @fsockopen($websocketHost, $websocketPort, $errno, $errstr, 5);
if ($connection) {
    echo "✅ WebSocket服务运行正常 ($websocketHost:$websocketPort)<br>\n";
    fclose($connection);
} else {
    echo "❌ WebSocket服务无法连接: $errstr ($errno)<br>\n";
}

echo "<h2>测试完成</h2>\n";
echo "<p>请检查服务器上的日志文件: /includes/logs/payment_debug_" . date('Y-m-d') . ".log</p>\n";
?>
