<template>
  <view class="websocket-status">
    <!-- WebSocket 状态指示器 -->
    <view class="status-indicator" @click="toggleDetails">
      <view class="status-dot" :style="{ backgroundColor: statusColor }"></view>
      <text class="status-text">{{ statusText }}</text>
      <text class="status-icon">{{ showDetails ? '▼' : '▶' }}</text>
    </view>

    <!-- 详细信息面板 -->
    <view v-if="showDetails" class="status-details">
      <!-- 连接信息 -->
      <view class="detail-section">
        <text class="section-title">🔗 连接信息</text>
        <view class="detail-item">
          <text class="label">状态:</text>
          <text class="value" :style="{ color: statusColor }">{{ statusText }}</text>
        </view>
        <view class="detail-item" v-if="websocketStatus.lastConnectTime">
          <text class="label">连接时间:</text>
          <text class="value">{{ formatTime(websocketStatus.lastConnectTime) }}</text>
        </view>
        <view class="detail-item">
          <text class="label">重连次数:</text>
          <text class="value">{{ websocketStatus.reconnectCount }}</text>
        </view>
      </view>

      <!-- 消息统计 -->
      <view class="detail-section">
        <text class="section-title">📊 消息统计</text>
        <view class="detail-item">
          <text class="label">总消息数:</text>
          <text class="value">{{ websocketStatus.totalMessages }}</text>
        </view>
        <view class="detail-item">
          <text class="label">支付通知:</text>
          <text class="value">{{ websocketStatus.paymentNotifications }}</text>
        </view>
        <view class="detail-item">
          <text class="label">今日支付:</text>
          <text class="value">{{ todayStats.count }}笔 / ¥{{ todayStats.amount }}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button 
          class="action-btn connect-btn" 
          @click="handleConnect"
          :disabled="websocketStatus.connected || websocketStatus.connecting"
        >
          {{ websocketStatus.connecting ? '连接中...' : '连接' }}
        </button>
        <button 
          class="action-btn disconnect-btn" 
          @click="handleDisconnect"
          :disabled="!websocketStatus.connected"
        >
          断开
        </button>
        <button 
          class="action-btn reconnect-btn" 
          @click="handleReconnect"
        >
          重连
        </button>
      </view>
    </view>

    <!-- 最新支付通知 -->
    <view v-if="latestNotification && showLatestNotification" class="latest-notification">
      <view class="notification-header">
        <text class="notification-title">💰 最新支付</text>
        <text class="notification-close" @click="hideLatestNotification">✕</text>
      </view>
      <view class="notification-content">
        <text class="notification-amount">¥{{ getNotificationAmount(latestNotification) }}</text>
        <text class="notification-time">{{ formatTime(latestNotification.timestamp) }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'WebSocketStatus',
  props: {
    // WebSocket 状态数据
    websocketStatus: {
      type: Object,
      default: () => ({
        connected: false,
        connecting: false,
        reconnectCount: 0,
        lastConnectTime: null,
        totalMessages: 0,
        paymentNotifications: 0
      })
    },
    // 今日统计数据
    todayStats: {
      type: Object,
      default: () => ({
        count: 0,
        amount: '0.00'
      })
    },
    // 最新通知
    latestNotification: {
      type: Object,
      default: null
    },
    // 是否显示详细信息
    defaultShowDetails: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      showDetails: this.defaultShowDetails,
      showLatestNotification: true
    }
  },

  computed: {
    /**
     * 状态文本
     */
    statusText() {
      if (this.websocketStatus.connected) {
        return '已连接'
      } else if (this.websocketStatus.connecting) {
        return '连接中'
      } else {
        return '未连接'
      }
    },

    /**
     * 状态颜色
     */
    statusColor() {
      if (this.websocketStatus.connected) {
        return '#52c41a' // 绿色
      } else if (this.websocketStatus.connecting) {
        return '#1890ff' // 蓝色
      } else {
        return '#ff4d4f' // 红色
      }
    }
  },

  watch: {
    // 监听最新通知变化
    latestNotification(newVal) {
      if (newVal) {
        this.showLatestNotification = true
        // 5秒后自动隐藏
        setTimeout(() => {
          this.showLatestNotification = false
        }, 5000)
      }
    }
  },

  methods: {
    /**
     * 切换详细信息显示
     */
    toggleDetails() {
      this.showDetails = !this.showDetails
    },

    /**
     * 处理连接
     */
    handleConnect() {
      this.$emit('connect')
    },

    /**
     * 处理断开
     */
    handleDisconnect() {
      this.$emit('disconnect')
    },

    /**
     * 处理重连
     */
    handleReconnect() {
      this.$emit('reconnect')
    },

    /**
     * 隐藏最新通知
     */
    hideLatestNotification() {
      this.showLatestNotification = false
    },

    /**
     * 格式化时间
     */
    formatTime(time) {
      if (!time) return '--'
      const date = new Date(time)
      return date.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    /**
     * 获取通知金额
     */
    getNotificationAmount(notification) {
      if (!notification || !notification.data) return '0.00'
      return notification.data.amount || notification.data.money || '0.00'
    }
  }
}
</script>

<style scoped>
.websocket-status {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx;
  overflow: hidden;
}

.status-indicator {
  display: flex;
  align-items: center;
  padding: 24rpx;
  cursor: pointer;
  transition: background-color 0.3s;
}

.status-indicator:hover {
  background-color: #f5f5f5;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  transition: background-color 0.3s;
}

.status-text {
  flex: 1;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.status-icon {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s;
}

.status-details {
  border-top: 1rpx solid #f0f0f0;
  padding: 24rpx;
}

.detail-section {
  margin-bottom: 32rpx;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
}

.label {
  font-size: 24rpx;
  color: #666;
}

.value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  margin-top: 24rpx;
}

.action-btn {
  flex: 1;
  height: 64rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
}

.connect-btn {
  background: #52c41a;
  color: white;
}

.connect-btn:disabled {
  background: #d9d9d9;
  color: #999;
}

.disconnect-btn {
  background: #ff4d4f;
  color: white;
}

.disconnect-btn:disabled {
  background: #d9d9d9;
  color: #999;
}

.reconnect-btn {
  background: #1890ff;
  color: white;
}

.latest-notification {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 24rpx;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(-20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.notification-title {
  font-size: 26rpx;
  font-weight: 600;
}

.notification-close {
  font-size: 32rpx;
  cursor: pointer;
  opacity: 0.8;
}

.notification-close:hover {
  opacity: 1;
}

.notification-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-amount {
  font-size: 36rpx;
  font-weight: 700;
}

.notification-time {
  font-size: 22rpx;
  opacity: 0.8;
}
</style>
