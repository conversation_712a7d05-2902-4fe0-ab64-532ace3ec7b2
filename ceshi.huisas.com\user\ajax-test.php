<?php
// 简化的测试文件，用于验证跨域配置
header('Content-Type: application/json; charset=UTF-8');

// 添加跨域支持
$allowed_origins = [
    'http://localhost:8080',
    'http://127.0.0.1:8080',
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://ceshi.huisas.com',
    'https://ceshi.huisas.com',
    'null' // 本地文件访问
];

$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
if (in_array($origin, $allowed_origins) || $origin === '') {
    if ($origin) {
        header("Access-Control-Allow-Origin: " . $origin);
    } else {
        header("Access-Control-Allow-Origin: *");
    }
    header("Access-Control-Allow-Credentials: true");
    header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, X-Requested-With, Authorization");
}

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

$act = isset($_GET['act']) ? $_GET['act'] : '';

switch($act) {
    case 'test':
        echo json_encode([
            'code' => 0,
            'msg' => 'CORS test successful',
            'server_info' => [
                'origin' => $origin,
                'referer' => isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '',
                'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
                'request_method' => $_SERVER['REQUEST_METHOD']
            ]
        ]);
        break;
    
    case 'getcsrf':
        session_start();
        $csrf_token = md5(mt_rand(0,999).time());
        $_SESSION['csrf_token'] = $csrf_token;
        echo json_encode([
            'code' => 0,
            'msg' => 'success',
            'csrf_token' => $csrf_token
        ]);
        break;
    
    default:
        echo json_encode([
            'code' => -1,
            'msg' => 'Invalid action',
            'available_actions' => ['test', 'getcsrf']
        ]);
        break;
}
?>