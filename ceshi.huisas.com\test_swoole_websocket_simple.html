<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Swoole WebSocket 连接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 10px;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        .status.connecting {
            background: #fff3cd;
            color: #856404;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 5px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .log-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        .log-content {
            background: white;
            border-radius: 8px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            border: 1px solid #e0e0e0;
        }
        .log-item {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 4px;
        }
        .log-item.info {
            background: #e3f2fd;
            color: #1976d2;
        }
        .log-item.success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .log-item.error {
            background: #ffebee;
            color: #c62828;
        }
        .log-item.send {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        .log-item.receive {
            background: #e0f2f1;
            color: #00695c;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">🔧 Swoole WebSocket 连接测试</div>
            <div>
                <span class="status" id="status">未连接</span>
            </div>
        </div>

        <div style="text-align: center; margin-bottom: 30px;">
            <button class="btn" onclick="connect()" id="connectBtn">连接</button>
            <button class="btn" onclick="disconnect()" id="disconnectBtn" disabled>断开</button>
            <button class="btn" onclick="sendPing()" id="pingBtn" disabled>发送Ping</button>
            <button class="btn" onclick="sendAuth()" id="authBtn" disabled>发送认证</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>

        <div class="log-section">
            <div style="font-weight: bold; margin-bottom: 15px;">📝 连接日志</div>
            <div class="log-content" id="logContent"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        function updateStatus(status, message) {
            const statusEl = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const pingBtn = document.getElementById('pingBtn');
            const authBtn = document.getElementById('authBtn');

            statusEl.className = 'status ' + status;
            statusEl.textContent = message;

            switch (status) {
                case 'connected':
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    pingBtn.disabled = false;
                    authBtn.disabled = false;
                    isConnected = true;
                    break;
                case 'disconnected':
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    pingBtn.disabled = true;
                    authBtn.disabled = true;
                    isConnected = false;
                    break;
                case 'connecting':
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = true;
                    pingBtn.disabled = true;
                    authBtn.disabled = true;
                    isConnected = false;
                    break;
            }
        }

        function addLog(type, message) {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            const logItem = document.createElement('div');
            logItem.className = 'log-item ' + type;
            logItem.textContent = `[${timestamp}] ${message}`;
            logContent.appendChild(logItem);
            logContent.scrollTop = logContent.scrollHeight;
        }

        function connect() {
            if (isConnected) {
                addLog('info', '已经连接，无需重复连接');
                return;
            }

            updateStatus('connecting', '连接中...');
            addLog('info', '正在连接 Swoole WebSocket 服务器...');

            // 使用标准的WebSocket连接格式
            const wsUrl = 'ws://ceshi.huisas.com:8080';
            addLog('info', `连接地址: ${wsUrl}`);

            ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                updateStatus('connected', '✅ 已连接');
                addLog('success', 'WebSocket连接成功！');
            };

            ws.onmessage = function(event) {
                addLog('receive', `收到消息: ${event.data}`);
                
                try {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                } catch (e) {
                    addLog('receive', `原始消息: ${event.data}`);
                }
            };

            ws.onclose = function(event) {
                updateStatus('disconnected', '❌ 已断开');

                // 详细的关闭代码说明
                let closeReason = '';
                switch(event.code) {
                    case 1000:
                        closeReason = '正常关闭';
                        break;
                    case 1001:
                        closeReason = '端点离开';
                        break;
                    case 1002:
                        closeReason = '协议错误';
                        break;
                    case 1003:
                        closeReason = '不支持的数据类型';
                        break;
                    case 1006:
                        closeReason = '异常关闭 (可能是网络问题或服务器重启)';
                        break;
                    case 1007:
                        closeReason = '数据格式错误';
                        break;
                    case 1008:
                        closeReason = '违反策略';
                        break;
                    case 1009:
                        closeReason = '消息过大';
                        break;
                    case 1010:
                        closeReason = '扩展协商失败';
                        break;
                    case 1011:
                        closeReason = '服务器内部错误';
                        break;
                    default:
                        closeReason = '未知原因';
                }

                addLog('error', `连接已关闭 (代码: ${event.code}, 原因: ${closeReason})`);
                if (event.reason) {
                    addLog('error', `服务器消息: ${event.reason}`);
                }

                // 如果是异常关闭，提供重连建议
                if (event.code === 1006) {
                    addLog('info', '💡 建议: 检查网络连接或稍后重试');
                }
            };

            ws.onerror = function(error) {
                updateStatus('disconnected', '❌ 连接错误');
                addLog('error', `连接错误: ${error}`);
            };
        }

        function disconnect() {
            if (ws && isConnected) {
                ws.close();
                addLog('info', '主动断开连接');
            }
        }

        function sendMessage(message) {
            if (!isConnected || !ws) {
                addLog('error', '未连接，无法发送消息');
                return;
            }

            const messageStr = JSON.stringify(message);
            ws.send(messageStr);
            addLog('send', `发送消息: ${messageStr}`);
        }

        function sendPing() {
            sendMessage({
                type: 'ping',
                data: {
                    timestamp: Date.now(),
                    message: 'Hello Swoole!'
                }
            });
        }

        function sendAuth() {
            sendMessage({
                type: 'auth',
                data: {
                    merchant_id: '1000',
                    staff_id: '',
                    token: 'test_token_1234567890'
                }
            });
        }

        function handleMessage(data) {
            switch (data.type) {
                case 'welcome':
                    addLog('info', '收到欢迎消息');
                    break;
                case 'auth_result':
                    if (data.data.success) {
                        addLog('success', '认证成功');
                    } else {
                        addLog('error', `认证失败: ${data.data.message}`);
                    }
                    break;
                case 'payment_notification':
                    addLog('success', `收到支付通知: ${data.data.amount}元`);
                    break;
                case 'pong':
                    addLog('info', '收到心跳响应');
                    break;
                case 'heartbeat':
                    addLog('info', '收到服务器心跳消息');
                    // 自动回复心跳
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'heartbeat_response',
                            data: {
                                timestamp: Date.now()
                            }
                        }));
                        addLog('send', '已回复心跳响应');
                    }
                    break;
                default:
                    addLog('info', `收到未知类型消息: ${data.type}`);
                    addLog('info', `消息内容: ${JSON.stringify(data)}`);
            }
        }

        function clearLog() {
            document.getElementById('logContent').innerHTML = '';
            addLog('info', '日志已清空');
        }

        // 页面加载时的初始化
        window.addEventListener('load', function() {
            addLog('info', 'Swoole WebSocket 测试页面已加载');
            addLog('info', '点击"连接"按钮开始测试');
        });
    </script>
</body>
</html>
