<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端WebSocket连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .log { background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; height: 300px; overflow-y: auto; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <h1>🔌 前端WebSocket连接测试</h1>
    
    <div id="status" class="status info">准备连接...</div>
    
    <div>
        <button onclick="connectWebSocket()">连接WebSocket</button>
        <button onclick="disconnectWebSocket()">断开连接</button>
        <button onclick="testVoiceMessage()">测试语音消息</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <h3>📝 连接日志</h3>
    <div id="log" class="log"></div>
    
    <script>
        let ws = null;
        let isConnected = false;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function connectWebSocket() {
            if (isConnected) {
                log('WebSocket已经连接', 'info');
                return;
            }
            
            log('🔌 开始连接WebSocket服务器...', 'info');
            
            // 连接WebSocket服务器
            ws = new WebSocket('ws://ceshi.huisas.com:8080');
            
            ws.onopen = function(event) {
                isConnected = true;
                log('✅ WebSocket连接成功!', 'success');
                updateStatus('已连接', 'success');
                
                // 发送认证消息（如果需要）
                const authMessage = {
                    type: 'auth',
                    data: {
                        merchant_id: '1000',  // 商户ID
                        client_type: 'web_test'
                    }
                };
                
                ws.send(JSON.stringify(authMessage));
                log('📤 发送认证消息: ' + JSON.stringify(authMessage), 'info');
            };
            
            ws.onmessage = function(event) {
                log('📨 收到消息: ' + event.data, 'success');
                
                try {
                    const data = JSON.parse(event.data);
                    
                    // 检查是否是支付通知
                    if (data.merchant_id || data.extra_data) {
                        log('💰 检测到支付通知!', 'success');
                        
                        // 播放语音
                        if (data.extra_data && data.extra_data.voice_text) {
                            playVoice(data.extra_data.voice_text);
                        } else if (data.amount) {
                            playVoice(`收到支付${data.amount}元`);
                        }
                        
                        // 显示通知
                        showNotification(data);
                    }
                } catch (e) {
                    log('❌ 解析消息失败: ' + e.message, 'error');
                }
            };
            
            ws.onclose = function(event) {
                isConnected = false;
                log('❌ WebSocket连接关闭', 'error');
                updateStatus('连接关闭', 'error');
            };
            
            ws.onerror = function(error) {
                log('❌ WebSocket错误: ' + error, 'error');
                updateStatus('连接错误', 'error');
            };
        }
        
        function disconnectWebSocket() {
            if (ws && isConnected) {
                ws.close();
                log('🔌 主动断开WebSocket连接', 'info');
            }
        }
        
        function testVoiceMessage() {
            if (!isConnected) {
                log('❌ 请先连接WebSocket', 'error');
                return;
            }
            
            // 发送测试消息到服务器
            fetch('test_websocket_message.php?action=voice')
                .then(response => response.json())
                .then(data => {
                    log('📤 发送测试语音消息: ' + JSON.stringify(data), 'info');
                })
                .catch(error => {
                    log('❌ 发送测试消息失败: ' + error, 'error');
                });
        }
        
        function playVoice(text) {
            log('🎵 播放语音: ' + text, 'success');
            
            // 使用Web Speech API播放语音
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = 'zh-CN';
                utterance.volume = 0.8;
                utterance.rate = 1.0;
                speechSynthesis.speak(utterance);
            } else {
                log('❌ 浏览器不支持语音播报', 'error');
            }
        }
        
        function showNotification(data) {
            const message = `收到支付通知: ${data.amount || '未知金额'}元`;
            log('📢 ' + message, 'success');
            
            // 显示浏览器通知
            if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('💰 收到新支付', {
                    body: message,
                    icon: '/favicon.ico'
                });
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 请求通知权限
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
        
        // 页面加载时自动连接
        window.onload = function() {
            log('🚀 页面加载完成，准备连接WebSocket...', 'info');
            setTimeout(connectWebSocket, 1000);
        };
    </script>
</body>
</html>
