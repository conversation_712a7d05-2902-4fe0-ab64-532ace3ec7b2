<template>
  <view class="login-container">
    <!-- 顶部占位 - 适配不同设备的状态栏高度 -->
    <view class="status-bar-placeholder" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- Logo section -->
    <view class="logo-section">
      <image class="logo-image" src="/static/logo.png" mode="aspectFit"></image>
      <text class="app-title">商家收款助手</text>
      <text class="app-subtitle">专业的商家收款解决方案</text>
    </view>
    
    <!-- Form section -->
    <view class="form-section">
      <!-- Phone input -->
      <view class="input-item">
        <uni-icons type="phone" size="20" color="#999"></uni-icons>
        <input class="input-field" type="text" v-model="loginForm.username" placeholder="手机号/邮箱/商户ID" />
      </view>
      
      <!-- Password input -->
      <view class="input-item">
        <uni-icons type="locked" size="20" color="#999"></uni-icons>
        <input class="input-field" :type="passwordVisible ? 'text' : 'password'" v-model="loginForm.password" placeholder="密码" />
        <uni-icons :type="passwordVisible ? 'eye-filled' : 'eye'" size="20" color="#bbb" @click="togglePasswordVisibility"></uni-icons>
      </view>
      
      <!-- Captcha container, shown when needed -->
      <view class="captcha-container" v-if="showCaptcha">
        <view id="captcha-box"></view>
      </view>
      
      <!-- Remember password -->
      <view class="remember-section">
        <checkbox class="remember-checkbox" :checked="rememberPassword" @click="rememberPassword = !rememberPassword" />
        <text class="remember-text">记住账号</text>
        <text class="forgot-text" @click="goToForgotPassword">忘记密码?</text>
      </view>
      
      <!-- Login button -->
      <button class="login-button" :loading="loading" :disabled="loading" @click="handleLogin">{{ loading ? '登录中...' : '登录' }}</button>
      
      <!-- Register link -->
      <view class="register-section">
        <text class="register-text">还没有账号?</text>
        <text class="register-link" @click="goToRegister">立即注册</text>
      </view>
    </view>
    
    <!-- Other login methods -->
    <view class="other-login-section">
      <view class="divider">
        <view class="divider-line"></view>
        <text class="divider-text">其他登录方式</text>
        <view class="divider-line"></view>
      </view>

      <view class="login-methods">
        <view class="login-method wechat" @click="handleThirdPartyLogin('wx')">
          <uni-icons type="weixin" size="30" color="#2aab5d"></uni-icons>
        </view>
        <view class="login-method qq" @click="handleThirdPartyLogin('qq')">
          <uni-icons type="notification" size="30" color="#3f8cf7"></uni-icons>
        </view>
        <view class="login-method alipay" @click="handleThirdPartyLogin('alipay')">
          <uni-icons type="smartphone" size="30" color="#fa5151"></uni-icons>
        </view>
      </view>
    </view>

    <!-- Staff login entry -->
    <view class="staff-login-section">
      <view class="staff-divider">
        <view class="staff-divider-line"></view>
      </view>

      <view class="staff-login-card" @click="goToStaffLogin">
        <view class="staff-icon-wrapper">
          <uni-icons type="person-filled" size="24" color="#667eea"></uni-icons>
        </view>
        <view class="staff-content">
          <text class="staff-title">员工登录</text>
          <text class="staff-subtitle">Employee Login</text>
        </view>
        <view class="staff-arrow">
          <uni-icons type="right" size="16" color="#999"></uni-icons>
        </view>
      </view>

      <view class="staff-tips">
        <text class="tips-text">员工用户请使用此入口登录收款系统</text>
      </view>
    </view>
  </view>
</template>

<script>
import { login, getCaptcha, connectLogin } from '@/api/auth.js'

export default {
  data() {
    return {
      // 系统信息
      statusBarHeight: 20, // 默认状态栏高度

      loginForm: {
        username: '',
        password: '',
        type: 1 // 默认使用账号密码登录
      },
      rememberPassword: false,
      passwordVisible: false,
      loading: false,
      captchaObj: null,
      showCaptcha: false,
      captchaResult: {}
    }
  },
  onLoad() {
    // 初始化系统信息
    this.initSystemInfo();

    // 检查登录状态
    this.checkLoginStatus();

    // 尝试从缓存读取用户名
    const savedUsername = uni.getStorageSync('remembered_username');
    if (savedUsername) {
      this.loginForm.username = savedUsername;
      this.rememberPassword = true;
    }

    // 初始化CSRF Token
    this.fetchCSRFToken();
  },
  methods: {
    // 初始化系统信息
    initSystemInfo() {
      const systemInfo = uni.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight || 20;

      console.log('登录页面设备信息:', {
        model: systemInfo.model,
        platform: systemInfo.platform,
        statusBarHeight: this.statusBarHeight,
        safeAreaInsets: systemInfo.safeAreaInsets
      });

      // iPhone 14 Pro Max等设备的特殊处理
      if (systemInfo.model && systemInfo.model.includes('iPhone') && this.statusBarHeight > 40) {
        console.log('检测到iPhone Pro Max系列设备，状态栏高度:', this.statusBarHeight);
      }
    },

    // 检查登录状态
    checkLoginStatus() {
      console.log('检查登录状态...');
      const userToken = uni.getStorageSync('user_token');
      const userUid = uni.getStorageSync('user_uid');

      console.log('存储的Token:', userToken);
      console.log('存储的UID:', userUid);

      if (userToken && userUid) {
        console.log('发现已登录状态，跳转到首页...');
        // 已登录，直接跳转到首页
        uni.switchTab({
          url: '/pages/index/index',
          success: () => {
            console.log('自动跳转到首页成功');
          },
          fail: (err) => {
            console.error('自动跳转到首页失败:', err);
            // 如果switchTab失败，尝试使用reLaunch
            uni.reLaunch({
              url: '/pages/index/index'
            });
          }
        });
      } else {
        console.log('未发现登录状态，停留在登录页面');
      }
    },

    // 获取CSRF Token
    fetchCSRFToken() {
      console.log('正在获取CSRF Token...');
      uni.request({
        url: '/user/ajax.php?act=getcsrf',
        method: 'GET',
        header: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('CSRF Token获取响应:', res);
          if (res.statusCode === 200 && res.data && res.data.code === 0 && res.data.csrf_token) {
            uni.setStorageSync('csrf_token', res.data.csrf_token);
            // 保存session_id用于跨域验证
            if (res.data.session_id) {
              uni.setStorageSync('session_id', res.data.session_id);
            }
            console.log('成功获取CSRF Token:', res.data.csrf_token);
            console.log('成功获取Session ID:', res.data.session_id);
          } else {
            console.error('获取CSRF Token失败:', res.data);
          }
        },
        fail: (err) => {
          console.error('CSRF Token请求错误:', err);
        }
      });
    },

    // 同步获取CSRF Token
    getCSRFTokenSync() {
      return new Promise((resolve, reject) => {
        console.log('同步获取CSRF Token...');
        uni.request({
          url: '/user/ajax.php?act=getcsrf',
          method: 'GET',
          header: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('同步CSRF Token获取响应:', res);
            if (res.statusCode === 200 && res.data && res.data.code === 0 && res.data.csrf_token) {
              uni.setStorageSync('csrf_token', res.data.csrf_token);
              // 保存session_id用于跨域验证
              if (res.data.session_id) {
                uni.setStorageSync('session_id', res.data.session_id);
              }
              console.log('同步成功获取CSRF Token:', res.data.csrf_token);
              console.log('同步成功获取Session ID:', res.data.session_id);
              resolve(res.data.csrf_token);
            } else {
              console.error('同步获取CSRF Token失败:', res.data);
              reject(new Error('获取CSRF Token失败'));
            }
          },
          fail: (err) => {
            console.error('同步CSRF Token请求错误:', err);
            reject(err);
          }
        });
      });
    },

    // 初始化验证码
    initCaptcha() {
      this.showCaptcha = true;
      getCaptcha().then(res => {
        if (res.success === 1) {
          // 根据版本选择不同的验证码实现
          if (res.version === 1) {
            // Geetest V4初始化
            this.initGeetestV4(res.gt);
          } else {
            // 旧版Geetest初始化
            this.initGeetestV3(res.gt, res.challenge, res.new_captcha);
          }
        }
      }).catch(err => {
        uni.showToast({
          title: '验证码加载失败',
          icon: 'none'
        });
      });
    },
    
    // 初始化GeetestV4
    initGeetestV4(gt) {
      // 这里需要引入GeetestV4的SDK
      // 实际实现取决于验证码SDK的集成方式
      console.log('初始化GeetestV4验证码');
    },
    
    // 初始化GeetestV3
    initGeetestV3(gt, challenge, new_captcha) {
      // 这里需要引入Geetest的SDK
      // 实际实现取决于验证码SDK的集成方式
      console.log('初始化GeetestV3验证码');
    },
    
    togglePasswordVisibility() {
      this.passwordVisible = !this.passwordVisible;
    },
    
    // 处理登录
    async handleLogin() {
      try {
        // 表单验证
        if (!this.loginForm.username) {
          uni.showToast({
            title: '请输入账号',
            icon: 'none'
          });
          return;
        }

        if (!this.loginForm.password) {
          uni.showToast({
            title: '请输入密码',
            icon: 'none'
          });
          return;
        }

        this.loading = true;
        console.log('开始登录处理，登录数据:', this.loginForm);

        // 每次登录前都重新获取CSRF Token，确保Session同步
        console.log('登录前重新获取CSRF Token...');
        await this.getCSRFTokenSync();

        const csrf_token = uni.getStorageSync('csrf_token');
        const session_id = uni.getStorageSync('session_id');

        console.log('使用CSRF Token:', csrf_token);
        console.log('使用Session ID:', session_id);

        // 准备登录数据，包含session_id用于跨域验证
        const loginData = {
          type: 1, // 密码登录
          user: this.loginForm.username,
          pass: this.loginForm.password,
          csrf_token: csrf_token || '',
          session_id: session_id || ''
        };

        console.log('准备发送登录请求，数据:', loginData);
        console.log('=== 开始登录请求 ===');

        // 使用直接的uni.request避开可能的问题，添加debug参数
        uni.request({
          url: '/user/ajax.php?act=login&debug=1',
          method: 'POST',
          data: loginData,
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          },
          success: (res) => {
            console.log('=== 登录响应成功 ===');
            console.log('登录响应:', res);
            console.log('响应状态码:', res.statusCode);
            console.log('响应数据:', res.data);
            console.log('数据类型:', typeof res.data);
            console.log('数据code:', res.data?.code);

            // 处理字符串格式的JSON响应
            let responseData = res.data;
            if (typeof responseData === 'string') {
              try {
                responseData = JSON.parse(responseData);
                console.log('解析后的数据:', responseData);
              } catch (e) {
                console.error('JSON解析失败:', e);
              }
            }

            console.log('登录响应检查:', {
              statusCode: res.statusCode,
              responseData: responseData,
              hasCode: responseData ? responseData.hasOwnProperty('code') : false,
              code: responseData ? responseData.code : 'undefined'
            });

            if (res.statusCode === 200 && responseData && responseData.code === 0) {
              uni.showToast({
                title: responseData.msg || '登录成功',
                icon: 'success'
              });

              // 保存登录状态
              if (responseData.token) {
                uni.setStorageSync('user_token', responseData.token);
                console.log('保存用户Token:', responseData.token);
              }
              if (responseData.uid) {
                uni.setStorageSync('user_uid', responseData.uid);
                console.log('保存用户UID:', responseData.uid);
              }

              // 保存用户信息
              const userInfo = {
                uid: responseData.uid,
                name: responseData.name || this.loginForm.username,
                token: responseData.token
              };
              uni.setStorageSync('user_info', userInfo);
              console.log('保存用户信息:', userInfo);

              // 记住账号
              if (this.rememberPassword) {
                uni.setStorageSync('remembered_username', this.loginForm.username);
              } else {
                uni.removeStorageSync('remembered_username');
              }

              // 处理跳转逻辑
              setTimeout(() => {
                // 首页是tabBar页面，使用switchTab跳转
                let targetUrl = '/pages/index/index';

                console.log('登录成功，跳转到:', targetUrl);
                uni.switchTab({
                  url: targetUrl,
                  success: () => {
                    console.log('跳转成功');
                  },
                  fail: (err) => {
                    console.error('跳转失败:', err);
                    // 如果switchTab失败，尝试使用reLaunch
                    uni.reLaunch({
                      url: targetUrl
                    });
                  }
                });
              }, 1500);
            } else {
              // 检查是否是CSRF错误，如果是则重新获取token
              if (responseData && responseData.msg && responseData.msg.includes('CSRF TOKEN ERROR')) {
                console.log('CSRF Token错误，尝试重新获取...');
                this.getCSRFTokenSync().then(() => {
                  uni.showToast({
                    title: 'CSRF Token已更新，请重试登录',
                    icon: 'none'
                  });
                }).catch(() => {
                  uni.showToast({
                    title: 'CSRF Token更新失败',
                    icon: 'none'
                  });
                });
                return;
              }

              // 检查是否需要验证码
              if (responseData && responseData.captcha) {
                this.showCaptcha = true;
                this.initCaptcha();
              }

              uni.showToast({
                title: responseData?.msg || '登录失败',
                icon: 'none'
              });
            }
          },
          fail: (err) => {
            console.error('=== 登录请求失败 ===');
            console.error('登录请求失败:', err);
            uni.showToast({
              title: '网络错误，请稍后重试',
              icon: 'none'
            });
          },
          complete: () => {
            this.loading = false;
          }
        });
      } catch (error) {
        console.error('登录操作异常:', error);
        uni.showToast({
          title: '登录异常，请重试',
          icon: 'none'
        });
        this.loading = false;
      }
    },
    
    // 跳转到注册页面
    goToRegister() {
      uni.navigateTo({
        url: '/pages/register/index'
      });
    },
    
    // 跳转到忘记密码页面
    goToForgotPassword() {
      uni.navigateTo({
        url: '/pages/login/forgot-password'
      });
    },

    // 跳转到员工登录页面
    goToStaffLogin() {
      console.log('跳转到员工登录页面');
      uni.navigateTo({
        url: '/pages/staff-login/index',
        success: () => {
          console.log('成功跳转到员工登录页面');
        },
        fail: (err) => {
          console.error('跳转员工登录页面失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },
    
    // 处理第三方登录
    handleThirdPartyLogin(type) {
      connectLogin(type).then(res => {
        if (res.code === 0 && res.url) {
          // 跳转到授权页面
          uni.navigateTo({
            url: '/pages/login/oauth?url=' + encodeURIComponent(res.url)
          });
        } else {
          uni.showToast({
            title: res.msg || '登录方式不可用',
            icon: 'none'
          });
        }
      }).catch(err => {
        uni.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      });
    }
  }
}
</script>

<style>
page {
  background-color: #ffffff;
}

.login-container {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 状态栏占位 */
.status-bar-placeholder {
  width: 100%;
  flex-shrink: 0;
  /* 最小高度保证，适配iPhone 14 Pro Max等设备 */
  min-height: 20px;
}

/* Logo section */
.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 60rpx; /* 增加顶部间距，让布局更美观 */
  margin-bottom: 60rpx;
}

.logo-image {
  width: 150rpx;
  height: 150rpx;
  border-radius: 20rpx;
}

.app-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-top: 30rpx;
}

.app-subtitle {
  font-size: 28rpx;
  color: #999;
  margin-top: 16rpx;
}

/* Form section */
.form-section {
  margin-top: 40rpx;
}

.input-item {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 26rpx 30rpx;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
}

.input-field {
  flex: 1;
  margin-left: 20rpx;
  font-size: 28rpx;
}

/* Captcha container */
.captcha-container {
  margin-bottom: 30rpx;
}

/* Remember password section */
.remember-section {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.remember-checkbox {
  transform: scale(0.8);
}

.remember-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 5rpx;
}

.forgot-text {
  font-size: 26rpx;
  color: #4c6ef7;
  margin-left: auto;
}

/* Login button */
.login-button {
  width: 100%;
  height: 90rpx;
  background-color: #4c6ef7;
  color: white;
  border-radius: 10rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  border: none;
}

/* Register section */
.register-section {
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
  margin-bottom: 60rpx;
}

.register-text {
  font-size: 26rpx;
  color: #666;
}

.register-link {
  font-size: 26rpx;
  color: #4c6ef7;
  margin-left: 10rpx;
}

/* Other login methods */
.other-login-section {
  margin-top: auto;
  margin-bottom: 60rpx;
}

.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}

.divider-line {
  height: 1px;
  background-color: #eee;
  flex: 1;
}

.divider-text {
  font-size: 26rpx;
  color: #999;
  margin: 0 20rpx;
}

.login-methods {
  display: flex;
  justify-content: center;
}

.login-method {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 30rpx;
}

/* Staff login section */
.staff-login-section {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}

.staff-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.staff-divider-line {
  height: 1px;
  background: linear-gradient(90deg, transparent, #e0e0e0, transparent);
  width: 100%;
}

.staff-login-card {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border: 1px solid #e8eaff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.staff-login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.staff-login-card:active::before {
  opacity: 1;
}

.staff-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.staff-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.staff-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
}

.staff-subtitle {
  font-size: 24rpx;
  color: #666666;
  font-weight: 400;
}

.staff-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
}

.staff-tips {
  margin-top: 20rpx;
  text-align: center;
}

.tips-text {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
}
</style>

