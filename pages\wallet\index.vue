<template>
	<view class="container">
		<custom-navbar
			title="我的钱包"
			:show-back="true"
			:shadow="true"
			@clickLeft="goBack"
		>
			<template #right>
				<image src="/static/mine/qianbao/more.png" mode="aspectFit" class="icon-more" @click="showMore"></image>
			</template>
		</custom-navbar>
		
		<!-- 余额展示 -->
		<view class="balance-card">
			<text class="balance-label">当前余额(元)</text>
			<text class="balance-amount">¥ {{balance}}</text>
			
			<!-- 操作按钮 -->
			<view class="action-buttons">
				<view class="action-button withdraw" @click="navigateToWithdraw">
					<image src="/static/mine/qianbao/tixianka.png" mode="aspectFit" class="button-icon"></image>
					<text class="button-text">提现到银行卡</text>
				</view>
				<view class="action-button alipay" @click="navigateToAlipayWithdraw">
					<image src="/static/home/<USER>" mode="aspectFit" class="button-icon"></image>
					<text class="button-text">提现到支付宝</text>
				</view>
				<view class="action-button manage" @click="navigateToManageCards">
					<image src="/static/mine/qianbao/guanlika.png" mode="aspectFit" class="button-icon"></image>
					<text class="button-text">管理银行卡</text>
				</view>
			</view>
		</view>
		
		<!-- 收入信息 -->
		<view class="income-info">
			<view class="income-item">
				<view class="income-icon-container">
					<image src="/static/mine/qianbao/jinrishouru.png" mode="aspectFit" class="income-icon"></image>
				</view>
				<view class="income-detail">
					<text class="income-label">今日收入</text>
					<text class="income-amount">¥ 382.50</text>
				</view>
			</view>
			
			<view class="income-item">
				<view class="income-icon-container month">
					<image src="/static/mine/qianbao/jinrishouru.png" mode="aspectFit" class="income-icon"></image>
				</view>
				<view class="income-detail">
					<text class="income-label">本月收入</text>
					<text class="income-amount">¥ 12,583.40</text>
				</view>
			</view>
			
			<view class="income-item">
				<view class="income-icon-container pending">
					<image src="/static/mine/qianbao/daijiesuan.png" mode="aspectFit" class="income-icon"></image>
				</view>
				<view class="income-detail">
					<text class="income-label">待结算</text>
					<text class="income-amount">¥ 1,283.30</text>
				</view>
			</view>
		</view>
		
		<!-- 交易明细 -->
		<view class="transaction-history">
			<view class="history-header">
				<text class="history-title">收支明细</text>
				<text class="view-all">全部 ></text>
			</view>
			
			<!-- 今日交易 -->
			<view class="date-group">
				<text class="date-header">今天 (4月14日)</text>
				
				<!-- 交易项 -->
				<view class="transaction-item">
					<view class="transaction-left">
						<view class="transaction-icon income">
							<image src="/static/mine/qianbao/shouru.png" mode="aspectFit"></image>
						</view>
						<view class="transaction-info">
							<text class="transaction-type">收款</text>
							<text class="transaction-time">15:23:45</text>
						</view>
					</view>
					<text class="transaction-amount income">+ ¥ 128.00</text>
				</view>
				
				<view class="transaction-item">
					<view class="transaction-left">
						<view class="transaction-icon income">
							<image src="/static/mine/qianbao/shouru.png" mode="aspectFit"></image>
						</view>
						<view class="transaction-info">
							<text class="transaction-type">收款</text>
							<text class="transaction-time">13:05:22</text>
						</view>
					</view>
					<text class="transaction-amount income">+ ¥ 85.50</text>
				</view>
				
				<view class="transaction-item">
					<view class="transaction-left">
						<view class="transaction-icon withdraw">
							<image src="/static/mine/qianbao/tixian.png" mode="aspectFit"></image>
						</view>
						<view class="transaction-info">
							<text class="transaction-type">提现</text>
							<text class="transaction-time">10:18:36</text>
						</view>
					</view>
					<text class="transaction-amount withdraw">- ¥ 1,000.00</text>
				</view>
			</view>
			
			<!-- 昨日交易 -->
			<view class="date-group">
				<text class="date-header">昨天 (4月13日)</text>
				
				<!-- 交易项 -->
				<view class="transaction-item">
					<view class="transaction-left">
						<view class="transaction-icon income">
							<image src="/static/mine/qianbao/shouru.png" mode="aspectFit"></image>
						</view>
						<view class="transaction-info">
							<text class="transaction-type">收款</text>
							<text class="transaction-time">18:45:12</text>
						</view>
					</view>
					<text class="transaction-amount income">+ ¥ 156.00</text>
				</view>
				
				<view class="transaction-item">
					<view class="transaction-left">
						<view class="transaction-icon income">
							<image src="/static/mine/qianbao/shouru.png" mode="aspectFit"></image>
						</view>
						<view class="transaction-info">
							<text class="transaction-type">收款</text>
							<text class="transaction-time">14:23:50</text>
						</view>
					</view>
					<text class="transaction-amount income">+ ¥ 95.80</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar.vue'

export default {
	components: {
		CustomNavbar
	},
	data() {
		return {
			// 界面状态
			loading: false
		};
	},
	computed: {
		// 获取余额
		balance() {
			return this.$store.getters['wallet/formattedBalance'];
		},
		// 判断是否加载中
		isLoading() {
			return this.$store.getters['wallet/balanceLoading'];
		}
	},
	onShow() {
		this.loadBalance();
	},
	methods: {
		// 加载余额
		async loadBalance() {
			try {
				this.loading = true;
				await this.$store.dispatch('wallet/getBalance');
			} catch (error) {
				uni.showToast({
					title: error.message || '获取余额失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		// 下拉刷新
		async onPullDownRefresh() {
			await this.loadBalance();
			uni.stopPullDownRefresh();
		},
		// 去提现页面
		goToWithdraw() {
			uni.navigateTo({
				url: '/pages/wallet/withdraw'
			});
		},
		// 去收款页面
		goToCollect() {
			uni.navigateTo({
				url: '/pages/pay/collect'
			});
		},
		// 去银行卡管理
		goToBankCards() {
			uni.navigateTo({
				url: '/pages/wallet/bank-cards'
			});
		},
		// 去交易记录
		goToTransactions() {
			uni.navigateTo({
				url: '/pages/wallet/transactions'
			});
		},
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 显示更多选项
		showMore() {
			uni.showActionSheet({
				itemList: ['设置提现密码', '绑定支付宝', '交易明细查询'],
				success: function (res) {
					console.log('选择了第' + (res.tapIndex + 1) + '个选项');
				}
			});
		},
		
		// 添加跳转方法
		navigateToManageCards() {
			uni.navigateTo({
				url: '/pages/wallet/bank-cards'
			});
		},
		
		navigateToWithdraw() {
			uni.navigateTo({
				url: '/pages/wallet/bank-cards?action=withdraw'
			});
		},
		
		navigateToAlipayWithdraw() {
			uni.navigateTo({
				url: '/pages/wallet/bank-cards?action=withdraw&channel=alipay'
			});
		}
	}
}
</script>

<style>
page {
	font-family: 'Segoe UI', sans-serif;
	background-color: #f5f5f5;
}

.container {
	width: 100%;
	min-height: 100vh;
}

.icon-more {
	width: 32rpx;
	height: 32rpx;
}

/* 余额展示 */
.balance-card {
	margin: 32rpx;
	background-color: #5145F7;
	border-radius: 16rpx;
	padding: 32rpx;
	padding-bottom: 100rpx;
	color: white;
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.balance-label {
	font-size: 28rpx;
	opacity: 0.8;
	margin-bottom: 16rpx;
	display: block;
}

.balance-amount {
	font-size: 60rpx;
	font-weight: bold;
	margin-bottom: 32rpx;
	display: block;
}

/* 操作按钮 */
.action-buttons {
	display: flex;
	flex-wrap: wrap;
	margin: 0 -8rpx;
}

.action-button {
	flex: 0 0 calc(33.33% - 16rpx);
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: white;
	height: 80rpx;
	border-radius: 8rpx;
	margin: 8rpx;
}

.button-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 8rpx;
}

.button-text {
	font-size: 24rpx;
	color: #333;
	font-weight: bold;
}

.action-button.alipay {
	background-color: #EFF9FF;
}

/* 收入信息 */
.income-info {
	margin: -60rpx 32rpx 32rpx;
	background-color: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.income-item {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1px solid #f1f1f1;
}

.income-item:last-child {
	border-bottom: none;
}

.income-icon-container {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	background-color: #E9EFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.income-icon-container.month {
	background-color: #E4FFEF;
}

.income-icon-container.pending {
	background-color: #EFE4FF;
}

.income-icon {
	width: 32rpx;
	height: 32rpx;
}

.income-detail {
	flex: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.income-label {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

.income-amount {
	font-size: 32rpx;
	font-weight: bold;
}

/* 交易明细 */
.transaction-history {
	margin: 32rpx;
	background-color: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.history-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 0 32rpx;
	border-bottom: 1px solid #f1f1f1;
}

.history-title {
	font-size: 32rpx;
	font-weight: bold;
}

.view-all {
	font-size: 28rpx;
	color: #999;
}

.date-group {
	margin-top: 24rpx;
}

.date-header {
	font-size: 26rpx;
	color: #999;
	margin-bottom: 16rpx;
}

.transaction-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1px solid #f5f5f5;
}

.transaction-item:last-child {
	border-bottom: none;
}

.transaction-left {
	display: flex;
	align-items: center;
}

.transaction-icon {
	width: 72rpx;
	height: 72rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.transaction-icon.income {
	background-color: #E9EFFF;
}

.transaction-icon.withdraw {
	background-color: #FFE4E4;
}

.transaction-icon image {
	width: 40rpx;
	height: 40rpx;
}

.transaction-info {
	display: flex;
	flex-direction: column;
}

.transaction-type {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.transaction-time {
	font-size: 24rpx;
	color: #999;
}

.transaction-amount {
	font-size: 32rpx;
	font-weight: bold;
}

.transaction-amount.income {
	color: #4CD964;
}

.transaction-amount.withdraw {
	color: #FF3B30;
}
</style> 