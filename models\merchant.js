// models/merchant.js - 商户数据模型

/**
 * 商户模型类
 */
export default class MerchantModel {
  /**
   * 构造函数
   * @param {Object} data - 商户数据
   */
  constructor(data = {}) {
    this.pid = data.pid || '';                  // 商户ID
    this.key = data.key || '';                  // 商户密钥
    this.active = data.active || 0;             // 商户状态
    this.money = data.money || '0.00';          // 商户余额
    this.type = data.type || '';                // 结算方式ID
    this.account = data.account || '';          // 结算账号
    this.username = data.username || '';        // 结算姓名
    this.orders = data.orders || 0;             // 总订单数
    this.orders_today = data.orders_today || 0; // 今日订单数
    this.orders_lastday = data.orders_lastday || 0; // 昨日订单数
    this.orders_today_all = data.orders_today_all || '0.00'; // 今日收入
    this.orders_lastday_all = data.orders_lastday_all || '0.00'; // 昨日收入
  }
  
  /**
   * 判断商户是否激活
   * @returns {Boolean} 是否激活
   */
  isActive() {
    return this.active === 1;
  }
  
  /**
   * 获取商户配置
   * @returns {Object} 商户配置
   */
  getConfig() {
    return {
      merchantId: this.pid,
      merchantKey: this.key
    };
  }
  
  /**
   * 保存商户配置到本地存储
   */
  saveToStorage() {
    uni.setStorageSync('merchantId', this.pid);
    uni.setStorageSync('merchantKey', this.key);
  }
  
  /**
   * 从本地存储读取商户配置
   * @returns {MerchantModel} 商户模型实例
   */
  static loadFromStorage() {
    return new MerchantModel({
      pid: uni.getStorageSync('merchantId') || '',
      key: uni.getStorageSync('merchantKey') || ''
    });
  }
  
  /**
   * 从API响应创建模型实例
   * @param {Object} response - API响应数据
   * @returns {MerchantModel} 商户模型实例
   */
  static fromApiResponse(response) {
    if (!response || response.code !== 1) {
      return new MerchantModel();
    }
    
    return new MerchantModel(response);
  }
} 