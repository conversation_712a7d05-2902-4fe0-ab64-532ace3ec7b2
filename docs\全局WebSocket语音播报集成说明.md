# 🎵 全局WebSocket语音播报集成说明

## 📋 概述

已成功将WebSocket语音播报功能集成到全应用范围，实现了真正的**全局语音播报**体验。无论用户在哪个页面，都能实时收到支付语音提醒。

## 🚀 核心特性

### ✅ 全局覆盖
- **App.vue 启动** - 应用启动时自动连接WebSocket
- **全页面支持** - 无论在首页、扫码页、动态码页、支付页等任何页面都能收到语音播报
- **后台保持** - 应用切换到后台时保持连接，回到前台自动恢复

### ✅ 专业语音播报
- **语音拼接技术** - 使用你现有的专业音频文件库
- **自然播报效果** - "收款一百二十三点四五元"，类似支付宝/微信体验
- **备用TTS方案** - 如果专业语音失败，自动使用系统TTS

### ✅ 智能设置管理
- **统一设置中心** - `/pages/settings/voice.vue` 统一管理所有语音设置
- **实时状态显示** - WebSocket连接状态、今日播报统计
- **一键开关控制** - 支持全局语音播报开关

### ✅ 状态指示器
- **首页状态显示** - 导航栏显示WebSocket连接状态和语音开关状态
- **扫码页状态显示** - 同样的状态指示器
- **实时状态更新** - 连接状态实时反馈

## 🏗️ 架构设计

### 核心组件

1. **App.vue** - 全局服务启动入口
2. **globalWebSocketService.js** - 全局WebSocket服务管理
3. **voiceManager.js** - 语音播报管理器（你现有的）
4. **audioPlayer.js** - 专业语音拼接播放器（你现有的）
5. **pages/settings/voice.vue** - 语音设置页面

### 数据流

```
App.vue 启动
    ↓
globalWebSocketService.start()
    ↓
连接WebSocket + 注册事件监听
    ↓
收到支付通知 → 检查语音设置 → 播放专业语音
    ↓
所有页面都能收到语音播报
```

## 📱 用户体验

### 状态指示器说明

**首页和扫码页导航栏右侧的语音状态按钮：**

- 🎵 **绿色脉冲** - WebSocket已连接，语音播报开启
- 🔇 **灰色** - WebSocket已连接，语音播报关闭
- 🎵 **橙色旋转** - WebSocket连接中
- ❌ **红色** - WebSocket未连接

**点击状态按钮：**
- 连接正常时：切换语音开关
- 连接异常时：提示重新连接

### 语音设置页面

访问路径：`/pages/settings/voice.vue`

**新增功能：**
- 📡 **WebSocket连接状态** - 实时显示连接状态
- 📊 **今日播报统计** - 显示今日播报次数和金额
- 🔊 **全局语音开关** - 一键控制所有页面的语音播报
- 🎵 **音量调节** - 实时调节播报音量

## 🔧 技术实现

### 全局WebSocket服务特性

```javascript
// 自动重连机制
- 连接断开自动重连
- 指数退避重连策略
- 最大重连次数限制

// 生命周期管理
- App启动时自动连接
- App切换后台时暂停
- App回到前台时恢复

// 事件系统
- 使用uni.$emit触发全局事件
- 页面可监听payment-notification事件
```

### 语音播报流程

```javascript
1. 收到WebSocket消息
2. 判断是否为支付通知
3. 检查语音设置是否开启
4. 使用AudioPlayer播放专业语音
5. 失败时使用VoiceManager备用TTS
6. 更新统计数据
7. 触发全局事件
```

## 📋 使用场景

### 场景1：用户在首页
```
用户查看今日收款统计 → 客户扫码支付 → 🎵 "收款八十八点八八元"
```

### 场景2：用户在扫码页
```
用户展示收款码给客户 → 客户扫码支付 → 🎵 "收款一百二十三点四五元"
```

### 场景3：用户在动态码页
```
用户生成动态收款码 → 客户扫码支付 → 🎵 "收款六十六点六六元"
```

### 场景4：用户在支付页
```
用户处理小额支付 → 同时有其他客户支付 → 🎵 "收款九十九点九九元"
```

### 场景5：用户在任意页面
```
用户在查看账单/设置/其他功能 → 有支付进来 → 🎵 及时语音通知
```

## ⚙️ 配置说明

### 语音设置存储

```javascript
// VoiceManager使用的存储键
'voice_enabled'     // 语音开关
'voice_volume'      // 音量设置
'voice_template'    // 播报模板
'voice_large_alert' // 大额提醒
```

### WebSocket配置

```javascript
// 使用现有的websocketService配置
// 连接地址、端口等配置保持不变
// 新增全局生命周期管理
```

## 🧪 测试方法

### 1. 集成测试页面
访问：`/pages/test/integration-test.vue`

**测试功能：**
- WebSocket连接状态检查
- 语音开关测试
- 自定义金额播报测试
- 模拟支付通知测试

### 2. 真实场景测试
1. 打开应用，检查WebSocket状态指示器
2. 在不同页面停留
3. 使用测试工具发送支付通知
4. 验证语音播报是否正常

### 3. 设置页面测试
1. 访问语音设置页面
2. 切换语音开关
3. 调节音量
4. 查看连接状态和统计

## 🔄 升级说明

### 从页面级WebSocket到全局WebSocket

**之前的实现：**
- 只在首页和扫码页集成WebSocket
- 用户在其他页面收不到语音播报
- 每个页面独立管理连接

**现在的实现：**
- App.vue统一管理全局WebSocket
- 所有页面都能收到语音播报
- 统一的状态管理和设置中心

### 兼容性保证

- ✅ 保持现有的VoiceManager接口不变
- ✅ 保持现有的AudioPlayer功能不变
- ✅ 保持现有的语音设置页面布局
- ✅ 新增功能向后兼容

## 🎯 下一步优化建议

1. **音频预加载** - 预加载常用金额的音频文件
2. **播报队列** - 支持多个支付通知的排队播报
3. **自定义铃声** - 支持用户自定义播报铃声
4. **播报历史** - 记录播报历史供用户查看
5. **智能音量** - 根据环境自动调节音量

## 📞 技术支持

如有问题，请检查：
1. WebSocket连接状态
2. 语音设置是否开启
3. 音频文件是否完整
4. 控制台错误日志

---

🎉 **恭喜！** 你的支付系统现在拥有了专业级的全局语音播报功能！
