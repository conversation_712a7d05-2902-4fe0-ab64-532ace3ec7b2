/**
 * WebSocket管理器 - 全局单例
 * 负责管理WebSocket连接的生命周期，确保连接的持久性
 */
class WebSocketManager {
  constructor() {
    this.ws = null
    this.wsUrl = 'ws://ceshi.huisas.com:8080'
    this.isConnected = false
    this.isConnecting = false
    this.isEnabled = false
    
    // 重连配置
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 10
    this.reconnectInterval = 5000
    this.reconnectTimer = null
    
    // 心跳配置
    this.heartbeatTimer = null
    this.heartbeatInterval = 30000
    
    // 用户信息（动态获取）
    this.userInfo = this.getUserInfo()
    
    // 事件监听器
    this.listeners = {
      connect: [],
      disconnect: [],
      message: [],
      error: []
    }
    
    // 消息队列（连接前的消息）
    this.messageQueue = []
    
    console.log('🌐 WebSocket管理器初始化')
  }

  // 获取用户信息
  getUserInfo() {
    // 从uni-app存储中获取真实的商户登录信息
    const userUid = uni.getStorageSync('user_uid')  // 主要的商户ID
    const userToken = uni.getStorageSync('user_token')  // 登录token
    const merchantId = userUid || uni.getStorageSync('merchant_id') || uni.getStorageSync('merchantId')

    console.log('🔍 获取用户信息:', {
      user_uid: userUid,
      merchant_id: merchantId,
      has_token: !!userToken
    })

    return {
      merchant_id: merchantId || '1000',  // 默认值用于测试
      uid: userUid,  // 商户UID
      staff_id: uni.getStorageSync('staff_id') || '',
      token: userToken || 'anonymous'
    }
  }

  // 更新用户信息
  updateUserInfo() {
    this.userInfo = this.getUserInfo()
    console.log('🔄 更新用户信息:', this.userInfo)
  }
  
  // 启用WebSocket
  enable() {
    if (this.isEnabled) return
    
    this.isEnabled = true
    console.log('🔊 启用WebSocket管理器')
    this.connect()
  }
  
  // 禁用WebSocket
  disable() {
    this.isEnabled = false
    console.log('🔇 禁用WebSocket管理器')
    this.disconnect()
  }
  
  // 连接WebSocket
  connect() {
    if (!this.isEnabled) {
      console.log('⚠️ WebSocket管理器未启用')
      return
    }

    if (this.isConnected || this.isConnecting) {
      console.log('⚠️ WebSocket已连接或正在连接中')
      return
    }

    // 连接前更新用户信息
    this.updateUserInfo()

    this.isConnecting = true
    console.log(`🔗 正在连接WebSocket: ${this.wsUrl}`)
    console.log('👤 当前用户信息:', this.userInfo)
    
    this.ws = uni.connectSocket({
      url: this.wsUrl,
      success: () => {
        console.log('🚀 WebSocket连接请求已发送')
      },
      fail: (error) => {
        this.isConnecting = false
        console.error('❌ WebSocket连接失败:', error)
        this.emit('error', error)
        this.scheduleReconnect()
      }
    })
    
    // 连接成功
    this.ws.onOpen(() => {
      this.isConnected = true
      this.isConnecting = false
      this.reconnectAttempts = 0
      
      console.log('✅ WebSocket连接成功！')
      this.emit('connect')
      
      // 发送认证
      this.authenticate()
      
      // 启动心跳
      this.startHeartbeat()
      
      // 发送队列中的消息
      this.flushMessageQueue()
    })
    
    // 接收消息
    this.ws.onMessage((res) => {
      console.log('📨 收到WebSocket消息:', res.data)
      
      try {
        const data = JSON.parse(res.data)
        this.handleMessage(data)
      } catch (error) {
        console.warn('⚠️ 消息解析失败:', error.message)
        this.handleRawMessage(res.data)
      }
    })
    
    // 连接关闭
    this.ws.onClose((res) => {
      this.isConnected = false
      this.isConnecting = false
      
      console.log(`🔌 WebSocket连接关闭: code=${res.code}`)
      this.emit('disconnect', res)
      
      // 清理心跳
      this.stopHeartbeat()
      
      // 如果仍然启用，尝试重连
      if (this.isEnabled) {
        this.scheduleReconnect()
      }
    })
    
    // 连接错误
    this.ws.onError((error) => {
      this.isConnected = false
      this.isConnecting = false
      
      console.error('❌ WebSocket错误:', error)
      this.emit('error', error)
    })
  }
  
  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    this.isConnected = false
    this.isConnecting = false
    
    // 清理定时器
    this.stopHeartbeat()
    this.clearReconnectTimer()
    
    console.log('🔌 WebSocket已断开')
  }
  
  // 发送消息
  sendMessage(message) {
    if (!this.isEnabled) {
      console.warn('⚠️ WebSocket管理器未启用，消息被忽略')
      return false
    }
    
    if (this.isConnected && this.ws) {
      const jsonData = JSON.stringify(message)
      this.ws.send({
        data: jsonData,
        success: () => {
          console.log('📤 消息发送成功:', jsonData)
        },
        fail: (error) => {
          console.error('❌ 消息发送失败:', error)
        }
      })
      return true
    } else {
      // 连接未建立，加入队列
      this.messageQueue.push(message)
      console.log('📝 消息已加入队列，等待连接建立')
      
      // 如果未连接，尝试连接
      if (!this.isConnecting) {
        this.connect()
      }
      
      return false
    }
  }
  
  // 认证
  authenticate() {
    // 尝试多种认证格式
    const authFormats = [
      // 格式1: 标准认证格式（根据后端代码）
      {
        type: 'auth',
        data: {
          merchant_id: this.userInfo.merchant_id,
          staff_id: this.userInfo.staff_id || '',
          token: this.userInfo.token
        }
      },
      // 格式2: 简化认证格式
      {
        type: 'auth',
        merchant_id: this.userInfo.merchant_id,
        uid: this.userInfo.merchant_id,
        timestamp: Date.now()
      }
    ]

    // 先尝试标准格式
    this.sendMessage(authFormats[0])
    console.log('🔐 发送认证信息 (标准格式)')

    // 1秒后尝试简化格式（如果第一个失败）
    setTimeout(() => {
      this.sendMessage(authFormats[1])
      console.log('🔐 发送认证信息 (简化格式)')
    }, 1000)

    // 认证后订阅支付频道
    setTimeout(() => {
      this.subscribePaymentChannel()
    }, 2000)
  }

  // 订阅支付频道
  subscribePaymentChannel() {
    const merchantId = this.userInfo.merchant_id

    if (!merchantId) {
      console.warn('⚠️ 商户ID为空，无法订阅支付频道')
      return
    }

    // 尝试多种订阅格式
    const subscribeFormats = [
      // 格式1: 商户专属频道（根据后端代码）
      {
        type: 'subscribe',
        data: {
          channel: `merchant_${merchantId}_payment`
        }
      },
      // 格式2: 简化订阅格式
      {
        type: 'subscribe',
        channel: `merchant_${merchantId}_payment`
      },
      // 格式3: 通用支付频道（备用）
      {
        type: 'subscribe',
        data: {
          channel: 'payment_channel'
        }
      }
    ]

    console.log(`🎯 为商户 ${merchantId} 订阅支付频道`)

    // 尝试所有格式
    subscribeFormats.forEach((format, index) => {
      setTimeout(() => {
        this.sendMessage(format)
        console.log(`📡 订阅支付频道 (格式${index + 1}): ${format.data?.channel || format.channel}`)
      }, index * 500)
    })
  }
  
  // 处理消息
  handleMessage(data) {
    console.log('📨 WebSocket管理器处理消息:', data)

    switch (data.type) {
      case 'welcome':
        console.log('👋 收到欢迎消息:', data.data?.message)
        break

      case 'auth_result':
        if (data.data?.success) {
          console.log('🔐 认证成功')
        } else {
          console.error('❌ 认证失败:', data.data?.message)
        }
        break

      case 'auth_success':
        console.log('🔐 认证成功')
        break

      case 'auth_failed':
        console.error('❌ 认证失败:', data.message)
        break

      case 'payment_notification':
        console.log('💰 收到支付通知:', data)
        this.emit('message', data)
        break

      case 'payment':
      case 'payment_success':
        console.log('💰 收到支付消息:', data)
        this.emit('message', data)
        break

      case 'pong':
        console.log('💓 收到心跳响应')
        break

      case 'heartbeat':
        console.log('💓 收到心跳消息')
        break

      default:
        console.log('📨 未知消息类型:', data.type, data)
        // 仍然发送给监听器，让页面自己处理
        this.emit('message', data)
    }
  }
  
  // 处理原始消息
  handleRawMessage(rawData) {
    console.log('📨 收到原始消息:', rawData)
    
    // 尝试解析支付通知
    if (rawData.includes('money') || rawData.includes('payment')) {
      const moneyMatch = rawData.match(/money["\s]*[:=]["\s]*([0-9.]+)/i)
      if (moneyMatch) {
        const money = moneyMatch[1]
        console.log('💰 从原始消息提取金额:', money)
        
        this.emit('message', {
          type: 'payment',
          money: money,
          typename: '未知支付方式'
        })
      }
    }
  }
  
  // 启动心跳
  startHeartbeat() {
    this.stopHeartbeat()
    
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.sendMessage({
          type: 'ping',
          timestamp: Date.now()
        })
      }
    }, this.heartbeatInterval)
  }
  
  // 停止心跳
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }
  
  // 安排重连
  scheduleReconnect() {
    if (!this.isEnabled) return
    
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ 重连次数已达上限，停止重连')
      return
    }
    
    this.clearReconnectTimer()
    
    this.reconnectAttempts++
    const delay = this.reconnectInterval * Math.min(this.reconnectAttempts, 5)
    
    console.log(`🔄 ${delay/1000}秒后进行第${this.reconnectAttempts}次重连...`)
    
    this.reconnectTimer = setTimeout(() => {
      this.connect()
    }, delay)
  }
  
  // 清除重连定时器
  clearReconnectTimer() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }
  
  // 发送队列中的消息
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      this.sendMessage(message)
    }
  }
  
  // 添加事件监听器
  on(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback)
    }
  }
  
  // 移除事件监听器
  off(event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback)
      if (index > -1) {
        this.listeners[event].splice(index, 1)
      }
    }
  }
  
  // 触发事件
  emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('事件监听器执行错误:', error)
        }
      })
    }
  }
  
  // 获取状态
  getStatus() {
    return {
      isEnabled: this.isEnabled,
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      queueLength: this.messageQueue.length
    }
  }
}

// 创建全局单例
const websocketManager = new WebSocketManager()

export default websocketManager
