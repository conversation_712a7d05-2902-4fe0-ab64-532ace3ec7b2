<template>
  <view class="container">
    <view class="header">
      <text class="title">🎵 WebSocket语音播报测试</text>
      <text class="subtitle">简化版本 - 专注连接和语音</text>
    </view>
    
    <!-- WebSocket连接状态 -->
    <view class="status-section">
      <view class="status-card" :class="{ 'connected': wsConnected, 'connecting': wsConnecting }">
        <text class="status-icon">{{ wsConnected ? '🟢' : (wsConnecting ? '🟡' : '🔴') }}</text>
        <text class="status-text">{{ getStatusText() }}</text>
        <button @click="toggleConnection" class="status-btn">
          {{ wsConnected ? '断开' : '连接' }}
        </button>
      </view>
    </view>

    <!-- 测试按钮 -->
    <view class="test-section">
      <text class="section-title">🧪 测试功能</text>
      <view class="test-buttons">
        <button @click="testConnection" class="test-btn primary">测试连接</button>
        <button @click="testVoice" class="test-btn success">测试语音</button>
        <button @click="simulatePayment" class="test-btn warning">模拟支付</button>
        <button @click="clearLogs" class="test-btn danger">清空日志</button>
      </view>
    </view>

    <!-- 语音设置 -->
    <view class="voice-section">
      <text class="section-title">🔊 语音设置</text>
      <view class="voice-controls">
        <view class="control-item">
          <text class="control-label">语音开关</text>
          <switch :checked="voiceEnabled" @change="onVoiceToggle" />
        </view>
        <view class="control-item">
          <text class="control-label">音量: {{ voiceVolume }}</text>
          <slider :value="voiceVolume * 100" @change="onVolumeChange" />
        </view>
      </view>
    </view>

    <!-- 日志显示 -->
    <view class="log-section">
      <text class="section-title">📝 连接日志</text>
      <scroll-view class="log-content" scroll-y>
        <text v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          [{{ formatTime(log.timestamp) }}] {{ log.message }}
        </text>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import AudioPlayer from '@/utils/audioPlayer.js'

export default {
  name: 'WebSocketVoiceSimple',
  
  data() {
    return {
      // WebSocket状态
      wsConnected: false,
      wsConnecting: false,
      socketTask: null,
      heartbeatTimer: null,

      // 认证信息
      authInfo: {
        merchant_id: '1000',
        staff_id: '',
        token: 'test_token_1234567890'
      },

      // 语音设置
      voiceEnabled: true,
      voiceVolume: 1.0,

      // 音频播放器
      audioPlayer: null,

      // 日志
      logs: []
    }
  },

  created() {
    // 初始化音频播放器
    this.audioPlayer = new AudioPlayer()
  },

  onUnload() {
    this.disconnectWebSocket()
  },

  methods: {
    // 连接WebSocket
    connectWebSocket() {
      if (this.wsConnected || this.wsConnecting) {
        this.addLog('⚠️ 已经连接或正在连接中', 'warning')
        return
      }

      this.wsConnecting = true
      const wsUrl = 'ws://ceshi.huisas.com:8080'
      this.addLog(`🔗 正在连接: ${wsUrl}`, 'info')

      this.socketTask = uni.connectSocket({
        url: wsUrl,
        success: () => {
          this.addLog('🚀 WebSocket连接请求已发送', 'info')
        },
        fail: (error) => {
          this.wsConnecting = false
          this.addLog(`❌ 连接失败: ${JSON.stringify(error)}`, 'error')
        }
      })

      // 连接成功
      this.socketTask.onOpen(() => {
        this.wsConnected = true
        this.wsConnecting = false
        this.addLog('✅ WebSocket连接成功！', 'success')

        // 启动心跳
        this.startHeartbeat()

        // 发送认证
        this.sendAuth()
      })

      // 接收消息
      this.socketTask.onMessage((res) => {
        // 记录所有收到的原始消息
        this.addLog(`📨 收到原始消息: ${res.data}`, 'info')

        try {
          const data = JSON.parse(res.data)
          this.addLog(`📨 解析后消息: ${JSON.stringify(data)}`, 'info')
          this.handleMessage(data)
        } catch (error) {
          this.addLog(`❌ JSON解析失败: ${error.message}`, 'error')
        }
      })

      // 连接关闭
      this.socketTask.onClose((res) => {
        this.wsConnected = false
        this.wsConnecting = false
        this.addLog(`🔌 连接关闭: ${res.code || 'unknown'}`, 'warning')

        // 清理心跳
        if (this.heartbeatTimer) {
          clearInterval(this.heartbeatTimer)
          this.heartbeatTimer = null
        }
      })

      // 连接错误
      this.socketTask.onError((error) => {
        this.wsConnected = false
        this.wsConnecting = false
        this.addLog(`❌ 连接错误: ${JSON.stringify(error)}`, 'error')
      })
    },

    // 断开连接
    disconnectWebSocket() {
      if (this.socketTask) {
        this.socketTask.close()
        this.socketTask = null
      }
      this.wsConnected = false
      this.wsConnecting = false

      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer)
        this.heartbeatTimer = null
      }

      this.addLog('🔌 主动断开连接', 'info')
    },

    // 发送认证
    sendAuth() {
      const authMessage = {
        type: 'auth',
        data: this.authInfo
      }
      this.sendMessage(authMessage)
      this.addLog('🔐 发送认证信息', 'info')

      // 认证后立即订阅支付频道
      setTimeout(() => {
        this.subscribePaymentChannel()
      }, 100)
    },

    // 订阅支付频道
    subscribePaymentChannel() {
      // 根据认证信息中的商户ID订阅专属频道
      const merchantId = this.authInfo.merchant_id || '1000'
      const channel = `merchant_${merchantId}_payment`

      const subscribeMessage = {
        type: 'subscribe',  // 修复：使用正确的消息类型
        data: {
          channel: channel
        }
      }
      this.sendMessage(subscribeMessage)
      this.addLog(`📡 订阅商户专属频道: ${channel}`, 'info')
    },

    // 发送消息
    sendMessage(message) {
      if (!this.wsConnected || !this.socketTask) {
        this.addLog('❌ 未连接，无法发送消息', 'error')
        return
      }

      const jsonData = JSON.stringify(message)
      this.addLog(`📤 发送消息: ${jsonData}`, 'info')

      this.socketTask.send({
        data: jsonData,
        success: () => {
          // this.addLog(`📤 消息发送成功`, 'info')
        },
        fail: (error) => {
          this.addLog(`❌ 消息发送失败: ${JSON.stringify(error)}`, 'error')
        }
      })
    },

    // 启动心跳
    startHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer)
      }

      this.heartbeatTimer = setInterval(() => {
        if (this.wsConnected && this.socketTask) {
          this.sendMessage({
            type: 'ping',
            data: { timestamp: Date.now() }
          })
        }
      }, 30000)
    },

    // 处理收到的消息
    handleMessage(data) {
      switch (data.type) {
        case 'welcome':
          this.addLog('👋 收到欢迎消息', 'info')
          break
        case 'auth_result':
          if (data.data.success) {
            this.addLog('🔐 认证成功', 'success')
          } else {
            this.addLog(`❌ 认证失败: ${data.data.message}`, 'error')
          }
          break
        case 'payment_notification':
          const paymentData = data.data
          this.addLog(`💰 收到支付通知: ${paymentData.amount}元 (订单: ${paymentData.order_id})`, 'success')

          // 播放语音
          this.playPaymentVoice({
            money: paymentData.amount,
            type: paymentData.extra_data?.pay_type || 'alipay'
          })
          break
        case 'heartbeat':
        case 'HEARTBEAT':
          // 心跳消息，不显示日志
          break
        case 'pong':
        case 'PONG':
          // 心跳响应，不显示日志
          break
        default:
          this.addLog(`📨 收到消息: ${JSON.stringify(data)}`, 'info')
      }
    },

    // 获取状态文本
    getStatusText() {
      if (this.wsConnected) return 'WebSocket已连接'
      if (this.wsConnecting) return 'WebSocket连接中...'
      return 'WebSocket未连接'
    },

    // 切换连接状态
    toggleConnection() {
      if (this.wsConnected) {
        this.disconnectWebSocket()
      } else {
        this.connectWebSocket()
      }
    },

    // 测试连接
    testConnection() {
      this.addLog('🧪 测试WebSocket连接...', 'info')

      if (this.wsConnected) {
        this.addLog('✅ WebSocket已连接，发送测试消息', 'success')
        this.sendMessage({
          type: 'ping',
          data: { timestamp: Date.now() }
        })
      } else {
        this.connectWebSocket()
      }
    },

    // 测试语音
    testVoice() {
      this.addLog('🎵 测试语音播报...', 'info')

      if (!this.voiceEnabled) {
        this.addLog('❌ 语音已关闭', 'warning')
        return
      }

      // 先测试单个音频文件
      this.addLog('🔊 测试单个音频文件...', 'info')
      const testAudio = uni.createInnerAudioContext()
      testAudio.src = '/static/music/_shoukuan.mp3'

      testAudio.onCanplay(() => {
        this.addLog('✅ 音频文件可以播放', 'success')
        testAudio.play()
      })

      testAudio.onError((error) => {
        this.addLog(`❌ 音频文件播放失败: ${JSON.stringify(error)}`, 'error')
        testAudio.destroy()
      })

      testAudio.onEnded(() => {
        this.addLog('🎵 单个音频测试完成，开始完整语音测试', 'info')
        testAudio.destroy()

        // 测试完整语音播报
        const testData = {
          money: '88.88',
          type: 'alipay'
        }

        this.playPaymentVoice(testData)
      })
    },

    // 模拟支付通知
    simulatePayment() {
      this.addLog('🧪 模拟支付通知...', 'info')
      
      const paymentData = {
        trade_no: 'TEST_' + Date.now(),
        money: '99.99',
        type: 'alipay',
        uid: '1001'
      }

      // 模拟收到支付通知
      this.addLog(`💰 模拟支付: ${paymentData.money}元`, 'success')
      this.playPaymentVoice(paymentData)
    },

    // 播放支付语音
    playPaymentVoice(data) {
      if (!this.voiceEnabled) {
        this.addLog('🔇 语音已关闭，跳过播放', 'info')
        return
      }

      this.addLog(`🎵 播放语音: ${data.money}元 (${data.type || 'alipay'})`, 'info')

      try {
        // 异步播放语音
        this.audioPlayer.playPaymentNotification(data.money, data.type || 'alipay')
          .then(() => {
            this.addLog('✅ 语音播放完成', 'success')
          })
          .catch((error) => {
            this.addLog(`❌ 语音播放失败: ${error.message || error}`, 'error')
          })

        this.addLog('🎵 语音播放已启动', 'info')
      } catch (error) {
        this.addLog(`❌ 语音播放启动失败: ${error.message}`, 'error')
      }
    },

    // 语音开关切换
    onVoiceToggle(e) {
      this.voiceEnabled = e.detail.value
      this.addLog(`🔊 语音${this.voiceEnabled ? '开启' : '关闭'}`, 'info')
    },

    // 音量调节
    onVolumeChange(e) {
      this.voiceVolume = e.detail.value / 100
      this.addLog(`🔊 音量调节: ${Math.round(this.voiceVolume * 100)}%`, 'info')
    },

    // 添加日志
    addLog(message, type = 'info') {
      this.logs.unshift({
        message,
        type,
        timestamp: Date.now()
      })
      
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs.pop()
      }
    },

    // 清空日志
    clearLogs() {
      this.logs = []
      this.addLog('📝 日志已清空', 'info')
    },

    // 格式化时间
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString()
    }
  },

  onUnload() {
    // 页面卸载时断开连接
    if (this.wsConnected) {
      paymentWebSocket.disconnect()
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 14px;
  color: #666;
}

.status-section {
  margin-bottom: 20px;
}

.status-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.status-card.connected {
  border-left: 4px solid #34C759;
}

.status-card.connecting {
  border-left: 4px solid #FF9500;
}

.status-icon {
  font-size: 20px;
  margin-right: 12px;
}

.status-text {
  flex: 1;
  font-size: 16px;
  color: #333;
}

.status-btn {
  background: #007AFF;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
}

.test-section, .voice-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15px;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.test-btn {
  flex: 1;
  min-width: 120px;
  padding: 12px;
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
}

.test-btn.primary { background: #007AFF; }
.test-btn.success { background: #34C759; }
.test-btn.warning { background: #FF9500; }
.test-btn.danger { background: #FF3B30; }

.voice-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.control-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.control-label {
  font-size: 16px;
  color: #333;
}

.log-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.log-content {
  background: #000;
  border-radius: 8px;
  padding: 15px;
  height: 300px;
  font-family: 'Courier New', monospace;
}

.log-item {
  display: block;
  font-size: 12px;
  line-height: 1.5;
  margin-bottom: 5px;
}

.log-item.success { color: #34C759; }
.log-item.error { color: #FF3B30; }
.log-item.warning { color: #FF9500; }
.log-item.info { color: #00ff00; }
</style>
