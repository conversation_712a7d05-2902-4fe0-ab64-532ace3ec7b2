<?php
/**
 * 自动创建员工管理相关表
 */

// 设置CORS头部
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

include("../includes/common.php");

$result = [
    'success' => false,
    'created_tables' => [],
    'errors' => [],
    'messages' => []
];

try {
    // 1. 创建员工信息表
    $sql1 = "CREATE TABLE IF NOT EXISTS `pre_staff` (
      `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
      `uid` int(11) unsigned NOT NULL COMMENT '所属商户ID',
      `name` varchar(50) NOT NULL COMMENT '员工姓名',
      `role` varchar(30) NOT NULL DEFAULT '收银员' COMMENT '员工角色',
      `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
      `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
      `avatar_color` varchar(20) DEFAULT 'blue' COMMENT '头像颜色',
      `permissions` text DEFAULT NULL COMMENT '权限配置JSON',
      `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
      `addtime` datetime DEFAULT NULL COMMENT '添加时间',
      `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
      PRIMARY KEY (`id`),
      KEY `uid` (`uid`),
      KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工信息表'";
    
    if ($DB->exec($sql1) !== false) {
        $result['created_tables'][] = 'pre_staff';
        $result['messages'][] = '员工信息表创建成功';
    } else {
        $result['errors'][] = '员工信息表创建失败';
    }
    
    // 2. 创建员工操作日志表
    $sql2 = "CREATE TABLE IF NOT EXISTS `pre_staff_log` (
      `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
      `uid` int(11) unsigned NOT NULL COMMENT '商户ID',
      `staff_id` int(11) unsigned NOT NULL COMMENT '员工ID',
      `action` varchar(50) NOT NULL COMMENT '操作类型',
      `content` text DEFAULT NULL COMMENT '操作内容JSON',
      `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
      `addtime` datetime DEFAULT NULL COMMENT '操作时间',
      PRIMARY KEY (`id`),
      KEY `uid` (`uid`),
      KEY `staff_id` (`staff_id`),
      KEY `action` (`action`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工操作日志表'";
    
    if ($DB->exec($sql2) !== false) {
        $result['created_tables'][] = 'pre_staff_log';
        $result['messages'][] = '员工操作日志表创建成功';
    } else {
        $result['errors'][] = '员工操作日志表创建失败';
    }
    
    // 3. 插入测试数据
    $testData = [
        'uid' => 99009,
        'name' => '测试员工',
        'role' => '收银员',
        'phone' => '13800138000',
        'email' => '<EMAIL>',
        'avatar_color' => 'blue',
        'status' => 1,
        'addtime' => date('Y-m-d H:i:s')
    ];
    
    // 检查是否已有测试数据
    $exists = $DB->getRow("SELECT id FROM pre_staff WHERE uid = 99009 AND name = '测试员工'");
    if (!$exists) {
        $staff_id = $DB->insert('staff', $testData);
        if ($staff_id) {
            $result['messages'][] = '测试员工数据插入成功，ID: ' . $staff_id;
        } else {
            $result['errors'][] = '测试员工数据插入失败';
        }
    } else {
        $result['messages'][] = '测试员工数据已存在';
    }
    
    if (count($result['errors']) == 0) {
        $result['success'] = true;
        $result['messages'][] = '所有表创建完成！';
    }
    
} catch (Exception $e) {
    $result['errors'][] = '创建表时发生错误: ' . $e->getMessage();
}

echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
