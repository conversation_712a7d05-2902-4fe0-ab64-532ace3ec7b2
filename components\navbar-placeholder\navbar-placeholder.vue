<template>
  <view 
    class="navbar-placeholder" 
    :style="placeholderStyle"
  ></view>
</template>

<script>
import { getTotalNavBarHeight } from '@/utils/navbar.js'

export default {
  name: 'NavbarPlaceholder',
  props: {
    // 额外高度
    extraHeight: {
      type: Number,
      default: 0
    },
    // 是否显示占位
    show: {
      type: Boolean,
      default: true
    }
  },
  
  computed: {
    placeholderStyle() {
      if (!this.show) {
        return {
          height: '0px',
          display: 'none'
        };
      }
      
      const totalHeight = getTotalNavBarHeight() + this.extraHeight;
      
      return {
        height: totalHeight + 'px',
        width: '100%',
        flexShrink: 0
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar-placeholder {
  width: 100%;
  flex-shrink: 0;
}
</style>
