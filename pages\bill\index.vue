<template>
	<view class="container">
		<!-- 优雅现代导航栏 -->
		<view class="elegant-navbar">
			<!-- 状态栏占位 -->
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

			<!-- 导航栏主体 -->
			<view class="navbar-main">
				<!-- 左侧标题信息 -->
				<view class="navbar-title-section">
					<text class="page-title">交易记录</text>
					<text class="page-subtitle">{{ currentDateText }} · 共{{ orderList.length }}笔</text>
				</view>

				<!-- 右侧操作 -->
				<view class="navbar-actions">
					<!-- 筛选按钮 -->
					<view class="filter-btn" @click="showFilterModal">
						<view class="filter-icon-wrapper">
							<text class="filter-icon">📊</text>
						</view>
						<text class="filter-text">筛选</text>
					</view>

					<!-- 通知按钮 -->
					<view class="notification-btn" @click="handleNotification">
						<text class="bell-icon">🔔</text>
						<view class="notification-badge" v-if="unreadCount > 0">
							<text class="badge-count">{{ unreadCount > 99 ? '99+' : unreadCount }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 导航栏占位 - 自适应安全区域 -->
		<view class="navbar-placeholder"></view>

		<!-- 筛选弹窗 -->
		<view class="filter-modal" v-if="showFilter" @click="showFilter = false">
			<view class="filter-content" @click.stop>
				<view class="filter-header">
					<text class="filter-title">筛选条件</text>
					<view class="close-btn" @click="showFilter = false">
						<text>✕</text>
					</view>
				</view>
				<view class="filter-options">
					<view class="filter-option" :class="{ active: currentFilter === 'all' }" @click="selectFilter('all')">
						<text>全部</text>
					</view>
					<view class="filter-option" :class="{ active: currentFilter === 'today' }" @click="selectFilter('today')">
						<text>今日</text>
					</view>
					<view class="filter-option" :class="{ active: currentFilter === 'yesterday' }" @click="selectFilter('yesterday')">
						<text>昨日</text>
					</view>
					<view class="filter-option" :class="{ active: currentFilter === 'week' }" @click="selectFilter('week')">
						<text>本周</text>
					</view>
					<view class="filter-option" :class="{ active: currentFilter === 'month' }" @click="selectFilter('month')">
						<text>本月</text>
					</view>
					<view class="filter-option" :class="{ active: currentFilter === 'custom' }" @click="selectCustomDate">
						<text>📅 自定义日期</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 可滚动内容区域 - 支持下拉刷新 -->
		<scroll-view
			class="scroll-content"
			scroll-y="true"
			refresher-enabled="true"
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
			@refresherrestore="onRestore"
			@scrolltolower="loadMore"
		>
			<!-- 收款总览 -->
			<view class="bill-summary">
				<text class="summary-title">收款总览</text>
				<view class="summary-content">
					<view class="summary-item">
						<text class="item-title">收款总额</text>
						<text class="item-value">¥{{ summaryData.successMoney || '0.00' }}</text>
					</view>
					<view class="summary-item">
						<text class="item-title">交易笔数</text>
						<text class="item-value">{{ summaryData.successCount || 0 }}</text>
					</view>
				</view>
			</view>

			<!-- 日期标题 -->
			<view class="date-header">
				<text class="date">{{ currentDateText }}</text>
				<text class="count">共{{ orderList.length }}笔</text>
			</view>

			<!-- 加载状态 -->
			<view v-if="loading && orderList.length === 0" class="loading-state">
				<text>加载中...</text>
			</view>

			<!-- 交易列表 -->
			<view class="transaction-list" v-else>
			<view v-if="orderList.length === 0" class="empty-state">
				<text>暂无交易记录</text>
			</view>
			<view v-for="(order, index) in orderList" :key="order.trade_no" class="transaction-item">
				<view class="transaction-left">
					<view :class="['payment-icon', getPaymentClass(order.typename)]">
						<image :src="getPaymentIcon(order.typename)" mode="aspectFit"></image>
					</view>
					<view class="transaction-info">
						<text class="payment-name">{{ order.typeshowname || order.typename }}</text>
						<text class="payment-time">{{ formatTime(order.addtime) }}</text>
						<text class="order-number">订单号: {{ order.trade_no }}</text>
					</view>
				</view>
				<view class="transaction-right">
					<text :class="['amount', getAmountClass(order.status)]">
						{{ getAmountText(order) }}
					</text>
					<text class="details" @click="viewDetails(order)">详情</text>
				</view>
			</view>
		</view>

		<!-- 加载更多 -->
		<view v-if="hasMore && !loading" class="load-more" @click="loadMore">
			<text>加载更多</text>
		</view>

		<!-- 没有更多数据 -->
		<view v-if="!hasMore && orderList.length > 0" class="no-more">
			<text>没有更多数据了</text>
		</view>

		<!-- 加载更多状态 -->
		<view v-if="loading && orderList.length > 0" class="loading-more">
			<text>加载中...</text>
		</view>
	</scroll-view>


</view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar/custom-navbar.vue'
import NavbarPlaceholder from '@/components/navbar-placeholder/navbar-placeholder.vue'
import {
  getBillOrderList,
  getBillOrderStatistics,
  getPaymentIcon,
  getPaymentClass,
  formatAmount,
  formatTime,
  getDateRange
} from '@/api/order'
import { websocketMixin } from '@/mixins/websocketMixin.js'

export default {
	// 🔧 重新启用WebSocket Mixin，确保账单页面也能播报
	mixins: [websocketMixin],
	components: {
		CustomNavbar,
		NavbarPlaceholder
	},
	data() {
		return {
			// 系统信息
			statusBarHeight: 20,
			unreadCount: 0,
			showFilter: false,
			currentFilter: 'all',
			dateRange: ['', ''],
			loading: false,
			refreshing: false, // 下拉刷新状态
			orderList: [],
			summaryData: {
				successMoney: '0.00',
				successCount: 0,
				totalMoney: '0.00',
				totalCount: 0,
				unpaidMoney: '0.00',
				unpaidCount: 0,
				refundMoney: '0.00',
				refundCount: 0
			},
			pagination: {
				offset: 0,
				limit: 20,
				total: 0
			},
			hasMore: true
		}
	},
	computed: {
		// 导航栏占位高度
		navbarPlaceholderHeight() {
			// 状态栏高度 + 导航栏高度(44px)
			return this.statusBarHeight + 44;
		},

		currentDateText() {
			if (this.currentFilter === 'custom' && this.dateRange[0] && this.dateRange[1]) {
				return `${this.formatDate(this.dateRange[0])} - ${this.formatDate(this.dateRange[1])}`;
			}

			const filterTextMap = {
				'all': '全部',
				'today': '今天',
				'yesterday': '昨天',
				'week': '本周',
				'month': '本月'
			};
			return filterTextMap[this.currentFilter] || '今天';
		}
	},
	async onLoad(options) {
		// 初始化系统信息
		this.initSystemInfo();

		// 检查登录状态
		const isLoggedIn = await this.checkLoginStatus();
		if (!isLoggedIn) {
			return;
		}

		// 设置默认选中全部
		this.changeFilter('all');

		// 如果从自定义日期页面返回
		if (options && options.startDate && options.endDate) {
			this.dateRange = [options.startDate, options.endDate];
			setTimeout(() => {
				this.currentFilter = 'custom';
				this.loadOrderList();
			}, 100);
		}
	},

	onShow() {
		console.log('📋 账单页面显示');
	},

	onHide() {
		console.log('📋 账单页面隐藏');
	},

	onUnload() {
		console.log('📋 账单页面卸载');
	},

	/**
	 * 下拉刷新
	 */
	async onPullDownRefresh() {
		console.log('🔄 下拉刷新交易记录...');

		try {
			// 重置分页
			this.pagination.offset = 0;
			this.orderList = [];
			this.hasMore = true;

			// 重新加载数据
			await this.loadOrderList();

			// 显示刷新成功提示
			uni.showToast({
				title: '刷新成功',
				icon: 'success',
				duration: 1500
			});
		} catch (error) {
			console.error('刷新失败:', error);
			uni.showToast({
				title: '刷新失败',
				icon: 'none',
				duration: 1500
			});
		} finally {
			// 停止下拉刷新动画
			uni.stopPullDownRefresh();
		}
	},
	methods: {

		/**
		 * 下拉刷新
		 */
		onRefresh() {
			console.log('🔄 下拉刷新交易记录...');
			this.refreshing = true;

			// 重置分页
			this.pagination.offset = 0;
			this.orderList = [];
			this.hasMore = true;

			// 重新加载数据
			this.loadOrderList().finally(() => {
				this.refreshing = false;
			});
		},

		/**
		 * 刷新恢复
		 */
		onRestore() {
			this.refreshing = false;
		},

		/**
		 * 初始化系统信息
		 */
		initSystemInfo() {
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight || 20;

			// 特殊处理iPhone 14 Pro Max等设备
			const model = systemInfo.model || '';
			const platform = systemInfo.platform || '';

			console.log('设备信息:', {
				model,
				platform,
				statusBarHeight: this.statusBarHeight,
				safeAreaInsets: systemInfo.safeAreaInsets
			});

			// iPhone 14 Pro Max的状态栏通常是47px
			if (model.includes('iPhone') && this.statusBarHeight > 40) {
				console.log('检测到iPhone Pro Max系列设备，状态栏高度:', this.statusBarHeight);
			}

			// 设置CSS变量，供样式使用
			const app = document.documentElement || document.body;
			if (app && app.style) {
				app.style.setProperty('--status-bar-height', this.statusBarHeight + 'px');
			}
		},

		/**
		 * 显示筛选弹窗
		 */
		showFilterModal() {
			this.showFilter = true;
		},

		/**
		 * 选择筛选条件
		 */
		selectFilter(filter) {
			this.showFilter = false;
			this.changeFilter(filter);
		},

		/**
		 * 选择自定义日期
		 */
		selectCustomDate() {
			this.showFilter = false;
			this.navigateToCustomDatePage();
		},

		/**
		 * 检查登录状态
		 */
		async checkLoginStatus() {
			const userToken = uni.getStorageSync('user_token');
			if (!userToken) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages/login/index'
					});
				}, 1500);
				return false;
			}
			return true;
		},

		/**
		 * 切换筛选条件
		 */
		async changeFilter(filter) {
			this.currentFilter = filter;
			this.pagination.offset = 0;
			this.orderList = [];
			this.hasMore = true;
			await this.loadOrderList();
		},

		/**
		 * 加载订单列表
		 */
		async loadOrderList(isLoadMore = false) {
			if (this.loading) return;

			this.loading = true;

			try {
				const params = {
					offset: this.pagination.offset,
					limit: this.pagination.limit,
					dstatus: 1 // 只查询已支付订单
				};

				// 根据筛选条件设置时间范围
				const dateRange = getDateRange(this.currentFilter, this.dateRange);
				if (dateRange.starttime) {
					params.starttime = dateRange.starttime;
				}
				if (dateRange.endtime) {
					params.endtime = dateRange.endtime;
				}

				console.log('请求订单列表参数:', params);

				const response = await getBillOrderList(params);
				console.log('订单列表响应:', response);

				if (response && response.rows) {
					if (isLoadMore) {
						this.orderList = [...this.orderList, ...response.rows];
					} else {
						this.orderList = response.rows;
					}

					this.pagination.total = response.total || 0;
					this.pagination.offset += this.pagination.limit;

					// 判断是否还有更多数据
					this.hasMore = this.orderList.length < this.pagination.total;

					// 加载统计数据
					await this.loadStatistics();
				} else {
					throw new Error('数据格式错误');
				}
			} catch (error) {
				console.error('加载订单列表失败:', error);
				uni.showToast({
					title: '加载失败，请重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		/**
		 * 加载统计数据
		 */
		async loadStatistics() {
			try {
				const params = {};

				// 根据筛选条件设置时间范围
				const dateRange = getDateRange(this.currentFilter, this.dateRange);
				if (dateRange.starttime) {
					params.starttime = dateRange.starttime;
				}
				if (dateRange.endtime) {
					params.endtime = dateRange.endtime;
				}

				const response = await getBillOrderStatistics(params);
				console.log('统计数据响应:', response);

				if (response && response.code === 0 && response.data) {
					this.summaryData = response.data;
				}
			} catch (error) {
				console.error('加载统计数据失败:', error);
			}
		},

		/**
		 * 加载更多
		 */
		async loadMore() {
			if (!this.hasMore || this.loading) return;
			await this.loadOrderList(true);
		},



		navigateToCustomDatePage() {
			// 导航到自定义日期选择页面
			uni.navigateTo({
				url: '/pages/bill/date-range'
			});
		},

		/**
		 * 格式化日期字符串 YYYY-MM-DD
		 */
		formatDateString(date) {
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			return `${year}-${month}-${day}`;
		},

		/**
		 * 格式化显示日期
		 */
		formatDate(dateStr) {
			if (!dateStr) return '';

			// 将YYYY-MM-DD格式转换为更友好的显示格式
			const parts = dateStr.split('-');
			if (parts.length === 3) {
				return `${parts[1].replace(/^0/, '')}月${parts[2].replace(/^0/, '')}日`;
			}
			return dateStr;
		},

		/**
		 * 格式化时间显示
		 */
		formatTime,

		/**
		 * 获取支付方式样式类
		 */
		getPaymentClass,

		/**
		 * 获取支付方式图标
		 */
		getPaymentIcon,

		/**
		 * 获取金额样式类
		 */
		getAmountClass(status) {
			switch (status) {
				case '1': // 已支付
					return 'amount-success';
				case '0': // 未支付
					return 'amount-pending';
				case '2': // 已退款
					return 'amount-refund';
				case '3': // 已冻结
					return 'amount-frozen';
				default:
					return 'amount-default';
			}
		},

		/**
		 * 获取金额显示文本
		 */
		getAmountText(order) {
			const amount = order.realmoney || order.money || '0.00';
			return formatAmount(amount, order.status);
		},

		/**
		 * 查看订单详情
		 */
		viewDetails(order) {
			// 显示订单详情弹窗
			uni.showModal({
				title: '订单详情',
				content: `订单号: ${order.trade_no}\n商品名称: ${order.name}\n支付金额: ¥${order.realmoney}\n支付时间: ${order.addtime}\n支付方式: ${order.typeshowname}`,
				showCancel: false,
				confirmText: '确定'
			});
		},

		/**
		 * 处理通知按钮点击
		 */
		handleNotification() {
			uni.navigateTo({
				url: '/pages/xiaoxi/index'
			});
		}
	}
}
</script>

<style lang="scss" scoped>
	page {
		font-family: 'Segoe UI', sans-serif;
		background-color: #f5f5f5;
	}

	.container {
		width: 100%;
		position: relative;
		min-height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
	}

	.scroll-content {
		flex: 1;
		height: calc(100vh - var(--navbar-height, 88px));
		background-color: #f5f5f5;
	}

	/* 优雅现代导航栏样式 */
	.elegant-navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		background: linear-gradient(135deg, #5145F7 0%, #6366F1 50%, #8B5CF6 100%);
		box-shadow: 0 4rpx 20rpx rgba(81, 69, 247, 0.15);
	}

	.status-bar {
		width: 100%;
	}

	.navbar-main {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 32rpx;
		min-height: 88rpx;
	}

	.navbar-title-section {
		display: flex;
		flex-direction: column;
		flex: 1;
	}

	.page-title {
		font-size: 36rpx;
		color: #fff;
		font-weight: 600;
		margin-bottom: 4rpx;
		letter-spacing: 0.5rpx;
	}

	.page-subtitle {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8);
		font-weight: 400;
	}

	.navbar-actions {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.filter-btn {
		display: flex;
		align-items: center;
		gap: 12rpx;
		padding: 16rpx 24rpx;
		background: rgba(255, 255, 255, 0.15);
		border-radius: 50rpx;
		transition: all 0.3s ease;
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
	}

	.filter-btn:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 0.25);
	}

	.filter-icon-wrapper {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.2);
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.filter-icon {
		font-size: 24rpx;
	}

	.filter-text {
		font-size: 26rpx;
		color: #fff;
		font-weight: 500;
	}

	.notification-btn {
		position: relative;
		width: 72rpx;
		height: 72rpx;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.15);
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
	}

	.notification-btn:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.25);
	}

	.bell-icon {
		font-size: 32rpx;
	}

	.notification-badge {
		position: absolute;
		top: -8rpx;
		right: -8rpx;
		min-width: 32rpx;
		height: 32rpx;
		background: #FF3B30;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 2rpx solid #fff;
		box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
	}

	.badge-count {
		font-size: 20rpx;
		color: #fff;
		font-weight: bold;
		padding: 0 6rpx;
	}

	/* 导航栏占位 - 自适应各种设备 */
	.navbar-placeholder {
		width: 100%;
		/* 针对iPhone 14 Pro Max等设备的特殊处理 */
		height: calc(var(--status-bar-height, 47px) + 44px);
		/* 使用安全区域，兼容刘海屏和动态岛 */
		height: calc(env(safe-area-inset-top, 47px) + 44px);
		/* 最小高度保证，适配iPhone 14 Pro Max */
		min-height: 91px;
		flex-shrink: 0;
	}

	/* 针对不同平台的适配 */
	/* #ifdef H5 */
	.navbar-placeholder {
		/* H5环境，针对iPhone 14 Pro Max的动态岛 */
		height: calc(env(safe-area-inset-top, 47px) + 44px);
		min-height: 91px;
	}
	/* #endif */

	/* #ifdef MP */
	.navbar-placeholder {
		height: calc(var(--status-bar-height, 47px) + 44px);
		min-height: 91px;
	}
	/* #endif */

	/* #ifdef APP-PLUS */
	.navbar-placeholder {
		height: calc(var(--status-bar-height, 47px) + 44px);
		min-height: 91px;
	}
	/* #endif */

	/* 多端适配 */
	/* #ifdef MP-WEIXIN */
	.elegant-navbar {
		.navbar-main {
			padding-right: 200rpx; /* 为胶囊按钮留出空间 */
		}
	}
	/* #endif */

	/* H5 悬停效果 */
	/* #ifdef H5 */
	.filter-btn:hover {
		background: rgba(255, 255, 255, 0.25);
		transform: translateY(-2rpx);
	}

	.notification-btn:hover {
		background: rgba(255, 255, 255, 0.25);
		transform: translateY(-2rpx);
	}
	/* #endif */

	/* 筛选弹窗 */
	.filter-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		z-index: 2000;
		display: flex;
		align-items: center;
		justify-content: center;
		backdrop-filter: blur(4px);
		-webkit-backdrop-filter: blur(4px);
	}

	.filter-content {
		width: 600rpx;
		background: #fff;
		border-radius: 24rpx;
		overflow: hidden;
		animation: modalSlideIn 0.3s ease;
	}

	@keyframes modalSlideIn {
		from {
			transform: translateY(100rpx);
			opacity: 0;
		}
		to {
			transform: translateY(0);
			opacity: 1;
		}
	}

	.filter-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 32rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.filter-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
	}

	.close-btn {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
		background: #f5f5f5;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		color: #666;
	}

	.filter-options {
		padding: 32rpx;
	}

	.filter-option {
		padding: 24rpx 32rpx;
		margin-bottom: 16rpx;
		background: #f8f9ff;
		border-radius: 16rpx;
		font-size: 28rpx;
		color: #333;
		text-align: center;
		transition: all 0.3s ease;
		border: 2rpx solid transparent;
	}

	.filter-option:last-child {
		margin-bottom: 0;
	}

	.filter-option.active {
		background: linear-gradient(135deg, #5145F7 0%, #6366F1 100%);
		color: #fff;
		border-color: #5145F7;
	}

	.filter-option:active {
		transform: scale(0.98);
	}
	
	/* 收款总览 */
	.bill-summary {
		margin: 24rpx;
		padding: 24rpx;
		background-color: white;
		border-radius: 16rpx;
	}
	
	.summary-title {
		font-size: 30rpx;
		color: #333;
		margin-bottom: 24rpx;
	}
	
	.summary-content {
		display: flex;
		justify-content: space-between;
	}
	
	.summary-item {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}
	
	.item-title {
		font-size: 26rpx;
		color: #999;
	}
	
	.item-value {
		font-size: 40rpx;
		font-weight: bold;
		color: #333;
	}
	
	/* 日期标题 */
	.date-header {
		display: flex;
		justify-content: space-between;
		padding: 24rpx;
		font-size: 28rpx;
		color: #666;
	}
	
	/* 交易列表 */
	.transaction-list {
		margin: 0 24rpx;
	}
	
	.transaction-item {
		display: flex;
		justify-content: space-between;
		padding: 24rpx;
		background-color: white;
		border-radius: 16rpx;
		margin-bottom: 16rpx;
	}
	
	.transaction-left {
		display: flex;
		gap: 16rpx;
	}
	
	.payment-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.payment-icon image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.payment-icon.wechat {
		background-color: #E4FFEF;
	}
	
	.payment-icon.alipay {
		background-color: #E9EFFF;
	}
	
	.payment-icon.cloud {
		background-color: #FFE4E4;
	}

	.payment-icon.default {
		background-color: #F5F5F5;
	}

	.transaction-info {
		display: flex;
		flex-direction: column;
		gap: 4rpx;
	}

	.payment-name {
		font-size: 28rpx;
		color: #333;
	}

	.payment-time {
		font-size: 24rpx;
		color: #999;
	}

	.order-number {
		font-size: 22rpx;
		color: #999;
	}

	.transaction-right {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
	}

	.amount {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 16rpx;
	}

	.amount-success {
		color: #4CD964;
	}

	.amount-pending {
		color: #FF9500;
	}

	.amount-refund {
		color: #FF3B30;
	}

	.amount-frozen {
		color: #8E8E93;
	}

	.amount-default {
		color: #333;
	}

	.details {
		font-size: 24rpx;
		color: #5145F7;
	}

	/* 加载状态 */
	.loading-state {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 200rpx;
		background-color: white;
		border-radius: 16rpx;
		color: #999;
		font-size: 28rpx;
		margin: 0 24rpx;
	}

	/* 空状态 */
	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 200rpx;
		background-color: white;
		border-radius: 16rpx;
		color: #999;
		font-size: 28rpx;
	}

	/* 加载更多 */
	.load-more {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 80rpx;
		margin: 24rpx;
		background-color: white;
		border-radius: 16rpx;
		color: #5145F7;
		font-size: 28rpx;
	}

	/* 没有更多数据 */
	.no-more {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 60rpx;
		color: #999;
		font-size: 24rpx;
	}
</style> 