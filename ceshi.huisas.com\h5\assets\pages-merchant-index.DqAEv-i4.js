import{o as t,e,w as i,a9 as s,z as o,a1 as l,h as a,t as n,j as r,l as d,i as c,n as u,q as h,c as p,S as f,d as g,f as m,k as y,a7 as b,y as k,a as _}from"./index-B1Q521gi.js";import{r as w,_ as x}from"./uni-app.es.DAfa8VxY.js";import{_ as S}from"./custom-navbar.DuzuSmPc.js";import{_ as C}from"./_plugin-vue_export-helper.BCo6x5W8.js";const T=C({name:"UniBadge",emits:["click"],props:{type:{type:String,default:"error"},inverted:{type:Boolean,default:!1},isDot:{type:Boolean,default:!1},maxNum:{type:Number,default:99},absolute:{type:String,default:""},offset:{type:Array,default:()=>[0,0]},text:{type:[String,Number],default:""},size:{type:String,default:"small"},customStyle:{type:Object,default:()=>({})}},data:()=>({}),computed:{width(){return 8*String(this.text).length+12},classNames(){const{inverted:t,type:e,size:i,absolute:s}=this;return[t?"uni-badge--"+e+"-inverted":"","uni-badge--"+e,"uni-badge--"+i,s?"uni-badge--absolute":""].join(" ")},positionStyle(){if(!this.absolute)return{};let t=this.width/2,e=10;this.isDot&&(t=5,e=5);const i=`${-t+this.offset[0]}px`,s=`${-e+this.offset[1]}px`,o={rightTop:{right:i,top:s},rightBottom:{right:i,bottom:s},leftBottom:{left:i,bottom:s},leftTop:{left:i,top:s}},l=o[this.absolute];return l||o.rightTop},dotStyle(){return this.isDot?{width:"10px",minWidth:"0",height:"10px",padding:"0",borderRadius:"10px"}:{}},displayValue(){const{isDot:t,text:e,maxNum:i}=this;return t?"":Number(e)>i?`${i}+`:e}},methods:{onClick(){this.$emit("click")}}},[["render",function(u,h,p,f,g,m){const y=d,b=c;return t(),e(b,{class:"uni-badge--x"},{default:i((()=>[s(u.$slots,"default",{},void 0,!0),p.text?(t(),e(y,{key:0,class:o([m.classNames,"uni-badge"]),style:l([m.positionStyle,p.customStyle,m.dotStyle]),onClick:h[0]||(h[0]=t=>m.onClick())},{default:i((()=>[a(n(m.displayValue),1)])),_:1},8,["class","style"])):r("",!0)])),_:3})}],["__scopeId","data-v-414d462c"]]);const B=C({name:"UniListItem",emits:["click","switchChange"],props:{direction:{type:String,default:"row"},title:{type:String,default:""},note:{type:String,default:""},ellipsis:{type:[Number,String],default:0},disabled:{type:[Boolean,String],default:!1},clickable:{type:Boolean,default:!1},showArrow:{type:[Boolean,String],default:!1},link:{type:[Boolean,String],default:!1},to:{type:String,default:""},showBadge:{type:[Boolean,String],default:!1},showSwitch:{type:[Boolean,String],default:!1},switchChecked:{type:[Boolean,String],default:!1},badgeText:{type:String,default:""},badgeType:{type:String,default:"success"},badgeStyle:{type:Object,default:()=>({})},rightText:{type:String,default:""},thumb:{type:String,default:""},thumbSize:{type:String,default:"base"},showExtraIcon:{type:[Boolean,String],default:!1},extraIcon:{type:Object,default:()=>({type:"",color:"#000000",size:20,customPrefix:""})},border:{type:Boolean,default:!0},customStyle:{type:Object,default:()=>({padding:"",backgroundColor:"#FFFFFF"})},keepScrollPosition:{type:Boolean,default:!1}},watch:{"customStyle.padding":{handler(t){"number"==typeof t&&(t+="");let e=t.split(" ");if(1===e.length){const t=e[0];this.padding={top:t,right:t,bottom:t,left:t}}else if(2===e.length){const[t,i]=e;this.padding={top:t,right:i,bottom:t,left:i}}else if(4===e.length){const[t,i,s,o]=e;this.padding={top:t,right:i,bottom:s,left:o}}},immediate:!0}},data:()=>({isFirstChild:!1,padding:{top:"",right:"",bottom:"",left:""}}),mounted(){this.list=this.getForm(),this.list&&(this.list.firstChildAppend||(this.list.firstChildAppend=!0,this.isFirstChild=!0))},methods:{getForm(t="uniList"){let e=this.$parent,i=e.$options.name;for(;i!==t;){if(e=e.$parent,!e)return!1;i=e.$options.name}return e},onClick(){""===this.to?(this.clickable||this.link)&&this.$emit("click",{data:{}}):this.openPage()},onSwitchChange(t){this.$emit("switchChange",t.detail)},openPage(){-1!==["navigateTo","redirectTo","reLaunch","switchTab"].indexOf(this.link)?this.pageApi(this.link):this.pageApi("navigateTo")},pageApi(t){let e={url:this.to,success:t=>{this.$emit("click",{data:t})},fail:t=>{this.$emit("click",{data:t})}};switch(t){case"navigateTo":default:u(e);break;case"redirectTo":f(e);break;case"reLaunch":p(e);break;case"switchTab":h(e)}}}},[["render",function(u,h,p,f,k,_){const S=c,C=y,B=w(g("uni-icons"),x),F=d,I=w(g("uni-badge"),T),v=b;return t(),e(S,{class:o([{"uni-list-item--disabled":p.disabled},"uni-list-item"]),style:l({"background-color":p.customStyle.backgroundColor}),"hover-class":!p.clickable&&!p.link||p.disabled||p.showSwitch?"":"uni-list-item--hover",onClick:_.onClick},{default:i((()=>[k.isFirstChild?r("",!0):(t(),e(S,{key:0,class:o(["border--left",{"uni-list--border":p.border}])},null,8,["class"])),m(S,{class:o(["uni-list-item__container",{"container--right":p.showArrow||p.link,"flex--direction":"column"===p.direction}]),style:l({paddingTop:k.padding.top,paddingLeft:k.padding.left,paddingRight:k.padding.right,paddingBottom:k.padding.bottom})},{default:i((()=>[s(u.$slots,"header",{},(()=>[m(S,{class:"uni-list-item__header"},{default:i((()=>[p.thumb?(t(),e(S,{key:0,class:"uni-list-item__icon"},{default:i((()=>[m(C,{src:p.thumb,class:o(["uni-list-item__icon-img",["uni-list--"+p.thumbSize]])},null,8,["src","class"])])),_:1})):p.showExtraIcon?(t(),e(S,{key:1,class:"uni-list-item__icon"},{default:i((()=>[m(B,{customPrefix:p.extraIcon.customPrefix,color:p.extraIcon.color,size:p.extraIcon.size,type:p.extraIcon.type},null,8,["customPrefix","color","size","type"])])),_:1})):r("",!0)])),_:1})]),!0),s(u.$slots,"body",{},(()=>[m(S,{class:o(["uni-list-item__content",{"uni-list-item__content--center":p.thumb||p.showExtraIcon||p.showBadge||p.showSwitch}])},{default:i((()=>[p.title?(t(),e(F,{key:0,class:o(["uni-list-item__content-title",[0!==p.ellipsis&&p.ellipsis<=2?"uni-ellipsis-"+p.ellipsis:""]])},{default:i((()=>[a(n(p.title),1)])),_:1},8,["class"])):r("",!0),p.note?(t(),e(F,{key:1,class:"uni-list-item__content-note"},{default:i((()=>[a(n(p.note),1)])),_:1})):r("",!0)])),_:1},8,["class"])]),!0),s(u.$slots,"footer",{},(()=>[p.rightText||p.showBadge||p.showSwitch?(t(),e(S,{key:0,class:o(["uni-list-item__extra",{"flex--justify":"column"===p.direction}])},{default:i((()=>[p.rightText?(t(),e(F,{key:0,class:"uni-list-item__extra-text"},{default:i((()=>[a(n(p.rightText),1)])),_:1})):r("",!0),p.showBadge?(t(),e(I,{key:1,type:p.badgeType,text:p.badgeText,"custom-style":p.badgeStyle},null,8,["type","text","custom-style"])):r("",!0),p.showSwitch?(t(),e(v,{key:2,disabled:p.disabled,checked:p.switchChecked,onChange:_.onSwitchChange},null,8,["disabled","checked","onChange"])):r("",!0)])),_:1},8,["class"])):r("",!0)]),!0)])),_:3},8,["class","style"]),p.showArrow||p.link?(t(),e(B,{key:1,size:16,class:"uni-icon-wrapper",color:"#bbb",type:"arrowright"})):r("",!0)])),_:3},8,["class","style","hover-class","onClick"])}],["__scopeId","data-v-482be9c5"]]);const F=C({name:"uniList","mp-weixin":{options:{multipleSlots:!1}},props:{stackFromEnd:{type:Boolean,default:!1},enableBackToTop:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},border:{type:Boolean,default:!0},renderReverse:{type:Boolean,default:!1}},created(){this.firstChildAppend=!1},methods:{loadMore(t){this.$emit("scrolltolower")},scroll(t){this.$emit("scroll",t)}}},[["render",function(o,l,a,n,d,u){const h=c;return t(),e(h,{class:"uni-list uni-border-top-bottom"},{default:i((()=>[a.border?(t(),e(h,{key:0,class:"uni-list--border-top"})):r("",!0),s(o.$slots,"default",{},void 0,!0),a.border?(t(),e(h,{key:1,class:"uni-list--border-bottom"})):r("",!0)])),_:3})}],["__scopeId","data-v-c1d7c358"]]);const I=C({components:{uniList:F,uniListItem:B,uniIcons:x,CustomNavbar:CustomNavbar},data:()=>({merchantInfo:{name:"优选咖啡店",category:"餐饮/咖啡厅",phone:"13812345678",hours:"08:00-22:00",address:"广东省深圳市南山区科技园南区5栋101"}}),methods:{goBack(){k()},editInfo(){_({title:"编辑商家信息",icon:"none"})},editField(t){_({title:`编辑${t}`,icon:"none"})}}},[["render",function(s,o,l,n,r,d){const u=w(g("uni-icons"),x),h=c,p=w(g("custom-navbar"),S),f=w(g("uni-list-item"),B),y=w(g("uni-list"),F);return t(),e(h,{class:"merchant-info"},{default:i((()=>[m(p,{title:"商家信息","show-back":!0,shadow:!0,onClickLeft:d.goBack},{right:i((()=>[m(h,{onClick:d.editInfo,style:{padding:"0 16rpx"}},{default:i((()=>[m(u,{type:"compose",size:"22",color:"#FFFFFF"})])),_:1},8,["onClick"])])),_:1},8,["onClickLeft"]),m(h,{class:"info-section"},{default:i((()=>[m(h,{class:"section-title"},{default:i((()=>[a("基本信息")])),_:1}),m(y,null,{default:i((()=>[m(f,{title:"商家名称",rightText:r.merchantInfo.name,clickable:"",showArrow:"",onClick:o[0]||(o[0]=t=>d.editField("name"))},null,8,["rightText"]),m(f,{title:"经营类目",rightText:r.merchantInfo.category,clickable:"",showArrow:"",onClick:o[1]||(o[1]=t=>d.editField("category"))},null,8,["rightText"]),m(f,{title:"联系电话",rightText:r.merchantInfo.phone,clickable:"",showArrow:"",onClick:o[2]||(o[2]=t=>d.editField("phone"))},null,8,["rightText"]),m(f,{title:"营业时间",rightText:r.merchantInfo.hours,clickable:"",showArrow:"",onClick:o[3]||(o[3]=t=>d.editField("hours"))},null,8,["rightText"])])),_:1})])),_:1}),m(h,{class:"info-section"},{default:i((()=>[m(h,{class:"section-title"},{default:i((()=>[a("位置信息")])),_:1}),m(y,null,{default:i((()=>[m(f,{title:"地址",rightText:r.merchantInfo.address,clickable:"",showArrow:"",onClick:o[4]||(o[4]=t=>d.editField("address"))},null,8,["rightText"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-e3d654cc"]]);export{I as default};
