// mixins/page-auth.js - 页面登录状态检测混入
import { checkLoginStatus, clearLoginInfo, redirectToLogin, getUserInfo } from '@/utils/auth'

/**
 * 页面登录状态检测混入
 * 为页面提供统一的登录状态管理功能
 */
export default {
  data() {
    return {
      // 页面加载状态
      pageLoading: false,
      // 用户信息
      userInfo: null,
      // 登录状态
      isLogin: false
    }
  },
  
  methods: {
    /**
     * 检查登录状态并处理
     * @param {boolean} autoRedirect - 未登录时是否自动跳转到登录页，默认true
     * @returns {Promise<boolean>} 是否已登录
     */
    async checkAndHandleLogin(autoRedirect = true) {
      try {
        console.log('🔍 检查登录状态...');
        
        const isLogin = await checkLoginStatus();
        this.isLogin = isLogin;
        
        if (!isLogin && autoRedirect) {
          console.log('❌ 用户未登录，跳转到登录页');
          
          // 获取当前页面路径作为回调地址
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          const currentRoute = currentPage ? currentPage.route : '';
          
          redirectToLogin(currentRoute);
          return false;
        }
        
        return isLogin;
      } catch (error) {
        console.error('❌ 登录状态检测失败:', error);
        
        if (autoRedirect) {
          redirectToLogin();
        }
        
        return false;
      }
    },
    
    /**
     * 获取用户信息
     * @returns {Promise<Object>} 用户信息
     */
    async loadUserInfo() {
      try {
        console.log('📊 获取用户信息...');
        
        const userInfo = await getUserInfo();
        this.userInfo = userInfo;
        
        console.log('✅ 用户信息获取成功:', userInfo);
        return userInfo;
      } catch (error) {
        console.error('❌ 获取用户信息失败:', error);
        
        // 如果是登录状态失效，跳转到登录页
        if (error && error.code === -3) {
          console.log('❌ 登录状态失效，跳转到登录页');
          redirectToLogin();
        } else {
          uni.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
        }
        
        throw error;
      }
    },
    
    /**
     * 页面初始化 - 检查登录状态并加载数据
     * @param {Function} loadDataCallback - 数据加载回调函数
     * @returns {Promise<boolean>} 是否初始化成功
     */
    async initPageWithAuth(loadDataCallback) {
      try {
        this.pageLoading = true;
        
        console.log('🚀 页面初始化开始...');
        
        // 1. 检查登录状态
        const isLogin = await this.checkAndHandleLogin();
        if (!isLogin) {
          return false; // 未登录会自动跳转到登录页
        }
        
        // 2. 获取用户信息
        await this.loadUserInfo();
        
        // 3. 执行页面特定的数据加载
        if (typeof loadDataCallback === 'function') {
          await loadDataCallback();
        }
        
        console.log('✅ 页面初始化完成');
        return true;
        
      } catch (error) {
        console.error('❌ 页面初始化失败:', error);
        
        uni.showToast({
          title: '页面加载失败',
          icon: 'none'
        });
        
        return false;
      } finally {
        this.pageLoading = false;
      }
    },
    
    /**
     * 页面显示时检查登录状态
     * 用于onShow生命周期
     */
    async checkLoginOnShow() {
      try {
        const isLogin = await this.checkAndHandleLogin(false); // 不自动跳转
        
        if (!isLogin) {
          console.log('❌ 页面显示时发现用户未登录，跳转到登录页');
          uni.reLaunch({
            url: '/pages/login/index'
          });
        }
      } catch (error) {
        console.error('❌ 页面显示时检查登录状态失败:', error);
      }
    },
    
    /**
     * 退出登录
     */
    async logout() {
      try {
        uni.showModal({
          title: '确认退出',
          content: '确定要退出登录吗？',
          success: (res) => {
            if (res.confirm) {
              console.log('🚪 用户确认退出登录');
              clearLoginInfo();
              redirectToLogin();
            }
          }
        });
      } catch (error) {
        console.error('❌ 退出登录失败:', error);
      }
    }
  }
}
