import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

export default defineConfig({
  plugins: [uni()],
  server: {
    host: '0.0.0.0',
    port: 5173,
    proxy: {
      '/api.php': {
        target: 'http://ceshi.huisas.com',
        changeOrigin: true,
        secure: false
      },
      '/user': {
        target: 'http://ceshi.huisas.com',
        changeOrigin: true,
        secure: false
      },
      '/paypage': {
        target: 'http://ceshi.huisas.com',
        changeOrigin: true,
        secure: false
      }
    }
  },

  define: {
    __UNI_FEATURE_PROMISE__: false
  }
})
