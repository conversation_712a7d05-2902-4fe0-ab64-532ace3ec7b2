<?php
/**
 * 员工管理API接口
 * 文件位置: epay_release_99009/user/staff.php
 */

// 设置CORS头部
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

include("../includes/common.php");

// 检查登录状态和获取商户ID
if($islogin2 != 1) {
    // 如果没有登录，尝试从POST参数获取uid（用于员工管理）
    $posted_uid = intval($_POST['uid'] ?? 0);
    if($posted_uid > 0) {
        // 使用前端传递的商户ID
        $uid = $posted_uid;
        $islogin2 = 1; // 临时设置登录状态用于员工管理
    } else {
        // 测试版本：强制设置登录状态和用户ID
        $islogin2 = 1;
        $uid = 1000; // 修改为实际存在的商户ID
    }
}

$act = isset($_GET['act']) ? $_GET['act'] : '';

switch($act) {
    
    // 获取员工列表 - 借鉴staff_test.php的成功实现
    case 'getStaffList':
        try {
            $sql = "SELECT * FROM {$dbconfig['dbqz']}_staff WHERE uid = :uid AND status = 1 ORDER BY id ASC";
            $staffList = $DB->getAll($sql, [':uid' => $uid]);

            $result = [];
            foreach($staffList as $staff) {
                $result[] = [
                    'id' => intval($staff['id']),
                    'name' => $staff['name'],
                    'role' => $staff['role'],
                    'color' => $staff['avatar_color'],
                    'phone' => $staff['phone'],
                    'email' => $staff['email'],
                    'addtime' => $staff['addtime']
                ];
            }

            exit(json_encode(['code' => 0, 'data' => $result, 'debug' => ['uid' => $uid, 'count' => count($staffList)]]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '获取员工列表失败: ' . $e->getMessage()]));
        }
        break;
    
    // 添加员工 - 借鉴staff_test.php的成功实现
    case 'addStaff':
        try {
            // 调试信息：记录接收到的数据
            $debug_info = [
                'uid' => $uid,
                'post_data' => $_POST,
                'islogin2' => $islogin2
            ];

            $name = trim($_POST['name']);
            $role = trim($_POST['role']);
            $phone = trim($_POST['phone']);
            $email = trim($_POST['email']);
            $avatar_color = trim($_POST['avatar_color']) ?: 'blue';

            if(empty($name)) {
                exit(json_encode(['code' => -1, 'msg' => '员工姓名不能为空', 'debug' => $debug_info]));
            }

            // 检查同名员工
            $exists = $DB->getRow("SELECT id FROM {$dbconfig['dbqz']}_staff WHERE uid = :uid AND name = :name",
                [':uid' => $uid, ':name' => $name]);
            if($exists) {
                exit(json_encode(['code' => -1, 'msg' => '员工姓名已存在']));
            }

            $data = [
                'uid' => $uid,
                'name' => $name,
                'role' => $role ?: '收银员',
                'phone' => $phone,
                'email' => $email,
                'avatar_color' => $avatar_color,
                'status' => 1,
                'addtime' => date('Y-m-d H:i:s')
            ];

            $staff_id = $DB->insert("{$dbconfig['dbqz']}_staff", $data);
            if($staff_id) {
                // 记录操作日志（可选，如果表不存在会跳过）
                try {
                    $DB->insert("{$dbconfig['dbqz']}_staff_log", [
                        'uid' => $uid,
                        'staff_id' => $staff_id,
                        'action' => 'add_staff',
                        'content' => json_encode(['name' => $name, 'role' => $role]),
                        'ip' => $_SERVER['REMOTE_ADDR'],
                        'addtime' => date('Y-m-d H:i:s')
                    ]);
                } catch (Exception $logError) {
                    // 日志记录失败不影响主流程
                }

                exit(json_encode(['code' => 0, 'msg' => '添加员工成功', 'staff_id' => $staff_id, 'debug' => array_merge($debug_info, ['insert_data' => $data])]));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '添加员工失败，数据库插入失败', 'debug' => array_merge($debug_info, ['data' => $data, 'error' => $DB->error()])]));
            }
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '添加员工失败: ' . $e->getMessage()]));
        }
        break;
    
    // 编辑员工 - 支持部分更新
    case 'editStaff':
        try {
            $staff_id = intval($_POST['staff_id']);
            $name = trim($_POST['name']);
            $role = trim($_POST['role']);
            $account = trim($_POST['account']);
            $password = trim($_POST['password']);
            $phone = trim($_POST['phone']);
            $email = trim($_POST['email']);
            $avatar_color = trim($_POST['avatar_color']);

            if(empty($staff_id)) {
                exit(json_encode(['code' => -1, 'msg' => '员工ID不能为空']));
            }

            if(empty($name)) {
                exit(json_encode(['code' => -1, 'msg' => '员工姓名不能为空']));
            }

            // 检查员工是否存在且属于当前商户
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND uid = :uid",
                [':id' => $staff_id, ':uid' => $uid]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 检查同名员工（排除自己）
            $exists = $DB->getRow("SELECT id FROM {$dbconfig['dbqz']}_staff WHERE uid = :uid AND name = :name AND id != :id",
                [':uid' => $uid, ':name' => $name, ':id' => $staff_id]);
            if($exists) {
                exit(json_encode(['code' => -1, 'msg' => '员工姓名已存在']));
            }

            // 构建更新数据 - 只更新提供的字段
            $data = [
                'name' => $name,
                'role' => $role ?: '收银员',
                'phone' => $phone,
                'email' => $email,
                'avatar_color' => $avatar_color ?: 'blue',
                'updatetime' => date('Y-m-d H:i:s')
            ];

            // 如果提供了账号，检查是否重复并更新
            if(!empty($account)) {
                $accountExists = $DB->getRow("SELECT id FROM {$dbconfig['dbqz']}_staff WHERE uid = :uid AND account = :account AND id != :id",
                    [':uid' => $uid, ':account' => $account, ':id' => $staff_id]);
                if($accountExists) {
                    exit(json_encode(['code' => -1, 'msg' => '登录账号已存在']));
                }
                $data['account'] = $account;
            }

            // 如果提供了密码，加密后更新
            if(!empty($password)) {
                $data['password'] = md5($password);
            }

            $result = $DB->update("{$dbconfig['dbqz']}_staff", $data, ['id' => $staff_id, 'uid' => $uid]);
            if($result !== false) {
                // 记录操作日志
                try {
                    $DB->insert("{$dbconfig['dbqz']}_staff_log", [
                        'uid' => $uid,
                        'staff_id' => $staff_id,
                        'action' => 'edit_staff',
                        'content' => json_encode(['old' => $staff, 'new' => $data]),
                        'ip' => $_SERVER['REMOTE_ADDR'],
                        'addtime' => date('Y-m-d H:i:s')
                    ]);
                } catch (Exception $logError) {
                    // 日志记录失败不影响主流程
                }

                exit(json_encode(['code' => 0, 'msg' => '编辑员工成功']));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '编辑员工失败']));
            }
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '编辑员工失败: ' . $e->getMessage()]));
        }
        break;
    
    // 删除员工
    case 'deleteStaff':
        try {
            $staff_id = intval($_POST['staff_id']);
            
            // 检查员工是否存在且属于当前商户
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND uid = :uid",
                [':id' => $staff_id, ':uid' => $uid]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 检查是否有绑定的收款码
            $qrcode_count = $DB->getColumn("SELECT COUNT(*) FROM {$dbconfig['dbqz']}_qrcode_config WHERE staff_id = :staff_id",
                [':staff_id' => $staff_id]);
            if($qrcode_count > 0) {
                exit(json_encode(['code' => -1, 'msg' => '该员工已绑定收款码，无法删除']));
            }

            // 软删除（设置状态为0）
            $result = $DB->update("{$dbconfig['dbqz']}_staff", ['status' => 0, 'updatetime' => date('Y-m-d H:i:s')],
                ['id' => $staff_id, 'uid' => $uid]);

            if($result !== false) {
                // 记录操作日志
                try {
                    $DB->insert("{$dbconfig['dbqz']}_staff_log", [
                        'uid' => $uid,
                        'staff_id' => $staff_id,
                        'action' => 'delete_staff',
                        'content' => json_encode(['staff' => $staff]),
                        'ip' => $_SERVER['REMOTE_ADDR'],
                        'addtime' => date('Y-m-d H:i:s')
                    ]);
                } catch (Exception $logError) {
                    // 日志记录失败不影响主流程
                }
                
                exit(json_encode(['code' => 0, 'msg' => '删除员工成功']));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '删除员工失败']));
            }
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '删除员工失败: ' . $e->getMessage()]));
        }
        break;
    
    // 保存收款码配置（包含员工绑定）
    case 'saveQRConfig':
        try {
            // 调试信息：记录接收到的数据
            $debug_info = [
                'uid' => $uid,
                'post_data' => $_POST,
                'islogin2' => $islogin2
            ];

            $staff_id = intval($_POST['staff_id']);
            $name = trim($_POST['name']);
            $qr_style = trim($_POST['qr_style']) ?: 'native';
            $amount = floatval($_POST['amount']);
            $description = trim($_POST['description']);

            if(empty($name)) {
                exit(json_encode(['code' => -1, 'msg' => '收款码名称不能为空', 'debug' => $debug_info]));
            }
            
            // 如果指定了员工，检查员工是否存在
            if($staff_id > 0) {
                $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND uid = :uid AND status = 1",
                    [':id' => $staff_id, ':uid' => $uid]);
                if(!$staff) {
                    exit(json_encode(['code' => -1, 'msg' => '指定的员工不存在']));
                }
            }
            
            // 生成收款码URL
            // 生成加密的商户ID
            $merchant = authcode($uid, 'ENCODE', SYS_KEY);
            $qr_url = "http://ceshi.huisas.com/paypage/?merchant=" . urlencode($merchant);
            if($amount > 0) {
                $qr_url .= "&amount={$amount}";
            }
            if($staff_id > 0) {
                $qr_url .= "&staff_id={$staff_id}";
            }
            
            $data = [
                'uid' => $uid,
                'staff_id' => $staff_id > 0 ? $staff_id : null,
                'name' => $name,
                'qr_style' => $qr_style,
                'amount' => $amount > 0 ? $amount : null,
                'description' => $description,
                'qr_url' => $qr_url,
                'status' => 1,
                'addtime' => date('Y-m-d H:i:s')
            ];
            
            // 使用直接SQL插入，避免前缀重复问题
            $sql = "INSERT INTO `{$dbconfig['dbqz']}_qrcode_config`
                    (`uid`, `staff_id`, `name`, `qr_style`, `amount`, `description`, `qr_url`, `status`, `addtime`)
                    VALUES (:uid, :staff_id, :name, :qr_style, :amount, :description, :qr_url, :status, :addtime)";

            $params = [
                ':uid' => $data['uid'],
                ':staff_id' => $data['staff_id'],
                ':name' => $data['name'],
                ':qr_style' => $data['qr_style'],
                ':amount' => $data['amount'],
                ':description' => $data['description'],
                ':qr_url' => $data['qr_url'],
                ':status' => $data['status'],
                ':addtime' => $data['addtime']
            ];

            $result = $DB->exec($sql, $params);
            if($result) {
                $config_id = $DB->lastInsertId();
                exit(json_encode(['code' => 0, 'msg' => '保存成功', 'config_id' => $config_id, 'qr_url' => $qr_url, 'debug' => array_merge($debug_info, ['insert_data' => $data, 'sql' => $sql, 'params' => $params])]));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '保存失败', 'debug' => array_merge($debug_info, ['data' => $data, 'sql' => $sql, 'params' => $params, 'error' => $DB->error()])]));
            }
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '保存失败: ' . $e->getMessage(), 'debug' => $debug_info]));
        }
        break;

    // 获取收款码配置列表
    case 'getQRConfigList':
        try {
            // 调试信息
            $debug_info = [
                'uid' => $uid,
                'islogin2' => $islogin2
            ];

            // 先查询所有数据看看实际情况
            $allData = $DB->getAll("SELECT * FROM {$dbconfig['dbqz']}_qrcode_config ORDER BY id DESC LIMIT 5");
            $debug_info['all_data'] = $allData;

            // 再查询指定条件的数据
            $sql = "SELECT * FROM {$dbconfig['dbqz']}_qrcode_config WHERE uid = :uid AND status = 1 ORDER BY id DESC";
            $configList = $DB->getAll($sql, [':uid' => $uid]);

            $debug_info['sql'] = $sql;
            $debug_info['query_result_count'] = count($configList);

            if(empty($configList)) {
                exit(json_encode(['code' => -1, 'msg' => '收款码不存在或数据权限错误', 'debug' => $debug_info]));
            }

            $result = [];
            foreach($configList as $config) {
                $result[] = [
                    'id' => intval($config['id']),
                    'name' => $config['name'],
                    'staff_id' => intval($config['staff_id']),
                    'staff_name' => $config['staff_id'] > 0 ? '员工' . $config['staff_id'] : '商户',
                    'staff_role' => $config['staff_id'] > 0 ? 'staff' : 'merchant',
                    'qr_style' => $config['qr_style'],
                    'amount' => floatval($config['amount']),
                    'description' => $config['description'],
                    'qr_url' => $config['qr_url'],
                    'addtime' => $config['addtime']
                ];
            }

            exit(json_encode(['code' => 0, 'data' => $result, 'debug' => $debug_info]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '获取配置列表失败: ' . $e->getMessage()]));
        }
        break;

    // 员工登录
    case 'staffLogin':
        try {
            $account = trim($_POST['account']);
            $password = trim($_POST['password']);

            // 支持两种方式：直接传uid或传merchantCode
            $uid = 0;
            if(isset($_POST['uid']) && !empty($_POST['uid'])) {
                $uid = intval($_POST['uid']); // 商户ID（旧方式）
            } elseif(isset($_POST['merchantCode']) && !empty($_POST['merchantCode'])) {
                $merchantCode = trim($_POST['merchantCode']); // 商户码（新方式）

                // 根据商户码查找商户ID
                $merchant = $DB->getRow("SELECT uid FROM {$dbconfig['dbqz']}_user WHERE uid = :merchant_code",
                    [':merchant_code' => $merchantCode]);
                if($merchant) {
                    $uid = intval($merchant['uid']);
                }
            }

            if(empty($account)) {
                exit(json_encode(['code' => -1, 'msg' => '请输入登录账号']));
            }

            if(empty($password)) {
                exit(json_encode(['code' => -1, 'msg' => '请输入登录密码']));
            }

            if(empty($uid)) {
                exit(json_encode(['code' => -1, 'msg' => '商户码不存在或无效']));
            }

            // 验证员工登录
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE uid = :uid AND account = :account AND password = :password AND status = 1",
                [':uid' => $uid, ':account' => $account, ':password' => md5($password)]);

            if($staff) {
                // 记录登录日志
                $DB->insert("{$dbconfig['dbqz']}_staff_log", [
                    'uid' => $uid,
                    'staff_id' => $staff['id'],
                    'action' => 'login',
                    'content' => json_encode(['account' => $account]),
                    'ip' => $_SERVER['REMOTE_ADDR'],
                    'addtime' => date('Y-m-d H:i:s')
                ]);

                // 返回员工信息（不包含密码）
                $result = [
                    'id' => intval($staff['id']),
                    'name' => $staff['name'],
                    'role' => $staff['role'],
                    'account' => $staff['account'],
                    'avatar_color' => $staff['avatar_color'],
                    'phone' => $staff['phone'],
                    'email' => $staff['email']
                ];

                exit(json_encode(['code' => 0, 'msg' => '登录成功', 'data' => $result]));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '账号或密码错误']));
            }
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '登录失败: ' . $e->getMessage()]));
        }
        break;

    // 生成收款码URL
    case 'generateQRUrl':
        try {
            $staff_id = intval($_POST['staff_id']);

            // 如果指定了员工，检查员工是否存在
            if($staff_id > 0) {
                $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND uid = :uid AND status = 1",
                    [':id' => $staff_id, ':uid' => $uid]);
                if(!$staff) {
                    exit(json_encode(['code' => -1, 'msg' => '指定的员工不存在']));
                }
            }

            // 生成加密的商户ID
            $merchant = authcode($uid, 'ENCODE', SYS_KEY);

            // 生成收款码URL
            $qr_url = "http://ceshi.huisas.com/paypage/?merchant=" . urlencode($merchant);
            if($staff_id > 0) {
                $qr_url .= "&staff_id=" . $staff_id;
            }

            exit(json_encode(['code' => 0, 'data' => ['qr_url' => $qr_url]]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '生成收款码URL失败: ' . $e->getMessage()]));
        }
        break;

    // 员工端：生成员工专属收款码
    case 'generateStaffQR':
        try {
            $staff_id = intval($_POST['staff_id']);

            if(empty($staff_id)) {
                exit(json_encode(['code' => -1, 'msg' => '员工ID不能为空']));
            }

            // 验证员工是否存在
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND status = 1",
                [':id' => $staff_id]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 生成加密的商户ID
            $merchant = authcode($staff['uid'], 'ENCODE', SYS_KEY);

            // 生成员工专属收款码URL
            $qr_url = "http://ceshi.huisas.com/paypage/?merchant=" . urlencode($merchant) . "&staff_id=" . $staff_id;

            exit(json_encode(['code' => 0, 'data' => ['qr_url' => $qr_url]]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '生成员工收款码失败: ' . $e->getMessage()]));
        }
        break;

    // 员工端：获取员工统计数据
    case 'getStats':
        try {
            $staff_id = intval($_POST['staff_id']);

            if(empty($staff_id)) {
                exit(json_encode(['code' => -1, 'msg' => '员工ID不能为空']));
            }

            // 验证员工是否存在
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND status = 1",
                [':id' => $staff_id]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 今日统计
            $today = date('Y-m-d');
            $todayStats = $DB->getRow("SELECT COUNT(*) as count, COALESCE(SUM(money), 0) as amount
                FROM {$dbconfig['dbqz']}_order
                WHERE operator_staff_id = :staff_id AND status = 1 AND DATE(addtime) = :today",
                [':staff_id' => $staff_id, ':today' => $today]);

            // 本月统计
            $monthStart = date('Y-m-01');
            $monthStats = $DB->getRow("SELECT COUNT(*) as count, COALESCE(SUM(money), 0) as amount
                FROM {$dbconfig['dbqz']}_order
                WHERE operator_staff_id = :staff_id AND status = 1 AND DATE(addtime) >= :month_start",
                [':staff_id' => $staff_id, ':month_start' => $monthStart]);

            $result = [
                'today' => [
                    'count' => intval($todayStats['count']),
                    'amount' => number_format($todayStats['amount'], 2)
                ],
                'month' => [
                    'count' => intval($monthStats['count']),
                    'amount' => number_format($monthStats['amount'], 2)
                ]
            ];

            exit(json_encode(['code' => 0, 'data' => $result]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '获取统计数据失败: ' . $e->getMessage()]));
        }
        break;

    // 员工端：获取最近订单
    case 'getRecentOrders':
        try {
            $staff_id = intval($_POST['staff_id']);
            $limit = intval($_POST['limit']) ?: 5;

            if(empty($staff_id)) {
                exit(json_encode(['code' => -1, 'msg' => '员工ID不能为空']));
            }

            // 验证员工是否存在
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND status = 1",
                [':id' => $staff_id]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 获取最近订单
            $orders = $DB->getAll("SELECT trade_no, money as amount, status, type as pay_type, addtime as create_time, endtime as pay_time
                FROM {$dbconfig['dbqz']}_order
                WHERE operator_staff_id = :staff_id
                ORDER BY id DESC
                LIMIT :limit",
                [':staff_id' => $staff_id, ':limit' => $limit]);

            $result = [];
            foreach($orders as $order) {
                $result[] = [
                    'id' => $order['trade_no'],
                    'trade_no' => $order['trade_no'],
                    'amount' => number_format($order['amount'], 2),
                    'status' => intval($order['status']),
                    'pay_type' => $order['pay_type'],
                    'create_time' => strtotime($order['create_time']),
                    'pay_time' => $order['pay_time'] ? strtotime($order['pay_time']) : null
                ];
            }

            exit(json_encode(['code' => 0, 'data' => $result]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '获取最近订单失败: ' . $e->getMessage()]));
        }
        break;

    // 员工端：获取员工订单列表
    case 'getStaffOrders':
        try {
            $staff_id = intval($_POST['staff_id']);
            $status = isset($_POST['status']) && $_POST['status'] !== '' ? intval($_POST['status']) : null;
            $start_date = trim($_POST['start_date']);
            $end_date = trim($_POST['end_date']);
            $page = intval($_POST['page']) ?: 1;
            $limit = intval($_POST['limit']) ?: 20;

            if(empty($staff_id)) {
                exit(json_encode(['code' => -1, 'msg' => '员工ID不能为空']));
            }

            // 验证员工是否存在
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND status = 1",
                [':id' => $staff_id]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 构建查询条件
            $where = "operator_staff_id = :staff_id";
            $params = [':staff_id' => $staff_id];

            if($status !== null) {
                $where .= " AND status = :status";
                $params[':status'] = $status;
            }

            if(!empty($start_date)) {
                $where .= " AND DATE(addtime) >= :start_date";
                $params[':start_date'] = $start_date;
            }

            if(!empty($end_date)) {
                $where .= " AND DATE(addtime) <= :end_date";
                $params[':end_date'] = $end_date;
            }

            // 计算偏移量
            $offset = ($page - 1) * $limit;

            // 获取订单列表
            $orders = $DB->getAll("SELECT trade_no, money as amount, status, type as pay_type, addtime as create_time, endtime as pay_time
                FROM {$dbconfig['dbqz']}_order
                WHERE {$where}
                ORDER BY id DESC
                LIMIT {$offset}, {$limit}", $params);

            $result = [];
            foreach($orders as $order) {
                $result[] = [
                    'id' => $order['trade_no'],
                    'trade_no' => $order['trade_no'],
                    'amount' => number_format($order['amount'], 2),
                    'status' => intval($order['status']),
                    'pay_type' => $order['pay_type'],
                    'create_time' => strtotime($order['create_time']),
                    'pay_time' => $order['pay_time'] ? strtotime($order['pay_time']) : null
                ];
            }

            exit(json_encode(['code' => 0, 'data' => ['list' => $result]]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '获取订单列表失败: ' . $e->getMessage()]));
        }
        break;

    // 员工端：获取今日统计
    case 'getTodayStats':
        try {
            $staff_id = intval($_POST['staff_id']);

            if(empty($staff_id)) {
                exit(json_encode(['code' => -1, 'msg' => '员工ID不能为空']));
            }

            // 验证员工是否存在
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND status = 1",
                [':id' => $staff_id]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 今日统计
            $today = date('Y-m-d');
            $todayStats = $DB->getRow("SELECT COUNT(*) as count, COALESCE(SUM(money), 0) as amount
                FROM {$dbconfig['dbqz']}_order
                WHERE operator_staff_id = :staff_id AND status = 1 AND DATE(addtime) = :today",
                [':staff_id' => $staff_id, ':today' => $today]);

            $result = [
                'count' => intval($todayStats['count']),
                'amount' => number_format($todayStats['amount'], 2)
            ];

            exit(json_encode(['code' => 0, 'data' => $result]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '获取今日统计失败: ' . $e->getMessage()]));
        }
        break;

    // 员工端：获取概览统计
    case 'getStaffOverviewStats':
        try {
            $staff_id = intval($_POST['staff_id']);
            $start_date = trim($_POST['start_date']);
            $end_date = trim($_POST['end_date']);

            if(empty($staff_id)) {
                exit(json_encode(['code' => -1, 'msg' => '员工ID不能为空']));
            }

            // 验证员工是否存在
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND status = 1",
                [':id' => $staff_id]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 构建查询条件
            $where = "operator_staff_id = :staff_id";
            $params = [':staff_id' => $staff_id];

            if(!empty($start_date)) {
                $where .= " AND DATE(addtime) >= :start_date";
                $params[':start_date'] = $start_date;
            }

            if(!empty($end_date)) {
                $where .= " AND DATE(addtime) <= :end_date";
                $params[':end_date'] = $end_date;
            }

            // 总统计
            $totalStats = $DB->getRow("SELECT COUNT(*) as totalCount, COALESCE(SUM(money), 0) as totalAmount
                FROM {$dbconfig['dbqz']}_order
                WHERE {$where}", $params);

            // 成功订单统计
            $successStats = $DB->getRow("SELECT COUNT(*) as successCount
                FROM {$dbconfig['dbqz']}_order
                WHERE {$where} AND status = 1", $params);

            $result = [
                'totalAmount' => number_format($totalStats['totalAmount'], 2),
                'totalCount' => intval($totalStats['totalCount']),
                'successCount' => intval($successStats['successCount'])
            ];

            exit(json_encode(['code' => 0, 'data' => $result]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '获取概览统计失败: ' . $e->getMessage()]));
        }
        break;

    // 员工端：获取支付方式统计
    case 'getStaffPaymentStats':
        try {
            $staff_id = intval($_POST['staff_id']);
            $start_date = trim($_POST['start_date']);
            $end_date = trim($_POST['end_date']);

            if(empty($staff_id)) {
                exit(json_encode(['code' => -1, 'msg' => '员工ID不能为空']));
            }

            // 验证员工是否存在
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND status = 1",
                [':id' => $staff_id]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 构建查询条件
            $where = "operator_staff_id = :staff_id AND status = 1";
            $params = [':staff_id' => $staff_id];

            if(!empty($start_date)) {
                $where .= " AND DATE(addtime) >= :start_date";
                $params[':start_date'] = $start_date;
            }

            if(!empty($end_date)) {
                $where .= " AND DATE(addtime) <= :end_date";
                $params[':end_date'] = $end_date;
            }

            // 按支付方式统计
            $paymentStats = $DB->getAll("SELECT type, COUNT(*) as count, COALESCE(SUM(money), 0) as amount
                FROM {$dbconfig['dbqz']}_order
                WHERE {$where}
                GROUP BY type", $params);

            $result = [];
            foreach($paymentStats as $stat) {
                $result[] = [
                    'type' => $stat['type'],
                    'count' => intval($stat['count']),
                    'amount' => number_format($stat['amount'], 2)
                ];
            }

            exit(json_encode(['code' => 0, 'data' => $result]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '获取支付方式统计失败: ' . $e->getMessage()]));
        }
        break;

    // 员工端：获取每日统计
    case 'getStaffDailyStats':
        try {
            $staff_id = intval($_POST['staff_id']);
            $start_date = trim($_POST['start_date']);
            $end_date = trim($_POST['end_date']);

            if(empty($staff_id)) {
                exit(json_encode(['code' => -1, 'msg' => '员工ID不能为空']));
            }

            // 验证员工是否存在
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND status = 1",
                [':id' => $staff_id]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 构建查询条件
            $where = "operator_staff_id = :staff_id AND status = 1";
            $params = [':staff_id' => $staff_id];

            if(!empty($start_date)) {
                $where .= " AND DATE(addtime) >= :start_date";
                $params[':start_date'] = $start_date;
            }

            if(!empty($end_date)) {
                $where .= " AND DATE(addtime) <= :end_date";
                $params[':end_date'] = $end_date;
            }

            // 按日期统计
            $dailyStats = $DB->getAll("SELECT DATE(addtime) as date, COALESCE(SUM(money), 0) as amount
                FROM {$dbconfig['dbqz']}_order
                WHERE {$where}
                GROUP BY DATE(addtime)
                ORDER BY DATE(addtime) ASC", $params);

            $result = [];
            foreach($dailyStats as $stat) {
                $result[] = [
                    'date' => $stat['date'],
                    'amount' => number_format($stat['amount'], 2)
                ];
            }

            exit(json_encode(['code' => 0, 'data' => $result]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '获取每日统计失败: ' . $e->getMessage()]));
        }
        break;

    // 员工端：获取员工排名
    case 'getStaffRanking':
        try {
            $staff_id = intval($_POST['staff_id']);

            if(empty($staff_id)) {
                exit(json_encode(['code' => -1, 'msg' => '员工ID不能为空']));
            }

            // 验证员工是否存在
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND status = 1",
                [':id' => $staff_id]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 本月排名
            $monthStart = date('Y-m-01');
            $monthRanking = $DB->getAll("SELECT operator_staff_id, SUM(money) as total_amount
                FROM {$dbconfig['dbqz']}_order
                WHERE operator_staff_id IS NOT NULL AND status = 1 AND DATE(addtime) >= :month_start
                GROUP BY operator_staff_id
                ORDER BY total_amount DESC", [':month_start' => $monthStart]);

            $monthRank = 0;
            foreach($monthRanking as $index => $rank) {
                if($rank['operator_staff_id'] == $staff_id) {
                    $monthRank = $index + 1;
                    break;
                }
            }

            // 今日排名
            $today = date('Y-m-d');
            $todayRanking = $DB->getAll("SELECT operator_staff_id, SUM(money) as total_amount
                FROM {$dbconfig['dbqz']}_order
                WHERE operator_staff_id IS NOT NULL AND status = 1 AND DATE(addtime) = :today
                GROUP BY operator_staff_id
                ORDER BY total_amount DESC", [':today' => $today]);

            $todayRank = 0;
            foreach($todayRanking as $index => $rank) {
                if($rank['operator_staff_id'] == $staff_id) {
                    $todayRank = $index + 1;
                    break;
                }
            }

            $result = [
                'monthRank' => $monthRank,
                'todayRank' => $todayRank
            ];

            exit(json_encode(['code' => 0, 'data' => $result]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '获取排名数据失败: ' . $e->getMessage()]));
        }
        break;

    // 员工端：更新个人资料
    case 'updateProfile':
        try {
            $staff_id = intval($_POST['staff_id']);
            $name = trim($_POST['name']);
            $phone = trim($_POST['phone']);
            $email = trim($_POST['email']);

            if(empty($staff_id)) {
                exit(json_encode(['code' => -1, 'msg' => '员工ID不能为空']));
            }

            if(empty($name)) {
                exit(json_encode(['code' => -1, 'msg' => '姓名不能为空']));
            }

            // 验证员工是否存在
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND status = 1",
                [':id' => $staff_id]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 检查同名员工（排除自己）
            $exists = $DB->getRow("SELECT id FROM {$dbconfig['dbqz']}_staff WHERE uid = :uid AND name = :name AND id != :id AND status = 1",
                [':uid' => $staff['uid'], ':name' => $name, ':id' => $staff_id]);
            if($exists) {
                exit(json_encode(['code' => -1, 'msg' => '员工姓名已存在']));
            }

            // 更新员工信息
            $data = [
                'name' => $name,
                'phone' => $phone,
                'email' => $email,
                'updatetime' => date('Y-m-d H:i:s')
            ];

            $result = $DB->update("{$dbconfig['dbqz']}_staff", $data, ['id' => $staff_id]);
            if($result !== false) {
                exit(json_encode(['code' => 0, 'msg' => '更新成功']));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '更新失败']));
            }
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '更新个人资料失败: ' . $e->getMessage()]));
        }
        break;

    // 员工端：修改密码
    case 'changePassword':
        try {
            $staff_id = intval($_POST['staff_id']);
            $old_password = trim($_POST['old_password']);
            $new_password = trim($_POST['new_password']);

            if(empty($staff_id)) {
                exit(json_encode(['code' => -1, 'msg' => '员工ID不能为空']));
            }

            if(empty($old_password)) {
                exit(json_encode(['code' => -1, 'msg' => '原密码不能为空']));
            }

            if(empty($new_password)) {
                exit(json_encode(['code' => -1, 'msg' => '新密码不能为空']));
            }

            if(strlen($new_password) < 6) {
                exit(json_encode(['code' => -1, 'msg' => '新密码长度不能少于6位']));
            }

            // 验证员工是否存在且原密码正确
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND password = :password AND status = 1",
                [':id' => $staff_id, ':password' => md5($old_password)]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '原密码错误']));
            }

            // 更新密码
            $result = $DB->update("{$dbconfig['dbqz']}_staff",
                ['password' => md5($new_password), 'updatetime' => date('Y-m-d H:i:s')],
                ['id' => $staff_id]);

            if($result !== false) {
                // 记录操作日志
                try {
                    $DB->insert("{$dbconfig['dbqz']}_staff_log", [
                        'uid' => $staff['uid'],
                        'staff_id' => $staff_id,
                        'action' => 'change_password',
                        'content' => json_encode(['staff_id' => $staff_id]),
                        'ip' => $_SERVER['REMOTE_ADDR'],
                        'addtime' => date('Y-m-d H:i:s')
                    ]);
                } catch (Exception $logError) {
                    // 日志记录失败不影响主流程
                }

                exit(json_encode(['code' => 0, 'msg' => '密码修改成功']));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '密码修改失败']));
            }
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '修改密码失败: ' . $e->getMessage()]));
        }
        break;

    // 编辑收款码配置
    case 'editQRConfig':
        $config_id = intval($_POST['config_id']);
        $uid = intval($_POST['uid']); // 直接从POST获取uid
        $name = trim($_POST['name']);
        $qr_style = trim($_POST['qr_style']) ?: 'default';
        $staff_id = $_POST['staff_id'] ? intval($_POST['staff_id']) : null;
        $amount = $_POST['amount'] ? floatval($_POST['amount']) : null;
        $description = trim($_POST['description']);

        // 添加调试信息
        $debug_info = [
            'config_id' => $config_id,
            'uid_from_post' => $uid,
            'uid_from_userrow' => $userrow['uid'] ?? 'null',
            'name' => $name,
            'qr_style' => $qr_style,
            'staff_id' => $staff_id,
            'amount' => $amount,
            'table_name' => "{$dbconfig['dbqz']}_qrcode_config"
        ];

        if(empty($uid)) {
            exit(json_encode(['code' => -1, 'msg' => '商户ID不能为空', 'debug' => $debug_info]));
        }

        if(empty($name)) {
            exit(json_encode(['code' => -1, 'msg' => '收款码名称不能为空', 'debug' => $debug_info]));
        }

        // 验证权限：只能编辑自己的收款码
        $existing = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_qrcode_config` WHERE `id`=:id AND `uid`=:uid",
            [':id'=>$config_id, ':uid'=>$uid]);

        $debug_info['existing_record'] = $existing;

        if(!$existing) {
            exit(json_encode(['code' => -1, 'msg' => '收款码不存在或无权限编辑', 'debug' => $debug_info]));
        }

        // 更新收款码配置
        $result = $DB->exec("UPDATE `{$dbconfig['dbqz']}_qrcode_config` SET
            `name`=:name, `qr_style`=:qr_style, `staff_id`=:staff_id,
            `amount`=:amount, `description`=:description, `updatetime`=NOW()
            WHERE `id`=:id AND `uid`=:uid", [
            ':name'=>$name, ':qr_style'=>$qr_style, ':staff_id'=>$staff_id,
            ':amount'=>$amount, ':description'=>$description,
            ':id'=>$config_id, ':uid'=>$uid
        ]);

        $debug_info['update_result'] = $result;

        if($result) {
            exit(json_encode(['code' => 0, 'msg' => '编辑成功', 'debug' => $debug_info]));
        } else {
            $debug_info['error'] = $DB->error();
            exit(json_encode(['code' => -1, 'msg' => '编辑失败', 'debug' => $debug_info]));
        }
        break;

    // 删除收款码配置
    case 'deleteQRConfig':
        $config_id = intval($_POST['config_id']);
        $uid = intval($_POST['uid']); // 直接从POST获取uid

        // 添加调试信息
        $debug_info = [
            'config_id' => $config_id,
            'uid_from_post' => $uid,
            'uid_from_userrow' => $userrow['uid'] ?? 'null',
            'table_name' => "{$dbconfig['dbqz']}_qrcode_config"
        ];

        if(empty($uid)) {
            exit(json_encode(['code' => -1, 'msg' => '商户ID不能为空', 'debug' => $debug_info]));
        }

        // 验证权限：只能删除自己的收款码
        $existing = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_qrcode_config` WHERE `id`=:id AND `uid`=:uid",
            [':id'=>$config_id, ':uid'=>$uid]);

        $debug_info['existing_record'] = $existing;

        if(!$existing) {
            exit(json_encode(['code' => -1, 'msg' => '收款码不存在或无权限删除', 'debug' => $debug_info]));
        }

        // 软删除：设置status=0
        $result = $DB->exec("UPDATE `{$dbconfig['dbqz']}_qrcode_config` SET `status`=0, `updatetime`=NOW()
            WHERE `id`=:id AND `uid`=:uid", [':id'=>$config_id, ':uid'=>$uid]);

        $debug_info['update_result'] = $result;

        if($result) {
            exit(json_encode(['code' => 0, 'msg' => '删除成功', 'debug' => $debug_info]));
        } else {
            $debug_info['error'] = $DB->error();
            exit(json_encode(['code' => -1, 'msg' => '删除失败', 'debug' => $debug_info]));
        }
        break;

    // 获取收款码详情
    case 'getQRConfigDetail':
        $config_id = intval($_GET['config_id']);

        // 验证权限：只能查看自己的收款码
        $config = $DB->getRow("SELECT c.*, s.name as staff_name FROM `{$dbconfig['dbqz']}_qrcode_config` c
            LEFT JOIN `{$dbconfig['dbqz']}_staff` s ON c.staff_id = s.id
            WHERE c.id=:id AND c.uid=:uid AND c.status=1",
            [':id'=>$config_id, ':uid'=>$userrow['uid']]);

        if($config) {
            exit(json_encode(['code' => 0, 'msg' => '获取成功', 'data' => $config]));
        } else {
            exit(json_encode(['code' => -1, 'msg' => '收款码不存在']));
        }
        break;

    // 绑定预制码
    case 'bindPreCode':
        $precode = trim($_POST['precode']);
        $name = trim($_POST['name']);
        $style = trim($_POST['style']) ?: 'default';
        $staff_id = $_POST['staff_id'] ? intval($_POST['staff_id']) : null;
        $fixed_amount = $_POST['fixed_amount'] ? floatval($_POST['fixed_amount']) : null;
        $description = trim($_POST['description']);

        if(empty($precode)) {
            exit(json_encode(['code' => -1, 'msg' => '预制码不能为空']));
        }
        if(empty($name)) {
            exit(json_encode(['code' => -1, 'msg' => '收款码名称不能为空']));
        }

        // 验证预制码是否存在且未绑定
        $precode_info = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_precode` WHERE `code`=:code AND `status`=0",
            [':code'=>$precode]);
        if(!$precode_info) {
            exit(json_encode(['code' => -1, 'msg' => '预制码不存在或已被绑定']));
        }

        // 如果指定了员工ID，验证员工是否属于当前商户
        if($staff_id) {
            $staff_exists = $DB->getColumn("SELECT COUNT(*) FROM `{$dbconfig['dbqz']}_staff` WHERE `id`=:id AND `uid`=:uid AND `status`=1",
                [':id'=>$staff_id, ':uid'=>$userrow['uid']]);
            if(!$staff_exists) {
                exit(json_encode(['code' => -1, 'msg' => '指定的员工不存在']));
            }
        }

        $DB->beginTransaction();
        try {
            // 绑定预制码
            $DB->exec("UPDATE `{$dbconfig['dbqz']}_precode` SET `uid`=:uid, `status`=1, `bindtime`=NOW() WHERE `code`=:code",
                [':uid'=>$userrow['uid'], ':code'=>$precode]);

            // 创建收款码配置
            $qr_url = $siteurl . 'pay/qrcode/' . $precode . '/';
            $result = $DB->exec("INSERT INTO `{$dbconfig['dbqz']}_qrcode_config`
                (`uid`, `name`, `style`, `staff_id`, `fixed_amount`, `description`, `precode`, `qr_url`, `addtime`)
                VALUES (:uid, :name, :style, :staff_id, :fixed_amount, :description, :precode, :qr_url, NOW())", [
                ':uid'=>$userrow['uid'], ':name'=>$name, ':style'=>$style, ':staff_id'=>$staff_id,
                ':fixed_amount'=>$fixed_amount, ':description'=>$description,
                ':precode'=>$precode, ':qr_url'=>$qr_url
            ]);

            $DB->commit();
            exit(json_encode(['code' => 0, 'msg' => '预制码绑定成功', 'qr_url' => $qr_url]));
        } catch(Exception $e) {
            $DB->rollback();
            exit(json_encode(['code' => -1, 'msg' => '绑定失败：' . $e->getMessage()]));
        }
        break;

    // 获取可用预制码列表
    case 'getAvailablePreCodes':
        $page = intval($_GET['page']) ?: 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;

        // 获取未绑定的预制码
        $list = $DB->getAll("SELECT `code`, `addtime` FROM `{$dbconfig['dbqz']}_precode`
            WHERE `status`=0 ORDER BY `addtime` DESC LIMIT $offset, $limit");

        $total = $DB->getColumn("SELECT COUNT(*) FROM `{$dbconfig['dbqz']}_precode` WHERE `status`=0");

        exit(json_encode([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]
        ]));
        break;

    // 验证预制码
    case 'verifyPreCode':
        $precode = trim($_POST['precode']);

        if(empty($precode)) {
            exit(json_encode(['code' => -1, 'msg' => '预制码不能为空']));
        }

        // 验证预制码是否存在且未绑定
        $precode_info = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_precode` WHERE `code`=:code",
            [':code'=>$precode]);

        if(!$precode_info) {
            exit(json_encode(['code' => -1, 'msg' => '预制码不存在']));
        }

        if($precode_info['status'] == 1) {
            exit(json_encode(['code' => -1, 'msg' => '预制码已被绑定']));
        }

        exit(json_encode([
            'code' => 0,
            'msg' => '预制码有效',
            'data' => [
                'precode' => $precode_info['code'],
                'addtime' => $precode_info['addtime']
            ]
        ]));
        break;

    // 绑定预制码
    case 'bindPreCode':
        $code = trim($_POST['code']);
        $name = trim($_POST['name']);
        $style = trim($_POST['style']);
        $staff_id = intval($_POST['staff_id']) ?: null;
        $description = trim($_POST['description']);

        // 音响配置
        $speaker_status = intval($_POST['speaker_status']);
        $speaker_brand = trim($_POST['speaker_brand']);
        $speaker_config = '';
        if($speaker_status && $speaker_brand) {
            $config = [];
            if($speaker_brand == 'pinsheng' || $speaker_brand == 'bailihua') {
                $config['devid'] = trim($_POST['speaker_devid']);
            }
            $speaker_config = json_encode($config);
        }

        $uid = $userrow['uid'];

        // 验证必填字段
        if(empty($code) || empty($name)) {
            exit(json_encode(['code' => -1, 'msg' => '预制码和名称不能为空']));
        }

        // 验证预制码格式
        if(!preg_match('/^[A-Z0-9]{8}$/', $code)) {
            exit(json_encode(['code' => -1, 'msg' => '预制码格式不正确']));
        }

        // 验证预制码是否存在且未绑定
        $precode = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_precode` WHERE `code`=? AND `status`=0", [$code]);
        if(!$precode) {
            exit(json_encode(['code' => -1, 'msg' => '预制码不存在或已被绑定']));
        }

        // 如果指定了员工，验证员工是否属于当前商户
        if($staff_id) {
            $staff = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_staff` WHERE `id`=? AND `uid`=?", [$staff_id, $uid]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '指定的员工不存在']));
            }
        }

        $DB->beginTransaction();
        try {
            // 更新预制码状态
            $DB->exec("UPDATE `{$dbconfig['dbqz']}_precode` SET
                `uid`=?, `staff_id`=?, `name`=?, `qr_style`=?, `description`=?,
                `speaker_status`=?, `speaker_brand`=?, `speaker_config`=?,
                `status`=1, `bindtime`=NOW()
                WHERE `code`=?",
                [$uid, $staff_id, $name, $style, $description,
                 $speaker_status, $speaker_brand, $speaker_config, $code]);

            // 创建收款码配置
            $qr_url = $siteurl . 'paypage/precode/' . $code;
            $DB->exec("INSERT INTO `{$dbconfig['dbqz']}_qrcode_config`
                (`uid`, `name`, `style`, `staff_id`, `description`, `precode`, `qr_url`,
                 `speaker_status`, `speaker_brand`, `speaker_config`, `addtime`)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())",
                [$uid, $name, $style, $staff_id, $description, $code, $qr_url,
                 $speaker_status, $speaker_brand, $speaker_config]);

            $qrconfig_id = $DB->lastInsertId();

            $DB->commit();

            exit(json_encode([
                'code' => 0,
                'msg' => '绑定成功',
                'data' => [
                    'qrconfig_id' => $qrconfig_id,
                    'precode' => $code,
                    'qr_url' => $qr_url
                ]
            ]));

        } catch(Exception $e) {
            $DB->rollback();
            exit(json_encode(['code' => -1, 'msg' => '绑定失败：' . $e->getMessage()]));
        }
        break;

    // 获取可用预制码列表
    case 'getAvailablePreCodes':
        $page = intval($_GET['page']) ?: 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;

        // 查询未绑定的预制码
        $list = $DB->getAll("SELECT `code`, `addtime`, `qr_url`
            FROM `{$dbconfig['dbqz']}_precode`
            WHERE `status`=0
            ORDER BY `addtime` DESC
            LIMIT $offset, $limit");

        $total = $DB->getColumn("SELECT COUNT(*) FROM `{$dbconfig['dbqz']}_precode` WHERE `status`=0");

        exit(json_encode([
            'code' => 0,
            'msg' => 'success',
            'data' => [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]
        ]));
        break;

    // 获取商户的预制码绑定记录
    case 'getMyPreCodes':
        $uid = $userrow['uid'];
        $page = intval($_GET['page']) ?: 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;

        // 查询当前商户绑定的预制码
        $list = $DB->getAll("SELECT p.*, s.name as staff_name
            FROM `{$dbconfig['dbqz']}_precode` p
            LEFT JOIN `{$dbconfig['dbqz']}_staff` s ON p.staff_id = s.id
            WHERE p.uid=? AND p.status=1
            ORDER BY p.bindtime DESC
            LIMIT $offset, $limit", [$uid]);

        $total = $DB->getColumn("SELECT COUNT(*) FROM `{$dbconfig['dbqz']}_precode` WHERE `uid`=? AND `status`=1", [$uid]);

        exit(json_encode([
            'code' => 0,
            'msg' => 'success',
            'data' => [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]
        ]));
        break;

    default:
        exit(json_encode(['code' => -1, 'msg' => '无效的操作']));
        break;
}
?>
