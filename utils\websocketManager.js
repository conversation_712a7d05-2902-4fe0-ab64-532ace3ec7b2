/**
 * WebSocket管理器 - 全局单例
 * 负责管理WebSocket连接的生命周期，确保连接的持久性
 */
class WebSocketManager {
  constructor() {
    this.ws = null
    this.wsUrl = 'ws://ceshi.huisas.com:8080'
    this.isConnected = false
    this.isConnecting = false
    this.isEnabled = false
    
    // 重连配置
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 10
    this.reconnectInterval = 5000
    this.reconnectTimer = null
    
    // 心跳配置
    this.heartbeatTimer = null
    this.heartbeatInterval = 30000
    
    // 用户信息
    this.userInfo = {
      merchant_id: '1000',
      staff_id: '',
      token: 'test_token_1234567890'
    }
    
    // 事件监听器
    this.listeners = {
      connect: [],
      disconnect: [],
      message: [],
      error: []
    }
    
    // 消息队列（连接前的消息）
    this.messageQueue = []
    
    console.log('🌐 WebSocket管理器初始化')
  }
  
  // 启用WebSocket
  enable() {
    if (this.isEnabled) return
    
    this.isEnabled = true
    console.log('🔊 启用WebSocket管理器')
    this.connect()
  }
  
  // 禁用WebSocket
  disable() {
    this.isEnabled = false
    console.log('🔇 禁用WebSocket管理器')
    this.disconnect()
  }
  
  // 连接WebSocket
  connect() {
    if (!this.isEnabled) {
      console.log('⚠️ WebSocket管理器未启用')
      return
    }
    
    if (this.isConnected || this.isConnecting) {
      console.log('⚠️ WebSocket已连接或正在连接中')
      return
    }
    
    this.isConnecting = true
    console.log(`🔗 正在连接WebSocket: ${this.wsUrl}`)
    
    this.ws = uni.connectSocket({
      url: this.wsUrl,
      success: () => {
        console.log('🚀 WebSocket连接请求已发送')
      },
      fail: (error) => {
        this.isConnecting = false
        console.error('❌ WebSocket连接失败:', error)
        this.emit('error', error)
        this.scheduleReconnect()
      }
    })
    
    // 连接成功
    this.ws.onOpen(() => {
      this.isConnected = true
      this.isConnecting = false
      this.reconnectAttempts = 0
      
      console.log('✅ WebSocket连接成功！')
      this.emit('connect')
      
      // 发送认证
      this.authenticate()
      
      // 启动心跳
      this.startHeartbeat()
      
      // 发送队列中的消息
      this.flushMessageQueue()
    })
    
    // 接收消息
    this.ws.onMessage((res) => {
      console.log('📨 收到WebSocket消息:', res.data)
      
      try {
        const data = JSON.parse(res.data)
        this.handleMessage(data)
      } catch (error) {
        console.warn('⚠️ 消息解析失败:', error.message)
        this.handleRawMessage(res.data)
      }
    })
    
    // 连接关闭
    this.ws.onClose((res) => {
      this.isConnected = false
      this.isConnecting = false
      
      console.log(`🔌 WebSocket连接关闭: code=${res.code}`)
      this.emit('disconnect', res)
      
      // 清理心跳
      this.stopHeartbeat()
      
      // 如果仍然启用，尝试重连
      if (this.isEnabled) {
        this.scheduleReconnect()
      }
    })
    
    // 连接错误
    this.ws.onError((error) => {
      this.isConnected = false
      this.isConnecting = false
      
      console.error('❌ WebSocket错误:', error)
      this.emit('error', error)
    })
  }
  
  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    this.isConnected = false
    this.isConnecting = false
    
    // 清理定时器
    this.stopHeartbeat()
    this.clearReconnectTimer()
    
    console.log('🔌 WebSocket已断开')
  }
  
  // 发送消息
  sendMessage(message) {
    if (!this.isEnabled) {
      console.warn('⚠️ WebSocket管理器未启用，消息被忽略')
      return false
    }
    
    if (this.isConnected && this.ws) {
      const jsonData = JSON.stringify(message)
      this.ws.send({
        data: jsonData,
        success: () => {
          console.log('📤 消息发送成功:', jsonData)
        },
        fail: (error) => {
          console.error('❌ 消息发送失败:', error)
        }
      })
      return true
    } else {
      // 连接未建立，加入队列
      this.messageQueue.push(message)
      console.log('📝 消息已加入队列，等待连接建立')
      
      // 如果未连接，尝试连接
      if (!this.isConnecting) {
        this.connect()
      }
      
      return false
    }
  }
  
  // 认证
  authenticate() {
    const authMessage = {
      type: 'auth',
      uid: this.userInfo.merchant_id,
      merchant_id: this.userInfo.merchant_id,
      timestamp: Date.now()
    }
    
    this.sendMessage(authMessage)
    console.log('🔐 发送认证信息')
    
    // 认证后订阅支付频道
    setTimeout(() => {
      this.subscribePaymentChannel()
    }, 1000)
  }
  
  // 订阅支付频道
  subscribePaymentChannel() {
    const subscribeMessage = {
      type: 'subscribe',
      channel: 'payment',
      uid: this.userInfo.merchant_id
    }
    
    this.sendMessage(subscribeMessage)
    console.log('📡 订阅支付频道')
  }
  
  // 处理消息
  handleMessage(data) {
    switch (data.type) {
      case 'auth_success':
        console.log('🔐 认证成功')
        break
        
      case 'auth_failed':
        console.error('❌ 认证失败:', data.message)
        break
        
      case 'payment':
      case 'payment_notification':
        console.log('💰 收到支付通知:', data)
        this.emit('message', data)
        break
        
      case 'pong':
        // 心跳响应，不记录日志
        break
        
      default:
        console.log('📨 收到消息:', data)
        this.emit('message', data)
    }
  }
  
  // 处理原始消息
  handleRawMessage(rawData) {
    console.log('📨 收到原始消息:', rawData)
    
    // 尝试解析支付通知
    if (rawData.includes('money') || rawData.includes('payment')) {
      const moneyMatch = rawData.match(/money["\s]*[:=]["\s]*([0-9.]+)/i)
      if (moneyMatch) {
        const money = moneyMatch[1]
        console.log('💰 从原始消息提取金额:', money)
        
        this.emit('message', {
          type: 'payment',
          money: money,
          typename: '未知支付方式'
        })
      }
    }
  }
  
  // 启动心跳
  startHeartbeat() {
    this.stopHeartbeat()
    
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.sendMessage({
          type: 'ping',
          timestamp: Date.now()
        })
      }
    }, this.heartbeatInterval)
  }
  
  // 停止心跳
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }
  
  // 安排重连
  scheduleReconnect() {
    if (!this.isEnabled) return
    
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ 重连次数已达上限，停止重连')
      return
    }
    
    this.clearReconnectTimer()
    
    this.reconnectAttempts++
    const delay = this.reconnectInterval * Math.min(this.reconnectAttempts, 5)
    
    console.log(`🔄 ${delay/1000}秒后进行第${this.reconnectAttempts}次重连...`)
    
    this.reconnectTimer = setTimeout(() => {
      this.connect()
    }, delay)
  }
  
  // 清除重连定时器
  clearReconnectTimer() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }
  
  // 发送队列中的消息
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      this.sendMessage(message)
    }
  }
  
  // 添加事件监听器
  on(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback)
    }
  }
  
  // 移除事件监听器
  off(event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback)
      if (index > -1) {
        this.listeners[event].splice(index, 1)
      }
    }
  }
  
  // 触发事件
  emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('事件监听器执行错误:', error)
        }
      })
    }
  }
  
  // 获取状态
  getStatus() {
    return {
      isEnabled: this.isEnabled,
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      queueLength: this.messageQueue.length
    }
  }
}

// 创建全局单例
const websocketManager = new WebSocketManager()

export default websocketManager
