<?php
/**
 * 测试支付回调处理
 * 模拟真实的支付回调流程
 */

// 引入系统文件
require_once __DIR__ . '/includes/common.php';
require_once __DIR__ . '/includes/functions.php';

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

echo "<h1>🧪 支付回调测试</h1>\n";

// 模拟一个真实的订单数据
$testOrder = [
    'trade_no' => '2025072705284558591',  // 使用你刚才支付成功的订单号
    'out_trade_no' => '2025072705284558591',
    'api_trade_no' => 'TEST_API_' . time(),
    'uid' => '1000',  // 商户ID
    'operator_staff_id' => null,
    'shop_id' => null,
    'tid' => '3',
    'type' => '1',  // 支付宝
    'channel' => '5',
    'name' => '在线收款',
    'money' => '0.01',
    'realmoney' => '0.01',
    'getmoney' => '0.01',
    'profitmoney' => null,
    'refundmoney' => null,
    'notify_url' => 'http://ceshi.huisas.com/paypage/success.php?trade_no=2025072705284558591',
    'return_url' => 'http://ceshi.huisas.com/paypage/success.php?trade_no=2025072705284558591',
    'param' => null,
    'addtime' => date('Y-m-d H:i:s'),
    'endtime' => null,
    'date' => null,
    'domain' => 'ceshi.huisas.com',
    'domain2' => null,
    'ip' => '127.0.0.1',
    'buyer' => '<EMAIL>',
    'status' => '1',  // 已支付
    'notify' => '0',
    'notifytime' => null,
    'invite' => '0',
    'invitemoney' => null,
    'combine' => '0',
    'profits' => '0',
    'profits2' => '0',
    'settle' => '0',
    'subchannel' => '0',
    'payurl' => null,
    'ext' => null,
    'version' => '0',
    'bill_trade_no' => null,
    'bill_mch_trade_no' => null,
    'mobile' => null,
    'typename' => 'alipay',
    'typeshowname' => '支付宝',
    'plugin' => 'oldeasypay'
];

echo "<h2>📋 测试订单数据</h2>\n";
echo "<pre>" . json_encode($testOrder, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

echo "<h2>🔍 函数检查</h2>\n";

// 检查函数是否存在
$functionExists = function_exists('sendWebSocketPaymentNotification');
echo "sendWebSocketPaymentNotification函数存在: " . ($functionExists ? '✅ 是' : '❌ 否') . "<br>\n";

$processOrderExists = function_exists('processOrder');
echo "processOrder函数存在: " . ($processOrderExists ? '✅ 是' : '❌ 否') . "<br>\n";

echo "<h2>🧪 测试processOrder函数</h2>\n";

if ($processOrderExists) {
    echo "开始调用processOrder函数...<br>\n";
    
    try {
        // 调用processOrder函数（模拟支付回调）
        processOrder($testOrder, true);
        echo "✅ processOrder函数调用成功<br>\n";
    } catch (Exception $e) {
        echo "❌ processOrder函数调用失败: " . $e->getMessage() . "<br>\n";
    }
} else {
    echo "❌ processOrder函数不存在，无法测试<br>\n";
}

echo "<h2>🧪 直接测试WebSocket通知</h2>\n";

if ($functionExists) {
    echo "开始直接调用sendWebSocketPaymentNotification函数...<br>\n";
    
    try {
        $result = sendWebSocketPaymentNotification($testOrder);
        echo "WebSocket通知结果: " . ($result ? '✅ 成功' : '❌ 失败') . "<br>\n";
    } catch (Exception $e) {
        echo "❌ WebSocket通知异常: " . $e->getMessage() . "<br>\n";
    }
} else {
    echo "❌ sendWebSocketPaymentNotification函数不存在，无法测试<br>\n";
}

echo "<h2>📝 查看日志</h2>\n";
echo "<p>请检查以下日志文件：</p>\n";
echo "<ul>\n";
echo "<li>支付处理日志: /includes/logs/payment_debug_" . date('Y-m-d') . ".log</li>\n";
echo "<li>WebSocket集成日志: /logs/websocket_integration.log</li>\n";
echo "</ul>\n";

echo "<h2>🎯 测试完成</h2>\n";
echo "<p>如果上面的测试都成功，说明函数正常。如果真实支付时没有语音播报，可能是支付回调流程的问题。</p>\n";

?>
