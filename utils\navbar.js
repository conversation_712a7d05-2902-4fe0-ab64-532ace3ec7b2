/**
 * 导航栏工具函数
 * 提供统一的导航栏高度计算和多端适配
 */

// 缓存系统信息
let systemInfo = null;

/**
 * 获取系统信息
 */
export function getSystemInfo() {
  if (!systemInfo) {
    systemInfo = uni.getSystemInfoSync();
  }
  return systemInfo;
}

/**
 * 获取状态栏高度
 */
export function getStatusBarHeight() {
  const info = getSystemInfo();
  return info.statusBarHeight || 20;
}

/**
 * 获取导航栏高度（不包含状态栏）
 */
export function getNavBarHeight() {
  // #ifdef MP-WEIXIN
  // 微信小程序
  const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
  if (menuButtonInfo) {
    return menuButtonInfo.height + (menuButtonInfo.top - getStatusBarHeight()) * 2;
  }
  return 44;
  // #endif
  
  // #ifdef MP-ALIPAY
  // 支付宝小程序
  return 44;
  // #endif
  
  // #ifdef MP-BAIDU
  // 百度小程序
  return 44;
  // #endif
  
  // #ifdef MP-TOUTIAO
  // 字节跳动小程序
  return 44;
  // #endif
  
  // #ifdef H5
  // H5平台
  return 44;
  // #endif
  
  // #ifdef APP-PLUS
  // APP平台
  return 44;
  // #endif
  
  // 默认高度
  return 44;
}

/**
 * 获取导航栏总高度（包含状态栏）
 */
export function getTotalNavBarHeight() {
  return getStatusBarHeight() + getNavBarHeight();
}

/**
 * 获取安全区域信息
 */
export function getSafeAreaInsets() {
  const info = getSystemInfo();
  const safeArea = info.safeArea || {};
  const safeAreaInsets = info.safeAreaInsets || {};
  
  return {
    top: safeAreaInsets.top || safeArea.top || getStatusBarHeight(),
    bottom: safeAreaInsets.bottom || 0,
    left: safeAreaInsets.left || 0,
    right: safeAreaInsets.right || 0
  };
}

/**
 * 判断是否为刘海屏
 */
export function isNotchScreen() {
  const info = getSystemInfo();
  const safeAreaInsets = getSafeAreaInsets();
  
  // 如果状态栏高度大于20px，通常是刘海屏
  return safeAreaInsets.top > 20;
}

/**
 * 获取胶囊按钮信息（仅小程序）
 */
export function getMenuButtonInfo() {
  // #ifdef MP
  try {
    return uni.getMenuButtonBoundingClientRect();
  } catch (e) {
    return null;
  }
  // #endif
  
  // #ifndef MP
  return null;
  // #endif
}

/**
 * 计算导航栏右侧安全距离（避免与胶囊按钮重叠）
 */
export function getNavBarRightSafeDistance() {
  // #ifdef MP-WEIXIN
  const menuButtonInfo = getMenuButtonInfo();
  if (menuButtonInfo) {
    const info = getSystemInfo();
    return info.windowWidth - menuButtonInfo.left;
  }
  // #endif
  
  return 0;
}

/**
 * 获取导航栏样式配置
 */
export function getNavBarStyleConfig() {
  const statusBarHeight = getStatusBarHeight();
  const navBarHeight = getNavBarHeight();
  const totalHeight = getTotalNavBarHeight();
  const safeAreaInsets = getSafeAreaInsets();
  const rightSafeDistance = getNavBarRightSafeDistance();
  
  return {
    statusBarHeight,
    navBarHeight,
    totalHeight,
    safeAreaInsets,
    rightSafeDistance,
    isNotchScreen: isNotchScreen()
  };
}

/**
 * 转换rpx到px
 */
export function rpxToPx(rpx) {
  const info = getSystemInfo();
  return (rpx / 750) * info.windowWidth;
}

/**
 * 转换px到rpx
 */
export function pxToRpx(px) {
  const info = getSystemInfo();
  return (px / info.windowWidth) * 750;
}

/**
 * 获取平台信息
 */
export function getPlatformInfo() {
  const info = getSystemInfo();
  
  return {
    platform: info.platform,
    isIOS: info.platform === 'ios',
    isAndroid: info.platform === 'android',
    isH5: false,
    isMP: false,
    isApp: false,
    // #ifdef H5
    isH5: true,
    // #endif
    // #ifdef MP
    isMP: true,
    // #endif
    // #ifdef APP-PLUS
    isApp: true,
    // #endif
  };
}

/**
 * 设置页面样式（处理导航栏占位）
 */
export function setPageStyle(options = {}) {
  const config = getNavBarStyleConfig();
  const {
    paddingTop = true,
    backgroundColor = '#f5f5f5'
  } = options;
  
  const style = {
    backgroundColor,
    minHeight: '100vh'
  };
  
  if (paddingTop) {
    style.paddingTop = config.totalHeight + 'px';
  }
  
  return style;
}
