import{g as e,af as t,s as n}from"./index-B1Q521gi.js";import{p as r,g as a}from"./request.DGmokXb9.js";import{s as c}from"./order.ZBwgueDH.js";function m(){const n=e("merchantId")||t.merchant.id,a=e("merchantKey")||t.merchant.key,m={pid:n,timestamp:Math.floor(Date.now()/1e3).toString(),sign_type:"MD5"};return m.sign=c(m,a),r("/api/merchant/info",m)}function i(){const n=e("merchantId")||t.merchant.id,r=e("merchantKey")||t.merchant.key;return a("/api.php",{act:"query",pid:n,key:r})}function o(n=10,r=0){const c=e("merchantId")||t.merchant.id,m=e("merchantKey")||t.merchant.key;return a("/api.php",{act:"settle",pid:c,key:m,limit:n,offset:r})}function h(){return m()}function s(e,t){return n("merchantId",e),n("merchantKey",t),Promise.resolve({code:0,message:"保存成功"})}function p(){return{merchantId:e("merchantId")||t.merchant.id,merchantKey:e("merchantKey")||t.merchant.key}}export{p as getMerchantConfig,m as getMerchantInfo,i as getMerchantInfoLegacy,h as getMerchantStats,o as getSettlements,s as saveMerchantConfig};
