import{q as a,u as e,a as t,n as s,o as A,e as l,w as c,f as n,h as d,k as o,l as i,i as m,d as r,v as u}from"./index-B1Q521gi.js";import{_ as f}from"./custom-navbar.DuzuSmPc.js";import{r as p}from"./uni-app.es.DAfa8VxY.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as g,a as h}from"./shanfu.Cee0BMqH.js";import{_ as C}from"./alipay.TVRnAsOv.js";const w=y({components:{PaymentMethods:y({name:"PaymentMethods",data:()=>({}),methods:{navigateToScan(){a({url:"/pages/scan/index"})},scanToPay(){e({onlyFromCamera:!0,success:a=>{console.log("扫码结果：",a),t({title:"扫码成功",icon:"success"})},fail:()=>{t({title:"扫码失败",icon:"none"})}})},navigateToManualPay(){s({url:"/pages/code/index"}),console.log("正在导航到手动收款页面")}}},[["render",function(a,e,t,s,r,u){const f=o,p=i,y=m;return A(),l(y,{class:"payment-methods"},{default:c((()=>[n(y,{class:"payment-methods-cards"},{default:c((()=>[n(y,{class:"payment-method-card qr",onClick:u.navigateToScan},{default:c((()=>[n(f,{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADkAAAA5CAYAAACMGIOFAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKISURBVHgB7ZvBTxNBFMa/t1sqKhcPFjEm0iYlQWOk4US8QKJnJfHoAf4D9eQNqie9qGcPNkbv3uFQbl5IqiZ6qKG9YfXQxkKL0N1x3pSgEhvb7CztDvNLtjs78w79+qZfXl5mCR0obYrxPRezRLiICOD7eB9vIJ9MUu3wGh2eKH4Xs/K2BKHukYMgcq7nZJNjVP499wfFircMoiVEHCmq7O/S/MQFKuw/tyl+9e7Coacwh1rMowxnVInk/1/LFSWYRz6dcOaUyC/fxEsBsQAT8WjOaY+iaTLdIFzcdD7LrSqAcRiKdNspB4bDCTReJGNFmoIVaQpWpClYkaZwLETGoJkPG3sd10bPOPJye4rTgVaRlaqHBy9+dFy/f3sEo9Nu13G6sNs1CLeuDWPmUhxbTYFHr+uB44IQmsjUWAxXUkNqa+qIC0JoIis1T5nL/758t3FBCE3km9Wm/GxqiwvCsTAe4h5PTFM7ks2jtNnquJ46H8PpYeo6Tgey/VHWKnIQYZEDUfGMnCTlssyGzDBn0biK58b0CdyTa8zDV3XcuX5Ka8UTmrv2wsr6T3WFRd8rnqOg7xUP/xDSHPDu0y7Cou8VD4tko2ns+AdzCY2mw/S94mFmLsfVFRYDUfGsrO9g8UlVjRceV7Gq2YT6XvHwHD9vN301/ii3uK14eoRF2m6dKViRpmBFmoIVaQpWpCkYL5KPhTqT7aOSNRiKABVUJgVRDoZChLdKZKuF54ZmM58+S2tKJG9Z4YssDEIIlFseLfL4wHjS59xnMrfLMAAWKLXMT+4ftv/LXWVqs1J9UlpSHtGkJiBy2w3KTCTah+yZjj0G1TEYwlVpT1OIAoTCVh1rmX+8F/ILnOQkdVMIkV0AAAAASUVORK5CYII=",mode:"aspectFit",style:{width:"80rpx",height:"80rpx"}}),n(p,{class:"method-text"},{default:c((()=>[d("收款码")])),_:1})])),_:1},8,["onClick"]),n(y,{class:"payment-method-card scan",onClick:u.scanToPay},{default:c((()=>[n(f,{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADkAAAA5CAYAAACMGIOFAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMCSURBVHgB7ZrPTxNBFMffmxZqIZLW2lYTTWjiCUwQLuqJljMkcjBcPID+A8S7oca7iXrWxMSDxoMmeuFi68kfB4VEvJH2QKxuLS2YVgvdHfct2cb+1OLOhh32kyy7M482+fb75r1pMwgdyOd43OuBMY1DAA44DKFUU2E1fBLT7eLYPFH4qi4iwyXuAHHN6GKyiJgMRvBh0/wexQwP8EF4xjmPg8PRRT3HCi4EY1iisdcM8AGe0t07BxLAAS6Bn1MmJmjM6M/3HJ/XA1IINOEI8cIXdZGejXTd/KZldJHDIB+lUJQFkaooYzwFkqJpmGBMbxMgMXp7GWPgwFbREwgBBocAV6QsuCJlwRUpC65IWTgUIr1gA0qmBjvl1vmhCDMu0QgX+ebxT3j75FfH+ORVP0zMHAGRCP8YP6d2usY/vqyCaCx1klzbWKvVx9Uyh21F6/oaij+98aNh7vTZPrgwZ527lolce1Xtmpbd2PhUaxkfH2Zw5nw/WIFl6fo3x3oln1HBKmyprr5BNIrLSGLPGUpjWqsfXuzP+V4RLpJaxOVbR41nSmly/E/RtB5JtEiEi7w45zfuj65vN4hZf79riB+f9u17Lf8rQlsIOTYy1Q/Ld8stbpGjy/fKhqP0fyIRKjIc8xj3fLZ9ETFT19EizYrbSYQ5L3pNChdJ18R0+8ZO8xR3tEgi/aAC4zO+hh0MORi/NmCs1/T9CohGeHVdf7drCKECQxe5ZqYpFR6qsqKxZTNAm3Bq/qdGvXWBJE50mprYIpIgQXa41g7L1iRtpq1qBfQ+o1M+sArLnKSeeOX2EGwp/7+xjsS8lvZOS9PVrp8zesX9tU4WXJGy4IqUBVekLLgiZYEBQglkhkOJaSqsgsQwD6wYW/2CohUlPX6WDUVZzFiTqsZvgoRwxCTd61/aNhWNTi7HQRLoiPaxKJul53p1xT6cRYQ0SIBuVnqrggvmuOXrt5Lj8x7Gl8CJJ5mpU2iYDJ3AO43THSjm+aRGB++dUJB0cQxhJRjG1+********************************==",mode:"aspectFit",style:{width:"80rpx",height:"80rpx"}}),n(p,{class:"method-text"},{default:c((()=>[d("扫码收款")])),_:1})])),_:1},8,["onClick"]),n(y,{class:"payment-method-card manual",onClick:u.navigateToManualPay},{default:c((()=>[n(f,{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADkAAAA5CAYAAACMGIOFAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJ4SURBVHgB7Zu/axNhGMe/z5smaaDFgEubVpMORnAwPets08Gt0S7udnFV/wIbcFRUcHRwEycRNxdvVEHSiAji0BS1WSMtlCS99+nd0Sul3NAm7yW5t+9neX/kEvLleZ7vey/3HiGEGm9k0U2uCKI8YoCUTh3SWbcyc42wz+nowBOX6KaeMbDiDrOIGUxk81579bjYQ5G1nb/zIiXeuZcWEHMko2qlZ9aCsS+yttssiAR/0kFggJT80Bqffe71fZH17r8aGPPQi5Z0OpaXuqLW/VPWUKBHVowl73sdIZC4DV1huus1ws1XHaMYkP3JW3nBjAI0R+AMYETqghGpC0akLhiRujAGxbz58QXbnTZ6ITdxDsvFElSjVOTTzx9dkV/RL6qFKk3Xb81N9IuK3ziO0kgGaVrOX8aTm3dO81VU3r5Ec7uFKIjEeCZTaYwSZgnRhUhE9rKE7LR3ERXK10kPe/MXrr96jFHB3PH0Sm4yi+VLV/1+cAd00rkoiETkwtRF3Lt2w+9/+P3d//MnnYuCyI0nMJSwuUExMOMZphkZ4+mVYZpMGEM1nkExVOMZlBlRvbO1oerhq4rtUsVN6UeLt6CKZJIKSiNZOai5fljIFaAapTXp1dy0azC9RrN4fsrdcBehGuXGoyKaqjH7SV0wInXBiNQFI1IXzohI4mgeQIwIbbT/u5EkG7rCaFg01xJyz3kPTWGC7bXCylywSdNostOpeq1vPI5Dq26jV21KehCcRfdFWpnphuzwklufDegAY600nnsRDA+XEGtidl06tMTAa8QUBttEolxKz1SPzlPYxf6rE05qkeNybJtEI5lg+wrlQg8c7AN7sAsfrdsOlQAAAABJRU5ErkJggg==",mode:"aspectFit",style:{width:"80rpx",height:"80rpx"}}),n(p,{class:"method-text"},{default:c((()=>[d("手动收款")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})}],["__scopeId","data-v-82e86b20"]]),RecentPayments:y({name:"RecentPayments",data:()=>({})},[["render",function(a,e,t,s,r,u){const f=i,p=m,y=o;return A(),l(p,{class:"recent-payments"},{default:c((()=>[n(p,{class:"recent-payments-header"},{default:c((()=>[n(f,{class:"recent-payments-title"},{default:c((()=>[d("最近收款")])),_:1}),n(f,{class:"view-all"},{default:c((()=>[d("查看全部")])),_:1})])),_:1}),n(p,{class:"payment-list"},{default:c((()=>[n(p,{class:"payment-item"},{default:c((()=>[n(p,{class:"payment-item-left"},{default:c((()=>[n(p,{class:"payment-icon wechat"},{default:c((()=>[n(y,{src:g,mode:"aspectFit",style:{width:"40rpx",height:"40rpx"}})])),_:1}),n(p,{class:"payment-details"},{default:c((()=>[n(f,{class:"payment-name"},{default:c((()=>[d("微信支付")])),_:1}),n(f,{class:"payment-time"},{default:c((()=>[d("10:23 "),n(f,{class:"payment-category"},{default:c((()=>[d("扫码")])),_:1})])),_:1})])),_:1})])),_:1}),n(f,{class:"payment-amount"},{default:c((()=>[d("+ ¥ 128.00")])),_:1})])),_:1}),n(p,{class:"payment-item"},{default:c((()=>[n(p,{class:"payment-item-left"},{default:c((()=>[n(p,{class:"payment-icon alipay"},{default:c((()=>[n(y,{src:C,mode:"aspectFit",style:{width:"40rpx",height:"40rpx"}})])),_:1}),n(p,{class:"payment-details"},{default:c((()=>[n(f,{class:"payment-name"},{default:c((()=>[d("支付宝")])),_:1}),n(f,{class:"payment-time"},{default:c((()=>[d("09:45 "),n(f,{class:"payment-category"},{default:c((()=>[d("收银台")])),_:1})])),_:1})])),_:1})])),_:1}),n(f,{class:"payment-amount"},{default:c((()=>[d("+ ¥ 85.50")])),_:1})])),_:1}),n(p,{class:"payment-item"},{default:c((()=>[n(p,{class:"payment-item-left"},{default:c((()=>[n(p,{class:"payment-icon cloud"},{default:c((()=>[n(y,{src:h,mode:"aspectFit",style:{width:"40rpx",height:"40rpx"}})])),_:1}),n(p,{class:"payment-details"},{default:c((()=>[n(f,{class:"payment-name"},{default:c((()=>[d("云闪付")])),_:1}),n(f,{class:"payment-time"},{default:c((()=>[d("09:12 "),n(f,{class:"payment-category"},{default:c((()=>[d("扫码")])),_:1})])),_:1})])),_:1})])),_:1}),n(f,{class:"payment-amount"},{default:c((()=>[d("+ ¥ 299.00")])),_:1})])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-431ff72a"]]),CustomNavbar:f},data:()=>({title:"商家收款"}),onLoad(){},methods:{navigateToMessage(){s({url:"/pages/xiaoxi/index"})}},errorCaptured:(a,e,t)=>(console.error(`组件渲染错误: ${t}`,a),!1)},[["render",function(a,e,t,s,y,g){const h=i,C=m,w=o,x=p(r("custom-navbar"),f),R=u("payment-methods"),I=u("recent-payments");return A(),l(C,{class:"container"},{default:c((()=>[n(x,{title:"商家收款","show-back":!1,shadow:!0},{default:c((()=>[n(C,{style:{display:"flex","flex-direction":"column","align-items":"center"}},{default:c((()=>[n(h,{style:{"font-size":"32rpx",color:"#fff"}},{default:c((()=>[d("你好，商家名称")])),_:1}),n(h,{style:{"font-size":"24rpx",color:"#fff"}},{default:c((()=>[d("今日是个收款好日子")])),_:1})])),_:1})])),right:c((()=>[n(C,{class:"notification",onClick:g.navigateToMessage},{default:c((()=>[n(h,{class:"notification-badge"},{default:c((()=>[d("2")])),_:1}),n(w,{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAC7SURBVHgBpZThEcIgDIWDEzACG9QNZIRuUDdwBN1EncAROoLnBLiBIzzDtSpHhRL47t6ftvkulADRCgAuHMfZUgv4caQauHDPGQOR7+rMMaUCw7kjz1AicijD0spySnE50QgZ9lO7iVyWZHQpkRS9EHGbmhoIO6qZXPNP1JGcfvEE5fMTM4SSE+p5wR8byIYwhWvd/i9qXtqO2nio1BtM10V8yp9KqStJYJGef2TIgWrgwh7TWHjhLfftGwMw2afJROJWAAAAAElFTkSuQmCC",mode:"aspectFit",style:{width:"48rpx",height:"48rpx"}})])),_:1},8,["onClick"])])),_:1}),n(C,{class:"today-income"},{default:c((()=>[n(h,{class:"today-income-title"},{default:c((()=>[d("今日收款(元)")])),_:1}),n(C,{class:"today-income-amount"},{default:c((()=>[n(h,{class:"amount"},{default:c((()=>[d("¥ 3,286.50")])),_:1}),n(h,{class:"growth"},{default:c((()=>[d("↑ 12.5%")])),_:1})])),_:1})])),_:1}),n(C,{class:"yesterday-month"},{default:c((()=>[n(C,{class:"income-card"},{default:c((()=>[n(h,{class:"income-card-title"},{default:c((()=>[d("昨日收款(元)")])),_:1}),n(h,{class:"income-card-amount"},{default:c((()=>[d("¥2,980.20")])),_:1})])),_:1}),n(C,{class:"income-card"},{default:c((()=>[n(h,{class:"income-card-title"},{default:c((()=>[d("本月总收款(元)")])),_:1}),n(h,{class:"income-card-amount"},{default:c((()=>[d("¥42,568.90")])),_:1})])),_:1})])),_:1}),n(R),n(I)])),_:1})}],["__scopeId","data-v-c361c7c7"]]);export{w as default};
