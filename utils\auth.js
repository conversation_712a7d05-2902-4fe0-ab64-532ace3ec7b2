// utils/auth.js - 统一的登录状态管理工具
import { request } from '@/utils/request';

/**
 * 检查登录状态
 * @returns {Promise<boolean>} 是否已登录
 */
export async function checkLoginStatus() {
  try {
    // 1. 检查本地token
    const token = uni.getStorageSync('user_token');
    if (!token) {
      console.log('❌ 未找到登录token');
      return false;
    }

    // 2. 验证token有效性 - 调用用户信息接口
    const response = await request({
      url: '/user/ajax2.php?act=getcount',
      method: 'POST',
      data: {},
      loading: false, // 不显示加载提示
      showError: false // 不显示错误提示
    });

    console.log('🔍 登录状态检测响应:', response);

    // 3. 判断响应结果
    if (response && response.code === 0) {
      console.log('✅ 登录状态有效');
      return true;
    } else if (response && response.code === -3) {
      console.log('❌ 登录状态无效，需要重新登录');
      clearLoginInfo();
      return false;
    } else {
      console.log('❌ 登录状态检测失败:', response);
      return false;
    }
  } catch (error) {
    console.error('❌ 登录状态检测异常:', error);
    
    // 如果是-3错误（未登录），清除登录信息
    if (error && error.code === -3) {
      clearLoginInfo();
      return false;
    }
    
    // 其他错误，可能是网络问题，暂时认为已登录
    return true;
  }
}

/**
 * 清除登录信息
 */
export function clearLoginInfo() {
  console.log('🧹 清除登录信息');
  uni.removeStorageSync('user_token');
  uni.removeStorageSync('user_uid');
  uni.removeStorageSync('csrf_token');
  uni.removeStorageSync('session_id');
  uni.removeStorageSync('user_info');
}

/**
 * 跳转到登录页
 * @param {string} redirectUrl - 登录成功后的跳转地址
 */
export function redirectToLogin(redirectUrl = '') {
  console.log('🔄 跳转到登录页, 回调地址:', redirectUrl);
  
  // 清除登录信息
  clearLoginInfo();
  
  // 构建登录页URL
  let loginUrl = '/pages/login/index';
  if (redirectUrl) {
    loginUrl += `?redirect=${encodeURIComponent(redirectUrl)}`;
  }
  
  // 跳转到登录页
  uni.reLaunch({
    url: loginUrl
  });
}

/**
 * 页面登录状态检测混入
 * 在页面的onLoad中调用，自动处理登录状态
 */
export const loginCheckMixin = {
  methods: {
    /**
     * 检查登录状态并处理
     * @param {boolean} autoRedirect - 未登录时是否自动跳转到登录页，默认true
     * @returns {Promise<boolean>} 是否已登录
     */
    async checkAndHandleLogin(autoRedirect = true) {
      try {
        const isLogin = await checkLoginStatus();
        
        if (!isLogin && autoRedirect) {
          // 获取当前页面路径作为回调地址
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          const currentRoute = currentPage ? currentPage.route : '';
          
          redirectToLogin(currentRoute);
          return false;
        }
        
        return isLogin;
      } catch (error) {
        console.error('❌ 登录状态检测失败:', error);
        
        if (autoRedirect) {
          redirectToLogin();
        }
        
        return false;
      }
    }
  }
};

/**
 * 获取用户信息
 * @returns {Promise<Object>} 用户信息
 */
export async function getUserInfo() {
  try {
    const response = await request({
      url: '/user/ajax2.php?act=getcount',
      method: 'POST',
      data: {},
      loading: false
    });

    if (response && response.code === 0) {
      // 保存用户信息到本地
      uni.setStorageSync('user_info', response);
      return response;
    } else {
      throw new Error(response?.msg || '获取用户信息失败');
    }
  } catch (error) {
    console.error('❌ 获取用户信息失败:', error);
    throw error;
  }
}

/**
 * 检查是否已登录（同步方法）
 * 只检查本地token，不验证服务器
 * @returns {boolean} 是否有登录token
 */
export function hasLoginToken() {
  const token = uni.getStorageSync('user_token');
  return !!token;
}

/**
 * 获取当前用户ID（同步方法）
 * @returns {string|null} 用户ID，未登录返回null
 */
export function getCurrentUserId() {
  const uid = uni.getStorageSync('user_uid');
  return uid || null;
}

/**
 * 获取当前用户的WebSocket频道名
 * @returns {string|null} 频道名，未登录返回null
 */
export function getCurrentUserChannel() {
  const uid = getCurrentUserId();
  return uid ? `payment_merchant_${uid}` : null;
}
