const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// 读取配置文件
const configPath = './epay_release_99009/user/assets/js/config.json';
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

// 目标目录
const targetDir = './epay_release_99009/assets/img/styles';

// 确保目标目录存在
if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
}

// 下载图片的函数
function downloadImage(url, filename) {
    return new Promise((resolve, reject) => {
        const protocol = url.startsWith('https:') ? https : http;
        const filePath = path.join(targetDir, filename);
        
        console.log(`正在下载: ${url}`);
        console.log(`保存到: ${filePath}`);
        
        protocol.get(url, (response) => {
            if (response.statusCode === 200) {
                const fileStream = fs.createWriteStream(filePath);
                response.pipe(fileStream);
                
                fileStream.on('finish', () => {
                    fileStream.close();
                    console.log(`✅ 下载完成: ${filename}`);
                    resolve();
                });
                
                fileStream.on('error', (err) => {
                    fs.unlink(filePath, () => {}); // 删除不完整的文件
                    reject(err);
                });
            } else {
                reject(new Error(`HTTP ${response.statusCode}: ${url}`));
            }
        }).on('error', (err) => {
            reject(err);
        });
    });
}

// 主函数
async function downloadAllImages() {
    console.log('开始下载样式图片...');
    
    const newConfig = { ...config };
    
    for (const [styleName, styleConfig] of Object.entries(config)) {
        if (styleConfig.url && styleConfig.url.startsWith('https://img.alicdn.com')) {
            try {
                // 从URL中提取文件扩展名
                const urlParts = styleConfig.url.split('.');
                const extension = urlParts[urlParts.length - 1].split('?')[0]; // 去掉查询参数
                const filename = `${styleName}.${extension}`;
                
                // 下载图片
                await downloadImage(styleConfig.url, filename);
                
                // 更新配置中的URL为本地路径
                newConfig[styleName].url = `/assets/img/styles/${filename}`;
                
            } catch (error) {
                console.error(`❌ 下载失败 ${styleName}:`, error.message);
            }
        } else {
            console.log(`⏭️  跳过 ${styleName}: 已经是本地路径或无效URL`);
        }
    }
    
    // 保存更新后的配置文件
    const newConfigPath = './epay_release_99009/user/assets/js/config_local.json';
    fs.writeFileSync(newConfigPath, JSON.stringify(newConfig, null, 4));
    console.log(`✅ 配置文件已保存到: ${newConfigPath}`);
    
    console.log('🎉 所有图片下载完成！');
}

// 运行
downloadAllImages().catch(console.error);
