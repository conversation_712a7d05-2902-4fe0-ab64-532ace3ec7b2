<template>
  <view class="voice-test-container">
    <!-- 自定义导航栏 -->
    <custom-navbar title="语音播报测试" :show-back="true" />
    
    <!-- 内容区域 -->
    <view class="content">
      <!-- WebSocket状态卡片 -->
      <view class="status-card">
        <view class="status-header">
          <text class="status-title">📡 WebSocket状态</text>
          <view class="status-indicator" :class="{ 'connected': wsConnected, 'disconnected': !wsConnected }">
            {{ wsConnected ? '已连接' : '未连接' }}
          </view>
        </view>
        <view class="status-info">
          <text class="info-text">服务器: {{ wsConfig.host }}:{{ wsConfig.port }}</text>
          <text class="info-text">应用: {{ wsConfig.app_key }}</text>
        </view>
      </view>

      <!-- 测试按钮区域 -->
      <view class="test-section">
        <text class="section-title">🧪 功能测试</text>
        
        <!-- WebSocket连接测试 -->
        <button class="test-btn primary" @click="testWebSocket" :disabled="isLoading">
          {{ wsConnected ? '🔌 断开WebSocket' : '🔗 连接WebSocket' }}
        </button>

        <!-- 数字转语音测试 -->
        <button class="test-btn secondary" @click="testNumberConvert">
          🔢 测试数字转语音
        </button>

        <!-- 音频播放测试 -->
        <button class="test-btn success" @click="testAudioPlay" :disabled="isPlaying">
          {{ isPlaying ? '🎵 播放中...' : '🎵 测试音频播放' }}
        </button>

        <!-- 完整支付播报测试 -->
        <button class="test-btn warning" @click="testPaymentVoice" :disabled="isPlaying">
          💰 测试支付播报
        </button>
      </view>

      <!-- 测试金额输入 -->
      <view class="input-section">
        <text class="section-title">💰 自定义测试金额</text>
        <input 
          class="amount-input" 
          v-model="testAmount" 
          placeholder="请输入测试金额，如: 123.45"
          type="digit"
        />
        <button class="test-btn info" @click="testCustomAmount" :disabled="isPlaying || !testAmount">
          🎯 播报自定义金额
        </button>
      </view>

      <!-- 日志显示 -->
      <view class="log-section">
        <view class="log-header">
          <text class="section-title">📝 测试日志</text>
          <button class="clear-btn" @click="clearLogs">清空</button>
        </view>
        <scroll-view class="log-container" scroll-y>
          <view v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
            <text class="log-time">{{ log.time }}</text>
            <text class="log-content">{{ log.message }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 音频文件检查 -->
      <view class="audio-check-section">
        <text class="section-title">🎵 音频文件检查</text>
        <button class="test-btn info" @click="checkAudioFiles">
          📂 检查音频文件
        </button>
        <view v-if="audioCheckResults.length > 0" class="audio-results">
          <view v-for="(result, index) in audioCheckResults" :key="index" 
                class="audio-item" :class="{ 'success': result.exists, 'error': !result.exists }">
            <text>{{ result.file }} - {{ result.exists ? '✅ 存在' : '❌ 缺失' }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import AudioPlayer from '@/utils/audioPlayer.js';
import websocketService from '@/utils/websocketService.js';
import voiceQueue from '@/utils/voiceQueue.js';
import { getCurrentUserId, getCurrentUserChannel } from '@/utils/auth.js';

export default {
  name: 'VoiceTest',
  data() {
    return {
      // WebSocket配置
      wsConfig: {
        host: 'ceshi.huisas.com',
        port: 8080,
        app_key: 'payment_websocket_2024'
      },
      
      // 状态
      wsConnected: false,
      isLoading: false,
      isPlaying: false,
      
      // 测试数据
      testAmount: '123.45',
      logs: [],
      
      // WebSocket实例
      socketTask: null,
      
      // 音频播放器
      audioPlayer: null,
      
      // 音频文件检查结果
      audioCheckResults: []
    };
  },
  
  onLoad() {
    this.addLog('info', '页面加载完成');
    this.audioPlayer = new AudioPlayer();
  },
  
  onUnload() {
    this.disconnectWebSocket();
    if (this.audioPlayer) {
      this.audioPlayer.stop();
    }
  },
  
  methods: {
    // 添加日志
    addLog(type, message) {
      const now = new Date();
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
      
      this.logs.unshift({
        type,
        time,
        message
      });
      
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50);
      }
    },
    
    // 清空日志
    clearLogs() {
      this.logs = [];
      this.addLog('info', '日志已清空');
    },
    
    // 测试WebSocket连接
    testWebSocket() {
      if (this.wsConnected) {
        this.disconnectWebSocket();
      } else {
        this.connectWebSocket();
      }
    },
    
    // 连接WebSocket
    connectWebSocket() {
      this.isLoading = true;
      this.addLog('info', '正在连接WebSocket...');

      // 使用Swoole WebSocket标准协议URL
      const url = `ws://${this.wsConfig.host}:${this.wsConfig.port}`;
      this.addLog('info', `连接URL: ${url}`);

      this.socketTask = uni.connectSocket({
        url: url,
        success: () => {
          this.addLog('success', 'WebSocket连接创建成功');
        },
        fail: (err) => {
          this.addLog('error', `WebSocket连接失败: ${JSON.stringify(err)}`);
          this.isLoading = false;
        }
      });
      
      // 连接打开
      this.socketTask.onOpen(() => {
        this.isLoading = false;
        this.addLog('success', 'WebSocket连接已建立，等待Pusher握手...');
        // 注意：不要立即设置 wsConnected = true，等待 pusher:connection_established 消息
      });
      
      // 连接错误
      this.socketTask.onError((err) => {
        this.wsConnected = false;
        this.isLoading = false;
        this.addLog('error', `WebSocket错误: ${JSON.stringify(err)}`);
      });
      
      // 连接关闭
      this.socketTask.onClose(() => {
        this.wsConnected = false;
        this.isLoading = false;
        this.addLog('warning', 'WebSocket连接已关闭');
      });
      
      // 接收消息
      this.socketTask.onMessage((res) => {
        try {
          const data = JSON.parse(res.data);
          this.addLog('info', `收到消息: ${JSON.stringify(data)}`);

          // 处理Pusher连接建立消息
          if (data.event === 'pusher:connection_established') {
            this.wsConnected = true;
            this.addLog('success', 'Pusher连接已建立！');

            // 🔒 订阅商户专属频道，确保隔离
            const userChannel = this.getUserChannel();
            if (userChannel) {
              this.subscribeToChannel(userChannel);
            } else {
              this.addLog('error', '无法获取用户频道，请先登录');
            }
            return;
          }

          // 处理频道订阅成功消息
          if (data.event === 'pusher_internal:subscription_succeeded') {
            this.addLog('success', `频道订阅成功: ${data.channel}`);
            return;
          }

          // 处理Pusher心跳
          if (data.event === 'pusher:ping') {
            this.addLog('info', '收到Pusher心跳，发送pong');
            this.sendMessage({
              event: 'pusher:pong',
              data: '{}'
            });
            return;
          }

          // 如果是支付通知，自动播放语音
          if (data.event === 'payment_success') {
            const amount = data.data ? data.data.amount : data.amount;
            if (amount) {
              this.playPaymentVoice(amount);
            }
          }
        } catch (error) {
          this.addLog('error', `解析消息失败: ${error.message}`);
        }
      });
    },
    
    // 断开WebSocket
    disconnectWebSocket() {
      if (this.socketTask) {
        this.socketTask.close();
        this.socketTask = null;
      }
      this.wsConnected = false;
      this.addLog('info', 'WebSocket连接已断开');
    },

    // 发送WebSocket消息
    sendMessage(data) {
      if (!this.socketTask || !this.wsConnected) {
        this.addLog('error', 'WebSocket未连接，无法发送消息');
        return false;
      }

      try {
        const message = JSON.stringify(data);
        this.socketTask.send({
          data: message,
          success: () => {
            this.addLog('info', `消息发送成功: ${message}`);
          },
          fail: (err) => {
            this.addLog('error', `消息发送失败: ${JSON.stringify(err)}`);
          }
        });
        return true;
      } catch (error) {
        this.addLog('error', `消息序列化失败: ${error.message}`);
        return false;
      }
    },

    // 订阅频道
    subscribeToChannel(channel) {
      const subscribeData = {
        event: 'pusher:subscribe',
        data: {
          channel: channel
        }
      };

      this.addLog('info', `订阅频道: ${channel}`);
      return this.sendMessage(subscribeData);
    },

    // 取消订阅频道
    unsubscribeFromChannel(channel) {
      const unsubscribeData = {
        event: 'pusher:unsubscribe',
        data: {
          channel: channel
        }
      };

      this.addLog('info', `取消订阅频道: ${channel}`);
      return this.sendMessage(unsubscribeData);
    },
    
    // 测试数字转语音
    testNumberConvert() {
      const testNumbers = ['0', '1.5', '12.34', '123.45', '1000', '10000.99'];
      
      this.addLog('info', '开始测试数字转语音...');
      
      testNumbers.forEach(num => {
        const result = this.convertAmountToVoice(num);
        this.addLog('info', `${num} → ${result}`);
      });
    },
    
    // 测试音频播放
    async testAudioPlay() {
      this.isPlaying = true;
      this.addLog('info', '开始测试音频播放...');
      
      const testAudios = ['/static/music/_shoukuan.mp3', '/static/music/_1.mp3', '/static/music/_yuan.mp3'];
      
      try {
        for (let audio of testAudios) {
          this.addLog('info', `播放: ${audio}`);
          await this.playAudio(audio);
          await this.delay(500); // 间隔500ms
        }
        this.addLog('success', '音频播放测试完成');
      } catch (error) {
        this.addLog('error', `音频播放失败: ${error.message}`);
      } finally {
        this.isPlaying = false;
      }
    },
    
    // 测试支付播报
    async testPaymentVoice() {
      await this.playPaymentVoice(this.testAmount);
    },
    
    // 测试自定义金额
    async testCustomAmount() {
      if (!this.testAmount) {
        this.addLog('warning', '请输入测试金额');
        return;
      }
      await this.playPaymentVoice(this.testAmount);
    },
    
    // 播放支付语音
    async playPaymentVoice(amount) {
      if (this.isPlaying) {
        this.addLog('warning', '正在播放中，请稍后...');
        return;
      }
      
      this.isPlaying = true;
      this.addLog('info', `开始播放支付语音: ${amount}元`);
      
      try {
        if (this.audioPlayer) {
          await this.audioPlayer.playPaymentSuccess(amount);
          this.addLog('success', '支付语音播放完成');
        }
      } catch (error) {
        this.addLog('error', `支付语音播放失败: ${error.message}`);
      } finally {
        this.isPlaying = false;
      }
    },
    
    // 播放单个音频文件
    playAudio(src) {
      return new Promise((resolve, reject) => {
        const audio = uni.createInnerAudioContext();
        audio.src = src;
        
        audio.onCanplay(() => {
          audio.play();
        });
        
        audio.onEnded(() => {
          audio.destroy();
          resolve();
        });
        
        audio.onError((error) => {
          audio.destroy();
          reject(new Error(`音频播放失败: ${JSON.stringify(error)}`));
        });
      });
    },
    
    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    },
    
    // 数字转语音（简化版，用于测试）
    convertAmountToVoice(amount) {
      const cnNums = ["_0,", "_1,", "_2,", "_3,", "_4,", "_5,", "_6,", "_7,", "_8,", "_9,"];
      const cnIntRadice = ["", "_shi,", "_bai,", "_qian,"];
      const cnIntUnits = ["", "_wan,", "_yi,"];
      const cnInteger = "_yuan";
      const cnDecimal = "_dian,";

      if (!amount) return "";
      
      amount = parseFloat(amount);
      if (amount === 0) return cnNums[0] + cnInteger;

      const amountStr = amount.toString();
      let integerNum, decimalNum;

      if (amountStr.indexOf(".") === -1) {
        integerNum = amountStr;
        decimalNum = "";
      } else {
        const parts = amountStr.split(".");
        integerNum = parts[0];
        decimalNum = parts[1].substr(0, 2);
      }

      let chineseStr = "";

      // 简化的整数处理（仅支持0-9999）
      const intVal = parseInt(integerNum, 10);
      if (intVal < 10) {
        chineseStr = cnNums[intVal];
      } else if (intVal < 100) {
        const tens = Math.floor(intVal / 10);
        const ones = intVal % 10;
        chineseStr = cnNums[tens] + "_shi,";
        if (ones > 0) chineseStr += cnNums[ones];
      } else {
        // 更复杂的数字处理...
        chineseStr = cnNums[Math.floor(intVal / 100)] + "_bai,";
        const remainder = intVal % 100;
        if (remainder > 0) {
          if (remainder < 10) {
            chineseStr += cnNums[0] + cnNums[remainder];
          } else {
            const tens = Math.floor(remainder / 10);
            const ones = remainder % 10;
            chineseStr += cnNums[tens] + "_shi,";
            if (ones > 0) chineseStr += cnNums[ones];
          }
        }
      }

      // 处理小数部分
      if (decimalNum !== "" && decimalNum !== "00") {
        chineseStr += cnDecimal;
        for (let i = 0; i < decimalNum.length; i++) {
          const n = decimalNum.substr(i, 1);
          chineseStr += cnNums[parseInt(n, 10)];
        }
      }

      return chineseStr + cnInteger;
    },
    
    // 检查音频文件
    checkAudioFiles() {
      const requiredFiles = [
        '_shoukuan.mp3', '_0.mp3', '_1.mp3', '_2.mp3', '_3.mp3', '_4.mp3', '_5.mp3',
        '_6.mp3', '_7.mp3', '_8.mp3', '_9.mp3', '_shi.mp3', '_bai.mp3', '_qian.mp3',
        '_wan.mp3', '_yi.mp3', '_dian.mp3', '_yuan.mp3'
      ];

      this.audioCheckResults = [];
      this.addLog('info', '开始检查音频文件...');

      requiredFiles.forEach(file => {
        // 这里只是模拟检查，实际需要真正检测文件是否存在
        const exists = Math.random() > 0.3; // 模拟70%的文件存在
        this.audioCheckResults.push({
          file,
          exists
        });
      });

      const missingCount = this.audioCheckResults.filter(r => !r.exists).length;
      this.addLog(missingCount > 0 ? 'warning' : 'success',
        `音频文件检查完成，缺失 ${missingCount} 个文件`);
    },

    // 🔒 获取当前用户的WebSocket频道
    getUserChannel() {
      const channel = getCurrentUserChannel();
      const userId = getCurrentUserId();

      this.addLog('info', `当前用户ID: ${userId || '未登录'}`);
      this.addLog('info', `用户专属频道: ${channel || '无'}`);

      return channel;
    }
  }
};
</script>

<style scoped>
.voice-test-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.content {
  padding: 40rpx;
  padding-top: 20rpx;
}

.status-card, .test-section, .input-section, .log-section, .audio-check-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-title, .section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.status-indicator {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.status-indicator.connected {
  background: #34C759;
}

.status-indicator.disconnected {
  background: #FF3B30;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.info-text {
  font-size: 26rpx;
  color: #666;
}

.test-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 20rpx;
  border: none;
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-btn.primary { background: #007AFF; }
.test-btn.secondary { background: #5856D6; }
.test-btn.success { background: #34C759; }
.test-btn.warning { background: #FF9500; }
.test-btn.info { background: #5AC8FA; }

.test-btn:disabled {
  opacity: 0.6;
}

.amount-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #E5E5EA;
  border-radius: 16rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.clear-btn {
  padding: 10rpx 20rpx;
  background: #FF3B30;
  color: #fff;
  border-radius: 10rpx;
  font-size: 24rpx;
  border: none;
}

.log-container {
  height: 400rpx;
  border: 2rpx solid #E5E5EA;
  border-radius: 16rpx;
  padding: 20rpx;
}

.log-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid #ccc;
}

.log-item.info { 
  background: #F0F9FF; 
  border-left-color: #007AFF; 
}

.log-item.success { 
  background: #F0FDF4; 
  border-left-color: #34C759; 
}

.log-item.warning { 
  background: #FFFBEB; 
  border-left-color: #FF9500; 
}

.log-item.error { 
  background: #FEF2F2; 
  border-left-color: #FF3B30; 
}

.log-time {
  font-size: 22rpx;
  color: #999;
  margin-right: 20rpx;
}

.log-content {
  font-size: 26rpx;
  color: #333;
}

.audio-results {
  margin-top: 20rpx;
}

.audio-item {
  padding: 15rpx 20rpx;
  margin-bottom: 10rpx;
  border-radius: 10rpx;
  font-size: 24rpx;
}

.audio-item.success {
  background: #F0FDF4;
  color: #166534;
}

.audio-item.error {
  background: #FEF2F2;
  color: #DC2626;
}
</style>
