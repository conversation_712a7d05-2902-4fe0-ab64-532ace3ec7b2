<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket语音播报测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .status.connecting { background: #d1ecf1; color: #0c5460; }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #6c757d; cursor: not-allowed; }
        .btn.danger { background: #dc3545; }
        .btn.success { background: #28a745; }
        .btn.warning { background: #ffc107; color: #212529; }
        .log {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-error { color: #ff4444; }
        .log-warning { color: #ffaa00; }
        .log-success { color: #44ff44; }
        .log-info { color: #44aaff; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 WebSocket语音播报测试</h1>
        <p>测试后端WebSocket服务连接和语音播报功能</p>
        
        <div id="status" class="status disconnected">
            🔴 未连接 - 点击连接按钮开始测试
        </div>
        
        <div>
            <button id="connectBtn" class="btn">连接WebSocket</button>
            <button id="disconnectBtn" class="btn danger" disabled>断开连接</button>
            <button id="testVoiceBtn" class="btn success" disabled>测试语音</button>
            <button id="simulatePaymentBtn" class="btn warning" disabled>模拟支付</button>
            <button id="clearLogBtn" class="btn">清空日志</button>
        </div>
    </div>

    <div class="container">
        <h3>📊 服务器统计</h3>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="totalConnections">0</div>
                <div class="stat-label">总连接数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="currentConnections">0</div>
                <div class="stat-label">当前连接</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="totalMessages">0</div>
                <div class="stat-label">总消息数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="paymentNotifications">0</div>
                <div class="stat-label">支付通知</div>
            </div>
        </div>
        <button id="refreshStatsBtn" class="btn">刷新统计</button>
    </div>

    <div class="container">
        <h3>📝 实时日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        class WebSocketTester {
            constructor() {
                this.ws = null;
                this.wsUrl = 'ws://ceshi.huisas.com:8080';
                this.isConnected = false;
                this.isConnecting = false;
                this.heartbeatTimer = null;
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                
                this.userInfo = {
                    uid: '1000',
                    merchant_id: 'test_merchant',
                    token: 'test_token_123'
                };
                
                this.initElements();
                this.bindEvents();
                this.addLog('🚀 WebSocket测试器初始化完成', 'info');
                this.refreshServerStats();
            }
            
            initElements() {
                this.statusEl = document.getElementById('status');
                this.connectBtn = document.getElementById('connectBtn');
                this.disconnectBtn = document.getElementById('disconnectBtn');
                this.testVoiceBtn = document.getElementById('testVoiceBtn');
                this.simulatePaymentBtn = document.getElementById('simulatePaymentBtn');
                this.clearLogBtn = document.getElementById('clearLogBtn');
                this.refreshStatsBtn = document.getElementById('refreshStatsBtn');
                this.logEl = document.getElementById('log');
            }
            
            bindEvents() {
                this.connectBtn.addEventListener('click', () => this.connect());
                this.disconnectBtn.addEventListener('click', () => this.disconnect());
                this.testVoiceBtn.addEventListener('click', () => this.testVoice());
                this.simulatePaymentBtn.addEventListener('click', () => this.simulatePayment());
                this.clearLogBtn.addEventListener('click', () => this.clearLog());
                this.refreshStatsBtn.addEventListener('click', () => this.refreshServerStats());
            }
            
            updateStatus(status, message) {
                this.statusEl.className = `status ${status}`;
                const icons = {
                    connected: '🟢',
                    connecting: '🟡', 
                    disconnected: '🔴',
                    error: '❌'
                };
                this.statusEl.textContent = `${icons[status]} ${message}`;
                
                // 更新按钮状态
                this.connectBtn.disabled = this.isConnected || this.isConnecting;
                this.disconnectBtn.disabled = !this.isConnected;
                this.testVoiceBtn.disabled = !this.isConnected;
                this.simulatePaymentBtn.disabled = !this.isConnected;
            }
            
            connect() {
                if (this.isConnected || this.isConnecting) return;
                
                this.isConnecting = true;
                this.updateStatus('connecting', '连接中...');
                this.addLog(`🔗 正在连接到 ${this.wsUrl}`, 'info');
                
                try {
                    this.ws = new WebSocket(this.wsUrl);
                    
                    this.ws.onopen = () => {
                        this.isConnected = true;
                        this.isConnecting = false;
                        this.reconnectAttempts = 0;
                        this.updateStatus('connected', 'WebSocket已连接');
                        this.addLog('✅ WebSocket连接成功！', 'success');
                        
                        // 发送认证
                        this.authenticate();
                        
                        // 开始心跳
                        this.startHeartbeat();
                    };
                    
                    this.ws.onmessage = (event) => {
                        this.addLog(`📨 收到消息: ${event.data}`, 'info');
                        this.handleMessage(event.data);
                    };
                    
                    this.ws.onclose = (event) => {
                        this.isConnected = false;
                        this.isConnecting = false;
                        this.updateStatus('disconnected', '连接已关闭');
                        this.addLog(`🔌 连接关闭: code=${event.code}, reason=${event.reason}`, 'warning');
                        
                        if (this.heartbeatTimer) {
                            clearInterval(this.heartbeatTimer);
                            this.heartbeatTimer = null;
                        }
                        
                        // 尝试重连
                        this.attemptReconnect();
                    };
                    
                    this.ws.onerror = (error) => {
                        this.isConnected = false;
                        this.isConnecting = false;
                        this.updateStatus('error', '连接错误');
                        this.addLog(`❌ WebSocket错误: ${error.message || '未知错误'}`, 'error');
                    };
                    
                } catch (error) {
                    this.isConnecting = false;
                    this.updateStatus('error', '连接失败');
                    this.addLog(`❌ 连接失败: ${error.message}`, 'error');
                }
            }
            
            disconnect() {
                if (this.ws) {
                    this.ws.close();
                    this.ws = null;
                }
                this.isConnected = false;
                this.isConnecting = false;
                this.updateStatus('disconnected', '已断开连接');
                this.addLog('🔌 主动断开连接', 'info');
                
                if (this.heartbeatTimer) {
                    clearInterval(this.heartbeatTimer);
                    this.heartbeatTimer = null;
                }
            }
            
            authenticate() {
                const authMessage = {
                    type: 'auth',
                    uid: this.userInfo.uid,
                    merchant_id: this.userInfo.merchant_id,
                    token: this.userInfo.token,
                    timestamp: Date.now()
                };
                
                this.sendMessage(authMessage);
                this.addLog('🔐 发送认证信息', 'info');
            }
            
            sendMessage(message) {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    const jsonData = JSON.stringify(message);
                    this.ws.send(jsonData);
                    this.addLog(`📤 发送: ${jsonData}`, 'info');
                    return true;
                } else {
                    this.addLog('❌ WebSocket未连接，无法发送消息', 'error');
                    return false;
                }
            }
            
            handleMessage(data) {
                try {
                    const message = JSON.parse(data);
                    
                    switch (message.type) {
                        case 'auth_success':
                            this.addLog('✅ 认证成功', 'success');
                            this.subscribeToPayments();
                            break;
                            
                        case 'auth_failed':
                            this.addLog(`❌ 认证失败: ${message.message}`, 'error');
                            break;
                            
                        case 'payment':
                            this.handlePaymentNotification(message);
                            break;
                            
                        case 'pong':
                            this.addLog('💓 心跳响应', 'info');
                            break;
                            
                        default:
                            this.addLog(`📨 未知消息类型: ${message.type}`, 'warning');
                    }
                } catch (error) {
                    this.addLog(`❌ 消息解析失败: ${error.message}`, 'error');
                }
            }
            
            subscribeToPayments() {
                const subscribeMessage = {
                    type: 'subscribe',
                    channel: 'payment',
                    uid: this.userInfo.uid
                };
                
                this.sendMessage(subscribeMessage);
                this.addLog('📡 订阅支付通知', 'info');
            }
            
            handlePaymentNotification(data) {
                const money = parseFloat(data.money || 0);
                this.addLog(`💰 收到支付通知: ${money}元`, 'success');
                
                // 语音播报
                this.playVoiceNotification(data);
            }
            
            playVoiceNotification(data) {
                try {
                    const money = parseFloat(data.money || 0);
                    const text = `收到${data.typename || ''}付款${money}元`;
                    
                    this.addLog(`🎵 语音播报: ${text}`, 'info');
                    
                    // 使用系统TTS
                    if ('speechSynthesis' in window) {
                        const utterance = new SpeechSynthesisUtterance(text);
                        utterance.lang = 'zh-CN';
                        utterance.rate = 1.0;
                        utterance.pitch = 1.0;
                        speechSynthesis.speak(utterance);
                    } else {
                        this.addLog('⚠️ 浏览器不支持语音合成', 'warning');
                    }
                    
                } catch (error) {
                    this.addLog(`❌ 语音播报失败: ${error.message}`, 'error');
                }
            }
            
            startHeartbeat() {
                this.heartbeatTimer = setInterval(() => {
                    if (this.isConnected) {
                        this.sendMessage({ type: 'ping', timestamp: Date.now() });
                    }
                }, 30000);
            }
            
            attemptReconnect() {
                if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                    this.addLog('❌ 重连次数已达上限，停止重连', 'error');
                    return;
                }
                
                this.reconnectAttempts++;
                this.addLog(`🔄 第${this.reconnectAttempts}次重连尝试...`, 'info');
                
                setTimeout(() => {
                    this.connect();
                }, 3000);
            }
            
            testVoice() {
                if (!this.isConnected) {
                    this.addLog('⚠️ 请先连接WebSocket', 'warning');
                    return;
                }
                
                const testData = {
                    money: '0.01',
                    typename: '支付宝'
                };
                
                this.playVoiceNotification(testData);
            }
            
            simulatePayment() {
                // 调用后端测试接口
                fetch('http://ceshi.huisas.com/test_websocket_notify.php')
                    .then(response => response.text())
                    .then(data => {
                        this.addLog('✅ 模拟支付通知发送成功', 'success');
                    })
                    .catch(error => {
                        this.addLog(`❌ 模拟支付通知失败: ${error.message}`, 'error');
                    });
            }
            
            refreshServerStats() {
                fetch('http://ceshi.huisas.com:8080/stats')
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('totalConnections').textContent = data.total_connections || 0;
                        document.getElementById('currentConnections').textContent = data.current_connections || 0;
                        document.getElementById('totalMessages').textContent = data.total_messages || 0;
                        document.getElementById('paymentNotifications').textContent = data.payment_notifications || 0;
                        this.addLog('📊 服务器统计已更新', 'info');
                    })
                    .catch(error => {
                        this.addLog(`❌ 获取服务器统计失败: ${error.message}`, 'error');
                    });
            }
            
            addLog(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = `log-${type}`;
                logEntry.textContent = `[${timestamp}] ${message}`;
                
                this.logEl.appendChild(logEntry);
                this.logEl.scrollTop = this.logEl.scrollHeight;
                
                // 限制日志数量
                while (this.logEl.children.length > 100) {
                    this.logEl.removeChild(this.logEl.firstChild);
                }
            }
            
            clearLog() {
                this.logEl.innerHTML = '';
                this.addLog('📝 日志已清空', 'info');
            }
        }
        
        // 初始化测试器
        window.addEventListener('DOMContentLoaded', () => {
            new WebSocketTester();
        });
    </script>
</body>
</html>
