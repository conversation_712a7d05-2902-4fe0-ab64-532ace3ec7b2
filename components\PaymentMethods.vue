<template>
	<view class="payment-methods">
		<view class="payment-methods-cards">
			<view class="payment-method-card qr" @click="navigateToScan">
				<image src="/static/home/<USER>" mode="aspectFit" style="width: 80rpx; height: 80rpx;"></image>
				<text class="method-text">收款码</text>
			</view>
			<view class="payment-method-card scan" @click="scanToPay">
				<image src="/static/home/<USER>" mode="aspectFit" style="width: 80rpx; height: 80rpx;"></image>
				<text class="method-text">扫码收款</text>
			</view>
			<view class="payment-method-card manual" @click="navigateToManualPay">
				<image src="/static/home/<USER>" mode="aspectFit" style="width: 80rpx; height: 80rpx;"></image>
				<text class="method-text">手动收款</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'PaymentMethods',
		data() {
			return {
				
			}
		},
		methods: {
			navigateToScan() {
				uni.switchTab({
					url: '/pages/scan/index'
				});
			},
			scanToPay() {
				// 调用扫码API
				uni.scanCode({
					onlyFromCamera: true,
					success: (res) => {
						console.log('扫码结果：', res);
						// 这里可以处理扫码结果，或者跳转到支付确认页面
						uni.showToast({
							title: '扫码成功',
							icon: 'success'
						});
					},
					fail: () => {
						uni.showToast({
							title: '扫码失败',
							icon: 'none'
						});
					}
				});
			},
			navigateToManualPay() {
				uni.navigateTo({
					url: '/pages/code/index'
				});
				console.log('正在导航到手动收款页面');
			}
		}
	}
</script>

<style>
	.payment-methods {
		margin: 32rpx;
		background-color: #fff;
		border-radius: 16rpx;
		padding: 24rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.payment-methods-cards {
		display: flex;
		gap: 32rpx;
	}

	.payment-method-card {
		flex: 1;
		background-color: #F0F0F0;
		border-radius: 16rpx;
		padding: 32rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 16rpx;
	}
	
	.method-text {
		font-size: 22rpx;
		color: #333;
	}

	.payment-method-card.qr {
		background-color: #E9EFFF;
	}

	.payment-method-card.scan {
		background-color: #EFE4FF;
	}

	.payment-method-card.manual {
		background-color: #E4FFEF;
	}
</style> 