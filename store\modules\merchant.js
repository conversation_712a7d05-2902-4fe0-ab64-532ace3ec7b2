// store/modules/merchant.js - 商户模块状态管理
import { merchant } from '@/api';
import { MerchantModel } from '@/models';

// 初始状态
const state = {
  // 商户信息
  merchantInfo: new MerchantModel(),
  // 商户配置已设置
  configSet: false,
  // 结算记录
  settlements: []
};

// 修改状态的方法
const mutations = {
  // 设置商户信息
  SET_MERCHANT_INFO(state, data) {
    state.merchantInfo = data instanceof MerchantModel 
      ? data 
      : MerchantModel.fromApiResponse(data);
  },
  // 设置商户配置状态
  SET_CONFIG_STATUS(state, status) {
    state.configSet = status;
  },
  // 设置结算记录
  SET_SETTLEMENTS(state, data) {
    state.settlements = data;
  },
  // 添加结算记录
  ADD_SETTLEMENTS(state, data) {
    state.settlements = [...state.settlements, ...data];
  }
};

// 异步操作
const actions = {
  // 获取商户信息
  async getMerchantInfo({ commit }) {
    try {
      const response = await merchant.getMerchantInfo();
      if (response.code === 1) {
        commit('SET_MERCHANT_INFO', response);
        return response;
      }
      return Promise.reject(new Error(response.msg || '获取商户信息失败'));
    } catch (error) {
      return Promise.reject(error);
    }
  },
  
  // 保存商户配置
  saveMerchantConfig({ commit }, { merchantId, merchantKey }) {
    return merchant.saveMerchantConfig(merchantId, merchantKey).then(() => {
      // 创建新的商户模型
      const merchantModel = new MerchantModel({ pid: merchantId, key: merchantKey });
      // 保存到本地存储
      merchantModel.saveToStorage();
      // 更新状态
      commit('SET_MERCHANT_INFO', merchantModel);
      commit('SET_CONFIG_STATUS', true);
      return merchantModel;
    });
  },
  
  // 从本地存储加载商户配置
  loadMerchantConfig({ commit }) {
    const merchantModel = MerchantModel.loadFromStorage();
    if (merchantModel.pid && merchantModel.key) {
      commit('SET_MERCHANT_INFO', merchantModel);
      commit('SET_CONFIG_STATUS', true);
      return merchantModel;
    }
    commit('SET_CONFIG_STATUS', false);
    return null;
  },
  
  // 获取结算记录
  async getSettlements({ commit }, { limit = 10, offset = 0, append = false }) {
    try {
      const response = await merchant.getSettlements(limit, offset);
      if (response.code === 1 && response.data) {
        if (append) {
          commit('ADD_SETTLEMENTS', response.data);
        } else {
          commit('SET_SETTLEMENTS', response.data);
        }
        return response.data;
      }
      return Promise.reject(new Error(response.msg || '获取结算记录失败'));
    } catch (error) {
      return Promise.reject(error);
    }
  }
};

// 获取状态的方法
const getters = {
  merchantInfo: state => state.merchantInfo,
  configSet: state => state.configSet,
  settlements: state => state.settlements,
  
  // 获取今日统计
  todayStats: state => {
    return {
      orders: state.merchantInfo.orders_today || 0,
      income: state.merchantInfo.orders_today_all || '0.00'
    };
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}; 