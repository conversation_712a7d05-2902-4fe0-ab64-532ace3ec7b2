<template>
	<view class="container">
		<!-- 顶部标题栏 -->
		<custom-navbar
			title="营销工具"
			:show-back="true"
			:shadow="true"
			@clickLeft="goBack"
		>
			<template #right>
				<view @click="searchTools" style="padding: 0 16rpx;">
					<uni-icons type="search" size="22" color="#FFFFFF"></uni-icons>
				</view>
			</template>
		</custom-navbar>
		
		<!-- 工具卡片区域 -->
		<view class="content">
			<!-- 营销活动 -->
			<view class="section">
				<view class="section-title">活动营销</view>
				<view class="card-grid">
					<view class="card" @click="navigateTo('coupon')">
						<view class="card-icon icon-blue">
							<uni-icons type="gift" color="#FFFFFF" size="24"></uni-icons>
						</view>
						<view class="card-content">
							<text class="card-title">优惠券</text>
							<text class="card-desc">会员折扣和优惠券管理</text>
						</view>
					</view>
					<view class="card" @click="navigateTo('discount')">
						<view class="card-icon icon-orange">
							<uni-icons type="star" color="#FFFFFF" size="24"></uni-icons>
						</view>
						<view class="card-content">
							<text class="card-title">满减活动</text>
							<text class="card-desc">设置满减优惠活动</text>
						</view>
					</view>
				</view>
				<view class="card-grid">
					<view class="card" @click="navigateTo('seckill')">
						<view class="card-icon icon-red">
							<uni-icons type="shop" color="#FFFFFF" size="24"></uni-icons>
						</view>
						<view class="card-content">
							<text class="card-title">限时秒杀</text>
							<text class="card-desc">设置限时特价商品</text>
						</view>
					</view>
					<view class="card" @click="navigateTo('group')">
						<view class="card-icon icon-green">
							<uni-icons type="staff" color="#FFFFFF" size="24"></uni-icons>
						</view>
						<view class="card-content">
							<text class="card-title">拼团活动</text>
							<text class="card-desc">多人拼团优惠</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 会员管理 -->
			<view class="section">
				<view class="section-title">会员管理</view>
				<view class="card-grid">
					<view class="card" @click="navigateTo('member')">
						<view class="card-icon icon-purple">
							<uni-icons type="person" color="#FFFFFF" size="24"></uni-icons>
						</view>
						<view class="card-content">
							<text class="card-title">会员体系</text>
							<text class="card-desc">管理会员等级和权益</text>
						</view>
					</view>
					<view class="card" @click="navigateTo('points')">
						<view class="card-icon icon-blue-light">
							<uni-icons type="medal" color="#FFFFFF" size="24"></uni-icons>
						</view>
						<view class="card-content">
							<text class="card-title">积分商城</text>
							<text class="card-desc">会员积分兑换商品</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 社交营销 -->
			<view class="section">
				<view class="section-title">社交营销</view>
				<view class="card-grid">
					<view class="card" @click="navigateTo('social')">
						<view class="card-icon icon-green-light">
							<uni-icons type="chat" color="#FFFFFF" size="24"></uni-icons>
						</view>
						<view class="card-content">
							<text class="card-title">社交分享</text>
							<text class="card-desc">社交媒体营销工具</text>
						</view>
					</view>
					<view class="card" @click="navigateTo('invite')">
						<view class="card-icon icon-yellow">
							<uni-icons type="reload" color="#FFFFFF" size="24"></uni-icons>
						</view>
						<view class="card-content">
							<text class="card-title">邀请有礼</text>
							<text class="card-desc">会员推荐奖励</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 数据分析 -->
			<view class="section">
				<view class="section-title">营销分析</view>
				<view class="card-grid">
					<view class="card" @click="navigateTo('analysis')">
						<view class="card-icon icon-blue-dark">
							<uni-icons type="paperplane" color="#FFFFFF" size="24"></uni-icons>
						</view>
						<view class="card-content">
							<text class="card-title">营销分析</text>
							<text class="card-desc">营销活动数据统计</text>
						</view>
					</view>
					<view class="card" @click="navigateTo('report')">
						<view class="card-icon icon-purple-dark">
							<uni-icons type="calendar" color="#FFFFFF" size="24"></uni-icons>
						</view>
						<view class="card-content">
							<text class="card-title">数据报表</text>
							<text class="card-desc">营销效果数据报表</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar.vue'

export default {
	components: {
		CustomNavbar
	},
	data() {
		return {
			
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		searchTools() {
			uni.showToast({
				title: '搜索营销工具',
				icon: 'none'
			});
		},
		navigateTo(page) {
			uni.showToast({
				title: `即将前往${page}页面`,
				icon: 'none'
			});
			// 实际开发时可以使用以下代码进行页面跳转
			// uni.navigateTo({
			// 	url: `/pages/yingxiao/${page}/index`
			// });
		}
	}
}
</script>

<style>
page {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
	background-color: #f8f8f8;
}

.container {
	width: 100%;
	min-height: 100vh;
	background-color: #f8f8f8;
}

/* 顶部标题栏 */
.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #5145F7;
	color: white;
	padding: 24rpx 30rpx;
	box-sizing: border-box;
	position: sticky;
	top: 0;
	z-index: 100;
	box-shadow: 0 4rpx 12rpx rgba(81, 69, 247, 0.2);
}

.header-left {
	display: flex;
	align-items: center;
}

.header-title {
	font-size: 36rpx;
	font-weight: 500;
	margin-left: 20rpx;
}

/* 内容区域 */
.content {
	padding: 30rpx 30rpx 100rpx 30rpx;
}

.section {
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	margin: 30rpx 0 20rpx 0;
	padding-left: 16rpx;
	border-left: 6rpx solid #5145F7;
	line-height: 1.2;
}

.card-grid {
	display: flex;
	justify-content: space-between;
	margin: 0 0 20rpx 0;
}

.card {
	width: calc(50% - 24rpx);
	margin: 12rpx;
	background-color: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
	transition: transform 0.2s, box-shadow 0.2s;
	display: flex;
	flex-direction: row;
	align-items: center;
	height: 110rpx;
	box-sizing: border-box;
}

.card:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.card-icon {
	width: 70rpx;
	height: 70rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 0;
	flex-shrink: 0;
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-content {
	flex: 1;
	margin-left: 16rpx;
	overflow: hidden;
}

.card-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin: 10rpx 0 6rpx 0;
	display: block;
}

.card-desc {
	font-size: 22rpx;
	color: #777;
	display: block;
	line-height: 1.4;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* 图标颜色 */
.icon-blue {
	background-color: #5145F7;
}

.icon-orange {
	background-color: #FF9043;
}

.icon-red {
	background-color: #FF5252;
}

.icon-green {
	background-color: #4CD964;
}

.icon-purple {
	background-color: #9C6AFF;
}

.icon-blue-light {
	background-color: #4A90E2;
}

.icon-green-light {
	background-color: #18C3B3;
}

.icon-yellow {
	background-color: #FFBB33;
}

.icon-blue-dark {
	background-color: #3A4A5A;
}

.icon-purple-dark {
	background-color: #7B4CDD;
}
</style>

