<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强WebSocket管理器测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            border: 1px solid #e9ecef;
        }
        
        .panel h3 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .status-item {
            background: white;
            padding: 10px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .status-item.connected {
            border-left-color: #28a745;
        }
        
        .status-item.disconnected {
            border-left-color: #dc3545;
        }
        
        .status-item.reconnecting {
            border-left-color: #ffc107;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.error {
            color: #fc8181;
        }
        
        .log-entry.warning {
            color: #f6e05e;
        }
        
        .log-entry.success {
            color: #68d391;
        }
        
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .stats-table th,
        .stats-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .stats-table th {
            background: #e9ecef;
            font-weight: 600;
        }
        
        .quality-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .quality-excellent {
            background: #28a745;
        }
        
        .quality-good {
            background: #17a2b8;
        }
        
        .quality-poor {
            background: #ffc107;
        }
        
        .quality-disconnected {
            background: #dc3545;
        }
        
        .quality-failed {
            background: #6c757d;
        }
        
        .quality-reconnecting {
            background: #fd7e14;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @media (max-width: 768px) {
            .content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 增强WebSocket管理器测试</h1>
            <p>测试长时间连接稳定性和自动重连功能</p>
        </div>
        
        <div class="content">
            <!-- 控制面板 -->
            <div class="panel">
                <h3>🎛️ 控制面板</h3>
                
                <div class="status-grid">
                    <div class="status-item" id="connectionStatus">
                        <strong>连接状态</strong><br>
                        <span id="statusText">未连接</span>
                    </div>
                    <div class="status-item">
                        <strong>连接质量</strong><br>
                        <span id="qualityText">
                            <span class="quality-indicator quality-disconnected"></span>
                            未知
                        </span>
                    </div>
                    <div class="status-item">
                        <strong>重连次数</strong><br>
                        <span id="reconnectCount">0</span>
                    </div>
                    <div class="status-item">
                        <strong>消息数量</strong><br>
                        <span id="messageCount">0</span>
                    </div>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <button class="btn success" onclick="connectWebSocket()">🔗 连接</button>
                    <button class="btn danger" onclick="disconnectWebSocket()">🔌 断开</button>
                    <button class="btn warning" onclick="reconnectWebSocket()">🔄 重连</button>
                    <button class="btn" onclick="sendTestMessage()">📤 发送测试消息</button>
                </div>
                
                <div>
                    <button class="btn" onclick="clearLogs()">🗑️ 清空日志</button>
                    <button class="btn" onclick="exportLogs()">📋 导出日志</button>
                    <button class="btn" onclick="showStats()">📊 显示统计</button>
                </div>
                
                <table class="stats-table">
                    <thead>
                        <tr>
                            <th>统计项</th>
                            <th>数值</th>
                        </tr>
                    </thead>
                    <tbody id="statsTableBody">
                        <tr><td>连接时间</td><td id="connectTime">-</td></tr>
                        <tr><td>总重连次数</td><td id="totalReconnects">0</td></tr>
                        <tr><td>心跳失败次数</td><td id="heartbeatFailures">0</td></tr>
                        <tr><td>最后断开时间</td><td id="lastDisconnectTime">-</td></tr>
                        <tr><td>待响应心跳</td><td id="pendingPings">0</td></tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 日志面板 -->
            <div class="panel">
                <h3>📋 实时日志</h3>
                <div class="log-container" id="logContainer">
                    <div class="log-entry">等待连接...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入增强WebSocket管理器 -->
    <script src="assets/js/enhanced_websocket_manager.js"></script>
    
    <script>
        let wsManager = null;
        let logContainer = null;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            logContainer = document.getElementById('logContainer');
            addLog('🚀 页面加载完成，准备初始化WebSocket管理器');
            
            // 自动连接
            setTimeout(() => {
                connectWebSocket();
            }, 1000);
            
            // 定期更新状态显示
            setInterval(updateStatusDisplay, 1000);
        });
        
        /**
         * 连接WebSocket
         */
        function connectWebSocket() {
            try {
                addLog('🔗 正在初始化WebSocket管理器...');
                
                wsManager = initWebSocketManager({
                    url: 'ws://**************:8080',
                    heartbeatInterval: 15000,
                    reconnectInterval: 2000,
                    maxReconnectAttempts: 100,
                    debug: true
                });
                
                // 添加事件监听
                wsManager.on('onConnect', (event) => {
                    addLog('✅ WebSocket连接成功', 'success');
                    updateConnectionStatus('connected');
                });
                
                wsManager.on('onDisconnect', (event) => {
                    addLog(`🔌 WebSocket连接断开: ${event.code} ${event.reason}`, 'warning');
                    updateConnectionStatus('disconnected');
                });
                
                wsManager.on('onMessage', (data) => {
                    addLog(`📨 收到消息: ${JSON.stringify(data)}`);
                });
                
                wsManager.on('onPaymentNotification', (data) => {
                    addLog(`💰 收到支付通知: ${JSON.stringify(data)}`, 'success');
                });
                
                wsManager.on('onError', (error) => {
                    addLog(`❌ WebSocket错误: ${JSON.stringify(error)}`, 'error');
                });
                
                wsManager.on('onStatusChange', (status) => {
                    addLog(`📊 状态变化: ${JSON.stringify(status)}`);
                    updateQualityDisplay(status.quality);
                });
                
            } catch (error) {
                addLog(`❌ 初始化失败: ${error.message}`, 'error');
            }
        }
        
        /**
         * 断开WebSocket
         */
        function disconnectWebSocket() {
            if (wsManager) {
                wsManager.disconnect();
                addLog('🔌 手动断开连接', 'warning');
            }
        }
        
        /**
         * 重连WebSocket
         */
        function reconnectWebSocket() {
            if (wsManager) {
                wsManager.reconnect();
                addLog('🔄 手动重连', 'warning');
            }
        }
        
        /**
         * 发送测试消息
         */
        function sendTestMessage() {
            if (wsManager) {
                const message = {
                    type: 'test',
                    data: {
                        timestamp: Date.now(),
                        message: 'Hello from test page!'
                    }
                };
                
                const success = wsManager.send(message);
                if (success) {
                    addLog('📤 发送测试消息成功');
                } else {
                    addLog('❌ 发送测试消息失败', 'error');
                }
            }
        }
        
        /**
         * 更新状态显示
         */
        function updateStatusDisplay() {
            if (!wsManager) return;
            
            const status = wsManager.getStatus();
            
            // 更新重连次数
            document.getElementById('reconnectCount').textContent = status.reconnectAttempts;
            
            // 更新消息数量
            document.getElementById('messageCount').textContent = status.stats.totalMessages;
            
            // 更新统计表
            document.getElementById('connectTime').textContent = 
                status.stats.connectTime ? new Date(status.stats.connectTime).toLocaleTimeString() : '-';
            document.getElementById('totalReconnects').textContent = status.stats.totalReconnects;
            document.getElementById('heartbeatFailures').textContent = status.stats.heartbeatFailures;
            document.getElementById('lastDisconnectTime').textContent = 
                status.stats.lastDisconnectTime ? new Date(status.stats.lastDisconnectTime).toLocaleTimeString() : '-';
            document.getElementById('pendingPings').textContent = status.pendingPings;
        }
        
        /**
         * 更新连接状态
         */
        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');
            
            statusElement.className = 'status-item ' + status;
            
            switch (status) {
                case 'connected':
                    statusText.textContent = '已连接';
                    break;
                case 'disconnected':
                    statusText.textContent = '已断开';
                    break;
                case 'reconnecting':
                    statusText.textContent = '重连中';
                    break;
                default:
                    statusText.textContent = '未知';
            }
        }
        
        /**
         * 更新质量显示
         */
        function updateQualityDisplay(quality) {
            const qualityText = document.getElementById('qualityText');
            const qualityNames = {
                'excellent': '优秀',
                'good': '良好',
                'poor': '较差',
                'disconnected': '断开',
                'failed': '失败',
                'reconnecting': '重连中'
            };
            
            qualityText.innerHTML = `
                <span class="quality-indicator quality-${quality}"></span>
                ${qualityNames[quality] || '未知'}
            `;
        }
        
        /**
         * 添加日志
         */
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 限制日志数量
            while (logContainer.children.length > 100) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }
        
        /**
         * 清空日志
         */
        function clearLogs() {
            logContainer.innerHTML = '<div class="log-entry">日志已清空</div>';
        }
        
        /**
         * 导出日志
         */
        function exportLogs() {
            const logs = Array.from(logContainer.children).map(entry => entry.textContent).join('\n');
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `websocket_logs_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        /**
         * 显示统计
         */
        function showStats() {
            if (wsManager) {
                const status = wsManager.getStatus();
                alert(JSON.stringify(status, null, 2));
            }
        }
    </script>
</body>
</html>
