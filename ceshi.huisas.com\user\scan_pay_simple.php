<?php
/**
 * 简化版反扫支付API - 直接使用系统现有逻辑
 */
include("../includes/common.php");

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json; charset=utf-8');

if($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 错误处理函数
function returnError($msg, $code = -1) {
    echo json_encode(['code' => $code, 'msg' => $msg], JSON_UNESCAPED_UNICODE);
    exit();
}

// 成功返回函数
function returnSuccess($data = [], $msg = 'success') {
    $result = ['code' => 1, 'msg' => $msg];
    if(!empty($data)) {
        $result = array_merge($result, $data);
    }
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // 检查请求方法
    if($_SERVER['REQUEST_METHOD'] !== 'POST') {
        returnError('只支持POST请求');
    }

    // 获取参数
    $amount = floatval($_POST['amount'] ?? 0);
    $auth_code = trim($_POST['auth_code'] ?? '');
    $subject = trim($_POST['subject'] ?? '测试商品');

    // 参数验证
    if($amount <= 0) {
        returnError('金额必须大于0');
    }
    
    if(empty($auth_code)) {
        returnError('付款码不能为空');
    }
    
    if(!preg_match('/^\d{18}$/', $auth_code)) {
        returnError('付款码格式不正确');
    }

    // 生成订单号
    $out_trade_no = date("YmdHis").rand(11111,99999);

    // 根据付款码识别支付类型（复制ajax2.php的逻辑）
    $prefix = substr($auth_code, 0, 2);
    if(in_array($prefix, ['25', '26', '27', '28', '29', '30'])) {
        $pay_type = 'alipay';
    } elseif(in_array($prefix, ['10', '11', '12', '13', '14', '15'])) {
        $pay_type = 'wxpay';
    } elseif($prefix == '91') {
        $pay_type = 'bank';
    } else {
        returnError('不支持的付款码类型');
    }

    // 使用测试商户
    $test_uid = 1000;
    
    // 获取支付通道
    $submitData = \lib\Channel::submit($pay_type, $test_uid, 2, $amount);
    if(!$submitData) {
        returnError('没有可用的支付通道');
    }

    // 生成平台订单号
    $trade_no = date("YmdHis").rand(11111,99999);

    // 插入订单记录
    $addtime = date('Y-m-d H:i:s');
    $endtime = date('Y-m-d H:i:s', time() + 300);
    $date = date('Y-m-d');
    
    $insert_sql = "INSERT INTO pay_order (trade_no, out_trade_no, uid, tid, type, channel, name, money, addtime, endtime, date, status) VALUES (
        '$trade_no', 
        '$out_trade_no', 
        '$test_uid', 
        '0',
        '{$submitData['typeid']}', 
        '{$submitData['channel']}', 
        '$subject', 
        '$amount', 
        '$addtime', 
        '$endtime', 
        '$date',
        '0'
    )";
    
    if(!$DB->exec($insert_sql)) {
        returnError('订单创建失败');
    }

    // 构建订单数据（按照系统标准格式）
    $order = [
        'trade_no' => $trade_no,
        'out_trade_no' => $out_trade_no,
        'uid' => $test_uid,
        'money' => sprintf("%.2f", $amount),
        'realmoney' => sprintf("%.2f", $amount),
        'type' => $submitData['typeid'],
        'channel' => $submitData['channel'],
        'subchannel' => $submitData['subchannel'] ?? 0,
        'status' => 0,
        'auth_code' => $auth_code,
        'typename' => $pay_type,
        'name' => $subject
    ];

    // 获取通道配置
    $channel = $DB->getRow("SELECT * FROM pay_channel WHERE id='{$submitData['channel']}' LIMIT 1");
    if(!$channel) {
        returnError('支付通道不存在');
    }
    
    // 设置全局变量
    $ordername = $subject;
    $clientip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    $method = 'scan';

    // 调用支付插件进行反扫支付（使用系统标准方法）
    $result = \lib\Plugin::loadForSubmit($submitData['plugin'], $trade_no, true);

    if($result['type'] == 'scan') {
        // 反扫支付成功
        returnSuccess([
            'trade_no' => $result['data']['trade_no'] ?? $trade_no,
            'money' => $result['data']['money'] ?? $amount,
            'pay_type' => $pay_type,
            'buyer' => $result['data']['buyer'] ?? '',
            'api_trade_no' => $result['data']['api_trade_no'] ?? '',
            'plugin' => $submitData['plugin'],
            'channel' => $submitData['channel']
        ], '反扫支付成功');
    } else {
        // 支付失败
        $error_msg = $result['msg'] ?? '付款码支付失败';
        returnError($error_msg);
    }

} catch(Exception $e) {
    returnError('系统错误: ' . $e->getMessage());
} catch(Error $e) {
    returnError('系统错误: ' . $e->getMessage());
}
?>
