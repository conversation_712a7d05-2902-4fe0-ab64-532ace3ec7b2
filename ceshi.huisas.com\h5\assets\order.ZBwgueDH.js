function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/api-merchant.BSxsjNyO.js","assets/index-B1Q521gi.js","assets/index-BaSB2f3O.css","assets/request.DGmokXb9.js"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
import{g as t,af as e,ad as r}from"./index-B1Q521gi.js";import{p as n,g as o}from"./request.DGmokXb9.js";function a(t,e){const r=Object.keys(t).sort();let n="";r.forEach((e=>{"sign"!==e&&"sign_type"!==e&&""!==t[e]&&null!==t[e]&&void 0!==t[e]&&(n+=`${e}=${t[e]}&`)}));return function(t){function e(t,e){return t<<e|t>>>32-e}function r(t,e){let r,n,o,a,i;return o=2147483648&t,a=2147483648&e,r=1073741824&t,n=1073741824&e,i=(1073741823&t)+(1073741823&e),r&n?2147483648^i^o^a:r|n?1073741824&i?3221225472^i^o^a:1073741824^i^o^a:i^o^a}function n(t,n,o,a,i,c,u){return t=r(t,r(r(function(t,e,r){return t&e|~t&r}(n,o,a),i),u)),r(e(t,c),n)}function o(t,n,o,a,i,c,u){return t=r(t,r(r(function(t,e,r){return t&r|e&~r}(n,o,a),i),u)),r(e(t,c),n)}function a(t,n,o,a,i,c,u){return t=r(t,r(r(function(t,e,r){return t^e^r}(n,o,a),i),u)),r(e(t,c),n)}function i(t,n,o,a,i,c,u){return t=r(t,r(r(function(t,e,r){return e^(t|~r)}(n,o,a),i),u)),r(e(t,c),n)}function c(t){let e,r,n="",o="";for(r=0;r<=3;r++)e=t>>>8*r&255,o="0"+e.toString(16),n+=o.substr(o.length-2,2);return n}let u,d,f,s,m,h,g,p,y,l=[];for(l=function(t){let e;const r=t.length,n=r+8,o=16*((n-n%64)/64+1),a=Array(o-1);let i=0,c=0;for(;c<r;)e=(c-c%4)/4,i=c%4*8,a[e]=a[e]|t.charCodeAt(c)<<i,c++;return e=(c-c%4)/4,i=c%4*8,a[e]=a[e]|128<<i,a[o-2]=r<<3,a[o-1]=r>>>29,a}(t=function(t){t=t.replace(/\r\n/g,"\n");let e="";for(let r=0;r<t.length;r++){const n=t.charCodeAt(r);n<128?e+=String.fromCharCode(n):n>127&&n<2048?(e+=String.fromCharCode(n>>6|192),e+=String.fromCharCode(63&n|128)):(e+=String.fromCharCode(n>>12|224),e+=String.fromCharCode(n>>6&63|128),e+=String.fromCharCode(63&n|128))}return e}(t)),h=1732584193,g=4023233417,p=2562383102,y=271733878,u=0;u<l.length;u+=16)d=h,f=g,s=p,m=y,h=n(h,g,p,y,l[u+0],7,3614090360),y=n(y,h,g,p,l[u+1],12,3905402710),p=n(p,y,h,g,l[u+2],17,606105819),g=n(g,p,y,h,l[u+3],22,3250441966),h=n(h,g,p,y,l[u+4],7,4118548399),y=n(y,h,g,p,l[u+5],12,1200080426),p=n(p,y,h,g,l[u+6],17,2821735955),g=n(g,p,y,h,l[u+7],22,4249261313),h=n(h,g,p,y,l[u+8],7,1770035416),y=n(y,h,g,p,l[u+9],12,2336552879),p=n(p,y,h,g,l[u+10],17,4294925233),g=n(g,p,y,h,l[u+11],22,2304563134),h=n(h,g,p,y,l[u+12],7,1804603682),y=n(y,h,g,p,l[u+13],12,4254626195),p=n(p,y,h,g,l[u+14],17,2792965006),g=n(g,p,y,h,l[u+15],22,1236535329),h=o(h,g,p,y,l[u+1],5,4129170786),y=o(y,h,g,p,l[u+6],9,3225465664),p=o(p,y,h,g,l[u+11],14,643717713),g=o(g,p,y,h,l[u+0],20,3921069994),h=o(h,g,p,y,l[u+5],5,3593408605),y=o(y,h,g,p,l[u+10],9,38016083),p=o(p,y,h,g,l[u+15],14,3634488961),g=o(g,p,y,h,l[u+4],20,3889429448),h=o(h,g,p,y,l[u+9],5,568446438),y=o(y,h,g,p,l[u+14],9,3275163606),p=o(p,y,h,g,l[u+3],14,4107603335),g=o(g,p,y,h,l[u+8],20,1163531501),h=o(h,g,p,y,l[u+13],5,2850285829),y=o(y,h,g,p,l[u+2],9,4243563512),p=o(p,y,h,g,l[u+7],14,1735328473),g=o(g,p,y,h,l[u+12],20,2368359562),h=a(h,g,p,y,l[u+5],4,4294588738),y=a(y,h,g,p,l[u+8],11,2272392833),p=a(p,y,h,g,l[u+11],16,1839030562),g=a(g,p,y,h,l[u+14],23,4259657740),h=a(h,g,p,y,l[u+1],4,2763975236),y=a(y,h,g,p,l[u+4],11,1272893353),p=a(p,y,h,g,l[u+7],16,4139469664),g=a(g,p,y,h,l[u+10],23,3200236656),h=a(h,g,p,y,l[u+13],4,681279174),y=a(y,h,g,p,l[u+0],11,3936430074),p=a(p,y,h,g,l[u+3],16,3572445317),g=a(g,p,y,h,l[u+6],23,76029189),h=a(h,g,p,y,l[u+9],4,3654602809),y=a(y,h,g,p,l[u+12],11,3873151461),p=a(p,y,h,g,l[u+15],16,530742520),g=a(g,p,y,h,l[u+2],23,3299628645),h=i(h,g,p,y,l[u+0],6,4096336452),y=i(y,h,g,p,l[u+7],10,1126891415),p=i(p,y,h,g,l[u+14],15,2878612391),g=i(g,p,y,h,l[u+5],21,4237533241),h=i(h,g,p,y,l[u+12],6,1700485571),y=i(y,h,g,p,l[u+3],10,2399980690),p=i(p,y,h,g,l[u+10],15,4293915773),g=i(g,p,y,h,l[u+1],21,2240044497),h=i(h,g,p,y,l[u+8],6,1873313359),y=i(y,h,g,p,l[u+15],10,4264355552),p=i(p,y,h,g,l[u+6],15,2734768916),g=i(g,p,y,h,l[u+13],21,1309151649),h=i(h,g,p,y,l[u+4],6,4149444226),y=i(y,h,g,p,l[u+11],10,3174756917),p=i(p,y,h,g,l[u+2],15,718787259),g=i(g,p,y,h,l[u+9],21,3951481745),h=r(h,d),g=r(g,f),p=r(p,s),y=r(y,m);return(c(h)+c(g)+c(p)+c(y)).toLowerCase()}(n+`key=${e}`).toUpperCase()}const i=Object.freeze(Object.defineProperty({__proto__:null,getOrder:function(r,o){const i=t("merchantId")||e.merchant.id,c=t("merchantKey")||e.merchant.key,u={pid:i,timestamp:Math.floor(Date.now()/1e3).toString(),sign_type:"MD5"};return r?u.out_trade_no=r:o&&(u.trade_no=o),u.sign=a(u,c),n("/api/pay/query",u)},getOrderLegacy:function(r){const n=t("merchantId")||e.merchant.id,a=t("merchantKey")||e.merchant.key;return o("/api.php",{act:"order",pid:n,key:a,out_trade_no:r})},getOrderList:function(r=10,o=0,i=null){const c=t("merchantId")||e.merchant.id,u=t("merchantKey")||e.merchant.key,d={pid:c,limit:r,offset:o,timestamp:Math.floor(Date.now()/1e3).toString(),sign_type:"MD5"};return null!==i&&(d.status=i),d.sign=a(d,u),n("/api/merchant/orders",d)},getOrderListLegacy:function(r=10,n=0){const a=t("merchantId")||e.merchant.id,i=t("merchantKey")||e.merchant.key;return o("/api.php",{act:"orders",pid:a,key:i,limit:r,offset:n})},getOrderStatusText:function(t){return{0:"未支付",1:"已支付",2:"已退款",3:"已关闭"}[t]||"未知状态"},getTodayOrderStats:function(){return r((()=>import("./api-merchant.BSxsjNyO.js")),__vite__mapDeps([0,1,2,3])).then((({getMerchantInfo:t})=>t().then((t=>1===t.code?{code:0,data:{count:t.orders_today||0,amount:t.orders_today_all||"0.00"}}:Promise.reject(new Error("获取今日订单统计失败"))))))},refundOrder:function(r,o,i,c=""){const u=t("merchantId")||e.merchant.id,d=t("merchantKey")||e.merchant.key,f=Math.floor(Date.now()/1e3).toString(),s={pid:u,money:i.toString(),timestamp:f,sign_type:"MD5"};return r?s.out_trade_no=r:o&&(s.trade_no=o),c&&(s.out_refund_no=c),s.sign=a(s,d),n("/api/pay/refund",s)},refundOrderLegacy:function(r,n){const a=t("merchantId")||e.merchant.id,i=t("merchantKey")||e.merchant.key;return o("/api.php",{act:"refund",pid:a,key:i,out_trade_no:r,money:n})}},Symbol.toStringTag,{value:"Module"}));export{i as o,a as s};
