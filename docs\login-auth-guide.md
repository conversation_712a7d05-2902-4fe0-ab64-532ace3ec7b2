# 登录状态检测使用指南

## 📋 概述

为了解决各个页面的登录状态检测问题，我们创建了统一的登录状态管理机制。现在所有页面都可以轻松地检测登录状态，并在用户未登录时自动跳转到登录页。

## 🔧 核心文件

### 1. `utils/auth.js` - 登录状态工具函数
提供基础的登录状态检测和管理功能：
- `checkLoginStatus()` - 检查登录状态
- `clearLoginInfo()` - 清除登录信息
- `redirectToLogin()` - 跳转到登录页
- `getUserInfo()` - 获取用户信息
- `hasLoginToken()` - 检查是否有登录token

### 2. `mixins/page-auth.js` - 页面登录状态混入
为页面提供统一的登录状态管理功能：
- `checkAndHandleLogin()` - 检查登录状态并处理
- `loadUserInfo()` - 获取用户信息
- `initPageWithAuth()` - 页面初始化（包含登录检测）
- `checkLoginOnShow()` - 页面显示时检查登录状态
- `logout()` - 退出登录

## 🚀 使用方法

### 方法一：使用页面混入（推荐）

```vue
<template>
  <view class="container">
    <!-- 加载状态 -->
    <view v-if="pageLoading" class="loading">
      <text>页面加载中...</text>
    </view>
    
    <!-- 页面内容 -->
    <view v-else class="content">
      <text>用户: {{ userInfo?.username }}</text>
    </view>
  </view>
</template>

<script>
import pageAuthMixin from '@/mixins/page-auth'

export default {
  mixins: [pageAuthMixin],
  
  async onLoad() {
    // 使用混入提供的方法初始化页面
    await this.initPageWithAuth(this.loadPageData);
  },
  
  onShow() {
    // 页面显示时检查登录状态
    this.checkLoginOnShow();
  },
  
  methods: {
    // 页面特定的数据加载方法
    async loadPageData() {
      // 这里编写页面特定的数据加载逻辑
      console.log('加载页面数据...');
    }
  }
}
</script>
```

### 方法二：手动调用工具函数

```vue
<script>
import { checkLoginStatus, redirectToLogin, getUserInfo } from '@/utils/auth'

export default {
  async onLoad() {
    try {
      // 检查登录状态
      const isLogin = await checkLoginStatus();
      if (!isLogin) {
        redirectToLogin();
        return;
      }
      
      // 获取用户信息
      const userInfo = await getUserInfo();
      this.userInfo = userInfo;
      
      // 加载页面数据
      await this.loadPageData();
      
    } catch (error) {
      console.error('页面初始化失败:', error);
    }
  }
}
</script>
```

## 📱 页面生命周期处理

### onLoad - 页面加载
```javascript
async onLoad() {
  // 使用混入方法（推荐）
  await this.initPageWithAuth(this.loadPageData);
  
  // 或者手动处理
  // const isLogin = await this.checkAndHandleLogin();
  // if (isLogin) {
  //   await this.loadPageData();
  // }
}
```

### onShow - 页面显示
```javascript
onShow() {
  // 使用混入方法（推荐）
  this.checkLoginOnShow();
  
  // 或者手动处理
  // this.checkAndHandleLogin(false);
}
```

## 🔄 登录状态检测流程

1. **检查本地token** - 验证是否存在 `user_token`
2. **验证token有效性** - 调用 `/user/ajax2.php?act=getcount` 接口
3. **处理响应结果**：
   - `code === 0` - 登录有效
   - `code === -3` - 登录失效，清除本地信息
   - 其他 - 检测失败

## ⚠️ 注意事项

### 1. 自动跳转控制
```javascript
// 自动跳转到登录页（默认）
await this.checkAndHandleLogin();

// 不自动跳转，只返回登录状态
await this.checkAndHandleLogin(false);
```

### 2. 错误处理
所有方法都会抛出异常，请使用 try-catch 处理：
```javascript
try {
  await this.initPageWithAuth(this.loadPageData);
} catch (error) {
  console.error('页面初始化失败:', error);
  // 处理错误
}
```

### 3. 数据状态
混入提供的数据状态：
- `pageLoading` - 页面加载状态
- `userInfo` - 用户信息
- `isLogin` - 登录状态

## 🛠️ 自定义配置

### 修改登录页路径
在 `utils/auth.js` 中修改：
```javascript
export function redirectToLogin(redirectUrl = '') {
  let loginUrl = '/pages/login/index'; // 修改这里
  // ...
}
```

### 修改用户信息接口
在 `utils/auth.js` 中修改：
```javascript
export async function getUserInfo() {
  const response = await request({
    url: '/user/ajax2.php?act=getcount', // 修改这里
    // ...
  });
}
```

## 📝 示例页面

参考 `pages/scan/index.vue` 和 `pages/example-page.vue` 查看完整的使用示例。

## 🔧 故障排除

### 1. 页面一直显示加载中
检查 `loadPageData` 方法是否抛出异常，确保正确处理错误。

### 2. 登录状态检测失败
检查网络连接和后端接口是否正常，查看控制台错误信息。

### 3. 无限跳转到登录页
检查登录页是否正确保存了登录信息，确保登录成功后设置了 `user_token`。
