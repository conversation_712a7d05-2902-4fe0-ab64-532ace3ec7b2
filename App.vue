<template>
  <view id="app">
  </view>
</template>

<script>
	import globalWebSocketService from '@/utils/globalWebSocketService.js'
	import voiceManager from '@/utils/voiceManager.js'
	import autoWebSocketEnabler from '@/utils/autoEnableWebSocket.js'

	export default {

		onLaunch: function() {
			console.log('🚀 App Launch')

			// 🔧 暂时禁用自动WebSocket，使用页面级别的统一连接
			// this.initAutoWebSocket()
			console.log('🔧 使用页面级别的统一WebSocket连接')
		},

		onShow: function() {
			console.log('👁️ App Show')

			// 🔧 暂时禁用全局WebSocket服务恢复
			// 改用语音设置页面统一管理WebSocket连接
		},

		onHide: function() {
			console.log('🙈 App Hide')

			// 🔧 暂时禁用全局WebSocket服务暂停
			// globalWebSocketService.pause()
		},

		onError: function(error) {
			console.error('❌ App Error:', error)
			// 可以在这里添加错误上报逻辑
		},

		methods: {
			/**
			 * 自动启用WebSocket功能
			 */
			async initAutoWebSocket() {
				try {
					console.log('🚀 自动启用WebSocket功能...')
					await autoWebSocketEnabler.init()

					// 定期检查状态
					setInterval(() => {
						const status = autoWebSocketEnabler.getStatus()
						if (!status.ready) {
							console.log('⚠️ WebSocket状态异常，尝试重新初始化')
							autoWebSocketEnabler.reinit()
						}
					}, 30000) // 每30秒检查一次

				} catch (error) {
					console.error('❌ 自动启用WebSocket失败:', error)
				}
			},

			/**
			 * 初始化全局服务
			 */
			async initGlobalServices() {
				try {
					console.log('🎵 初始化全局语音播报服务')

					// 检查语音设置
					const voiceStatus = voiceManager.getStatus()
					console.log('🔊 当前语音设置:', voiceStatus)

					// 🔧 暂时禁用全局WebSocket服务启动
					// await globalWebSocketService.start()

					console.log('✅ 全局服务初始化完成')

				} catch (error) {
					console.error('❌ 全局服务初始化失败:', error)
				}
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	page {
		font-family: 'Segoe UI', sans-serif;
		background-color: #f5f5f5;
	}

	/* 为底部导航腾出空间 */
	.page-container {
		padding-bottom: 120rpx;
	}

	/* 全局应用容器 */
	#app {
		position: relative;
		width: 100%;
		height: 100%;
	}
</style>
