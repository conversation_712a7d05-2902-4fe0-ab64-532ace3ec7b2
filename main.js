import App from './App'
import config from './config/index.js'
import websocketManager from './utils/websocketManager.js'
import voiceManager from './utils/voiceManager.js'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false

// 全局配置
Vue.prototype.$baseUrl = config.baseUrl
Vue.prototype.$config = config
Vue.prototype.$websocketManager = websocketManager
Vue.prototype.$voiceManager = voiceManager

App.mpType = 'app'
const app = new Vue({
  ...App
})

// 应用启动时检查语音设置
const voiceStatus = voiceManager.getStatus()
if (voiceStatus.enabled) {
  console.log('🔊 应用启动时检测到语音已开启，启用WebSocket管理器')
  websocketManager.enable()
}

app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)

  // 全局配置
  app.config.globalProperties.$baseUrl = config.baseUrl
  app.config.globalProperties.$config = config
  app.config.globalProperties.$websocketManager = websocketManager
  app.config.globalProperties.$voiceManager = voiceManager

  // 应用启动时检查语音设置
  const voiceStatus = voiceManager.getStatus()
  if (voiceStatus.enabled) {
    console.log('🔊 应用启动时检测到语音已开启，启用WebSocket管理器')
    websocketManager.enable()
  }

  return {
    app
  }
}
// #endif
