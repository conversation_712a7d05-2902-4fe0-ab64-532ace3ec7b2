/**
 * 语音播报器 - 基于CSDN文章实现
 * 参考: https://blog.csdn.net/m0_59256433/article/details/131451435
 * 实现音频拼接播放，支持任务队列，避免音频冲突
 */

/**
 * 任务队列管理器
 * 防止多个音频同时播放
 */
class Scheduler {
    constructor() {
        this._max = 1; // 支持同时播放的数量
        this.unwork = []; // 未执行的任务列表
        this.working = []; // 正在执行的任务列表
    }

    add(asyncTask) {
        return new Promise((resolve) => {
            asyncTask.resolve = resolve;
            if (this.working.length < this._max) {
                this.runTask(asyncTask);
            } else {
                this.unwork.push(asyncTask);
            }
        });
    }

    runTask(asyncTask) {
        this.working.push(asyncTask);
        asyncTask().then((res) => {
            asyncTask.resolve(); // asyncTask异步任务完成以后，再调用外层Promise的resolve以便add().then()的执行
            var index = this.working.indexOf(asyncTask);
            this.working.splice(index, 1); // 从正在进行的任务队列中删除
            if (this.unwork.length > 0) {
                this.runTask(this.unwork.shift());
            }
        });
    }
}

/**
 * 语音播报器类
 */
class VoicePlayer {
    constructor() {
        this.scheduler = new Scheduler();
        this.musicObj = null;
        this.isPlaying = false;
    }

    /**
     * 播放支付成功语音
     * @param {string|number} amount 支付金额
     */
    async playPaymentVoice(amount) {
        return new Promise((resolve) => {
            // 向任务队列里添加播放任务
            this.scheduler.add(() => this.splicingAudioFiles(amount)).then(() => {
                console.log(`语音播报完成: ${amount}元`);
                resolve();
            });
        });
    }

    /**
     * 拼接音频文件播放
     * @param {string|number} amount 金额
     */
    splicingAudioFiles(amount) {
        return new Promise((resolve) => {
            console.log(`开始播放语音: ${amount}元`);
            
            // 创建播放器对象
            this.musicObj = uni.createInnerAudioContext();
            
            // 调用数字转中文读法的方法
            const moneyArr = this.capitalAmount(amount).split(',').filter(item => item.trim());
            
            // 重组组成资源文件本地地址路径
            const arr = moneyArr.map((item) => `/static/music/${item.trim()}.mp3`);
            
            console.log('音频文件列表:', arr);
            
            // src为播放器的播放路径 先默认播放开头话语
            this.musicObj.src = '/static/music/_shoukuan.mp3';
            this.isPlaying = true;
            
            // play()为播放的方法
            this.musicObj.play();
            
            // onEnded()为播放结束的时候继续操作
            this.musicObj.onEnded(() => {
                // 这里调用playVoice()方法 arr为保存音频文件的数组 musicObj为播放器对象
                this.playVoice(arr, this.musicObj).then(() => {
                    this.isPlaying = false;
                    resolve();
                });
            });

            // 错误处理
            this.musicObj.onError((error) => {
                console.error('音频播放错误:', error);
                this.cleanup();
                resolve();
            });
        });
    }

    /**
     * 递归播放音频文件
     * @param {Array} arr 音频文件数组
     * @param {Object} music 音频播放器对象
     */
    playVoice(arr, music) {
        return new Promise((resolve) => {
            // playFile 保存arr头一个音频文件
            let playFile = arr.shift();
            
            // playFile 为空时结束语音播放
            if (!playFile) {
                music.destroy(); // 销毁音频实例
                resolve(); // resolve出去说明此段音频播放完毕，为队列做准备
                return;
            }
            
            console.log(`播放音频文件: ${playFile}`);
            music.src = playFile;
            music.play();
            
            // 解决实例化太多出现错误不播放问题
            music.onError((res) => {
                console.error('音频播放错误:', res);
                music.destroy(); // 发生错误后，销毁实例 
                this.musicObj = uni.createInnerAudioContext();
                resolve();
            });
            
            music.onEnded(() => {
                if (arr.length == 0) {
                    music.destroy(); // 销毁音频实例
                    resolve(); // resolve出去说明此段音频播放完毕，为队列做准备
                    return;
                } else {
                    // 短暂延迟后播放下一个
                    setTimeout(() => {
                        this.playVoice(arr, music).then(() => {
                            resolve();
                        });
                    }, 100);
                }
            });
        });
    }

    /**
     * 将数字转为文字金额读法
     * @param {string|number} amount 金额
     */
    capitalAmount(amount) {
        // 汉字的数字
        const cnNums = ["_0,", "_1,", "_2,", "_3,", "_4,", "_5,", "_6,", "_7,", "_8,", "_9,"];
        // 基本单位
        const cnIntRadice = ["", "_shi,", "_bai,", "_qian,"];
        // 对应整数部分扩展单位
        const cnIntUnits = ["", "_wan,", "_yi,"];
        // 整数金额时后面跟的字符
        const cnInteger = "_yuan";
        // 整型完以后的单位
        const cnIntLast = "_dian,";
        // 最大处理的数字
        const maxNum = 9999999999999999.99;
        
        // 金额整数部分
        let integerNum;
        // 金额小数部分
        let decimalNum;
        // 输出的中文金额字符串
        let chineseStr = "";
        // 分离金额后用的数组，预定义
        let parts;
        
        if (amount === "") {
            return "";
        }
        
        amount = parseFloat(amount);
        if (amount >= maxNum) {
            // 超出最大处理数字
            return "";
        }
        
        if (amount === 0) {
            chineseStr = cnNums[0];
            return chineseStr;
        }
        
        // 转换为字符串
        amount = amount.toString();
        if (amount.indexOf(".") === -1) {
            integerNum = amount;
            decimalNum = "";
        } else {
            parts = amount.split(".");
            integerNum = parts[0];
            decimalNum = parts[1].substr(0, 2);
        }

        // 获取整型部分转换
        if (parseInt(integerNum, 10) > 0) {
            let zeroCount = 0;
            const IntLen = integerNum.length;
            for (let i = 0; i < IntLen; i++) {
                const n = integerNum.substr(i, 1);
                const p = IntLen - i - 1;
                const q = Math.floor(p / 4);
                const m = p % 4;
                if (n === "0") {
                    zeroCount++;
                } else {
                    if (zeroCount > 0) {
                        chineseStr += cnNums[0];
                    }
                    // 归零
                    zeroCount = 0;
                    chineseStr += cnNums[parseInt(n, 10)] + cnIntRadice[m];
                }
                if (m === 0 && zeroCount < 4) {
                    chineseStr += cnIntUnits[q];
                }
            }
        } else {
            chineseStr = cnNums[0];
        }
        
        // 小数部分
        if (decimalNum !== "" && decimalNum !== "00") {
            const decLen = decimalNum.length;
            chineseStr += cnIntLast;
            for (let i = 0; i < decLen; i++) {
                const n = decimalNum.substr(i, 1);
                if (n !== "0") {
                    chineseStr += cnNums[Number(n)];
                } else {
                    chineseStr += cnNums[0];
                }
            }
        }
        
        if (chineseStr === "") {
            chineseStr += cnNums[0] + cnIntLast;
        } else if (decimalNum === "") {
            chineseStr;
        }
        
        return chineseStr + cnInteger;
    }

    /**
     * 停止播放
     */
    stop() {
        this.isPlaying = false;
        this.cleanup();
    }

    /**
     * 清理资源
     */
    cleanup() {
        if (this.musicObj) {
            this.musicObj.destroy();
            this.musicObj = null;
        }
        this.isPlaying = false;
    }

    /**
     * 获取播放状态
     */
    getPlayingStatus() {
        return this.isPlaying;
    }

    /**
     * 获取队列状态
     */
    getQueueStatus() {
        return {
            working: this.scheduler.working.length,
            waiting: this.scheduler.unwork.length
        };
    }
}

// 创建单例实例
const voicePlayer = new VoicePlayer();

export default voicePlayer;
