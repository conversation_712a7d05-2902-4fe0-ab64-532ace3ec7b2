<template>
	<view class="recent-payments">
		<view class="recent-payments-header">
			<text class="recent-payments-title">最近收款</text>
			<text class="view-all">查看全部</text>
		</view>
		<view class="payment-list">
			<view class="payment-item">
				<view class="payment-item-left">
					<view class="payment-icon wechat">
						<image src="/static/home/<USER>" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
					</view>
					<view class="payment-details">
						<text class="payment-name">微信支付</text>
						<text class="payment-time">10:23 <text class="payment-category">扫码</text></text>
					</view>
				</view>
				<text class="payment-amount">+ ¥ 128.00</text>
			</view>
			<view class="payment-item">
				<view class="payment-item-left">
					<view class="payment-icon alipay">
						<image src="/static/home/<USER>" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
					</view>
					<view class="payment-details">
						<text class="payment-name">支付宝</text>
						<text class="payment-time">09:45 <text class="payment-category">收银台</text></text>
					</view>
				</view>
				<text class="payment-amount">+ ¥ 85.50</text>
			</view>
			<view class="payment-item">
				<view class="payment-item-left">
					<view class="payment-icon cloud">
						<image src="/static/home/<USER>" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
					</view>
					<view class="payment-details">
						<text class="payment-name">云闪付</text>
						<text class="payment-time">09:12 <text class="payment-category">扫码</text></text>
					</view>
				</view>
				<text class="payment-amount">+ ¥ 299.00</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'RecentPayments',
		data() {
			return {
				
			}
		}
	}
</script>

<style>
	.recent-payments {
		margin: 32rpx;
		background-color: #fff;
		border-radius: 16rpx;
		padding: 24rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.recent-payments-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;
	}

	.recent-payments-title {
		font-size: 28rpx;
		color: #666;
	}

	.view-all {
		font-size: 28rpx;
		color: #5145F7;
	}

	.payment-list {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
	}

	.payment-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.payment-item-left {
		display: flex;
		align-items: center;
		gap: 24rpx;
	}

	.payment-icon {
		width: 72rpx;
		height: 72rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.payment-icon.wechat {
		background-color: #E4FFEF;
	}

	.payment-icon.alipay {
		background-color: #E9EFFF;
	}

	.payment-icon.cloud {
		background-color: #FFE4E4;
	}

	.payment-details {
		display: flex;
		flex-direction: column;
	}

	.payment-name {
		font-size: 28rpx;
	}

	.payment-time {
		font-size: 24rpx;
		color: #999;
	}
	
	.payment-category {
		color: #5587f8;
	}

	.payment-amount {
		color: #333;
		font-weight: bold;
	}
</style> 