<template>
  <view class="payment-page">
    <!-- Custom Navbar -->
    <custom-navbar
      title="收款"
      :show-back="true"
      :shadow="true"
      @clickLeft="goBack"
    >
      <template #right>
        <view @click="showPaymentHistory" style="padding: 0 16rpx;">
          <uni-icons type="redo" size="22" color="#FFFFFF"></uni-icons>
        </view>
      </template>
    </custom-navbar>
    
    <!-- Amount Display -->
    <view class="amount-section">
      <view class="amount-label">收款金额(元)</view>
      <view class="amount-display">{{ displayAmount }}</view>
      
      <!-- Add Note -->
      <view class="note-section" @click="showNoteInput = true">
        <image class="note-icon" src="/static/home/<USER>" mode="aspectFit"></image>
        <text class="note-text">{{ note || '添加备注' }}</text>
      </view>
    </view>
    
    <!-- Numeric Keypad -->
    <view class="keypad">
      <view class="keypad-row">
        <view class="key" @click="appendNumber(1)">1</view>
        <view class="key" @click="appendNumber(2)">2</view>
        <view class="key" @click="appendNumber(3)">3</view>
        </view>
      <view class="keypad-row">
        <view class="key" @click="appendNumber(4)">4</view>
        <view class="key" @click="appendNumber(5)">5</view>
        <view class="key" @click="appendNumber(6)">6</view>
        </view>
      <view class="keypad-row">
        <view class="key" @click="appendNumber(7)">7</view>
        <view class="key" @click="appendNumber(8)">8</view>
        <view class="key" @click="appendNumber(9)">9</view>
      </view>
      <view class="keypad-row">
        <view class="key" @click="appendNumber('.')">.</view>
        <view class="key" @click="appendNumber(0)">0</view>
        <view class="key delete-key" @click="deleteNumber">
          <image class="delete-icon" src="/static/code/delete.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
    
    <!-- Payment Methods -->
    <view class="payment-methods">
      <view class="payment-method wechat" :class="{ selected: selectedPaymentMethod === 'wechat' }" @click="selectPaymentMethod('wechat')">
        <image class="payment-icon" src="/static/home/<USER>" mode="aspectFit"></image>
        <text class="payment-label">微信支付</text>
      </view>
      <view class="payment-method alipay" :class="{ selected: selectedPaymentMethod === 'alipay' }" @click="selectPaymentMethod('alipay')">
        <image class="payment-icon" src="/static/home/<USER>" mode="aspectFit"></image>
        <text class="payment-label">支付宝</text>
          </view>
      <view class="payment-method shanfu" :class="{ selected: selectedPaymentMethod === 'shanfu' }" @click="selectPaymentMethod('shanfu')">
        <image class="payment-icon" src="/static/home/<USER>" mode="aspectFit"></image>
        <text class="payment-label">云闪付</text>
      </view>
    </view>
    
    <!-- Confirm Button -->
    <view class="confirm-button" @click="confirmPayment">
      <text>确认收款</text>
        </view>
        
    <!-- Note Input Modal -->
    <view class="note-modal" v-if="showNoteInput">
      <view class="note-modal-content">
        <view class="note-modal-header">添加备注</view>
        <input class="note-input" v-model="note" placeholder="请输入备注信息" />
        <view class="note-modal-buttons">
          <button @click="showNoteInput = false">取消</button>
          <button @click="confirmNote">确定</button>
        </view>
        </view>
      </view>
  </view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar.vue'

export default {
  components: {
    CustomNavbar
  },
  data() {
    return {
      amount: "0.00",
      note: "",
      showNoteInput: false,
      selectedPaymentMethod: "wechat",
      keySound: null
    }
  },
  onReady() {
    // Initialize the audio for key press sound with the downloaded file
    this.keySound = uni.createInnerAudioContext();
    this.keySound.src = '/static/code/anjian.wav';
    this.keySound.autoplay = false;
  },
  computed: {
    displayAmount() {
      return this.amount;
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showPaymentHistory() {
      uni.showToast({
        title: '支付历史功能开发中',
        icon: 'none'
      });
    },
    playKeySound() {
      // Play key press sound
      if (this.keySound) {
        this.keySound.stop();
        setTimeout(() => {
          this.keySound.play();
        }, 10);
      }
    },
    appendNumber(num) {
      // Play key sound
      this.playKeySound();
      
      // Handle first digit input
      if (this.amount === "0.00") {
        if (num === ".") {
          this.amount = "0.";
        } else {
          this.amount = String(num);
        }
        return;
      }
      
      // Handle decimal point
      if (num === ".") {
        if (this.amount.includes(".")) {
          return; // Don't allow multiple decimal points
        }
      }
      
      // Handle normal case
      this.amount += String(num);
      
      // Format amount
      if (this.amount.indexOf(".") !== -1) {
        const parts = this.amount.split(".");
        if (parts[1].length > 2) {
          parts[1] = parts[1].substring(0, 2);
          this.amount = parts.join(".");
        }
      }
    },
    deleteNumber() {
      // Play key sound
      this.playKeySound();
      
      if (this.amount.length > 1) {
        this.amount = this.amount.slice(0, -1);
      } else {
        this.amount = "0.00";
      }
    },
    confirmNote() {
      this.playKeySound();
      this.showNoteInput = false;
    },
    selectPaymentMethod(method) {
      this.playKeySound();
      this.selectedPaymentMethod = method;
    },
    confirmPayment() {
      this.playKeySound();
      // Handle payment confirmation
      uni.showToast({
        title: `确认收款 ${this.amount}元，使用${this.selectedPaymentMethod}`,
        icon: "none"
      });
    }
  }
}
</script>

<style>
.payment-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f6fa;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #5145F7;
  color: white;
  height: 90rpx;
}

.back-icon, .history-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.title {
  font-size: 36rpx;
}

.amount-section {
  padding: 40rpx 30rpx;
  background-color: white;
}

.amount-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.amount-display {
  font-size: 70rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.note-section {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-top: 1px solid #eee;
}

.note-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.note-text {
  color: #999;
  font-size: 28rpx;
}

.note-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.note-modal-content {
  width: 80%;
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx;
}

.note-modal-header {
  font-size: 32rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.note-input {
  border: 1px solid #ddd;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
}

.note-modal-buttons {
  display: flex;
  justify-content: space-between;
}

.note-modal-buttons button {
  flex: 1;
  margin: 0 10rpx;
}

.keypad {
  padding: 20rpx;
  padding-bottom: 0;
  margin-top: 20rpx;
}

.keypad-row {
  display: flex;
  margin-bottom: 20rpx;
}

.keypad-row:last-child {
  margin-bottom: 0;
}

.key {
  flex: 1;
  text-align: center;
  background-color: white;
  border-radius: 8rpx;
  padding: 40rpx 0;
  margin: 0 10rpx;
  font-size: 40rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.delete-key {
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  width: 40rpx;
  height: 40rpx;
}

.payment-methods {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
  margin-top: 20rpx;
}

.payment-method {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
  padding: 25rpx;
  border-radius: 8rpx;
  width: 28%;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.payment-method.selected {
  transform: scale(1.05);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.payment-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.payment-label {
  font-size: 24rpx;
  color: #666;
}

.wechat {
  background-color: #f0f9f0;
}

.wechat.selected {
  border: 2px solid #2aab5d;
}

.alipay {
  background-color: #f0f8ff;
}

.alipay.selected {
  border: 2px solid #1677ff;
}

.shanfu {
  background-color: #fff0f0;
}

.shanfu.selected {
  border: 2px solid #e54d42;
}

.confirm-button {
  margin: 30rpx 20rpx;
  background-color: #5145F7;
  color: white;
  text-align: center;
  padding: 25rpx 0;
  border-radius: 10rpx;
  font-size: 34rpx;
}
</style>
