/**
 * 自动启用WebSocket功能
 * 确保语音播报和WebSocket连接正常工作
 */

import voiceManager from '@/utils/voiceManager.js'
import websocketManager from '@/utils/websocketManager.js'

class AutoWebSocketEnabler {
  constructor() {
    this.isInitialized = false
  }

  /**
   * 初始化并自动启用WebSocket
   */
  async init() {
    if (this.isInitialized) return
    
    console.log('🚀 自动启用WebSocket功能...')
    
    try {
      // 1. 确保语音管理器已启用
      const voiceStatus = voiceManager.getStatus()
      if (!voiceStatus.enabled) {
        console.log('🔊 自动启用语音播报')
        voiceManager.setEnabled(true)
      }
      
      // 2. 启用WebSocket管理器
      console.log('🌐 启用WebSocket管理器')
      websocketManager.enable()
      
      // 3. 等待连接建立
      await this.waitForConnection()
      
      this.isInitialized = true
      console.log('✅ WebSocket自动启用完成')
      
      // 4. 发送测试消息验证连接
      this.testConnection()
      
    } catch (error) {
      console.error('❌ WebSocket自动启用失败:', error)
    }
  }

  /**
   * 等待WebSocket连接建立
   */
  waitForConnection(timeout = 10000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now()
      
      const checkConnection = () => {
        const status = websocketManager.getStatus()
        
        if (status.isConnected) {
          console.log('✅ WebSocket连接已建立')
          resolve()
          return
        }
        
        if (Date.now() - startTime > timeout) {
          reject(new Error('WebSocket连接超时'))
          return
        }
        
        // 继续等待
        setTimeout(checkConnection, 500)
      }
      
      checkConnection()
    })
  }

  /**
   * 测试WebSocket连接
   */
  testConnection() {
    console.log('🧪 测试WebSocket连接...')
    
    const testMessage = {
      type: 'ping',
      timestamp: Date.now(),
      test: true
    }
    
    const success = websocketManager.sendMessage(testMessage)
    
    if (success) {
      console.log('✅ WebSocket测试消息发送成功')
    } else {
      console.warn('⚠️ WebSocket测试消息发送失败')
    }
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    const wsStatus = websocketManager.getStatus()
    const voiceStatus = voiceManager.getStatus()

    return {
      initialized: this.isInitialized,
      websocket: wsStatus,
      voice: voiceStatus,
      ready: this.isInitialized && wsStatus.isConnected && voiceStatus.enabled
    }
  }

  /**
   * 强制重新初始化
   */
  async reinit() {
    this.isInitialized = false
    await this.init()
  }
}

// 创建全局实例
const autoWebSocketEnabler = new AutoWebSocketEnabler()

export default autoWebSocketEnabler
