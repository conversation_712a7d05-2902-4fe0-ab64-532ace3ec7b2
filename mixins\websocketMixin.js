/**
 * WebSocket Mixin - 参考CSDN文章实现
 * 为指定页面注入WebSocket功能
 */
import AudioPlayer from '@/utils/audioPlayer.js'

let isSocketClose = false // 是否关闭socket
let heartbeatInterval = null // 心跳定时器
let socketTask = null // websocket对象
let againTimer = null // 断线重连定时器
let onReFn = null
let onSucFn = null
let onErrFn = null

export const websocketMixin = {
  data() {
    return {
      // WebSocket状态
      wsConnected: false,
      wsConnecting: false,
      wsError: false,
      wsErrorMessage: '',
      
      // 音频播放器
      audioPlayer: null,
      
      // 语音播报状态
      voiceEnabled: true
    }
  },

  onLoad() {
    console.log('🎵 WebSocket Mixin: 页面加载，初始化音频播放器')
    this.audioPlayer = new AudioPlayer()
  },

  onShow() {
    console.log('🔄 WebSocket Mixin: 页面显示，启动WebSocket连接')
    // 延迟500ms确保上个WebSocket完全关闭
    setTimeout(() => {
      this.initWebSocket()
    }, 500)
  },

  onUnload() {
    console.log('🔌 WebSocket Mixin: 页面卸载，关闭WebSocket连接')
    this.closeWebSocket()
  },

  onHide() {
    console.log('👁️ WebSocket Mixin: 页面隐藏，保持WebSocket连接')
    // 页面隐藏时不关闭连接，保持后台接收
  },

  beforeDestroy() {
    console.log('💀 WebSocket Mixin: 组件销毁，清理WebSocket')
    this.closeWebSocket()
  },

  methods: {
    /**
     * 初始化WebSocket连接
     */
    initWebSocket() {
      // 检查用户登录状态
      const token = uni.getStorageSync('user_token')
      const uid = uni.getStorageSync('user_uid')
      
      if (!token || !uid) {
        console.log('⚠️ WebSocket Mixin: 用户未登录，跳过连接')
        return
      }

      console.log('🚀 WebSocket Mixin: 初始化WebSocket连接')
      this.connectWebSocket()
    },

    /**
     * 连接WebSocket（参考CSDN文章实现）
     */
    connectWebSocket() {
      const uid = uni.getStorageSync('user_uid')
      const wsUrl = `ws://ceshi.huisas.com:8080?id=${uid}`
      
      onReFn = this.handleWebSocketMessage
      onErrFn = this.handleWebSocketError  
      onSucFn = this.handleWebSocketSuccess

      isSocketClose = false

      // 判断是否有websocket对象，有的话清空
      if (socketTask) {
        socketTask.close()
        socketTask = null
        clearInterval(heartbeatInterval)
      }

      console.log(`🔗 WebSocket Mixin: 正在连接 ${wsUrl}`)
      this.wsConnecting = true

      // 连接WebSocket
      socketTask = uni.connectSocket({
        url: wsUrl,
        success: (data) => {
          console.log('✅ WebSocket Mixin: 连接请求发送成功')
          clearInterval(againTimer) // 清除断线重连定时器
        },
        fail: (err) => {
          console.error('❌ WebSocket Mixin: 连接失败', err)
          this.wsConnecting = false
        }
      })

      // 连接打开
      socketTask.onOpen((res) => {
        console.log('🎉 WebSocket Mixin: 连接已打开', res)
        this.wsConnected = true
        this.wsConnecting = false
        this.wsError = false
        
        clearInterval(againTimer) // 清除断线重连定时器
        this.handleWebSocketSuccess({ isShow: false })
        
        // 启动心跳
        this.startHeartbeat()
        
        // 发送认证
        this.sendAuth()
      })

      // 监听连接失败
      socketTask.onError((err) => {
        console.error('❌ WebSocket Mixin: 连接错误', err)
        this.wsConnected = false
        this.wsConnecting = false
        this.wsError = true
        
        // 停止发送心跳
        clearInterval(heartbeatInterval)
        
        // 如果不是人为关闭的话，进行重连
        if (!isSocketClose) {
          this.reconnectWebSocket()
        }
      })

      // 监听连接关闭
      socketTask.onClose((e) => {
        console.log('🔌 WebSocket Mixin: 连接已关闭')
        this.wsConnected = false
        this.wsConnecting = false
        
        clearInterval(heartbeatInterval)
        
        if (!isSocketClose) {
          this.reconnectWebSocket()
        }
      })

      // 监听收到信息
      socketTask.onMessage((res) => {
        try {
          const serverData = JSON.parse(res.data)
          console.log('📨 WebSocket Mixin: 收到消息', serverData)
          this.handleWebSocketMessage(serverData)
        } catch (error) {
          console.error('❌ WebSocket Mixin: 解析消息失败', error)
        }
      })
    },

    /**
     * 断线重连
     */
    reconnectWebSocket() {
      console.log('🔄 WebSocket Mixin: 开始断线重连')
      clearInterval(againTimer)
      clearInterval(heartbeatInterval)
      
      if (socketTask) {
        socketTask.close()
        socketTask = null
      }
      
      this.handleWebSocketError({
        isShow: true,
        message: 'WebSocket服务正在重连...'
      })
      
      // 5秒后重连
      againTimer = setInterval(() => {
        console.log('🔄 WebSocket Mixin: 正在重新连接...')
        this.connectWebSocket()
      }, 5000)
    },

    /**
     * 启动心跳
     */
    startHeartbeat() {
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval)
      }
      
      // 10秒发送一次心跳
      heartbeatInterval = setInterval(() => {
        if (socketTask && this.wsConnected) {
          this.sendMessage('心跳ing')
        }
      }, 10000)
    },

    /**
     * 发送消息
     */
    sendMessage(msg) {
      if (!socketTask || !this.wsConnected) {
        console.log('⚠️ WebSocket Mixin: 连接未建立，无法发送消息')
        return
      }
      
      try {
        const message = typeof msg === 'string' ? msg : JSON.stringify(msg)
        socketTask.send({
          data: message
        })
      } catch (e) {
        console.error('❌ WebSocket Mixin: 发送消息失败', e)
        if (!isSocketClose) {
          this.reconnectWebSocket()
        }
      }
    },

    /**
     * 发送认证消息
     */
    sendAuth() {
      const token = uni.getStorageSync('user_token') || 'anonymous'
      const merchantId = uni.getStorageSync('user_uid')
      
      console.log('🔐 WebSocket Mixin: 发送认证消息，商户ID:', merchantId)
      
      this.sendMessage({
        type: 'auth',
        data: {
          token: token,
          merchant_id: merchantId,
          client_type: 'uniapp_mixin',
          version: '1.0.0'
        }
      })
      
      // 认证后订阅商户专属频道
      if (merchantId) {
        setTimeout(() => {
          this.subscribeToMerchantChannel(merchantId)
        }, 1000)
      }
    },

    /**
     * 订阅商户专属频道
     */
    subscribeToMerchantChannel(merchantId) {
      const channel = `merchant_${merchantId}_payment`
      
      console.log('📡 WebSocket Mixin: 订阅商户专属频道:', channel)
      
      this.sendMessage({
        type: 'subscribe',
        data: {
          channel: channel
        }
      })
    },

    /**
     * 关闭WebSocket连接
     */
    closeWebSocket() {
      console.log('🔌 WebSocket Mixin: 关闭WebSocket连接')
      isSocketClose = true
      
      clearInterval(heartbeatInterval)
      clearInterval(againTimer)
      
      if (socketTask) {
        socketTask.close()
        socketTask = null
      }
      
      this.wsConnected = false
      this.wsConnecting = false
    },

    /**
     * 处理WebSocket消息（子类需要重写）
     */
    handleWebSocketMessage(data) {
      console.log('📨 WebSocket Mixin: 处理消息（默认实现）', data)
      
      switch (data.type) {
        case 'welcome':
          console.log('🎉 WebSocket Mixin: 收到服务器欢迎消息')
          break
        case 'auth_result':
          if (data.data.success) {
            console.log('✅ WebSocket Mixin: 认证成功')
          } else {
            console.error(`❌ WebSocket Mixin: 认证失败: ${data.data.message}`)
          }
          break
        case 'payment_notification':
          console.log('💰 WebSocket Mixin: 收到支付通知:', data.data)
          this.handlePaymentNotification(data.data)
          break
        case 'pong':
          console.log('💓 WebSocket Mixin: 收到心跳响应')
          break
        default:
          console.log(`ℹ️ WebSocket Mixin: 收到未知类型消息: ${data.type}`)
      }
    },

    /**
     * 处理支付通知
     */
    async handlePaymentNotification(paymentData) {
      try {
        const amount = paymentData.amount || '0.00'
        
        console.log(`🎵 WebSocket Mixin: 播放支付语音: 收款${amount}元`)
        
        // 检查语音设置
        if (!this.voiceEnabled) {
          console.log('🔇 语音播报已关闭，跳过播放')
          return
        }
        
        // 播放语音
        if (this.audioPlayer) {
          await this.audioPlayer.playPaymentSuccess(amount)
        }
        
        // 显示Toast提示
        uni.showToast({
          title: `收款 ¥${amount}`,
          icon: 'success',
          duration: 3000
        })
        
        console.log(`✅ WebSocket Mixin: 语音播放完成: 收款${amount}元`)
        
      } catch (error) {
        console.error('❌ WebSocket Mixin: 处理支付通知失败:', error)
      }
    },

    /**
     * 处理WebSocket错误
     */
    handleWebSocketError(error) {
      console.log('❌ WebSocket Mixin: 连接错误', error)
      this.wsError = true
      this.wsErrorMessage = error.message || '连接失败'
      
      if (error.isShow) {
        // 可以在这里显示错误提示
        // uni.showToast({
        //   title: error.message || '连接失败',
        //   icon: 'none'
        // })
      }
    },

    /**
     * 处理WebSocket成功
     */
    handleWebSocketSuccess(success) {
      console.log('✅ WebSocket Mixin: 连接成功', success)
      this.wsError = false
      this.wsErrorMessage = ''
    }
  }
}
