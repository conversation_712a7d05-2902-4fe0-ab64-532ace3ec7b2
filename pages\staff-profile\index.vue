<template>
  <view class="staff-profile-container">
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <view class="navbar-content">
        <view class="navbar-left" @tap="goBack">
          <text class="back-icon">←</text>
          <text class="navbar-title">个人设置</text>
        </view>
      </view>
    </view>

    <!-- 个人信息卡片 -->
    <view class="profile-card">
      <view class="profile-header">
        <view class="avatar-section">
          <view class="avatar">
            <text class="avatar-text">{{ staffInfo?.name?.charAt(0) || 'S' }}</text>
          </view>
          <button class="avatar-btn" @tap="changeAvatar">更换头像</button>
        </view>
        
        <view class="info-section">
          <text class="staff-name">{{ staffInfo?.name || '员工' }}</text>
          <text class="staff-role">{{ staffInfo?.role || '收银员' }}</text>
          <text class="staff-account">账号：{{ staffInfo?.account || 'N/A' }}</text>
        </view>
      </view>
    </view>

    <!-- 设置列表 -->
    <view class="settings-section">
      <view class="settings-group">
        <view class="group-title">基本信息</view>
        <view class="setting-item" @tap="editProfile">
          <view class="item-left">
            <text class="item-icon">👤</text>
            <text class="item-label">个人资料</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{ staffInfo?.name || '未设置' }}</text>
            <text class="item-arrow">></text>
          </view>
        </view>
        
        <view class="setting-item" @tap="changePassword">
          <view class="item-left">
            <text class="item-icon">🔒</text>
            <text class="item-label">修改密码</text>
          </view>
          <view class="item-right">
            <text class="item-value">安全设置</text>
            <text class="item-arrow">></text>
          </view>
        </view>
      </view>

      <view class="settings-group">
        <view class="group-title">工作设置</view>
        <view class="setting-item" @tap="viewQRCode">
          <view class="item-left">
            <text class="item-icon">📱</text>
            <text class="item-label">我的收款码</text>
          </view>
          <view class="item-right">
            <text class="item-value">查看二维码</text>
            <text class="item-arrow">></text>
          </view>
        </view>
        
        <view class="setting-item">
          <view class="item-left">
            <text class="item-icon">🔔</text>
            <text class="item-label">消息通知</text>
          </view>
          <view class="item-right">
            <switch 
              :checked="notificationEnabled" 
              @change="toggleNotification"
              color="#667eea"
            />
          </view>
        </view>
      </view>

      <view class="settings-group">
        <view class="group-title">其他设置</view>
        <view class="setting-item" @tap="viewHelp">
          <view class="item-left">
            <text class="item-icon">❓</text>
            <text class="item-label">帮助中心</text>
          </view>
          <view class="item-right">
            <text class="item-value">使用指南</text>
            <text class="item-arrow">></text>
          </view>
        </view>
        
        <view class="setting-item" @tap="aboutApp">
          <view class="item-left">
            <text class="item-icon">ℹ️</text>
            <text class="item-label">关于应用</text>
          </view>
          <view class="item-right">
            <text class="item-value">v1.0.0</text>
            <text class="item-arrow">></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-section">
      <button class="logout-btn" @tap="logout">
        <text class="logout-text">退出登录</text>
      </button>
    </view>

    <!-- 修改资料弹窗 -->
    <view class="modal-overlay" v-if="showProfileModal" @tap="closeProfilePopup">
      <view class="popup-content" @tap.stop>
        <view class="popup-header">
          <text class="popup-title">修改个人资料</text>
        </view>

        <view class="popup-body">
          <view class="form-item">
            <text class="form-label">姓名</text>
            <input
              class="form-input"
              v-model="editForm.name"
              placeholder="请输入姓名"
              maxlength="20"
            />
          </view>

          <view class="form-item">
            <text class="form-label">手机号</text>
            <input
              class="form-input"
              v-model="editForm.phone"
              placeholder="请输入手机号"
              type="number"
              maxlength="11"
            />
          </view>

          <view class="form-item">
            <text class="form-label">邮箱</text>
            <input
              class="form-input"
              v-model="editForm.email"
              placeholder="请输入邮箱"
              type="email"
            />
          </view>
        </view>

        <view class="popup-footer">
          <button class="popup-btn cancel" @tap="closeProfilePopup">取消</button>
          <button class="popup-btn confirm" @tap="saveProfile">保存</button>
        </view>
      </view>
    </view>

    <!-- 修改密码弹窗 -->
    <view class="modal-overlay" v-if="showPasswordModal" @tap="closePasswordPopup">
      <view class="popup-content" @tap.stop>
        <view class="popup-header">
          <text class="popup-title">修改密码</text>
        </view>

        <view class="popup-body">
          <view class="form-item">
            <text class="form-label">原密码</text>
            <input
              class="form-input"
              v-model="passwordForm.oldPassword"
              placeholder="请输入原密码"
              type="password"
              maxlength="20"
            />
          </view>

          <view class="form-item">
            <text class="form-label">新密码</text>
            <input
              class="form-input"
              v-model="passwordForm.newPassword"
              placeholder="请输入新密码"
              type="password"
              maxlength="20"
            />
          </view>

          <view class="form-item">
            <text class="form-label">确认密码</text>
            <input
              class="form-input"
              v-model="passwordForm.confirmPassword"
              placeholder="请再次输入新密码"
              type="password"
              maxlength="20"
            />
          </view>
        </view>

        <view class="popup-footer">
          <button class="popup-btn cancel" @tap="closePasswordPopup">取消</button>
          <button class="popup-btn confirm" @tap="savePassword">保存</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { request } from '@/utils/request.js'

export default {
  name: 'StaffProfile',
  data() {
    return {
      staffInfo: null,
      notificationEnabled: true,
      showProfileModal: false,
      showPasswordModal: false,
      editForm: {
        name: '',
        phone: '',
        email: ''
      },
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    }
  },
  
  onLoad() {
    console.log('📱 员工个人设置页面加载')
    this.initPage()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      this.checkLoginStatus()
      this.loadStaffInfo()
      this.loadSettings()
    },
    
    // 检查登录状态
    checkLoginStatus() {
      const staffToken = uni.getStorageSync('staff_token')
      const staffInfo = uni.getStorageSync('staff_info')
      
      if (!staffToken || !staffInfo) {
        console.log('❌ 员工未登录，跳转到登录页')
        uni.reLaunch({
          url: '/pages/staff-login/index'
        })
        return false
      }
      
      return true
    },
    
    // 加载员工信息
    loadStaffInfo() {
      const staffInfo = uni.getStorageSync('staff_info')
      if (staffInfo) {
        this.staffInfo = staffInfo
        this.editForm = {
          name: staffInfo.name || '',
          phone: staffInfo.phone || '',
          email: staffInfo.email || ''
        }
        console.log('✅ 员工信息加载成功:', staffInfo)
      }
    },
    
    // 加载设置
    loadSettings() {
      this.notificationEnabled = uni.getStorageSync('notification_enabled') !== false
    },
    
    // 更换头像
    changeAvatar() {
      uni.showActionSheet({
        itemList: ['拍照', '从相册选择'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.chooseImage('camera')
          } else if (res.tapIndex === 1) {
            this.chooseImage('album')
          }
        }
      })
    },
    
    // 选择图片
    chooseImage(sourceType) {
      uni.chooseImage({
        count: 1,
        sourceType: [sourceType],
        success: (res) => {
          // 这里可以上传头像
          console.log('选择的图片:', res.tempFilePaths[0])
          uni.showToast({
            title: '头像上传功能开发中',
            icon: 'none'
          })
        }
      })
    },
    
    // 编辑个人资料
    editProfile() {
      this.showProfileModal = true
    },

    // 关闭资料弹窗
    closeProfilePopup() {
      this.showProfileModal = false
    },
    
    // 保存个人资料
    async saveProfile() {
      try {
        if (!this.editForm.name.trim()) {
          uni.showToast({
            title: '请输入姓名',
            icon: 'none'
          })
          return
        }
        
        const response = await request({
          url: '/user/staff.php?act=updateProfile',
          method: 'POST',
          data: {
            staff_id: this.staffInfo?.id,
            name: this.editForm.name.trim(),
            phone: this.editForm.phone.trim(),
            email: this.editForm.email.trim()
          }
        })
        
        if (response && response.code === 0) {
          // 更新本地存储
          const updatedInfo = { ...this.staffInfo, ...this.editForm }
          uni.setStorageSync('staff_info', updatedInfo)
          this.staffInfo = updatedInfo
          
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
          
          this.closeProfilePopup()
        } else {
          throw new Error(response?.msg || '保存失败')
        }
        
      } catch (error) {
        console.error('❌ 保存个人资料失败:', error)
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        })
      }
    },
    
    // 修改密码
    changePassword() {
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.showPasswordModal = true
    },

    // 关闭密码弹窗
    closePasswordPopup() {
      this.showPasswordModal = false
    },
    
    // 保存密码
    async savePassword() {
      try {
        if (!this.passwordForm.oldPassword) {
          uni.showToast({
            title: '请输入原密码',
            icon: 'none'
          })
          return
        }
        
        if (!this.passwordForm.newPassword) {
          uni.showToast({
            title: '请输入新密码',
            icon: 'none'
          })
          return
        }
        
        if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
          uni.showToast({
            title: '两次密码输入不一致',
            icon: 'none'
          })
          return
        }
        
        if (this.passwordForm.newPassword.length < 6) {
          uni.showToast({
            title: '密码长度不能少于6位',
            icon: 'none'
          })
          return
        }
        
        const response = await request({
          url: '/user/staff.php?act=changePassword',
          method: 'POST',
          data: {
            staff_id: this.staffInfo?.id,
            old_password: this.passwordForm.oldPassword,
            new_password: this.passwordForm.newPassword
          }
        })
        
        if (response && response.code === 0) {
          uni.showToast({
            title: '密码修改成功',
            icon: 'success'
          })
          
          this.closePasswordPopup()
        } else {
          throw new Error(response?.msg || '密码修改失败')
        }
        
      } catch (error) {
        console.error('❌ 修改密码失败:', error)
        uni.showToast({
          title: error.message || '修改失败',
          icon: 'none'
        })
      }
    },
    
    // 查看收款码
    viewQRCode() {
      uni.navigateTo({
        url: '/pages/staff-qrcode/index'
      })
    },
    
    // 切换通知
    toggleNotification(e) {
      this.notificationEnabled = e.detail.value
      uni.setStorageSync('notification_enabled', this.notificationEnabled)
      
      uni.showToast({
        title: this.notificationEnabled ? '已开启通知' : '已关闭通知',
        icon: 'success'
      })
    },
    
    // 查看帮助
    viewHelp() {
      uni.showModal({
        title: '帮助中心',
        content: '1. 登录后可查看专属收款码\n2. 客户扫码支付后可在订单中查看\n3. 统计页面可查看收款数据\n4. 如有问题请联系店长',
        showCancel: false
      })
    },
    
    // 关于应用
    aboutApp() {
      uni.showModal({
        title: '关于应用',
        content: '员工收款系统 v1.0.0\n\n专为员工设计的收款管理工具\n安全、便捷、高效',
        showCancel: false
      })
    },
    
    // 退出登录
    logout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除登录信息
            uni.removeStorageSync('staff_token')
            uni.removeStorageSync('staff_info')
            uni.removeStorageSync('staff_merchant_uid')
            
            // 跳转到登录页
            uni.reLaunch({
              url: '/pages/staff-login/index'
            })
          }
        }
      })
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.staff-profile-container {
  min-height: 100vh;
  background: #f5f6fa;
}

.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  color: #ffffff;
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
}

.profile-card {
  margin: -20rpx 40rpx 40rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.profile-header {
  display: flex;
  align-items: center;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.avatar-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
}

.avatar-btn {
  font-size: 24rpx;
  color: #667eea;
  background: transparent;
  border: 1rpx solid #667eea;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
}

.info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.staff-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.staff-role {
  font-size: 28rpx;
  color: #666666;
}

.staff-account {
  font-size: 24rpx;
  color: #999999;
}

.settings-section {
  padding: 0 40rpx 40rpx;
}

.settings-group {
  margin-bottom: 40rpx;
}

.group-title {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
}

.setting-item {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 2rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.setting-item:first-child {
  border-radius: 12rpx 12rpx 2rpx 2rpx;
}

.setting-item:last-child {
  border-radius: 2rpx 2rpx 12rpx 12rpx;
  margin-bottom: 0;
}

.setting-item:only-child {
  border-radius: 12rpx;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.item-label {
  font-size: 32rpx;
  color: #333333;
}

.item-right {
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 28rpx;
  color: #666666;
  margin-right: 12rpx;
}

.item-arrow {
  font-size: 24rpx;
  color: #cccccc;
}

.logout-section {
  padding: 0 40rpx 60rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: #ff4d4f;
  border-radius: 12rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.popup-content {
  width: 600rpx;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 0 40rpx;
}

.popup-header {
  padding: 40rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.popup-body {
  padding: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
  font-size: 28rpx;
  color: #333333;
}

.popup-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.popup-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
}

.popup-btn.cancel {
  background: #f8f9fa;
  color: #666666;
}

.popup-btn.confirm {
  background: #667eea;
  color: #ffffff;
}
</style>
