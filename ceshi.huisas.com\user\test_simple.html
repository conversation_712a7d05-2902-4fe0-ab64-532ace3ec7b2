<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试动态码接口</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 500px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .loading { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h2>简单测试动态码接口</h2>
        
        <form id="testForm">
            <div class="form-group">
                <label for="money">支付金额：</label>
                <input type="number" id="money" name="money" value="1.00" step="0.01" min="0.01" required>
            </div>
            
            <div class="form-group">
                <label for="name">商品名称：</label>
                <input type="text" id="name" name="name" value="测试支付" required>
            </div>
            
            <div class="form-group">
                <label for="typeid">支付方式：</label>
                <select id="typeid" name="typeid" required>
                    <option value="">请选择支付方式</option>
                    <option value="1">支付宝</option>
                    <option value="2">微信支付</option>
                    <option value="3">QQ钱包</option>
                </select>
            </div>
            
            <button type="submit">测试接口</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            testAPI();
        });
        
        function testAPI() {
            const money = document.getElementById('money').value;
            const name = document.getElementById('name').value;
            const typeid = document.getElementById('typeid').value;
            
            if (!money || !name || !typeid) {
                showResult('请填写完整信息', 'error');
                return;
            }
            
            showResult('正在测试接口...', 'loading');
            
            // 创建FormData
            const formData = new FormData();
            formData.append('money', money);
            formData.append('name', name);
            formData.append('typeid', typeid);
            
            // 发送请求
            fetch('ajax_dtm.php?act=generate_qrcode_fast', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(text => {
                console.log('Raw response:', text);
                
                try {
                    const data = JSON.parse(text);
                    if (data.code === 0) {
                        showResult('接口调用成功！', 'success', data);
                    } else {
                        showResult('接口返回错误：' + data.msg, 'error', data);
                    }
                } catch (e) {
                    showResult('响应不是有效的JSON：' + text, 'error');
                }
            })
            .catch(error => {
                console.error('Request failed:', error);
                showResult('请求失败：' + error.message, 'error');
            });
        }
        
        function showResult(message, type, data = null) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + type;
            
            let html = '<h3>' + message + '</h3>';
            
            if (data) {
                html += '<h4>返回数据：</h4>';
                html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                if (data.data && data.data.qrcode_url) {
                    html += '<h4>二维码：</h4>';
                    html += '<img src="' + data.data.qrcode_url + '" alt="二维码" style="max-width: 300px;">';
                }
                
                if (data.data && data.data.payment_url) {
                    html += '<h4>支付链接：</h4>';
                    html += '<a href="' + data.data.payment_url + '" target="_blank">' + data.data.payment_url + '</a>';
                }
            }
            
            resultDiv.innerHTML = html;
        }
    </script>
</body>
</html>
