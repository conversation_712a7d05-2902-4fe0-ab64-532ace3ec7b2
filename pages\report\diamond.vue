<template>
	<view class="container">
		<!-- 顶部标题栏 -->
		<view class="header">
			<view class="header-left" @click="goBack">
				<image src="/static/home/<USER>" mode="aspectFit" class="icon-back"></image>
			</view>
			<text class="header-title">多维数据分析</text>
			<view class="header-right">
				<image src="/static/home/<USER>" mode="aspectFit" class="icon-share"></image>
			</view>
		</view>
		
		<!-- 数据选择器 -->
		<view class="selector">
			<view class="selector-item" :class="{ active: activeDataSet === 'business' }" @click="setDataSet('business')">
				<text>经营概览</text>
			</view>
			<view class="selector-item" :class="{ active: activeDataSet === 'product' }" @click="setDataSet('product')">
				<text>产品表现</text>
			</view>
			<view class="selector-item" :class="{ active: activeDataSet === 'customer' }" @click="setDataSet('customer')">
				<text>客户画像</text>
			</view>
		</view>
		
		<!-- 图表标题 -->
		<view class="chart-title">
			<text class="title">{{ chartTitle }}</text>
			<text class="subtitle">{{ chartSubtitle }}</text>
		</view>
		
		<!-- Diamagnetic 图表 -->
		<view class="chart-container">
			<diamond-chart :chartData="chartData" :options="chartOptions"></diamond-chart>
		</view>
		
		<!-- 数据解析 -->
		<view class="analysis-card">
			<view class="card-title">
				<text>数据解析</text>
			</view>
			<view class="insight-list">
				<view class="insight-item" v-for="(insight, index) in insights" :key="index">
					<view class="insight-icon" :style="{ backgroundColor: insight.color }">
						<text>{{ index + 1 }}</text>
					</view>
					<view class="insight-content">
						<text class="insight-title">{{ insight.title }}</text>
						<text class="insight-text">{{ insight.text }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 建议行动 -->
		<view class="action-card">
			<view class="card-title">
				<text>建议行动</text>
			</view>
			<view class="action-list">
				<view class="action-item" v-for="(action, index) in actions" :key="index" @click="handleAction(index)">
					<image :src="action.icon" mode="aspectFit" class="action-icon"></image>
					<text class="action-text">{{ action.text }}</text>
					<image src="/static/home/<USER>" mode="aspectFit" class="icon-arrow-right"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import DiamondChart from '@/components/DiamondChart.vue';

export default {
	components: {
		DiamondChart
	},
	data() {
		return {
			activeDataSet: 'business',
			chartTitle: '经营多维度分析',
			chartSubtitle: '旋转图表可查看不同维度数据',
			chartData: {},
			chartOptions: {},
			businessData: {
				categories: ['收入增长', '客户满意度', '交易频次', '客单价', '成本控制', '员工效率'],
				series: [
					{
						name: '本月表现',
						color: '#5145F7',
						data: [85, 92, 78, 80, 65, 88]
					},
					{
						name: '行业平均',
						color: '#FAAD14',
						data: [70, 75, 65, 72, 75, 68]
					}
				]
			},
			productData: {
				categories: ['销量', '利润率', '客户评分', '复购率', '增长趋势', '市场份额'],
				series: [
					{
						name: '主打产品',
						color: '#1890FF',
						data: [90, 85, 95, 82, 78, 75]
					},
					{
						name: '次要产品',
						color: '#4CD964',
						data: [75, 70, 80, 65, 60, 55]
					},
					{
						name: '新品',
						color: '#9013FE',
						data: [60, 75, 85, 45, 90, 40]
					}
				]
			},
			customerData: {
				categories: ['消费能力', '忠诚度', '活跃度', '影响力', '增长潜力', '价格敏感度'],
				series: [
					{
						name: '核心客户',
						color: '#FF4D4F',
						data: [92, 95, 88, 80, 85, 60]
					},
					{
						name: '普通客户',
						color: '#FAAD14',
						data: [70, 65, 60, 55, 75, 80]
					},
					{
						name: '潜在客户',
						color: '#1890FF',
						data: [50, 30, 45, 75, 95, 85]
					}
				]
			},
			insights: [
				{
					color: '#5145F7',
					title: '核心优势',
					text: '客户满意度表现优异，高于行业平均17个百分点，是您的核心竞争力。'
				},
				{
					color: '#FF4D4F',
					title: '需改进领域',
					text: '成本控制低于行业平均，可能影响长期盈利能力，建议优化供应链。'
				},
				{
					color: '#FAAD14',
					title: '潜在机会',
					text: '通过提高员工效率，进一步扩大与行业平均的差距，降低人力成本。'
				}
			],
			actions: [
				{
					icon: '/static/home/<USER>',
					text: '导出详细报告'
				},
				{
					icon: '/static/home/<USER>',
					text: '制定优化计划'
				},
				{
					icon: '/static/home/<USER>',
					text: '查看历史趋势'
				}
			]
		};
	},
	onReady() {
		this.setDataSet('business');
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		setDataSet(type) {
			this.activeDataSet = type;
			
			switch(type) {
				case 'business':
					this.chartData = this.businessData;
					this.chartTitle = '经营多维度分析';
					this.chartSubtitle = '旋转图表可查看不同维度数据';
					this.insights = [
						{
							color: '#5145F7',
							title: '核心优势',
							text: '客户满意度表现优异，高于行业平均17个百分点，是您的核心竞争力。'
						},
						{
							color: '#FF4D4F',
							title: '需改进领域',
							text: '成本控制低于行业平均，可能影响长期盈利能力，建议优化供应链。'
						},
						{
							color: '#FAAD14',
							title: '潜在机会',
							text: '通过提高员工效率，进一步扩大与行业平均的差距，降低人力成本。'
						}
					];
					break;
				case 'product':
					this.chartData = this.productData;
					this.chartTitle = '产品表现分析';
					this.chartSubtitle = '对比不同产品线的多维度表现';
					this.insights = [
						{
							color: '#1890FF',
							title: '主打产品优势',
							text: '主打产品在客户评分方面表现突出，是产品口碑的核心支撑。'
						},
						{
							color: '#9013FE',
							title: '新品增长亮点',
							text: '新品在增长趋势方面表现优异，但市场份额仍有提升空间。'
						},
						{
							color: '#4CD964',
							title: '产品矩阵建议',
							text: '次要产品的复购率偏低，建议提升产品体验或调整定价策略。'
						}
					];
					break;
				case 'customer':
					this.chartData = this.customerData;
					this.chartTitle = '客户画像分析';
					this.chartSubtitle = '了解不同客户群体的特征分布';
					this.insights = [
						{
							color: '#FF4D4F',
							title: '核心客户价值',
							text: '核心客户忠诚度极高，建议推出会员回馈计划，进一步增强客户粘性。'
						},
						{
							color: '#1890FF',
							title: '潜在客户转化',
							text: '潜在客户增长潜力高，但忠诚度低，可设计新客优惠活动促进转化。'
						},
						{
							color: '#FAAD14',
							title: '普通客户特点',
							text: '普通客户对价格较为敏感，可通过限时促销或套餐优惠提升消费频次。'
						}
					];
					break;
			}
		},
		handleAction(index) {
			const actions = ['导出报告', '制定计划', '查看趋势'];
			uni.showToast({
				title: `点击了${actions[index]}`,
				icon: 'none'
			});
		}
	}
};
</script>

<style>
page {
	background-color: #f5f5f5;
	font-family: 'Segoe UI', sans-serif;
}

.container {
	width: 100%;
	min-height: 100vh;
}

/* 顶部标题栏 */
.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #5145F7;
	color: white;
	padding: 32rpx;
}

.header-left, .header-right {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-back, .icon-share {
	width: 40rpx;
	height: 40rpx;
}

.header-title {
	font-size: 36rpx;
	font-weight: 500;
}

/* 数据选择器 */
.selector {
	display: flex;
	background-color: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.selector-item {
	flex: 1;
	padding: 24rpx 0;
	text-align: center;
	font-size: 28rpx;
	color: #666;
	position: relative;
}

.selector-item.active {
	color: #5145F7;
	font-weight: 500;
}

.selector-item.active:after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 25%;
	width: 50%;
	height: 4rpx;
	background-color: #5145F7;
	border-radius: 2rpx;
}

/* 图表标题 */
.chart-title {
	padding: 20rpx 32rpx;
	background-color: white;
}

.title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.subtitle {
	font-size: 24rpx;
	color: #999;
}

/* 图表容器 */
.chart-container {
	height: 750rpx;
	background-color: white;
	padding: 0;
	box-sizing: border-box;
	width: 100%;
	margin: 0;
}

/* 卡片样式 */
.analysis-card, .action-card {
	margin: 24rpx;
	padding: 24rpx;
	background-color: white;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 24rpx;
}

/* 数据解析 */
.insight-list {
	margin-top: 16rpx;
}

.insight-item {
	display: flex;
	margin-bottom: 24rpx;
}

.insight-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	flex-shrink: 0;
}

.insight-icon text {
	font-size: 24rpx;
	color: white;
	font-weight: 500;
}

.insight-content {
	flex: 1;
}

.insight-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.insight-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

/* 建议行动 */
.action-list {
	margin-top: 16rpx;
}

.action-item {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.action-item:last-child {
	border-bottom: none;
}

.action-icon {
	width: 48rpx;
	height: 48rpx;
	margin-right: 16rpx;
}

.action-text {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.icon-arrow-right {
	width: 32rpx;
	height: 32rpx;
}
</style> 