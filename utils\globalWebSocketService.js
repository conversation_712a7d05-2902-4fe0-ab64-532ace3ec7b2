/**
 * 全局 WebSocket 服务
 * 整合现有的 VoiceManager 和 WebSocket 功能
 * 提供全应用范围的实时语音播报
 */

import { webSocketService } from '@/utils/websocketService.js'
import voiceManager from '@/utils/voiceManager.js'
import AudioPlayer from '@/utils/audioPlayer.js'
import { getCurrentUserId, getCurrentUserChannel } from '@/utils/auth.js'

class GlobalWebSocketService {
  constructor() {
    console.log('🌐 初始化全局WebSocket服务')
    
    // 服务状态
    this.isStarted = false
    this.isPaused = false
    this.isConnected = false
    this.reconnectCount = 0
    this.maxReconnectAttempts = 5
    
    // 统计信息
    this.totalMessages = 0
    this.paymentNotifications = 0
    this.todayPayments = {
      count: 0,
      amount: 0
    }
    
    // 音频播放器
    this.audioPlayer = new AudioPlayer()
    
    // 绑定事件处理器
    this.handleWebSocketMessage = this.handleWebSocketMessage.bind(this)
    this.handleWebSocketConnect = this.handleWebSocketConnect.bind(this)
    this.handleWebSocketDisconnect = this.handleWebSocketDisconnect.bind(this)
    this.handleWebSocketError = this.handleWebSocketError.bind(this)
  }

  /**
   * 启动全局WebSocket服务
   */
  async start() {
    if (this.isStarted) {
      console.log('🌐 全局WebSocket服务已启动')
      return
    }

    try {
      console.log('🚀 启动全局WebSocket服务')
      
      // 检查用户认证
      const userId = getCurrentUserId()
      const userChannel = getCurrentUserChannel()
      
      if (!userId || !userChannel) {
        console.warn('⚠️ 用户未登录，跳过WebSocket连接')
        return
      }

      // 连接WebSocket（事件监听器在connect方法中注册）
      await this.connect()
      
      this.isStarted = true
      console.log('✅ 全局WebSocket服务启动成功')
      
    } catch (error) {
      console.error('❌ 全局WebSocket服务启动失败:', error)
      throw error
    }
  }

  /**
   * 停止全局WebSocket服务
   */
  stop() {
    console.log('🛑 停止全局WebSocket服务')

    this.disconnect()

    this.isStarted = false
    this.isPaused = false

    console.log('✅ 全局WebSocket服务已停止')
  }

  /**
   * 暂停服务（应用进入后台）
   */
  pause() {
    if (!this.isStarted) return
    
    console.log('⏸️ 暂停全局WebSocket服务')
    this.isPaused = true
    
    // 可以选择保持连接但降低心跳频率
    // 或者完全断开连接以节省资源
  }

  /**
   * 恢复服务（应用回到前台）
   */
  resume() {
    if (!this.isStarted) return
    
    console.log('▶️ 恢复全局WebSocket服务')
    this.isPaused = false
    
    // 如果连接断开，尝试重连
    if (!this.isConnected) {
      this.connect()
    }
  }

  /**
   * 连接WebSocket
   */
  async connect() {
    try {
      console.log('🔗 连接全局WebSocket')

      const userId = getCurrentUserId()
      const userChannel = getCurrentUserChannel()

      if (!userId || !userChannel) {
        throw new Error('用户认证信息缺失')
      }

      console.log('🔗 用户信息:', { userId, userChannel })

      // 使用现有的webSocketService连接，传入回调函数
      webSocketService.connect({
        onConnect: this.handleWebSocketConnect,
        onDisconnect: this.handleWebSocketDisconnect,
        onMessage: this.handleWebSocketMessage,
        onError: this.handleWebSocketError
      })

      // 订阅用户频道
      webSocketService.subscribe(userChannel)

      console.log('✅ WebSocket服务连接完成')

    } catch (error) {
      console.error('❌ WebSocket连接失败:', error)

      // 自动重连逻辑
      if (this.reconnectCount < this.maxReconnectAttempts) {
        this.reconnectCount++
        const delay = Math.min(1000 * Math.pow(2, this.reconnectCount), 30000)

        console.log(`🔄 ${delay}ms后尝试第${this.reconnectCount}次重连`)

        setTimeout(() => {
          this.connect()
        }, delay)
      } else {
        console.error('❌ WebSocket重连次数已达上限')
      }
    }
  }

  /**
   * 断开WebSocket
   */
  disconnect() {
    console.log('🔌 断开全局WebSocket')
    webSocketService.disconnect()
  }



  /**
   * 处理WebSocket消息
   */
  async handleWebSocketMessage(data) {
    if (this.isPaused) {
      console.log('⏸️ 服务已暂停，跳过消息处理')
      return
    }

    this.totalMessages++
    console.log('📨 收到WebSocket消息:', data)

    try {
      // 解析消息
      const message = typeof data === 'string' ? JSON.parse(data) : data
      
      // 处理支付通知
      if (this.isPaymentNotification(message)) {
        await this.handlePaymentNotification(message)
      }
      
    } catch (error) {
      console.error('❌ 处理WebSocket消息失败:', error)
    }
  }

  /**
   * 判断是否为支付通知
   */
  isPaymentNotification(message) {
    return message && (
           message.type === 'payment_success' ||
           message.type === 'payment_notification' ||  // Swoole服务器发送的类型
           (message.data && (message.data.amount || message.data.money))
    )
  }

  /**
   * 处理支付通知
   */
  async handlePaymentNotification(notification) {
    console.log('💰 处理支付通知:', notification)
    
    this.paymentNotifications++
    
    // 更新今日统计
    this.updateTodayStats(notification)
    
    // 检查语音设置
    const voiceStatus = voiceManager.getStatus()
    if (!voiceStatus.enabled) {
      console.log('🔇 语音播报已关闭，跳过播放')
      return
    }

    // 播放专业语音
    await this.playPaymentVoice(notification)
    
    // 触发全局事件（供页面监听）
    this.emitGlobalEvent('payment-notification', notification)
  }

  /**
   * 播放支付语音
   */
  async playPaymentVoice(notification) {
    try {
      const amount = notification.data?.amount || notification.data?.money || '0.00'
      
      console.log(`🎵 播放专业语音: 收款${amount}元`)
      
      // 使用专业语音拼接播放
      await this.audioPlayer.playPaymentSuccess(amount)
      
      console.log(`✅ 语音播放完成: 收款${amount}元`)
      
    } catch (error) {
      console.error('❌ 语音播放失败:', error)
      
      // 备用方案：使用VoiceManager的播放方法
      try {
        const amount = notification.data?.amount || notification.data?.money || '0.00'
        await voiceManager.speak(`收到支付${amount}元`)
      } catch (fallbackError) {
        console.error('❌ 备用语音播放也失败:', fallbackError)
      }
    }
  }

  /**
   * 更新今日统计
   */
  updateTodayStats(notification) {
    const amount = parseFloat(notification.data?.amount || notification.data?.money || 0)
    
    this.todayPayments.count++
    this.todayPayments.amount += amount
    
    console.log(`📊 今日统计更新: ${this.todayPayments.count}笔 / ¥${this.todayPayments.amount.toFixed(2)}`)
  }

  /**
   * 触发全局事件
   */
  emitGlobalEvent(eventName, data) {
    // 使用uni的事件系统
    uni.$emit(eventName, data)
  }

  /**
   * WebSocket连接成功
   */
  handleWebSocketConnect() {
    console.log('✅ 全局WebSocket连接成功')
    this.isConnected = true
    this.reconnectCount = 0
    
    this.emitGlobalEvent('websocket-connected', {
      timestamp: new Date().toISOString()
    })
  }

  /**
   * WebSocket连接断开
   */
  handleWebSocketDisconnect() {
    console.log('❌ 全局WebSocket连接断开')
    this.isConnected = false
    
    this.emitGlobalEvent('websocket-disconnected', {
      timestamp: new Date().toISOString()
    })
    
    // 如果服务未暂停，尝试重连
    if (this.isStarted && !this.isPaused) {
      setTimeout(() => {
        this.connect()
      }, 3000)
    }
  }

  /**
   * WebSocket错误
   */
  handleWebSocketError(error) {
    console.error('❌ 全局WebSocket错误:', error)
    
    this.emitGlobalEvent('websocket-error', {
      error: error.message || error,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      isStarted: this.isStarted,
      isPaused: this.isPaused,
      isConnected: this.isConnected,
      reconnectCount: this.reconnectCount,
      totalMessages: this.totalMessages,
      paymentNotifications: this.paymentNotifications,
      todayPayments: this.todayPayments,
      voiceEnabled: voiceManager.getStatus().enabled
    }
  }

  /**
   * 手动触发测试
   */
  async testPaymentNotification(amount = '88.88') {
    const testNotification = {
      type: 'payment_success',
      data: {
        amount: amount,
        order_id: `TEST_${Date.now()}`,
        timestamp: new Date().toISOString()
      }
    }
    
    console.log('🧪 触发测试支付通知:', testNotification)
    await this.handlePaymentNotification(testNotification)
  }
}

// 创建全局单例
const globalWebSocketService = new GlobalWebSocketService()

// 导出
export { globalWebSocketService }
export default globalWebSocketService
