<template>
  <view class="merchant-info">
    <custom-navbar
      title="商家信息"
      :show-back="true"
      :shadow="true"
      @clickLeft="goBack"
    >
      <template #right>
        <view @click="editInfo" style="padding: 0 16rpx;">
          <uni-icons type="compose" size="22" color="#FFFFFF"></uni-icons>
        </view>
      </template>
    </custom-navbar>
    
    <view class="info-section">
      <view class="section-title">基本信息</view>
      
      <uni-list>
        <uni-list-item 
          title="商家名称" 
          :rightText="merchantInfo.name" 
          clickable 
          showArrow 
          @click="editField('name')" 
        />
        <uni-list-item 
          title="经营类目" 
          :rightText="merchantInfo.category" 
          clickable 
          showArrow 
          @click="editField('category')" 
        />
        <uni-list-item 
          title="联系电话" 
          :rightText="merchantInfo.phone" 
          clickable 
          showArrow 
          @click="editField('phone')" 
        />
        <uni-list-item 
          title="营业时间" 
          :rightText="merchantInfo.hours" 
          clickable 
          showArrow 
          @click="editField('hours')" 
        />
      </uni-list>
    </view>
    
    <view class="info-section">
      <view class="section-title">位置信息</view>
      
      <uni-list>
        <uni-list-item 
          title="地址" 
          :rightText="merchantInfo.address" 
          clickable 
          showArrow 
          @click="editField('address')" 
        />
      </uni-list>
    </view>
  </view>
</template>

<script>
import uniList from '@/uni_modules/uni-list/components/uni-list/uni-list'
import uniListItem from '@/uni_modules/uni-list/components/uni-list-item/uni-list-item'
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons'

export default {
  components: {
    uniList,
    uniListItem,
  uniIcons,
    CustomNavbar
  },
  data() {
    return {
      merchantInfo: {
        name: '优选咖啡店',
        category: '餐饮/咖啡厅',
        phone: '13812345678',
        hours: '08:00-22:00',
        address: '广东省深圳市南山区科技园南区5栋101'
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    editInfo() {
      // 编辑全部信息
      uni.showToast({
        title: '编辑商家信息',
        icon: 'none'
      })
    },
    editField(field) {
      // 编辑单个字段
      uni.showToast({
        title: `编辑${field}`,
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss">
.merchant-info {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.info-section {
  margin-top: 15px;
}

.section-title {
  padding: 16px 20px;
  font-family: 'Segoe UI', sans-serif;
  font-weight: bold;
  font-size: 18px;
}

/* 自定义uni-list样式 */
:deep(.uni-list) {
  margin: 0;
}

:deep(.uni-list--border) {
  border-radius: 0;
}

:deep(.uni-list-item) {
  font-family: 'Segoe UI', sans-serif;
}

:deep(.uni-list-item__container) {
  padding: 18px 20px !important;
}

:deep(.uni-list-item__content-title) {
  font-weight: bold !important;
  font-size: 17px !important;
}

:deep(.uni-list-item__extra-text) {
  color: #333 !important;
  font-size: 17px !important;
}

:deep(.uni-icons) {
  align-items: center;
  line-height: 1;
}
</style>
