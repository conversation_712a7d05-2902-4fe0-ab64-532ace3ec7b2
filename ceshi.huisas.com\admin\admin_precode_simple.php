<?php
/**
 * 预制收款码管理 - 简化版本
 * 用于测试和调试
 */
include("../includes/common.php");
$title='预制收款码管理';
include './head.php';
if($islogin==1){}else exit("<script language='javascript'>window.location.href='./login.php';</script>");

// 错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>预制码功能测试</h2>";

// 1. 检查generatePreCode函数
echo "<h3>1. 检查generatePreCode函数</h3>";
if(function_exists('generatePreCode')) {
    echo "<p style='color:green'>✅ generatePreCode函数存在</p>";
    try {
        $test_code = generatePreCode();
        echo "<p style='color:green'>✅ 函数调用成功，生成测试码: {$test_code}</p>";
    } catch(Exception $e) {
        echo "<p style='color:red'>❌ 函数调用失败: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color:red'>❌ generatePreCode函数不存在</p>";
}

// 2. 检查数据库表
echo "<h3>2. 检查数据库表</h3>";
try {
    // 创建表
    $create_sql = "CREATE TABLE IF NOT EXISTS `{$dbconfig['dbqz']}_precode` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `code` varchar(50) NOT NULL COMMENT '预制码',
        `qr_url` text COMMENT '完整的二维码URL',
        `uid` int(11) DEFAULT NULL COMMENT '绑定的商户ID',
        `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0未绑定 1已绑定',
        `addtime` datetime DEFAULT NULL COMMENT '生成时间',
        `bindtime` datetime DEFAULT NULL COMMENT '绑定时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `code` (`code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预制收款码';";
    
    $DB->exec($create_sql);
    echo "<p style='color:green'>✅ 预制码表创建/检查成功</p>";
    
    // 检查表结构
    $columns = $DB->getAll("SHOW COLUMNS FROM `{$dbconfig['dbqz']}_precode`");
    echo "<p style='color:green'>✅ 表结构检查成功，字段数: " . count($columns) . "</p>";
    
} catch(Exception $e) {
    echo "<p style='color:red'>❌ 数据库操作失败: " . $e->getMessage() . "</p>";
}

// 3. 测试生成预制码
echo "<h3>3. 测试生成预制码</h3>";
if(isset($_POST['test_generate'])) {
    try {
        $code = generatePreCode();
        $qr_url = $siteurl . 'paypage/precode/' . $code;
        
        $result = $DB->exec("INSERT INTO `{$dbconfig['dbqz']}_precode` (`code`, `qr_url`, `addtime`) VALUES (:code, :qr_url, NOW())", 
            [':code'=>$code, ':qr_url'=>$qr_url]);
        
        if($result) {
            echo "<p style='color:green'>✅ 预制码生成成功: {$code}</p>";
            echo "<p>二维码URL: <a href='{$qr_url}' target='_blank'>{$qr_url}</a></p>";
        } else {
            echo "<p style='color:red'>❌ 预制码插入数据库失败</p>";
        }
    } catch(Exception $e) {
        echo "<p style='color:red'>❌ 生成预制码失败: " . $e->getMessage() . "</p>";
    }
}

// 4. 显示现有预制码
echo "<h3>4. 现有预制码列表</h3>";
try {
    $list = $DB->getAll("SELECT * FROM `{$dbconfig['dbqz']}_precode` ORDER BY id DESC LIMIT 10");
    if($list) {
        echo "<table border='1' style='border-collapse:collapse; width:100%'>";
        echo "<tr><th>ID</th><th>预制码</th><th>状态</th><th>生成时间</th><th>二维码URL</th></tr>";
        foreach($list as $row) {
            $status_text = $row['status'] == 1 ? '已绑定' : '未绑定';
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['code']}</td>";
            echo "<td>{$status_text}</td>";
            echo "<td>{$row['addtime']}</td>";
            echo "<td><a href='{$row['qr_url']}' target='_blank'>查看</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>暂无预制码记录</p>";
    }
} catch(Exception $e) {
    echo "<p style='color:red'>❌ 查询预制码失败: " . $e->getMessage() . "</p>";
}

// 5. 检查配置信息
echo "<h3>5. 系统配置信息</h3>";
echo "<p>数据库前缀: {$dbconfig['dbqz']}</p>";
echo "<p>站点URL: {$siteurl}</p>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";

?>

<h3>6. 操作面板</h3>
<form method="post">
    <button type="submit" name="test_generate" style="padding:10px 20px; background:#007bff; color:white; border:none; border-radius:4px;">
        生成测试预制码
    </button>
</form>

<br><br>
<a href="precode.php" style="padding:10px 20px; background:#28a745; color:white; text-decoration:none; border-radius:4px;">
    返回完整管理页面
</a>

</body>
</html>
