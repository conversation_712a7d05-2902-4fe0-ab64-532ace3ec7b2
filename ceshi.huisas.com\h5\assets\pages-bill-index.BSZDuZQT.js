import{n as t,A as a,B as e,d as n,e as r,w as m,i as s,o as l,f as o,z as i,h as u,t as c,j as d,C as p,D as y,F as f,l as h,k as N}from"./index-B1Q521gi.js";import{_}from"./custom-navbar.DuzuSmPc.js";import{r as g}from"./uni-app.es.DAfa8VxY.js";import{_ as T}from"./date.5CEjHFwh.js";import{_ as b}from"./_plugin-vue_export-helper.BCo6x5W8.js";const D=b({components:{CustomNavbar:_},data:()=>({currentFilter:"all",dateRange:["",""],filterData:{all:{totalAmount:"5,328.30",transactionCount:42,transactions:[{paymentType:"wechat",paymentName:"微信支付",time:"15:23:45",orderNumber:"202504140001",amount:"128.00",date:"今天 4月14日"},{paymentType:"cloud",paymentName:"云闪付支付",time:"13:05:22",orderNumber:"202504140002",amount:"85.50",date:"今天 4月14日"},{paymentType:"alipay",paymentName:"支付宝",time:"10:18:36",orderNumber:"202504140003",amount:"299.00",date:"今天 4月14日"},{paymentType:"wechat",paymentName:"微信支付",time:"18:42:15",orderNumber:"202504130001",amount:"156.80",date:"昨天 4月13日"},{paymentType:"alipay",paymentName:"支付宝",time:"16:30:45",orderNumber:"202504130002",amount:"420.00",date:"昨天 4月13日"},{paymentType:"cloud",paymentName:"云闪付支付",time:"09:24:33",orderNumber:"202504120001",amount:"78.50",date:"4月12日"}]},today:{totalAmount:"512.50",transactionCount:3,transactions:[{paymentType:"wechat",paymentName:"微信支付",time:"15:23:45",orderNumber:"202504140001",amount:"128.00",date:"今天 4月14日"},{paymentType:"cloud",paymentName:"云闪付支付",time:"13:05:22",orderNumber:"202504140002",amount:"85.50",date:"今天 4月14日"},{paymentType:"alipay",paymentName:"支付宝",time:"10:18:36",orderNumber:"202504140003",amount:"299.00",date:"今天 4月14日"}]},yesterday:{totalAmount:"576.80",transactionCount:2,transactions:[{paymentType:"wechat",paymentName:"微信支付",time:"18:42:15",orderNumber:"202504130001",amount:"156.80",date:"昨天 4月13日"},{paymentType:"alipay",paymentName:"支付宝",time:"16:30:45",orderNumber:"202504130002",amount:"420.00",date:"昨天 4月13日"}]},week:{totalAmount:"2,243.60",transactionCount:12,transactions:[{paymentType:"wechat",paymentName:"微信支付",time:"15:23:45",orderNumber:"202504140001",amount:"128.00",date:"今天 4月14日"},{paymentType:"cloud",paymentName:"云闪付支付",time:"13:05:22",orderNumber:"202504140002",amount:"85.50",date:"今天 4月14日"},{paymentType:"alipay",paymentName:"支付宝",time:"10:18:36",orderNumber:"202504140003",amount:"299.00",date:"今天 4月14日"},{paymentType:"wechat",paymentName:"微信支付",time:"18:42:15",orderNumber:"202504130001",amount:"156.80",date:"昨天 4月13日"},{paymentType:"alipay",paymentName:"支付宝",time:"16:30:45",orderNumber:"202504130002",amount:"420.00",date:"昨天 4月13日"}]},month:{totalAmount:"4,825.30",transactionCount:38,transactions:[{paymentType:"wechat",paymentName:"微信支付",time:"15:23:45",orderNumber:"202504140001",amount:"128.00",date:"今天 4月14日"},{paymentType:"cloud",paymentName:"云闪付支付",time:"13:05:22",orderNumber:"202504140002",amount:"85.50",date:"今天 4月14日"},{paymentType:"alipay",paymentName:"支付宝",time:"10:18:36",orderNumber:"202504140003",amount:"299.00",date:"今天 4月14日"},{paymentType:"wechat",paymentName:"微信支付",time:"18:42:15",orderNumber:"202504130001",amount:"156.80",date:"昨天 4月13日"},{paymentType:"alipay",paymentName:"支付宝",time:"16:30:45",orderNumber:"202504130002",amount:"420.00",date:"昨天 4月13日"},{paymentType:"cloud",paymentName:"云闪付支付",time:"09:24:33",orderNumber:"202504120001",amount:"78.50",date:"4月12日"}]},custom:{totalAmount:"0.00",transactionCount:0,transactions:[]}}}),computed:{summaryData(){return this.filterData[this.currentFilter]},currentTransactions(){return this.filterData[this.currentFilter].transactions},currentDateText(){if("custom"===this.currentFilter&&this.dateRange[0]&&this.dateRange[1])return`${this.formatDate(this.dateRange[0])} - ${this.formatDate(this.dateRange[1])}`;if(this.currentTransactions.length>0)return this.currentTransactions[0].date;return{all:"全部",today:"今天",yesterday:"昨天",week:"本周",month:"本月"}[this.currentFilter]||"今天"}},onLoad(t){this.changeFilter("all"),t&&t.startDate&&t.endDate&&(this.dateRange=[t.startDate,t.endDate],setTimeout((()=>{this.fetchCustomDateTransactions(t.startDate,t.endDate),this.currentFilter="custom"}),100))},methods:{changeFilter(t){this.currentFilter=t},navigateToCustomDatePage(){t({url:"/pages/bill/date-range"})},formatDate(t){if(!t)return"";const a=t.split("-");return 3===a.length?`${a[1].replace(/^0/,"")}月${a[2].replace(/^0/,"")}日`:t},fetchCustomDateTransactions(t,n){a({title:"加载中..."}),setTimeout((()=>{const a=Math.floor(10*Math.random())+1,r=(1e3*Math.random()+100).toFixed(2),m=[],s=["wechat","alipay","cloud"],l={wechat:"微信支付",alipay:"支付宝",cloud:"云闪付支付"};for(let e=0;e<a;e++){const a=s[Math.floor(Math.random()*s.length)],r=(200*Math.random()+50).toFixed(2),o=Math.floor(24*Math.random()),i=Math.floor(60*Math.random()),u=Math.floor(60*Math.random()),c=`${o.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}`;m.push({paymentType:a,paymentName:l[a],time:c,orderNumber:`20250414${(e+1).toString().padStart(4,"0")}`,amount:r,date:`${this.formatDate(t)} - ${this.formatDate(n)}`})}this.filterData.custom={totalAmount:r,transactionCount:a,transactions:m},e()}),500)},getPaymentClass:t=>({wechat:"wechat",alipay:"alipay",cloud:"cloud"}[t]||""),getPaymentIcon:t=>({wechat:"/static/home/<USER>",alipay:"/static/home/<USER>",cloud:"/static/home/<USER>"}[t]||""),viewDetails(a){t({url:`/pages/bill/details?id=${a.orderNumber}`})},getCurrentDate(){const t=new Date;return`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")}`}}},[["render",function(t,a,e,b,D,w){const C=g(n("custom-navbar"),_),F=h,k=s,v=N;return l(),r(k,{class:"container"},{default:m((()=>[o(C,{title:"交易记录","show-back":!1,shadow:!0}),o(k,{class:"time-filter"},{default:m((()=>[o(k,{class:"filter-row"},{default:m((()=>[o(k,{class:i(["filter-item",{active:"all"===D.currentFilter}]),onClick:a[0]||(a[0]=t=>w.changeFilter("all"))},{default:m((()=>[o(F,null,{default:m((()=>[u("全部")])),_:1})])),_:1},8,["class"]),o(k,{class:i(["filter-item",{active:"today"===D.currentFilter}]),onClick:a[1]||(a[1]=t=>w.changeFilter("today"))},{default:m((()=>[o(F,null,{default:m((()=>[u("今日")])),_:1})])),_:1},8,["class"]),o(k,{class:i(["filter-item",{active:"yesterday"===D.currentFilter}]),onClick:a[2]||(a[2]=t=>w.changeFilter("yesterday"))},{default:m((()=>[o(F,null,{default:m((()=>[u("昨日")])),_:1})])),_:1},8,["class"])])),_:1}),o(k,{class:"filter-row"},{default:m((()=>[o(k,{class:i(["filter-item",{active:"week"===D.currentFilter}]),onClick:a[3]||(a[3]=t=>w.changeFilter("week"))},{default:m((()=>[o(F,null,{default:m((()=>[u("本周")])),_:1})])),_:1},8,["class"]),o(k,{class:i(["filter-item",{active:"month"===D.currentFilter}]),onClick:a[4]||(a[4]=t=>w.changeFilter("month"))},{default:m((()=>[o(F,null,{default:m((()=>[u("本月")])),_:1})])),_:1},8,["class"]),o(k,{class:i(["filter-item",{active:"custom"===D.currentFilter}]),onClick:w.navigateToCustomDatePage},{default:m((()=>[o(v,{src:T,mode:"aspectFit",class:"icon-calendar"}),o(F,null,{default:m((()=>[u("自定义")])),_:1})])),_:1},8,["class","onClick"])])),_:1})])),_:1}),o(k,{class:"bill-summary"},{default:m((()=>[o(F,{class:"summary-title"},{default:m((()=>[u("收款总览")])),_:1}),o(k,{class:"summary-content"},{default:m((()=>[o(k,{class:"summary-item"},{default:m((()=>[o(F,{class:"item-title"},{default:m((()=>[u("收款总额")])),_:1}),o(F,{class:"item-value"},{default:m((()=>[u("¥"+c(w.summaryData.totalAmount),1)])),_:1})])),_:1}),o(k,{class:"summary-item"},{default:m((()=>[o(F,{class:"item-title"},{default:m((()=>[u("交易笔数")])),_:1}),o(F,{class:"item-value"},{default:m((()=>[u(c(w.summaryData.transactionCount),1)])),_:1})])),_:1})])),_:1})])),_:1}),o(k,{class:"date-header"},{default:m((()=>[o(F,{class:"date"},{default:m((()=>[u(c(w.currentDateText),1)])),_:1}),o(F,{class:"count"},{default:m((()=>[u("共"+c(w.currentTransactions.length)+"笔",1)])),_:1})])),_:1}),o(k,{class:"transaction-list"},{default:m((()=>[0===w.currentTransactions.length?(l(),r(k,{key:0,class:"empty-state"},{default:m((()=>[o(F,null,{default:m((()=>[u("暂无交易记录")])),_:1})])),_:1})):d("",!0),(l(!0),p(f,null,y(w.currentTransactions,((t,a)=>(l(),r(k,{key:a,class:"transaction-item"},{default:m((()=>[o(k,{class:"transaction-left"},{default:m((()=>[o(k,{class:i(["payment-icon",w.getPaymentClass(t.paymentType)])},{default:m((()=>[o(v,{src:w.getPaymentIcon(t.paymentType),mode:"aspectFit"},null,8,["src"])])),_:2},1032,["class"]),o(k,{class:"transaction-info"},{default:m((()=>[o(F,{class:"payment-name"},{default:m((()=>[u(c(t.paymentName),1)])),_:2},1024),o(F,{class:"payment-time"},{default:m((()=>[u(c(t.time),1)])),_:2},1024),o(F,{class:"order-number"},{default:m((()=>[u("订单号: "+c(t.orderNumber),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),o(k,{class:"transaction-right"},{default:m((()=>[o(F,{class:"amount"},{default:m((()=>[u("+ ¥ "+c(t.amount),1)])),_:2},1024),o(F,{class:"details",onClick:a=>w.viewDetails(t)},{default:m((()=>[u("详情")])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1})}],["__scopeId","data-v-9ad89093"]]);export{D as default};
