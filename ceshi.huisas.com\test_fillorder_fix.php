<?php
/**
 * 测试手动补单修复
 * 验证订单状态更新和WebSocket通知是否正常工作
 */

include_once './includes/common.php';

echo "<h1>🧪 测试手动补单修复</h1>\n";

// 测试订单号
$test_trade_no = '2025072717481113375';

echo "<h2>📋 测试订单: {$test_trade_no}</h2>\n";

// 1. 检查当前订单状态
echo "<h3>🔍 1. 检查当前订单状态</h3>\n";
$order = $DB->getRow("SELECT * FROM pay_order WHERE trade_no = ?", [$test_trade_no]);

if (!$order) {
    echo "❌ 订单不存在！\n";
    exit;
}

echo "<p>当前订单状态: <strong>" . $order['status'] . "</strong></p>\n";
echo "<p>订单金额: {$order['money']} 元</p>\n";
echo "<p>创建时间: {$order['addtime']}</p>\n";
echo "<p>结束时间: " . ($order['endtime'] ?: '未设置') . "</p>\n";

// 2. 如果订单已经是已支付状态，先重置为未支付状态进行测试
if ($order['status'] == '1') {
    echo "<h3>🔄 2. 重置订单状态为未支付（用于测试）</h3>\n";
    $resetResult = $DB->exec("UPDATE pay_order SET status = '0', endtime = NULL WHERE trade_no = ?", [$test_trade_no]);
    if ($resetResult !== false) {
        echo "<p>✅ 订单状态已重置为未支付</p>\n";
        $order['status'] = '0'; // 更新本地变量
    } else {
        echo "<p>❌ 重置订单状态失败: " . $DB->error() . "</p>\n";
    }
}

// 3. 测试修复后的手动补单功能
echo "<h3>🔧 3. 测试修复后的手动补单功能</h3>\n";

// 模拟POST请求数据
$_POST['trade_no'] = $test_trade_no;

echo "<p>📞 模拟调用手动补单接口...</p>\n";

// 获取订单数据（补单前）
$row = $DB->getRow("SELECT A.*,B.name typename,B.showname typeshowname FROM pay_order A left join pay_type B on A.type=B.id WHERE trade_no=:trade_no limit 1", [':trade_no'=>$test_trade_no]);

if (!$row) {
    echo "<p>❌ 订单不存在！</p>\n";
    exit;
}

if ($row['status'] > 0) {
    echo "<p>❌ 当前订单不是未完成状态！状态: {$row['status']}</p>\n";
    exit;
}

echo "<p>📋 补单前订单状态: {$row['status']}</p>\n";

// 执行状态更新
$date = date('Y-m-d H:i:s');
$updateResult = $DB->exec("update `pay_order` set `status` ='1' where `trade_no`='$test_trade_no'");

if ($updateResult !== false) {
    echo "<p>✅ 订单状态更新成功</p>\n";
    
    // 更新结束时间
    $DB->exec("update `pay_order` set `endtime` ='$date',`date` =NOW() where `trade_no`='$test_trade_no'");
    echo "<p>✅ 订单结束时间更新成功</p>\n";
    
    // 获取渠道信息
    $channel = \lib\Channel::get($row['channel']);
    echo "<p>📡 获取渠道信息: " . ($channel ? "成功 (ID: {$channel['id']})" : "失败") . "</p>\n";
    
    // 🔥 使用修复后的逻辑：重新获取更新后的订单数据
    echo "<p>🔄 重新获取更新后的订单数据...</p>\n";
    $updatedRow = $DB->getRow("SELECT A.*,B.name typename,B.showname typeshowname FROM pay_order A left join pay_type B on A.type=B.id WHERE trade_no=:trade_no limit 1", [':trade_no'=>$test_trade_no]);
    
    if ($updatedRow) {
        echo "<p>✅ 重新获取订单数据成功</p>\n";
        echo "<p>📋 更新后订单状态: <strong>{$updatedRow['status']}</strong></p>\n";
        echo "<p>📋 更新后结束时间: {$updatedRow['endtime']}</p>\n";
        
        // 调用processOrder函数
        echo "<p>📞 调用 processOrder 函数...</p>\n";
        
        try {
            // 添加日志记录开始标记
            \lib\Payment::writePaymentLog('fillorder_test_start', [
                'message' => '开始测试修复后的手动补单功能',
                'trade_no' => $test_trade_no,
                'order_status_before_process' => $updatedRow['status']
            ]);
            
            processOrder($updatedRow);
            echo "<p>✅ processOrder 函数调用成功</p>\n";
            
            // 添加日志记录结束标记
            \lib\Payment::writePaymentLog('fillorder_test_end', [
                'message' => '手动补单功能测试完成',
                'trade_no' => $test_trade_no
            ]);
            
        } catch (Exception $e) {
            echo "<p>❌ processOrder 函数调用失败: " . $e->getMessage() . "</p>\n";
        }
        
    } else {
        echo "<p>⚠️ 重新获取订单数据失败，使用备用方案</p>\n";
        $row['status'] = '1';
        $row['endtime'] = $date;
        $row['date'] = date('Y-m-d H:i:s');
        
        try {
            processOrder($row);
            echo "<p>✅ 备用方案 processOrder 函数调用成功</p>\n";
        } catch (Exception $e) {
            echo "<p>❌ 备用方案 processOrder 函数调用失败: " . $e->getMessage() . "</p>\n";
        }
    }
    
} else {
    echo "<p>❌ 订单状态更新失败: " . $DB->error() . "</p>\n";
}

// 4. 检查最终结果
echo "<h3>🎯 4. 检查最终结果</h3>\n";

$finalOrder = $DB->getRow("SELECT * FROM pay_order WHERE trade_no = ?", [$test_trade_no]);
if ($finalOrder) {
    echo "<p>最终订单状态: <strong>{$finalOrder['status']}</strong></p>\n";
    echo "<p>最终结束时间: " . ($finalOrder['endtime'] ?: '未设置') . "</p>\n";
    
    if ($finalOrder['status'] == '1') {
        echo "<p>✅ 订单状态更新成功！</p>\n";
    } else {
        echo "<p>❌ 订单状态更新失败！</p>\n";
    }
} else {
    echo "<p>❌ 无法获取最终订单状态</p>\n";
}

echo "<h3>📝 5. 查看相关日志</h3>\n";
echo "<p>请检查日志文件: <code>includes/logs/payment_debug_" . date('Y-m-d') . ".log</code></p>\n";
echo "<p>查找关键词: <code>fillorder_test</code>, <code>websocket_check_conditions</code>, <code>websocket_notification</code></p>\n";

echo "<h3>🎉 测试完成</h3>\n";
echo "<p>如果看到 WebSocket 通知相关的日志，说明修复成功！</p>\n";
?>
