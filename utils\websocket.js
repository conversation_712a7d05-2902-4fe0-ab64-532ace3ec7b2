/**
 * WebSocket 工具类 (uni-app 适配版本)
 * 基于开源项目：https://gitee.com/mail_osc/websocket.js
 * 适配 uni-app 环境，支持断线重连和内置心跳
 * 
 * 特性：
 * 1. 断线自动重连（正常挂着时，断线自动重连；发送消息时，断线自动重连，并将之前的消息在连接成功后发送）
 * 2. 内置心跳机制（自动发送心跳消息；用onmessage接收消息时会自动过滤掉心跳消息）
 * 3. uni-app 兼容（使用 uni.connectSocket 替代原生 WebSocket）
 */

class WebSocketClient {
  constructor() {
    this.version = 'v1.0.0.20241217_uniapp'
    this.socketTask = null
    this.url = null
    this.connecting = false // 当前websocket是否正在连接中
    this.isConnected = false // 连接状态
    this.reconnectAttempts = 0 // 重连尝试次数
    this.maxReconnectAttempts = 10 // 最大重连次数（增加）
    this.reconnectInterval = 1000 // 重连间隔(毫秒)（减少到1秒）
    this.heartbeatTimer = null // 心跳定时器
    this.reconnectTimer = null // 重连定时器
    this.connectTimeout = null // 连接超时定时器
    this.connectTimeoutDuration = 5000 // 连接超时时间(5秒)
    
    // 回调函数
    this.onOpenCallback = null
    this.onMessageCallback = null
    this.onCloseCallback = null
    this.onErrorCallback = null
    
    // 心跳配置
    this.heartBeat = {
      time: 40, // 心跳间隔时间(秒)
      text: '{"type":"HEARTBEAT","text":"AreYouThere"}', // 心跳消息内容
      isStart: false // 心跳是否已启动
    }
    
    // 消息队列（用于重连后重发）
    this.messageQueue = []
  }

  /**
   * 连接WebSocket
   * @param {Object} options 连接配置
   * @param {String} options.url WebSocket服务器地址
   * @param {Function} options.onopen 连接成功回调
   * @param {Function} options.onmessage 消息接收回调
   * @param {Function} options.onclose 连接关闭回调
   * @param {Function} options.onerror 错误回调
   */
  connect(options = {}) {
    if (typeof options === 'string') {
      // 兼容旧版本，直接传入URL的情况
      this.url = options
    } else {
      if (options.url) this.url = options.url
      if (options.onopen) this.onOpenCallback = options.onopen
      if (options.onmessage) this.onMessageCallback = options.onmessage
      if (options.onclose) this.onCloseCallback = options.onclose
      if (options.onerror) this.onErrorCallback = options.onerror
    }

    if (!this.url) {
      console.error('WebSocket URL is required')
      return
    }

    if (this.connecting) {
      console.log('WebSocket is connecting, ignore duplicate connect request')
      return
    }

    console.log('WebSocket connecting...', new Date().toLocaleString())
    this.connecting = true

    // 设置连接超时
    this.connectTimeout = setTimeout(() => {
      if (this.connecting) {
        console.warn('WebSocket连接超时，尝试重连...')
        this.connecting = false
        if (this.socketTask) {
          uni.closeSocket()
          this.socketTask = null
        }
        this.scheduleReconnect()
      }
    }, this.connectTimeoutDuration)

    // 创建WebSocket连接
    this.socketTask = uni.connectSocket({
      url: this.url,
      success: () => {
        console.log('WebSocket connect request sent successfully')
      },
      fail: (error) => {
        console.error('WebSocket connect request failed:', error)
        this.connecting = false
        this.clearConnectTimeout()
        this.handleError('连接请求失败', error)
      }
    })

    // 监听连接打开
    this.socketTask.onOpen(() => {
      console.log('WebSocket connection opened')
      this.connecting = false
      this.isConnected = true
      this.reconnectAttempts = 0

      // 清除连接超时
      this.clearConnectTimeout()

      // 启动心跳
      this.startHeartBeat()

      // 重发队列中的消息
      this.resendQueuedMessages()

      // 执行用户自定义的onopen回调
      if (this.onOpenCallback) {
        this.onOpenCallback()
      }
    })

    // 监听消息接收
    this.socketTask.onMessage((res) => {
      try {
        const message = JSON.parse(res.data)
        
        // 过滤心跳消息
        if (message.type === 'HEARTBEAT') {
          console.log('Received heartbeat message, ignored')
          return
        }
        
        // 执行用户自定义的onmessage回调
        if (this.onMessageCallback) {
          this.onMessageCallback(message)
        }
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error)
      }
    })

    // 监听连接关闭
    this.socketTask.onClose((res) => {
      console.log('WebSocket connection closed:', res)
      this.isConnected = false
      this.connecting = false
      this.stopHeartBeat()
      
      // 执行用户自定义的onclose回调
      if (this.onCloseCallback) {
        this.onCloseCallback(res)
      }
      
      // 自动重连
      this.scheduleReconnect()
    })

    // 监听连接错误
    this.socketTask.onError((error) => {
      console.error('WebSocket error:', error)
      this.isConnected = false
      this.connecting = false
      this.handleError('连接错误', error)
    })
  }

  /**
   * 发送消息
   * @param {String|Object} message 要发送的消息
   */
  send(message) {
    if (!this.isConnected) {
      console.log('WebSocket not connected, adding message to queue and attempting reconnect')
      
      // 将消息加入队列
      this.messageQueue.push(message)
      
      // 尝试重连
      setTimeout(() => {
        this.connect()
      }, 200)
      
      return
    }

    // 转换消息格式
    let messageStr = message
    if (typeof message === 'object') {
      messageStr = JSON.stringify(message)
    }

    // 发送消息
    uni.sendSocketMessage({
      data: messageStr,
      success: () => {
        console.log('Message sent successfully:', messageStr)
      },
      fail: (error) => {
        console.error('Failed to send message:', error)
        // 发送失败，加入重发队列
        this.messageQueue.push(message)
      }
    })
  }

  /**
   * 关闭连接
   */
  close() {
    this.stopHeartBeat()
    this.stopReconnect()
    
    if (this.socketTask) {
      uni.closeSocket()
      this.socketTask = null
    }
    
    this.isConnected = false
    this.connecting = false
    console.log('WebSocket connection closed manually')
  }

  /**
   * 启动心跳
   */
  startHeartBeat() {
    if (this.heartBeat.isStart) {
      console.log('Heartbeat already started')
      return
    }

    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.send(this.heartBeat.text)
        console.log('Heartbeat sent')
      }
    }, this.heartBeat.time * 1000)

    this.heartBeat.isStart = true
    console.log(`Heartbeat started, interval: ${this.heartBeat.time}s`)
  }

  /**
   * 停止心跳
   */
  stopHeartBeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
    this.heartBeat.isStart = false
    console.log('Heartbeat stopped')
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnect attempts reached, giving up')
      return
    }

    this.reconnectAttempts++
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`)

    this.reconnectTimer = setTimeout(() => {
      console.log('Attempting to reconnect...')
      this.connect()
    }, this.reconnectInterval)
  }

  /**
   * 停止重连
   */
  stopReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 清除连接超时
   */
  clearConnectTimeout() {
    if (this.connectTimeout) {
      clearTimeout(this.connectTimeout)
      this.connectTimeout = null
    }
  }

  /**
   * 重发队列中的消息
   */
  resendQueuedMessages() {
    if (this.messageQueue.length > 0) {
      console.log(`Resending ${this.messageQueue.length} queued messages`)
      const messages = [...this.messageQueue]
      this.messageQueue = []
      
      messages.forEach(message => {
        this.send(message)
      })
    }
  }

  /**
   * 处理错误
   */
  handleError(type, error) {
    console.error(`WebSocket ${type}:`, error)
    
    if (this.onErrorCallback) {
      this.onErrorCallback({ type, error })
    }
    
    // 自动重连
    this.scheduleReconnect()
  }
}

// 创建全局实例（兼容原版本的使用方式）
const websocket = new WebSocketClient()

// 兼容原版本的API
websocket.connect = function(options) {
  return WebSocketClient.prototype.connect.call(this, options)
}

export default WebSocketClient
export { websocket }
