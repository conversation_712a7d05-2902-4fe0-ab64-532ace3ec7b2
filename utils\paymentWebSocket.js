/**
 * 支付系统 WebSocket 服务类
 * 基于 websocket.js 封装，专门用于支付通知功能
 * 
 * 功能特性：
 * 1. 🔄 自动重连机制
 * 2. 💓 内置心跳检测  
 * 3. 🎵 支付成功语音播报
 * 4. 📱 消息通知提醒
 * 5. 📊 连接状态管理
 */

import WebSocketClient from './websocket.js'

class PaymentWebSocketService {
  constructor() {
    this.client = new WebSocketClient()
    this.isInitialized = false
    this.config = {
      host: 'ceshi.huisas.com',
      port: 8080,
      app_key: 'payment_websocket_2024', // 添加app_key配置
      merchantId: '1000', // 默认商户ID设为1000
      reconnectInterval: 1000, // 减少重连间隔到1秒
      heartbeatInterval: 15000 // 减少心跳间隔到15秒
    }
    
    // 事件监听器
    this.listeners = {
      connect: [],
      disconnect: [],
      payment: [],
      error: [],
      message: []
    }
    
    // 统计信息
    this.stats = {
      connectTime: null,
      totalMessages: 0,
      paymentNotifications: 0,
      reconnectCount: 0
    }
  }

  /**
   * 初始化WebSocket连接
   * @param {Object} options 配置选项
   */
  init(options = {}) {
    if (this.isInitialized) {
      console.log('PaymentWebSocket already initialized')
      return
    }

    // 合并配置
    this.config = { ...this.config, ...options }

    // 构建WebSocket URL - 修复：Swoole WebSocket使用标准协议，无需路径
    // Swoole WebSocket服务器监听标准WebSocket协议，直接连接端口即可
    const wsUrl = `ws://${this.config.host}:${this.config.port}`

    console.log('🚀 初始化Swoole WebSocket服务:', wsUrl)
    console.log('🔧 服务器类型: Swoole WebSocket')
    
    // 配置WebSocket客户端
    this.client.heartBeat.time = this.config.heartbeatInterval / 1000
    this.client.reconnectInterval = this.config.reconnectInterval
    
    // 连接WebSocket
    this.client.connect({
      url: wsUrl,
      onopen: this.handleConnect.bind(this),
      onmessage: this.handleMessage.bind(this),
      onclose: this.handleDisconnect.bind(this),
      onerror: this.handleError.bind(this)
    })
    
    this.isInitialized = true
  }

  /**
   * 连接成功处理
   */
  handleConnect() {
    console.log('✅ 支付WebSocket连接成功')
    this.stats.connectTime = new Date()
    this.stats.reconnectCount++

    // 订阅支付频道
    this.subscribePaymentChannel()

    // 触发连接事件
    this.emit('connect', {
      timestamp: this.stats.connectTime,
      reconnectCount: this.stats.reconnectCount
    })

    // 显示连接成功提示
    uni.showToast({
      title: '💰 支付监听已开启',
      icon: 'success',
      duration: 2000
    })
  }

  /**
   * 消息处理 - 添加商户ID过滤
   */
  handleMessage(message) {
    console.log('📨 收到WebSocket消息:', message)
    this.stats.totalMessages++

    // 检查是否是当前商户的消息
    if (message.merchant_id && !this.isCurrentMerchant(message.merchant_id)) {
      console.log('🔒 消息不属于当前商户，已过滤:', message.merchant_id)
      return
    }

    // 根据消息类型处理
    switch (message.type) {
      case 'welcome':
        this.handleWelcome(message)
        break
      case 'payment_success':
      case 'payment_notification':  // Swoole服务器发送的类型
        this.handlePaymentSuccess(message)
        break
      case 'voice_alert':
        this.handleVoiceAlert(message)
        break
      case 'payment_failed':
        this.handlePaymentFailed(message)
        break
      case 'auth_result':
        this.handleAuthResult(message)
        break
      default:
        this.handleGenericMessage(message)
    }
    
    // 触发通用消息事件
    this.emit('message', message)
  }

  /**
   * 处理欢迎消息
   */
  handleWelcome(message) {
    console.log('🎉 收到服务器欢迎消息:', message.data?.message)
  }

  /**
   * 处理支付成功消息
   */
  handlePaymentSuccess(message) {
    console.log('💰 支付成功通知:', message.data)
    this.stats.paymentNotifications++
    
    const paymentData = message.data || {}
    
    // 播放语音提示
    this.playPaymentSound(paymentData)
    
    // 显示通知
    this.showPaymentNotification(paymentData)
    
    // 触发支付事件
    this.emit('payment', {
      type: 'success',
      data: paymentData,
      timestamp: new Date()
    })
  }

  /**
   * 处理支付失败消息
   */
  handlePaymentFailed(message) {
    console.log('❌ 支付失败通知:', message.data)
    
    this.emit('payment', {
      type: 'failed',
      data: message.data || {},
      timestamp: new Date()
    })
  }

  /**
   * 处理认证结果
   */
  handleAuthResult(message) {
    const { success, message: authMessage } = message.data || {}
    
    if (success) {
      console.log('✅ WebSocket认证成功')
    } else {
      console.error('❌ WebSocket认证失败:', authMessage)
    }
  }

  /**
   * 处理通用消息
   */
  handleGenericMessage(message) {
    console.log('📝 收到通用消息:', message)
  }

  /**
   * 连接断开处理
   */
  handleDisconnect(event) {
    console.log('❌ 支付WebSocket连接断开:', event)
    this.stats.connectTime = null
    
    this.emit('disconnect', {
      event,
      timestamp: new Date()
    })
    
    // 显示断开提示
    uni.showToast({
      title: '⚠️ 支付监听已断开',
      icon: 'none',
      duration: 2000
    })
  }

  /**
   * 错误处理
   */
  handleError(error) {
    console.error('❌ 支付WebSocket错误:', error)
    
    this.emit('error', {
      error,
      timestamp: new Date()
    })
  }

  /**
   * 发送认证消息
   */
  sendAuth() {
    // 获取用户token和商户ID
    const token = uni.getStorageSync('user_token') || 'anonymous'
    const merchantId = uni.getStorageSync('user_uid') || uni.getStorageSync('merchant_id') || this.config.merchantId

    console.log('🔐 发送认证消息，商户ID:', merchantId)

    this.client.send({
      type: 'auth',
      data: {
        token: token,
        merchant_id: merchantId,
        client_type: 'uniapp',
        version: '1.0.0'
      }
    })

    // 认证后自动订阅商户专属频道
    if (merchantId) {
      setTimeout(() => {
        this.subscribeToMerchantChannel(merchantId)
      }, 1000)
    }
  }

  /**
   * 播放支付语音提示
   */
  playPaymentSound(paymentData) {
    const amount = paymentData.amount || paymentData.money || '0.00'
    
    // 使用uni-app的语音播报API
    // #ifdef APP-PLUS
    plus.speech.speak(`收到支付${amount}元`, {
      volume: 0.8,
      speed: 1.0
    })
    // #endif
    
    // #ifdef H5
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(`收到支付${amount}元`)
      utterance.lang = 'zh-CN'
      utterance.volume = 0.8
      utterance.rate = 1.0
      speechSynthesis.speak(utterance)
    }
    // #endif
    
    console.log(`🎵 播放语音: 收到支付${amount}元`)
  }

  /**
   * 显示支付通知
   */
  showPaymentNotification(paymentData) {
    const amount = paymentData.amount || paymentData.money || '0.00'
    const tradeNo = paymentData.trade_no || paymentData.order_no || ''
    
    uni.showModal({
      title: '💰 收到新支付',
      content: `金额: ¥${amount}\n订单: ${tradeNo}`,
      showCancel: false,
      confirmText: '确定',
      success: () => {
        console.log('支付通知已确认')
      }
    })
  }

  /**
   * 检查是否是当前商户的消息
   */
  isCurrentMerchant(merchantId) {
    // 从本地存储或全局状态获取当前商户ID
    const currentMerchantId = uni.getStorageSync('merchant_id') ||
                             uni.getStorageSync('uid') ||
                             uni.getStorageSync('user_id')

    console.log('🔍 商户ID检查:', { current: currentMerchantId, message: merchantId })

    // 如果没有商户ID，默认接收所有消息（开发测试用）
    if (!currentMerchantId) {
      console.log('⚠️ 未找到当前商户ID，接收所有消息')
      return true
    }

    return String(currentMerchantId) === String(merchantId)
  }

  /**
   * 处理语音播报消息
   */
  handleVoiceAlert(message) {
    console.log('🎵 处理语音播报:', message)
    this.stats.paymentNotifications++

    // 触发语音播报事件
    this.emit('payment', {
      type: 'voice_alert',
      money: message.amount,
      pay_type: message.pay_type,
      voice_text: message.voice_text,
      trade_no: message.trade_no,
      timestamp: message.timestamp
    })

    // 显示通知
    uni.showToast({
      title: `💰 ${message.voice_text}`,
      icon: 'success',
      duration: 3000
    })
  }

  /**
   * 发送消息
   */
  send(message) {
    this.client.send(message)
  }

  /**
   * 订阅支付频道 - 统一频道方案
   */
  subscribePaymentChannel() {
    const subscribeMessage = {
      event: 'pusher:subscribe',
      data: {
        channel: 'payment_channel'  // 统一频道，所有商户共用
      }
    }

    console.log('📡 订阅统一支付频道:', subscribeMessage)
    this.send(JSON.stringify(subscribeMessage))
  }

  /**
   * 订阅商户专属频道 - Swoole服务器方案
   */
  subscribeToMerchantChannel(merchantId) {
    if (!merchantId) {
      console.warn('⚠️ 商户ID为空，无法订阅专属频道')
      return
    }

    const channel = `merchant_${merchantId}_payment`
    const subscribeMessage = {
      type: 'subscribe',
      data: {
        channel: channel
      }
    }

    console.log('📡 订阅商户专属频道:', channel)
    this.client.send(subscribeMessage)
  }

  /**
   * 添加事件监听器
   */
  on(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback)
    }
  }

  /**
   * 移除事件监听器
   */
  off(event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback)
      if (index > -1) {
        this.listeners[event].splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件监听器执行错误 [${event}]:`, error)
        }
      })
    }
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      connected: this.client.isConnected,
      connecting: this.client.connecting,
      stats: this.stats,
      config: this.config
    }
  }

  /**
   * 关闭连接
   */
  close() {
    this.client.close()
    this.isInitialized = false
    console.log('🛑 支付WebSocket服务已关闭')
  }

  /**
   * 重新连接
   */
  reconnect() {
    console.log('🔄 手动重连支付WebSocket...')
    this.client.close()
    setTimeout(() => {
      this.init()
    }, 1000)
  }
}

// 创建全局实例
const paymentWebSocket = new PaymentWebSocketService()

export default PaymentWebSocketService
export { paymentWebSocket }
