<template>
	<view class="container">
		<!-- 顶部标题栏 -->
		<custom-navbar
			title="员工管理"
			:show-back="true"
			:shadow="true"
			@clickLeft="goBack"
		>
			<template #right>
				<view style="display: flex; align-items: center;">
					<view v-if="originalEmployeeList" @click="clearSearch" style="padding: 0 16rpx;">
						<uni-icons type="clear" size="20" color="#FFFFFF"></uni-icons>
					</view>
					<view @click="searchEmployee" style="padding: 0 16rpx;">
						<uni-icons type="search" size="22" color="#FFFFFF"></uni-icons>
					</view>
				</view>
			</template>
		</custom-navbar>
		
		<!-- 编辑员工表单（直接在页面上显示） -->
		<view v-if="showEditForm" class="edit-form">
			<view class="edit-header">
				<text class="edit-title">编辑员工信息</text>
			</view>
			
			<view class="edit-body">
				<view class="form-group">
					<text class="form-label">员工姓名</text>
					<input class="form-input" v-model="editForm.name" placeholder="请输入员工姓名" />
				</view>
				
				<view class="form-group">
					<text class="form-label">登录账号 <text class="optional-text">（不修改请留空）</text></text>
					<input class="form-input" v-model="editForm.account" placeholder="不修改请留空" />
				</view>

				<view class="form-group">
					<text class="form-label">登录密码 <text class="optional-text">（不修改请留空）</text></text>
					<input class="form-input" type="password" v-model="editForm.password" placeholder="不修改请留空" />
				</view>

				<view class="form-group">
					<text class="form-label">手机号</text>
					<input class="form-input" v-model="editForm.phone" placeholder="请输入手机号（选填）" type="number" maxlength="11" />
				</view>

				<view class="form-group">
					<text class="form-label">邮箱</text>
					<input class="form-input" v-model="editForm.email" placeholder="请输入邮箱（选填）" type="email" />
				</view>
				
				<view class="form-group">
					<text class="form-label">职位</text>
					<picker :value="positionIndex" :range="positionOptions" @change="onPositionChange">
						<view class="form-input picker-input">
							<text>{{editForm.role || '请选择职位'}}</text>
							<uni-icons type="arrowdown" size="14" color="#666"></uni-icons>
						</view>
					</picker>
				</view>

				<view class="form-group">
					<text class="form-label">头像颜色</text>
					<view class="color-picker">
						<view
							v-for="(color, index) in avatarColors"
							:key="index"
							class="color-option"
							:class="{'selected': editForm.avatar_color === color}"
							:style="{backgroundColor: getColorValue(color)}"
							@click="editForm.avatar_color = color"
						>
							<uni-icons v-if="editForm.avatar_color === color" type="checkmarkempty" color="#FFFFFF" size="16"></uni-icons>
						</view>
					</view>
				</view>
				
				<view class="form-actions">
					<button class="btn-cancel" @click="cancelEdit">取消</button>
					<button class="btn-confirm" @click="confirmEdit">确定</button>
				</view>
			</view>
		</view>
		
		<!-- 添加员工表单（直接在页面上显示） -->
		<view v-if="showAddForm" class="edit-form">
			<view class="edit-header">
				<text class="edit-title">添加新员工</text>
			</view>
			
			<view class="edit-body">
				<view class="form-group">
					<text class="form-label">员工姓名</text>
					<input class="form-input" v-model="newEmployee.name" placeholder="请输入员工姓名" />
				</view>
				
				<view class="form-group">
					<text class="form-label">登录账号</text>
					<input class="form-input" v-model="newEmployee.account" placeholder="请输入登录账号" />
				</view>

				<view class="form-group">
					<text class="form-label">登录密码</text>
					<input class="form-input" type="password" v-model="newEmployee.password" placeholder="请输入登录密码" />
				</view>

				<view class="form-group">
					<text class="form-label">手机号</text>
					<input class="form-input" v-model="newEmployee.phone" placeholder="请输入手机号（选填）" type="number" maxlength="11" />
				</view>

				<view class="form-group">
					<text class="form-label">邮箱</text>
					<input class="form-input" v-model="newEmployee.email" placeholder="请输入邮箱（选填）" type="email" />
				</view>
				
				<view class="form-group">
					<text class="form-label">职位</text>
					<picker :value="newPositionIndex" :range="positionOptions" @change="onNewPositionChange">
						<view class="form-input picker-input">
							<text>{{newEmployee.role || '请选择职位'}}</text>
							<uni-icons type="arrowdown" size="14" color="#666"></uni-icons>
						</view>
					</picker>
				</view>

				<view class="form-group">
					<text class="form-label">头像颜色</text>
					<view class="color-picker">
						<view
							v-for="(color, index) in avatarColors"
							:key="index"
							class="color-option"
							:class="{'selected': newEmployee.avatar_color === color}"
							:style="{backgroundColor: getColorValue(color)}"
							@click="newEmployee.avatar_color = color"
						>
							<uni-icons v-if="newEmployee.avatar_color === color" type="checkmarkempty" color="#FFFFFF" size="16"></uni-icons>
						</view>
					</view>
				</view>
				
				<view class="form-actions">
					<button class="btn-cancel" @click="cancelAdd">取消</button>
					<button class="btn-confirm" @click="confirmAdd">确定</button>
				</view>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view v-if="loading && !showEditForm && !showAddForm" class="loading-container">
			<uni-icons type="spinner-cycle" size="30" color="#4740FF"></uni-icons>
			<text class="loading-text">加载中...</text>
		</view>

		<!-- 员工列表 -->
		<view v-if="!showEditForm && !showAddForm && !loading" class="employee-list">
			<!-- 空状态提示 -->
			<view v-if="employeeList.length === 0" class="empty-state">
				<uni-icons type="person" size="60" color="#ccc"></uni-icons>
				<text class="empty-text">暂无员工数据</text>
				<text class="empty-hint">点击下方按钮添加第一个员工</text>
			</view>

			<view v-for="(employee, index) in employeeList" :key="index" class="employee-item">
				<view class="employee-left">
					<view class="employee-avatar" :class="employee.avatarClass">
						<uni-icons :type="employee.icon" color="#FFFFFF" size="20"></uni-icons>
					</view>
					<view class="employee-info">
						<text class="employee-name">{{ employee.name }}</text>
						<view class="employee-position">
							<text class="position-text">{{ employee.position }}</text>
							<view class="status-indicator" :class="{'online': employee.status === '在线', 'offline': employee.status === '离线'}"></view>
							<text class="status-text">{{ employee.status }}</text>
						</view>
					</view>
				</view>
				<view class="employee-actions">
					<view class="action-icon" @click="editEmployee(index)">
						<uni-icons type="compose" color="#666" size="18"></uni-icons>
					</view>
					<view class="action-icon" @click="deleteEmployee(index)">
						<uni-icons type="trash" color="#666" size="18"></uni-icons>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 创建员工按钮 -->
		<view v-if="!showEditForm && !showAddForm" class="create-button" @click="showAddEmployeeForm">
			<uni-icons type="plus" color="#FFFFFF" size="18"></uni-icons>
			<text class="create-text">创建员工</text>
		</view>
	</view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar.vue'
// import pageAuthMixin from '@/mixins/page-auth'  // 暂时注释掉
import {
	getStaffList,
	addStaff,
	editStaff as editStaffApi,
	deleteStaff as deleteStaffApi,
	getAvatarColorOptions,
	getStaffRoleOptions
} from '@/api/staff'

export default {
	components: {
		CustomNavbar
	},
	// mixins: [pageAuthMixin],  // 暂时注释掉
	data() {
		return {
			showEditForm: false,
			showAddForm: false,
			employeeList: [],
			loading: false,
			editForm: {
				id: null,
				name: '',
				role: '',
				account: '',
				password: '',
				phone: '',
				email: '',
				avatar_color: 'blue'
			},
			newEmployee: {
				name: '',
				role: '收银员',
				account: '',
				password: '',
				phone: '',
				email: '',
				avatar_color: 'blue'
			},
			editIndex: -1,
			positionOptions: ['店长', '收银员', '服务员', '销售员', '管理员'],
			positionIndex: 0,
			newPositionIndex: 1, // 默认选择收银员
			avatarColors: ['blue', 'green', 'purple', 'orange', 'red', 'cyan', 'pink', 'yellow'],
			originalEmployeeList: null
		}
	},
	async onLoad() {
		// 临时跳过登录检查，直接加载数据
		console.log('🚀 员工页面加载开始...')

		// 检查是否有登录Token，如果没有则设置测试Token
		this.ensureLoginToken()

		try {
			await this.loadStaffList()
			console.log('✅ 员工页面加载完成')
		} catch (error) {
			console.error('❌ 员工页面加载失败:', error)
			uni.showToast({
				title: '加载失败: ' + error.message,
				icon: 'none',
				duration: 3000
			})
		}
	},
	async onShow() {
		// 暂时注释掉登录检查
		// await this.checkLoginOnShow()
		console.log('📱 员工页面显示')
	},
	methods: {
		// 确保有登录Token
		ensureLoginToken() {
			const userToken = uni.getStorageSync('user_token')
			const userUid = uni.getStorageSync('user_uid')

			if (!userToken || !userUid) {
				console.log('🔧 未发现登录Token，设置测试Token...')
				// 使用从调试页面获取的Token
				uni.setStorageSync('user_token', 'e7a6SlNmzcjo7fP2djJxEAHEb04ALskwhwsHOVqUF0tQJTgT5T5g7qGXXXUT3cy/eFxtb5kNbA4CVTNjpFU5xQ4PpOmXdjUxpkXOs')
				uni.setStorageSync('user_uid', '1000')
				uni.setStorageSync('csrf_token', '5eae1623e2d194232db8b4c36cbbde3a')
				uni.setStorageSync('session_id', 'e0f1vj3aj8qa4s5lcnde8q803')
				console.log('✅ 测试Token设置完成')
			} else {
				console.log('✅ 发现现有登录Token:', { userToken: userToken.substring(0, 20) + '...', userUid })
			}
		},

		goBack() {
			uni.navigateBack();
		},
		searchEmployee() {
			// 显示搜索输入框
			uni.showModal({
				title: '搜索员工',
				editable: true,
				placeholderText: '请输入员工姓名或账号',
				success: (res) => {
					if (res.confirm && res.content.trim()) {
						this.performSearch(res.content.trim());
					}
				}
			});
		},

		// 执行搜索
		performSearch(keyword) {
			if (!keyword) {
				// 如果搜索关键词为空，显示所有员工
				this.loadStaffList();
				return;
			}

			// 在当前员工列表中搜索
			const filteredList = this.employeeList.filter(employee => {
				return employee.name.includes(keyword) ||
					   employee.account.includes(keyword) ||
					   employee.position.includes(keyword);
			});

			if (filteredList.length > 0) {
				// 临时保存原始列表
				if (!this.originalEmployeeList) {
					this.originalEmployeeList = [...this.employeeList];
				}
				this.employeeList = filteredList;

				uni.showToast({
					title: `找到 ${filteredList.length} 个员工`,
					icon: 'success'
				});
			} else {
				uni.showToast({
					title: '未找到匹配的员工',
					icon: 'none'
				});
			}
		},

		// 清除搜索，恢复完整列表
		clearSearch() {
			if (this.originalEmployeeList) {
				this.employeeList = [...this.originalEmployeeList];
				this.originalEmployeeList = null;
			}
		},

		// 加载员工列表 - 基于测试文件的成功模式
		async loadStaffList() {
			try {
				this.loading = true
				console.log('👥 加载员工列表...')

				const response = await getStaffList()
				console.log('📥 员工列表响应:', response)
				console.log('📥 响应类型:', typeof response)

				// 根据测试文件，成功的响应可能是数组或包含data字段的对象
				let staffList = []
				if (Array.isArray(response)) {
					staffList = response
				} else if (response && response.data && Array.isArray(response.data)) {
					staffList = response.data
				} else if (response && response.code === 0 && response.data) {
					staffList = Array.isArray(response.data) ? response.data : []
				}

				this.employeeList = staffList.map(staff => ({
					id: staff.id,
					name: staff.name,
					position: staff.role || '收银员',
					account: staff.account || '',
					password: staff.password || '',
					status: '在线', // 默认状态
					phone: staff.phone || '',
					email: staff.email || '',
					icon: 'person',
					avatarClass: this.getAvatarClass(staff.avatar_color || staff.color || 'blue'),
					avatar_color: staff.avatar_color || staff.color || 'blue'
				}))

				console.log('✅ 员工列表加载成功:', this.employeeList.length, '个员工')
				console.log('📋 员工详细信息:', this.employeeList.map(emp => `${emp.name}(${emp.account})`).join(', '))

				// 如果没有员工数据，显示空状态而不是错误
				if (this.employeeList.length === 0) {
					console.log('📝 员工列表为空')
				}
			} catch (error) {
				console.error('❌ 加载员工列表失败:', error)
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
				// 清空列表，显示空状态
				this.employeeList = []
			} finally {
				this.loading = false
			}
		},

		// 获取头像样式类
		getAvatarClass(color) {
			return `avatar-${color}`
		},

		// 获取颜色值
		getColorValue(color) {
			const colorMap = {
				'blue': '#4740FF',
				'green': '#4CD964',
				'purple': '#9c27b0',
				'orange': '#FF9500',
				'red': '#FF3B30',
				'cyan': '#00BCD4',
				'pink': '#E91E63',
				'yellow': '#FFC107'
			};
			return colorMap[color] || '#4740FF';
		},

		// 验证手机号格式
		validatePhone(phone) {
			if (!phone) return true; // 手机号是可选的
			const phoneRegex = /^1[3-9]\d{9}$/;
			return phoneRegex.test(phone);
		},

		// 验证邮箱格式
		validateEmail(email) {
			if (!email) return true; // 邮箱是可选的
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			return emailRegex.test(email);
		},

		// 验证添加员工表单数据（账号密码必填）
		validateAddFormData(data) {
			if (!data.name.trim()) {
				return { valid: false, message: '请输入员工姓名' };
			}

			if (!data.account.trim()) {
				return { valid: false, message: '请输入登录账号' };
			}

			if (data.account.length < 3) {
				return { valid: false, message: '登录账号至少3个字符' };
			}

			if (!data.password.trim()) {
				return { valid: false, message: '请输入登录密码' };
			}

			if (data.password.length < 6) {
				return { valid: false, message: '登录密码至少6个字符' };
			}

			if (data.phone && !this.validatePhone(data.phone)) {
				return { valid: false, message: '请输入正确的手机号格式' };
			}

			if (data.email && !this.validateEmail(data.email)) {
				return { valid: false, message: '请输入正确的邮箱格式' };
			}

			return { valid: true };
		},

		// 验证编辑员工表单数据（账号密码可选）
		validateEditFormData(data) {
			if (!data.name.trim()) {
				return { valid: false, message: '请输入员工姓名' };
			}

			// 如果填写了账号，检查长度
			if (data.account && data.account.trim() && data.account.length < 3) {
				return { valid: false, message: '登录账号至少3个字符' };
			}

			// 如果填写了密码，检查长度
			if (data.password && data.password.trim() && data.password.length < 6) {
				return { valid: false, message: '登录密码至少6个字符' };
			}

			if (data.phone && !this.validatePhone(data.phone)) {
				return { valid: false, message: '请输入正确的手机号格式' };
			}

			if (data.email && !this.validateEmail(data.email)) {
				return { valid: false, message: '请输入正确的邮箱格式' };
			}

			return { valid: true };
		},
		editEmployee(index) {
			this.showEditForm = true;
			this.editIndex = index;
			const employee = this.employeeList[index];
			this.editForm.id = employee.id;
			this.editForm.name = employee.name;
			this.editForm.role = employee.position;
			this.editForm.account = ''; // 编辑时清空，让用户选择是否修改
			this.editForm.password = ''; // 编辑时清空，让用户选择是否修改
			this.editForm.phone = employee.phone || '';
			this.editForm.email = employee.email || '';
			this.editForm.avatar_color = employee.avatar_color || 'blue';

			// 设置当前职位在选择器中的索引
			this.positionIndex = this.positionOptions.indexOf(employee.position);
		},
		cancelEdit() {
			this.showEditForm = false;
		},
		async confirmEdit() {
			// 使用编辑专用的表单验证（账号密码可选）
			const validation = this.validateEditFormData(this.editForm);
			if (!validation.valid) {
				uni.showToast({
					title: validation.message,
					icon: 'none'
				});
				return;
			}

			try {
				const staffData = {
					staff_id: this.editForm.id,
					name: this.editForm.name.trim(),
					role: this.editForm.role || '收银员',
					account: this.editForm.account.trim(),
					password: this.editForm.password.trim(),
					phone: this.editForm.phone.trim(),
					email: this.editForm.email.trim(),
					avatar_color: this.editForm.avatar_color
				}

				console.log('📝 编辑员工:', staffData)

				const response = await editStaffApi(staffData)
				console.log('📥 编辑响应:', response)

				if (response && response.code === 0) {
					// 更新本地数据
					const employee = this.employeeList[this.editIndex];
					employee.name = this.editForm.name;
					employee.position = this.editForm.role;
					employee.account = this.editForm.account;
					employee.password = this.editForm.password;
					employee.phone = this.editForm.phone;
					employee.email = this.editForm.email;
					employee.avatar_color = this.editForm.avatar_color;
					employee.avatarClass = this.getAvatarClass(this.editForm.avatar_color);

					this.showEditForm = false;

					uni.showToast({
						title: '员工信息已更新',
						icon: 'success'
					});

					// 重新加载列表
					await this.loadStaffList()
				} else {
					throw new Error(response?.msg || '编辑失败')
				}
			} catch (error) {
				console.error('❌ 编辑员工失败:', error)
				uni.showToast({
					title: error.message || '编辑失败',
					icon: 'none'
				});
			}
		},
		onPositionChange(e) {
			const index = e.detail.value;
			this.positionIndex = index;
			this.editForm.role = this.positionOptions[index];
		},
		onNewPositionChange(e) {
			const index = e.detail.value;
			this.newPositionIndex = index;
			this.newEmployee.role = this.positionOptions[index];
		},
		deleteEmployee(index) {
			const employee = this.employeeList[index];
			uni.showModal({
				title: '确认删除',
				content: `确定要删除员工"${employee.name}"吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							console.log('🗑️ 删除员工:', employee.id)

							const response = await deleteStaffApi(employee.id)
							console.log('📥 删除响应:', response)

							if (response && response.code === 0) {
								this.employeeList.splice(index, 1);
								uni.showToast({
									title: '员工已删除',
									icon: 'success'
								});

								// 重新加载列表
								await this.loadStaffList()
							} else {
								throw new Error(response?.msg || '删除失败')
							}
						} catch (error) {
							console.error('❌ 删除员工失败:', error)
							uni.showToast({
								title: error.message || '删除失败',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		showAddEmployeeForm() {
			this.showAddForm = true;
			// 重置新员工表单
			this.newEmployee = {
				name: '',
				role: '收银员',
				account: '',
				password: '',
				phone: '',
				email: '',
				avatar_color: 'blue'
			};
			this.newPositionIndex = 1; // 默认选择收银员
		},
		cancelAdd() {
			this.showAddForm = false;
		},
		async confirmAdd() {
			// 使用添加专用的表单验证（账号密码必填）
			const validation = this.validateAddFormData(this.newEmployee);
			if (!validation.valid) {
				uni.showToast({
					title: validation.message,
					icon: 'none'
				});
				return;
			}

			try {
				const staffData = {
					name: this.newEmployee.name.trim(),
					role: this.newEmployee.role || '收银员',
					account: this.newEmployee.account.trim(),
					password: this.newEmployee.password.trim(),
					phone: this.newEmployee.phone.trim(),
					email: this.newEmployee.email.trim(),
					avatar_color: this.newEmployee.avatar_color
				}

				console.log('➕ 添加员工:', staffData)

				const response = await addStaff(staffData)
				console.log('📥 添加响应:', response)
				console.log('📥 响应类型:', typeof response)
				console.log('📥 响应详情:', JSON.stringify(response, null, 2))

				// 检查响应格式 - 基于测试文件的成功模式
				const isSuccess = response && (
					response.code === 0 ||
					response.code === 1 ||
					(response.success === true) ||
					(typeof response === 'object' && !response.error)
				)

				if (isSuccess) {
					this.showAddForm = false;

					uni.showToast({
						title: '员工添加成功',
						icon: 'success'
					});

					// 等待一下再重新加载列表，确保数据库已更新
					console.log('⏳ 等待数据库更新...')
					setTimeout(async () => {
						console.log('🔄 重新加载员工列表...')
						await this.loadStaffList()
						console.log('✅ 员工列表刷新完成，当前员工数:', this.employeeList.length)
					}, 500)
				} else {
					console.error('❌ 添加失败，响应:', response)
					const errorMsg = response?.msg || response?.message || response?.error || '添加失败'
					throw new Error(errorMsg)
				}
			} catch (error) {
				console.error('❌ 添加员工失败:', error)
				uni.showToast({
					title: error.message || '添加失败',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style>
page {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
	background-color: #f8f8f8;
}

.container {
	width: 100%;
	min-height: 100vh;
	background-color: #f8f8f8;
}

/* 顶部标题栏 */
.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #4740FF;
	color: white;
	padding: 20rpx 30rpx;
	box-sizing: border-box;
	position: sticky;
	top: 0;
	z-index: 100;
}

.header-left {
	display: flex;
	align-items: center;
}

.header-title {
	font-size: 36rpx;
	font-weight: 500;
	margin-left: 20rpx;
}

/* 编辑表单样式 */
.edit-form {
	background-color: #fff;
	margin-bottom: 100rpx;
}

.edit-header {
	background-color: #4740FF;
	padding: 20rpx 30rpx;
}

.edit-title {
	color: #fff;
	font-size: 32rpx;
	font-weight: 500;
}

.edit-body {
	padding: 20rpx 30rpx;
}

.form-group {
	margin-bottom: 30rpx;
}

.form-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 16rpx;
}

.optional-text {
	font-size: 24rpx;
	color: #999;
	font-weight: normal;
}

.form-input {
	width: 100%;
	height: 80rpx;
	font-size: 28rpx;
	color: #333;
	border: 1px solid #eee;
	border-radius: 8rpx;
	padding: 0 20rpx;
	box-sizing: border-box;
}

.picker-input {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-right: 20rpx;
}

.form-actions {
	display: flex;
	margin-top: 40rpx;
}

.btn-cancel, .btn-confirm {
	flex: 1;
	padding: 24rpx 0;
	font-size: 28rpx;
	border: none;
	border-radius: 8rpx;
}

.btn-cancel {
	background-color: #f5f5f5;
	color: #666;
	margin-right: 20rpx;
}

.btn-confirm {
	background-color: #4740FF;
	color: white;
}

/* 员工列表样式 */
.employee-list {
	padding: 0;
	margin-bottom: 160rpx;
	background-color: white;
}

.employee-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 30rpx;
	background-color: white;
	border-bottom: 1rpx solid #f2f2f2;
}

.employee-left {
	display: flex;
	align-items: center;
}

.employee-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 30rpx;
}

.avatar-blue {
	background-color: #4740FF;
}

.avatar-green {
	background-color: #4CD964;
}

.avatar-purple {
	background-color: #9c27b0;
}

.avatar-orange {
	background-color: #FF9500;
}

.avatar-red {
	background-color: #FF3B30;
}

.avatar-cyan {
	background-color: #00BCD4;
}

.avatar-pink {
	background-color: #E91E63;
}

.avatar-yellow {
	background-color: #FFC107;
}

/* 颜色选择器样式 */
.color-picker {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	margin-top: 10rpx;
}

.color-option {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	border: 3rpx solid transparent;
	transition: all 0.3s ease;
}

.color-option.selected {
	border-color: #333;
	transform: scale(1.1);
}

.color-option:hover {
	transform: scale(1.05);
}

/* 加载状态样式 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
	background-color: white;
}

.loading-text {
	margin-top: 20rpx;
	font-size: 28rpx;
	color: #666;
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	text-align: center;
}

.empty-text {
	margin-top: 20rpx;
	font-size: 32rpx;
	color: #666;
	font-weight: 500;
}

.empty-hint {
	margin-top: 10rpx;
	font-size: 26rpx;
	color: #999;
}

.employee-info {
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.employee-name {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.employee-position {
	display: flex;
	align-items: center;
	color: #999;
	font-size: 26rpx;
}

.position-text {
	color: #666;
}

.status-indicator {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	margin: 0 8rpx 0 16rpx;
}

.online {
	background-color: #4CD964;
}

.offline {
	background-color: #FF3B30;
}

.status-text {
	color: #999;
}

.employee-actions {
	display: flex;
	align-items: center;
}

.action-icon {
	margin-left: 30rpx;
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 创建员工按钮 */
.create-button {
	position: fixed;
	bottom: 40rpx;
	left: 50%;
	transform: translateX(-50%);
	background-color: #4740FF;
	color: white;
	border-radius: 8rpx;
	padding: 24rpx 0;
	width: 90%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.create-text {
	margin-left: 10rpx;
	font-size: 30rpx;
}
</style>