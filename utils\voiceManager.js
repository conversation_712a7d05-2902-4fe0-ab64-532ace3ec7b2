// utils/voiceManager.js - 语音播报管理器
import { request } from '@/utils/request.js'

class VoiceManager {
  constructor() {
    console.log('🔊 初始化语音播报管理器');
    
    // 基础配置
    this.enabled = uni.getStorageSync('voice_enabled') !== false; // 默认开启
    this.volume = uni.getStorageSync('voice_volume') || 0.8; // 默认音量80%
    this.lastOrderId = uni.getStorageSync('last_order_id') || '';
    this.templateMode = uni.getStorageSync('voice_template') || 'normal';
    this.largeAmountAlert = uni.getStorageSync('voice_large_alert') !== false;
    
    // 监听状态
    this.isMonitoring = false;
    this.monitorTimer = null;
    this.currentStrategy = null;
    this.currentPage = '';
    
    // 页面策略配置
    this.pageStrategies = {
      'pages/index/index': {
        interval: 3000,
        priority: 'normal',
        description: '首页常规监听'
      },
      'pages/scan/index': {
        interval: 3000,
        priority: 'normal',
        description: '收款码页常规监听'
      },
      'pages/dynamic-code/index': {
        interval: 2000,
        priority: 'high',
        checkAfterQRGenerate: true,
        description: '动态收款码高频监听'
      },
      'pages/pay/mini-payment': {
        interval: 1000,
        priority: 'urgent',
        checkAfterScan: true,
        description: '反扫收银台超高频监听'
      },
      'pages/bill/index': {
        interval: 5000,
        priority: 'low',
        description: '账单页低频监听'
      }
    };
    
    // 初始化
    this.init();
  }
  
  // 初始化
  init() {
    this.setupVisibilityListener();
    this.setupNetworkListener();
    console.log('✅ 语音播报管理器初始化完成');
  }
  
  // 开始监听
  startMonitoring(pagePath) {
    if (!this.enabled) {
      console.log('🔇 语音播报已关闭，跳过监听');
      return;
    }
    
    if (this.isMonitoring && this.currentPage === pagePath) {
      console.log('🔊 当前页面已在监听中');
      return;
    }
    
    // 停止之前的监听
    this.stopMonitoring();
    
    this.currentPage = pagePath;
    this.currentStrategy = this.pageStrategies[pagePath] || this.pageStrategies['pages/index/index'];
    this.isMonitoring = true;
    
    console.log(`🔊 启动语音监听 - ${this.currentStrategy.description}，间隔${this.currentStrategy.interval}ms`);
    
    // 立即检查一次
    this.checkNewOrders();
  }
  
  // 停止监听
  stopMonitoring() {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    if (this.monitorTimer) {
      clearTimeout(this.monitorTimer);
      this.monitorTimer = null;
    }
    
    console.log('🔇 停止语音监听');
  }
  
  // 检查新订单
  async checkNewOrders() {
    if (!this.isMonitoring || !this.enabled) return;

    try {
      // 静默检查，不显示加载状态
      console.log('🔍 静默检查新订单...');

      // 只获取最近3分钟的订单，减少数据量和服务器压力
      const starttime = this.getRecentTime(3);

      const response = await request({
        url: '/user/ajax2.php',
        method: 'POST',
        data: {
          act: 'orderList',
          dstatus: 1, // 只查询已支付订单
          offset: 0,
          limit: 3, // 只获取最新3条，进一步减少数据量
          starttime: starttime
        },
        // 添加静默标识，避免显示加载状态
        silent: true
      });

      if (response.rows && response.rows.length > 0) {
        const latestOrder = response.rows[0];

        // 检查是否是新订单
        if (latestOrder.trade_no !== this.lastOrderId) {
          console.log('🎉 发现新订单:', latestOrder.trade_no);

          // 立即播报
          await this.speakPayment(latestOrder);

          // 更新最后订单ID
          this.lastOrderId = latestOrder.trade_no;
          uni.setStorageSync('last_order_id', this.lastOrderId);
        }
      }

    } catch (error) {
      // 静默处理错误，不影响用户界面
      console.warn('⚠️ 静默检查订单失败:', error.message || error);
    }

    // 安排下次检查
    this.scheduleNextCheck();
  }
  
  // 播报收款信息
  async speakPayment(order) {
    try {
      const template = this.getVoiceTemplate(order);
      const text = this.formatVoiceText(template, order);
      
      console.log('🔊 语音播报:', text);
      
      // 执行语音播放
      await this.speak(text);
      
      // 记录播报日志
      this.logVoiceEvent(order, true);
      
      // 显示Toast提示（可选）
      uni.showToast({
        title: `收款 ¥${order.money}`,
        icon: 'success',
        duration: 2000
      });
      
    } catch (error) {
      console.error('❌ 语音播报失败:', error);
      this.logVoiceEvent(order, false);
    }
  }
  
  // 多端语音播放
  async speak(text) {
    if (!text || !this.enabled) return;
    
    try {
      // #ifdef H5
      if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
        return new Promise((resolve, reject) => {
          const utterance = new SpeechSynthesisUtterance(text);
          utterance.volume = this.volume;
          utterance.rate = 1.0;
          utterance.lang = 'zh-CN';
          utterance.onend = resolve;
          utterance.onerror = reject;
          
          // 确保语音合成器可用
          if (speechSynthesis.paused) {
            speechSynthesis.resume();
          }
          
          speechSynthesis.speak(utterance);
        });
      }
      // #endif
      
      // #ifdef APP-PLUS
      return new Promise((resolve, reject) => {
        plus.speech.startSpeaking({
          content: text,
          volume: this.volume,
          speed: 1.0,
          success: () => {
            console.log('✅ App端语音播报成功');
            resolve();
          },
          error: (error) => {
            console.error('❌ App端语音播报失败:', error);
            reject(error);
          }
        });
      });
      // #endif
      
      // #ifdef MP
      // 小程序端使用音频播放（需要预录制音频文件）
      console.log('📱 小程序端语音播报:', text);
      // 这里可以实现音频文件播放逻辑
      return Promise.resolve();
      // #endif
      
    } catch (error) {
      console.error('❌ 语音播放失败:', error);
      throw error;
    }
  }
  
  // 获取语音模板
  getVoiceTemplate(order) {
    const amount = parseFloat(order.money || 0);
    
    // 大额收款特殊提醒
    if (this.largeAmountAlert && amount >= 1000) {
      return "大额收款成功，{payType}，金额{amount}元，请注意核实";
    }
    
    // 根据页面场景选择模板
    switch(this.currentPage) {
      case 'pages/dynamic-code/index':
        return "收款成功，{payType}，{productName}，金额{amount}元";
      case 'pages/pay/mini-payment':
        return "反扫收款成功，{payType}，金额{amount}元";
      default:
        return "收款成功，{payType}，金额{amount}元";
    }
  }
  
  // 格式化语音文本
  formatVoiceText(template, order) {
    try {
      const payType = this.getPayTypeName(order.type);
      const amount = this.formatAmount(order.money);
      const productName = order.name || '商品';

      return template
        .replace('{payType}', payType)
        .replace('{amount}', amount)
        .replace('{productName}', productName);
    } catch (error) {
      console.error('❌ 格式化语音文本失败:', error);
      return `收款成功，金额${order.money || 0}元`;
    }
  }
  
  // 获取支付方式名称
  getPayTypeName(type) {
    const payTypes = {
      1: '支付宝',
      2: '微信支付',
      3: 'QQ钱包',
      4: '银联支付',
      5: '云闪付'
    };
    return payTypes[type] || '在线支付';
  }
  
  // 格式化金额播报
  formatAmount(amount) {
    const num = parseFloat(amount || 0);
    
    if (num >= 10000) {
      const wan = (num / 10000).toFixed(1);
      return wan.endsWith('.0') ? `${parseInt(wan)}万` : `${wan}万`;
    } else if (num >= 1000) {
      const qian = (num / 1000).toFixed(1);
      return qian.endsWith('.0') ? `${parseInt(qian)}千` : `${qian}千`;
    }
    
    // 小数点处理
    return num % 1 === 0 ? num.toString() : num.toFixed(2);
  }
  
  // 获取最近时间
  getRecentTime(minutes) {
    const now = new Date();
    const recent = new Date(now.getTime() - minutes * 60 * 1000);
    return recent.toISOString().slice(0, 19).replace('T', ' ');
  }
  
  // 安排下次检查 - 智能间隔调整
  scheduleNextCheck() {
    if (!this.isMonitoring) return;

    // 基础检查间隔
    let interval = this.currentStrategy.interval;

    // 根据时间段调整检查间隔
    const currentHour = new Date().getHours();

    // 夜间时段(22:00-6:00)减少检查频率，节省资源
    if (currentHour >= 22 || currentHour < 6) {
      interval = interval * 2; // 延长间隔
      console.log('🌙 夜间模式，检查间隔延长至', interval / 1000, '秒');
    }

    // #ifdef H5
    if (typeof document !== 'undefined' && document.visibilityState === 'hidden') {
      interval = interval * 3; // 页面不可见时进一步降低检查频率
      console.log('👁️ 页面隐藏，检查间隔延长至', interval / 1000, '秒');
    }
    // #endif

    this.monitorTimer = setTimeout(() => {
      this.checkNewOrders();
    }, interval);
  }
  
  // 立即检查
  immediateCheck() {
    if (!this.enabled || !this.isMonitoring) return;
    
    console.log('⚡ 立即检查新订单');
    
    if (this.monitorTimer) {
      clearTimeout(this.monitorTimer);
      this.monitorTimer = null;
    }
    
    this.checkNewOrders();
  }
  
  // 设置页面可见性监听
  setupVisibilityListener() {
    // #ifdef H5
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible' && this.isMonitoring) {
          console.log('👁️ 页面变为可见，立即检查订单');
          this.immediateCheck();
        }
      });
    }
    // #endif
  }
  
  // 设置网络状态监听
  setupNetworkListener() {
    uni.onNetworkStatusChange((res) => {
      if (res.isConnected && this.isMonitoring) {
        console.log('🌐 网络已连接，立即检查订单');
        setTimeout(() => {
          this.immediateCheck();
        }, 1000); // 延迟1秒确保网络稳定
      }
    });
  }
  
  // 记录播报日志
  logVoiceEvent(order, success) {
    const logData = {
      time: new Date().toLocaleString(),
      orderId: order.trade_no,
      amount: order.money,
      payType: this.getPayTypeName(order.type),
      page: this.currentPage,
      success: success
    };
    
    console.log('📝 语音播报日志:', logData);
    
    // 可以在这里添加统计上报逻辑
  }
  
  // === 设置方法 ===
  
  // 设置开关
  setEnabled(enabled) {
    this.enabled = enabled;
    uni.setStorageSync('voice_enabled', enabled);
    
    if (!enabled) {
      this.stopMonitoring();
      console.log('🔇 语音播报已关闭');
    } else {
      console.log('🔊 语音播报已开启');
    }
  }
  
  // 设置音量
  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume)); // 限制在0-1之间
    uni.setStorageSync('voice_volume', this.volume);
    console.log('🔊 音量设置为:', Math.round(this.volume * 100) + '%');
  }
  
  // 设置大额提醒
  setLargeAmountAlert(enabled) {
    this.largeAmountAlert = enabled;
    uni.setStorageSync('voice_large_alert', enabled);
    console.log('💰 大额提醒:', enabled ? '已开启' : '已关闭');
  }

  // 设置语音模板
  setTemplate(templateId) {
    this.templateMode = templateId;
    uni.setStorageSync('voice_template', templateId);
    console.log('🎵 语音模板已切换:', templateId);
  }
  
  // 测试播报
  testVoice() {
    const testOrder = {
      trade_no: 'TEST_' + Date.now(),
      money: '88.88',
      type: 2, // 微信支付
      name: '测试商品',
      addtime: new Date().toISOString()
    };
    
    console.log('🧪 测试语音播报');
    this.speakPayment(testOrder);
  }
  
  // 获取当前状态
  getStatus() {
    return {
      enabled: this.enabled,
      volume: this.volume,
      isMonitoring: this.isMonitoring,
      currentPage: this.currentPage,
      currentStrategy: this.currentStrategy,
      lastOrderId: this.lastOrderId
    };
  }
}

// 创建全局单例
const voiceManager = new VoiceManager();

// 导出
export { voiceManager };
export default voiceManager;
