// store/modules/auth.js - 用户认证状态管理
import api from '@/api';

// 初始状态
const state = {
  // 登录状态
  isLogin: false,
  // 用户信息
  userInfo: null,
  // 登录加载状态
  loading: false,
  // 验证码实例
  captchaObj: null
};

// 修改状态的方法
const mutations = {
  // 设置登录状态
  SET_LOGIN_STATE(state, isLogin) {
    state.isLogin = isLogin;
  },
  // 设置用户信息
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo;
  },
  // 设置加载状态
  SET_LOADING(state, loading) {
    state.loading = loading;
  },
  // 设置验证码实例
  SET_CAPTCHA_OBJ(state, captchaObj) {
    state.captchaObj = captchaObj;
  },
  // 清除认证信息
  CLEAR_AUTH(state) {
    state.isLogin = false;
    state.userInfo = null;
  }
};

// 异步操作
const actions = {
  // 登录操作
  async login({ commit }, { username, password, type = 1, captchaResult }) {
    try {
      commit('SET_LOADING', true);
      
      const response = await api.auth.login({
        username,
        password,
        type
      }, captchaResult);
      
      if (response.code === 0) {
        // 登录成功
        commit('SET_LOGIN_STATE', true);
        return response;
      }
      
      return Promise.reject(new Error(response.msg || '登录失败'));
    } catch (error) {
      return Promise.reject(error);
    } finally {
      commit('SET_LOADING', false);
    }
  },
  
  // 注册操作
  async register({ commit }, registerData) {
    try {
      commit('SET_LOADING', true);
      
      const response = await api.auth.register(registerData);
      
      if (response.code === 1) {
        // 注册成功
        return response;
      } else if (response.code === 2) {
        // 需要支付
        return response;
      }
      
      return Promise.reject(new Error(response.msg || '注册失败'));
    } catch (error) {
      return Promise.reject(error);
    } finally {
      commit('SET_LOADING', false);
    }
  },
  
  // 发送验证码
  async sendCode({ commit }, { sendto, captchaResult }) {
    try {
      commit('SET_LOADING', true);
      
      const response = await api.auth.sendCode(sendto, captchaResult);
      
      if (response.code === 0) {
        return response;
      }
      
      return Promise.reject(new Error(response.msg || '发送验证码失败'));
    } catch (error) {
      return Promise.reject(error);
    } finally {
      commit('SET_LOADING', false);
    }
  },
  
  // 获取验证码配置
  async getCaptcha({ commit }) {
    try {
      const response = await api.auth.getCaptcha();
      return response;
    } catch (error) {
      return Promise.reject(error);
    }
  },
  
  // 退出登录
  async logout({ commit }) {
    try {
      await api.auth.logout();
      commit('CLEAR_AUTH');
      return true;
    } catch (error) {
      return Promise.reject(error);
    }
  },
  
  // 第三方登录
  async thirdLogin({ commit }, { type, bind }) {
    try {
      commit('SET_LOADING', true);
      
      const response = await api.auth.connectLogin(type, bind);
      
      if (response.code === 0) {
        return response;
      }
      
      return Promise.reject(new Error(response.msg || '第三方登录失败'));
    } catch (error) {
      return Promise.reject(error);
    } finally {
      commit('SET_LOADING', false);
    }
  }
};

// 获取状态的方法
const getters = {
  isLogin: state => state.isLogin,
  userInfo: state => state.userInfo,
  loading: state => state.loading,
  captchaObj: state => state.captchaObj
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}; 