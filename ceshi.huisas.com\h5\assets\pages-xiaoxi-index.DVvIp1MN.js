import{y as e,E as s,a as t,A as a,B as i,d as n,v as c,e as o,w as l,i as r,o as g,f as m,C as y,D as d,F as f,h,j as p,k as u,l as v,a8 as M,z as b,t as k}from"./index-B1Q521gi.js";import{_ as x}from"./custom-navbar.DuzuSmPc.js";import{r as w}from"./uni-app.es.DAfa8VxY.js";import{_}from"./_plugin-vue_export-helper.BCo6x5W8.js";const C=_({components:{MessageItem:{props:{message:{type:Object,required:!0},iconClass:{type:String,default:""},iconSrc:{type:String,default:""}},template:'\n    <view class="message-item">\n      <view class="message-icon" :class="iconClass">\n        <image :src="iconSrc" mode="aspectFit" class="icon-image"></image>\n      </view>\n      <view class="message-content">\n        <view class="message-header">\n          <text class="message-title">{{ message.title }}</text>\n          <text class="message-time">{{ message.time }}</text>\n        </view>\n        <text class="message-text">{{ message.content }}</text>\n        \n        \x3c!-- 活动通知特殊处理 --\x3e\n        <view class="activity-card" v-if="message.type === \'activity\' && message.activityInfo">\n          <image :src="message.activityInfo.image" mode="aspectFill" class="activity-image"></image>\n          <view class="activity-info">\n            <text class="activity-title">{{ message.activityInfo.title }}</text>\n            <text class="activity-desc">{{ message.activityInfo.description }}</text>\n          </view>\n        </view>\n        \n        \x3c!-- 银行功能的特殊处理 --\x3e\n        <view class="bank-function" v-if="message.type === \'staff\' && message.bankFunction">\n          <text class="bank-function-text">{{ message.bankFunction }}</text>\n        </view>\n      </view>\n    </view>\n  '},CustomNavbar:x},data:()=>({tabs:["全部","系统","交易","活动"],currentTab:0,isRefreshing:!1,hasMoreMessages:!0,isLoading:!1,messages:{today:[{type:"transaction",title:"交易通知",time:"09:45",content:"您有一笔2835.60元的自动结算已到账"},{type:"activity",title:"活动通知",time:"08:30",content:"恭喜您获得新商家专享福利！",activityInfo:{title:"新商家专享优惠",description:"限时7天，商家收款手续费5折",image:"/static/activity/discount.jpg"}}],yesterday:[{type:"system",title:"系统通知",time:"昨天 18:45",content:"系统将于04月15日凌晨2:00-4:00进行维护升级，期间交易数据将延迟同步，请知悉"},{type:"transaction",title:"交易通知",time:"昨天 15:30",content:"您有一笔3280.20元的自动结算已到账"},{type:"staff",title:"员工通知",time:"昨天 11:20",content:'员工"王收银"已完成账号激活，可以正常使用',bankFunction:"用收银功能"}]}}),computed:{filteredMessages(){if(0===this.currentTab)return this.messages;const e={1:"system",2:"transaction",3:"activity"}[this.currentTab];return{today:this.messages.today.filter((s=>s.type===e)),yesterday:this.messages.yesterday.filter((s=>s.type===e))}}},onLoad(){this.fetchMessages()},methods:{fetchMessages(){setTimeout((()=>{this.isRefreshing=!1}),500)},goBack(){e()},changeTab(e){this.currentTab!==e&&(this.currentTab=e)},clearMessages(){s({title:"确认清空",content:"是否清空所有消息通知？",success:e=>{e.confirm&&(this.messages={today:[],yesterday:[]},t({title:"消息已清空",icon:"success"}))}})},getIconClass:e=>({system:"icon-system",transaction:"icon-transaction",activity:"icon-activity",staff:"icon-staff"}[e]||""),getIconSrc:e=>({system:"/static/message/system.png",transaction:"/static/message/transaction.png",activity:"/static/message/activity.png",staff:"/static/message/staff.png"}[e]||""),loadMoreMessages(){this.isLoading||(this.isLoading=!0,a({title:"加载中..."}),setTimeout((()=>{this.messages.yesterday=[...this.messages.yesterday,{type:"transaction",title:"交易通知",time:"前天 14:30",content:"您有一笔1980.00元的自动结算已到账"}],this.hasMoreMessages=!1,this.isLoading=!1,i()}),1e3))},onScrollToLower(){this.hasMoreMessages&&!this.isLoading&&this.loadMoreMessages()},onRefresh(){this.isRefreshing=!0,this.fetchMessages()}}},[["render",function(e,s,t,a,i,_){const C=u,I=r,T=w(n("custom-navbar"),x),S=v,L=c("message-item"),F=M;return g(),o(I,{class:"container"},{default:l((()=>[m(T,{title:"消息通知","show-back":!0,shadow:!0,onClickLeft:_.goBack},{right:l((()=>[m(I,{onClick:_.clearMessages,style:{padding:"0 16rpx"}},{default:l((()=>[m(C,{src:"/h5/static/home/<USER>",mode:"aspectFit",class:"delete-icon"})])),_:1},8,["onClick"])])),_:1},8,["onClickLeft"]),m(I,{class:"tabs"},{default:l((()=>[(g(!0),y(f,null,d(i.tabs,((e,s)=>(g(),o(I,{key:s,class:b(["tab-item",{active:i.currentTab===s}]),onClick:e=>_.changeTab(s)},{default:l((()=>[m(S,null,{default:l((()=>[h(k(e),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1}),m(F,{"scroll-y":"",class:"message-list",onScrolltolower:_.onScrollToLower,"refresher-enabled":"","refresher-triggered":i.isRefreshing,onRefresherpulling:_.onRefresh},{default:l((()=>[_.filteredMessages.today.length>0||_.filteredMessages.yesterday.length>0?(g(),y(f,{key:0},[_.filteredMessages.today.length>0?(g(),y(f,{key:0},[m(I,{class:"date-divider"},{default:l((()=>[h("今天")])),_:1}),(g(!0),y(f,null,d(_.filteredMessages.today,((e,s)=>(g(),o(L,{key:"today-"+s,message:e,"icon-class":_.getIconClass(e.type),"icon-src":_.getIconSrc(e.type)},null,8,["message","icon-class","icon-src"])))),128))],64)):p("",!0),_.filteredMessages.yesterday.length>0?(g(),y(f,{key:1},[m(I,{class:"date-divider"},{default:l((()=>[h("昨天")])),_:1}),(g(!0),y(f,null,d(_.filteredMessages.yesterday,((e,s)=>(g(),o(L,{key:"yesterday-"+s,message:e,"icon-class":_.getIconClass(e.type),"icon-src":_.getIconSrc(e.type)},null,8,["message","icon-class","icon-src"])))),128))],64)):p("",!0),i.hasMoreMessages?(g(),o(I,{key:2,class:"view-more-btn",onClick:_.loadMoreMessages},{default:l((()=>[m(S,null,{default:l((()=>[h("查看更早消息")])),_:1})])),_:1},8,["onClick"])):p("",!0)],64)):(g(),o(I,{key:1,class:"empty-state"},{default:l((()=>[m(C,{src:"/h5/static/empty-message.png",mode:"aspectFit",class:"empty-icon"}),m(S,{class:"empty-text"},{default:l((()=>[h("暂无消息")])),_:1})])),_:1}))])),_:1},8,["onScrolltolower","refresher-triggered","onRefresherpulling"])])),_:1})}],["__scopeId","data-v-b3383cf2"]]);export{C as default};
