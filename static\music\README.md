# 语音播报音频文件说明

## 📁 音频文件列表

### 🔢 数字音频文件
- `_0.mp3` - 零
- `_1.mp3` - 一
- `_2.mp3` - 二
- `_3.mp3` - 三
- `_4.mp3` - 四
- `_5.mp3` - 五
- `_6.mp3` - 六
- `_7.mp3` - 七
- `_8.mp3` - 八
- `_9.mp3` - 九

### 📊 单位音频文件
- `_shi.mp3` - 十
- `_bai.mp3` - 百
- `_qian.mp3` - 千
- `_wan.mp3` - 万
- `_yi.mp3` - 亿

### 🎵 特殊音频文件
- `_dian.mp3` - 点（小数点）
- `_yuan.mp3` - 元
- `_shoukuan.mp3` - 收款

## 🎯 使用示例

### 金额转换示例：
- `123.45` → `_shoukuan.mp3` + `_1.mp3` + `_bai.mp3` + `_2.mp3` + `_shi.mp3` + `_3.mp3` + `_dian.mp3` + `_4.mp3` + `_5.mp3` + `_yuan.mp3`
- 播报效果：**"收款一百二十三点四五元"**

## 📋 音频文件要求

### 🎵 格式要求
- **文件格式**: MP3
- **采样率**: 44.1kHz 或 48kHz
- **比特率**: 128kbps 或更高
- **声道**: 单声道（推荐）或立体声
- **时长**: 建议每个文件 0.5-1.5 秒

### 🎤 录音要求
- **语音清晰**: 发音标准，无杂音
- **音量一致**: 所有文件音量保持一致
- **语速适中**: 不要过快或过慢
- **语调自然**: 避免机械化语调

## 🔧 文件命名规则

所有音频文件必须严格按照以下命名规则：

```
_[内容].mp3
```

**注意事项：**
- 文件名以下划线 `_` 开头
- 使用英文或拼音标识内容
- 文件扩展名必须是 `.mp3`
- 文件名区分大小写

## 🚀 快速测试

### 测试单个音频文件
```javascript
// 在浏览器控制台或UniApp中测试
const audio = uni.createInnerAudioContext();
audio.src = '/static/music/_shoukuan.mp3';
audio.play();
```

### 测试完整播报
访问测试页面：`pages/test/voice-test`

## 📝 制作音频文件

### 方法一：在线语音合成
1. 使用百度语音、讯飞语音等在线TTS服务
2. 输入对应文字，生成音频
3. 下载并转换为MP3格式

### 方法二：录音制作
1. 使用录音软件（如Audacity）
2. 录制清晰的语音
3. 剪辑并导出为MP3格式

### 方法三：AI语音合成
1. 使用AI语音合成工具
2. 选择合适的音色和语速
3. 批量生成所需音频文件

## ⚠️ 注意事项

1. **文件完整性**: 确保所有必需的音频文件都存在
2. **音质统一**: 所有文件的音质和音量应保持一致
3. **测试验证**: 制作完成后务必进行完整测试
4. **备份保存**: 建议保留原始录音文件作为备份

## 🔍 故障排除

### 音频无法播放
- 检查文件路径是否正确
- 确认文件格式为MP3
- 验证文件是否损坏

### 音量不一致
- 使用音频编辑软件统一音量
- 建议使用标准化处理

### 播放卡顿
- 检查文件大小，建议单个文件小于100KB
- 优化音频压缩设置

---

**制作完成后，请使用测试页面验证所有音频文件的播放效果！**
