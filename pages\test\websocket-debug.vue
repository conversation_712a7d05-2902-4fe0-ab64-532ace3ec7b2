<template>
  <view class="debug-container">
    <view class="debug-header">
      <text class="debug-title">🔧 WebSocket调试面板</text>
    </view>

    <!-- 连接状态 -->
    <view class="debug-section">
      <view class="section-title">📡 连接状态</view>
      <view class="status-grid">
        <view class="status-item">
          <text class="status-label">全局服务状态:</text>
          <text class="status-value" :class="globalStatus.isStarted ? 'success' : 'error'">
            {{ globalStatus.isStarted ? '已启动' : '未启动' }}
          </text>
        </view>
        <view class="status-item">
          <text class="status-label">WebSocket连接:</text>
          <text class="status-value" :class="globalStatus.isConnected ? 'success' : 'error'">
            {{ globalStatus.isConnected ? '已连接' : '未连接' }}
          </text>
        </view>
        <view class="status-item">
          <text class="status-label">重连次数:</text>
          <text class="status-value">{{ globalStatus.reconnectCount }}</text>
        </view>
        <view class="status-item">
          <text class="status-label">语音播报:</text>
          <text class="status-value" :class="globalStatus.voiceEnabled ? 'success' : 'warning'">
            {{ globalStatus.voiceEnabled ? '已开启' : '已关闭' }}
          </text>
        </view>
      </view>
    </view>

    <!-- 用户信息 -->
    <view class="debug-section">
      <view class="section-title">👤 用户信息</view>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">用户ID:</text>
          <text class="info-value">{{ userInfo.userId || '未获取' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">WebSocket频道:</text>
          <text class="info-value">{{ userInfo.channel || '未获取' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">登录Token:</text>
          <text class="info-value">{{ userInfo.hasToken ? '存在' : '不存在' }}</text>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="debug-section">
      <view class="section-title">📊 统计信息</view>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-label">总消息数:</text>
          <text class="stats-value">{{ globalStatus.totalMessages }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">支付通知:</text>
          <text class="stats-value">{{ globalStatus.paymentNotifications }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">今日笔数:</text>
          <text class="stats-value">{{ globalStatus.todayPayments.count }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">今日金额:</text>
          <text class="stats-value">¥{{ globalStatus.todayPayments.amount.toFixed(2) }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="debug-section">
      <view class="section-title">🎮 操作控制</view>
      <view class="button-grid">
        <button class="debug-btn primary" @click="startService">启动服务</button>
        <button class="debug-btn danger" @click="stopService">停止服务</button>
        <button class="debug-btn warning" @click="reconnectService">重新连接</button>
        <button class="debug-btn success" @click="testPayment">测试支付</button>
      </view>
    </view>

    <!-- 日志输出 -->
    <view class="debug-section">
      <view class="section-title">📝 实时日志</view>
      <view class="log-container">
        <view v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-message">{{ log.message }}</text>
        </view>
      </view>
      <button class="debug-btn secondary" @click="clearLogs">清空日志</button>
    </view>
  </view>
</template>

<script>
import globalWebSocketService from '@/utils/globalWebSocketService.js'
import voiceManager from '@/utils/voiceManager.js'
import { getCurrentUserId, getCurrentUserChannel, hasLoginToken } from '@/utils/auth.js'

export default {
  data() {
    return {
      globalStatus: {
        isStarted: false,
        isConnected: false,
        reconnectCount: 0,
        totalMessages: 0,
        paymentNotifications: 0,
        todayPayments: { count: 0, amount: 0 },
        voiceEnabled: false
      },
      userInfo: {
        userId: null,
        channel: null,
        hasToken: false
      },
      logs: [],
      updateTimer: null
    }
  },

  onLoad() {
    console.log('🔧 WebSocket调试页面加载')
    this.updateStatus()
    this.startAutoUpdate()
    this.addLog('info', '调试页面已加载')
  },

  onUnload() {
    this.stopAutoUpdate()
  },

  methods: {
    // 更新状态
    updateStatus() {
      // 更新全局WebSocket状态
      this.globalStatus = globalWebSocketService.getStatus()
      
      // 更新用户信息
      this.userInfo = {
        userId: getCurrentUserId(),
        channel: getCurrentUserChannel(),
        hasToken: hasLoginToken()
      }
    },

    // 开始自动更新
    startAutoUpdate() {
      this.updateTimer = setInterval(() => {
        this.updateStatus()
      }, 1000)
    },

    // 停止自动更新
    stopAutoUpdate() {
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
        this.updateTimer = null
      }
    },

    // 启动服务
    async startService() {
      try {
        this.addLog('info', '正在启动WebSocket服务...')
        await globalWebSocketService.start()
        this.addLog('success', 'WebSocket服务启动成功')
      } catch (error) {
        this.addLog('error', `启动失败: ${error.message}`)
      }
    },

    // 停止服务
    stopService() {
      try {
        this.addLog('info', '正在停止WebSocket服务...')
        globalWebSocketService.stop()
        this.addLog('warning', 'WebSocket服务已停止')
      } catch (error) {
        this.addLog('error', `停止失败: ${error.message}`)
      }
    },

    // 重新连接
    async reconnectService() {
      try {
        this.addLog('info', '正在重新连接...')
        globalWebSocketService.stop()
        await new Promise(resolve => setTimeout(resolve, 1000))
        await globalWebSocketService.start()
        this.addLog('success', '重新连接成功')
      } catch (error) {
        this.addLog('error', `重连失败: ${error.message}`)
      }
    },

    // 测试支付通知
    async testPayment() {
      try {
        const amount = (Math.random() * 100 + 10).toFixed(2)
        this.addLog('info', `测试支付通知: ¥${amount}`)
        await globalWebSocketService.testPaymentNotification(amount)
        this.addLog('success', '测试支付通知已发送')
      } catch (error) {
        this.addLog('error', `测试失败: ${error.message}`)
      }
    },

    // 添加日志
    addLog(type, message) {
      const now = new Date()
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
      
      this.logs.unshift({
        type,
        time,
        message
      })

      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
    },

    // 清空日志
    clearLogs() {
      this.logs = []
      this.addLog('info', '日志已清空')
    }
  }
}
</script>

<style scoped>
.debug-container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.debug-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.debug-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.debug-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #5145F7;
  padding-left: 12rpx;
}

.status-grid, .info-grid, .stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.status-item, .info-item, .stats-item {
  display: flex;
  flex-direction: column;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.status-label, .info-label, .stats-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.status-value, .info-value, .stats-value {
  font-size: 26rpx;
  font-weight: bold;
}

.success { color: #52c41a; }
.warning { color: #faad14; }
.error { color: #ff4d4f; }

.button-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.debug-btn {
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: bold;
  border: none;
}

.primary { background: #5145F7; color: #fff; }
.danger { background: #ff4d4f; color: #fff; }
.warning { background: #faad14; color: #fff; }
.success { background: #52c41a; color: #fff; }
.secondary { background: #d9d9d9; color: #333; }

.log-container {
  max-height: 400rpx;
  overflow-y: auto;
  background: #000;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.log-item {
  display: flex;
  margin-bottom: 8rpx;
  font-size: 24rpx;
}

.log-time {
  color: #888;
  margin-right: 16rpx;
  min-width: 120rpx;
}

.log-message {
  flex: 1;
}

.log-item.info .log-message { color: #fff; }
.log-item.success .log-message { color: #52c41a; }
.log-item.warning .log-message { color: #faad14; }
.log-item.error .log-message { color: #ff4d4f; }
</style>
