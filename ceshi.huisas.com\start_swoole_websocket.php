<?php
/**
 * Swoole WebSocket 服务器启动脚本
 * 简化版启动脚本，用于快速测试
 */

// 检查Swoole扩展
if (!extension_loaded('swoole')) {
    die("❌ Swoole扩展未安装，请先在宝塔面板安装Swoole扩展\n");
}

echo "✅ Swoole扩展已安装，版本：" . swoole_version() . "\n";
echo "✅ PHP版本：" . PHP_VERSION . "\n";

// 包含主服务器文件
require_once __DIR__ . '/swoole_websocket_server.php';

// 启动配置
$config = [
    'host' => '0.0.0.0',
    'port' => 8080,
    'worker_num' => 1,  // 测试阶段使用1个进程
    'heartbeat_interval' => 30,
    'max_connections' => 100
];

echo "🚀 启动Swoole WebSocket支付服务器...\n";
echo "配置信息：\n";
echo "- 监听地址: {$config['host']}:{$config['port']}\n";
echo "- 工作进程: {$config['worker_num']}\n";
echo "- 心跳间隔: {$config['heartbeat_interval']}秒\n";
echo "- 最大连接: {$config['max_connections']}\n\n";

$server = new PaymentWebSocketServer($config);
$server->start();
?>