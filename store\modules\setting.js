// store/modules/setting.js - 设置模块状态管理

// 初始状态
const state = {
  // 主题设置
  theme: uni.getStorageSync('theme') || 'light',
  
  // 支付设置
  paymentSetting: {
    // 默认支付方式
    defaultPayType: uni.getStorageSync('defaultPayType') || '',
    // 支付成功后自动关闭
    autoClose: uni.getStorageSync('autoClose') !== 'false',
    // 支付完成后跳转页面
    completePage: uni.getStorageSync('completePage') || 'result'
  },
  
  // 功能开关
  features: {
    // 启用生物识别
    biometrics: uni.getStorageSync('useBiometrics') === 'true',
    // 保存收款二维码到相册
    saveQrCode: uni.getStorageSync('saveQrCode') !== 'false',
    // 显示交易通知
    notifications: uni.getStorageSync('showNotifications') !== 'false'
  }
};

// 修改状态的方法
const mutations = {
  // 设置主题
  SET_THEME(state, theme) {
    state.theme = theme;
    uni.setStorageSync('theme', theme);
  },
  
  // 设置支付设置
  SET_PAYMENT_SETTING(state, setting) {
    state.paymentSetting = { ...state.paymentSetting, ...setting };
    
    // 保存到本地存储
    Object.keys(setting).forEach(key => {
      uni.setStorageSync(key, setting[key]);
    });
  },
  
  // 设置功能开关
  SET_FEATURES(state, features) {
    state.features = { ...state.features, ...features };
    
    // 保存到本地存储
    if (features.biometrics !== undefined) {
      uni.setStorageSync('useBiometrics', features.biometrics.toString());
    }
    
    if (features.saveQrCode !== undefined) {
      uni.setStorageSync('saveQrCode', features.saveQrCode.toString());
    }
    
    if (features.notifications !== undefined) {
      uni.setStorageSync('showNotifications', features.notifications.toString());
    }
  }
};

// 异步操作
const actions = {
  // 切换主题
  toggleTheme({ commit, state }) {
    const newTheme = state.theme === 'light' ? 'dark' : 'light';
    commit('SET_THEME', newTheme);
    return newTheme;
  },
  
  // 保存支付设置
  savePaymentSetting({ commit }, setting) {
    commit('SET_PAYMENT_SETTING', setting);
    return setting;
  },
  
  // 保存功能开关设置
  saveFeatures({ commit }, features) {
    commit('SET_FEATURES', features);
    return features;
  }
};

// 获取状态的方法
const getters = {
  theme: state => state.theme,
  isDarkTheme: state => state.theme === 'dark',
  paymentSetting: state => state.paymentSetting,
  features: state => state.features
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}; 