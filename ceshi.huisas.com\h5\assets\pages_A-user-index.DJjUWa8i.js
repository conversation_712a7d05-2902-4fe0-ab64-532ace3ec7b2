import{g as s,n as e,E as a,b as t,a as l,d as i,e as o,w as c,i as n,o as r,f as d,h as u,t as g,j as f,k as _,l as p,p as v}from"./index-B1Q521gi.js";import{_ as m}from"./custom-navbar.DuzuSmPc.js";import{r as h}from"./uni-app.es.DAfa8VxY.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";const C=k({data:()=>({isLoggedIn:!1,userInfo:{nickname:"",avatar:"",level:"",balance:0,points:0,coupons:0},orderCounts:{pending:0,processing:0}}),onLoad(){this.checkLoginStatus()},onShow(){this.loadUserInfo(),this.loadOrderCounts()},methods:{checkLoginStatus(){const e=s("token");this.isLoggedIn=!!e,this.isLoggedIn&&(this.loadUserInfo(),this.loadOrderCounts())},loadUserInfo(){setTimeout((()=>{this.userInfo={nickname:"测试用户",avatar:"/static/images/default-avatar.png",level:"普通会员",balance:258.66,points:520,coupons:3}}),500)},loadOrderCounts(){setTimeout((()=>{this.orderCounts={pending:2,processing:1}}),500)},formatMoney:s=>"¥"+s.toFixed(2),navigateTo(s){e({url:s})},handleLogin(){e({url:"/pages_A/user/login"})},handleLogout(){a({title:"提示",content:"确定要退出登录吗？",success:s=>{s.confirm&&(t("token"),this.isLoggedIn=!1,this.userInfo={nickname:"",avatar:"",level:"",balance:0,points:0,coupons:0},l({title:"已退出登录",icon:"none"}))}})}}},[["render",function(s,e,a,t,l,k){const C=h(i("custom-navbar"),m),b=_,I=n,T=p,A=v;return r(),o(I,{class:"container"},{default:c((()=>[d(C,{title:"会员中心",shadow:!0}),d(I,{class:"user-card"},{default:c((()=>[d(I,{class:"avatar-container"},{default:c((()=>[d(b,{class:"avatar",src:l.userInfo.avatar||"/static/images/default-avatar.png"},null,8,["src"])])),_:1}),d(I,{class:"user-info"},{default:c((()=>[d(I,{class:"username"},{default:c((()=>[u(g(l.userInfo.nickname||"测试用户"),1)])),_:1}),l.userInfo.level?(r(),o(I,{key:0,class:"user-level"},{default:c((()=>[d(T,{class:"level-tag"},{default:c((()=>[u(g(l.userInfo.level),1)])),_:1})])),_:1})):f("",!0)])),_:1}),l.isLoggedIn?f("",!0):(r(),o(I,{key:0,class:"login-btn",onClick:k.handleLogin},{default:c((()=>[u(" 登录/注册 ")])),_:1},8,["onClick"]))])),_:1}),d(I,{class:"statistics-card"},{default:c((()=>[d(I,{class:"statistics-item",onClick:e[0]||(e[0]=s=>k.navigateTo("/pages_A/user/wallet"))},{default:c((()=>[d(I,{class:"statistics-value"},{default:c((()=>[u(g(k.formatMoney(l.userInfo.balance||0)),1)])),_:1}),d(I,{class:"statistics-label"},{default:c((()=>[u("余额")])),_:1})])),_:1}),d(I,{class:"statistics-item",onClick:e[1]||(e[1]=s=>k.navigateTo("/pages_A/user/points"))},{default:c((()=>[d(I,{class:"statistics-value"},{default:c((()=>[u(g(l.userInfo.points||0),1)])),_:1}),d(I,{class:"statistics-label"},{default:c((()=>[u("积分")])),_:1})])),_:1}),d(I,{class:"statistics-item",onClick:e[2]||(e[2]=s=>k.navigateTo("/pages_A/user/coupons"))},{default:c((()=>[d(I,{class:"statistics-value"},{default:c((()=>[u(g(l.userInfo.coupons||0),1)])),_:1}),d(I,{class:"statistics-label"},{default:c((()=>[u("优惠券")])),_:1})])),_:1})])),_:1}),d(I,{class:"section-card orders-card"},{default:c((()=>[d(I,{class:"section-header"},{default:c((()=>[d(T,{class:"section-title"},{default:c((()=>[u("我的订单")])),_:1}),d(I,{class:"more-link",onClick:e[3]||(e[3]=s=>k.navigateTo("/pages_A/order/list"))},{default:c((()=>[u(" 查看全部 "),d(T,{class:"arrow"},{default:c((()=>[u(">")])),_:1})])),_:1})])),_:1}),d(I,{class:"order-icons"},{default:c((()=>[d(I,{class:"order-icon-item",onClick:e[4]||(e[4]=s=>k.navigateTo("/pages_A/order/list?status=0"))},{default:c((()=>[d(I,{class:"icon-container"},{default:c((()=>[d(b,{src:"/h5/static/images/order-pending.png",class:"order-icon"}),l.orderCounts.pending>0?(r(),o(I,{key:0,class:"badge"},{default:c((()=>[u(g(l.orderCounts.pending),1)])),_:1})):f("",!0)])),_:1}),d(T,{class:"icon-label"},{default:c((()=>[u("待付款")])),_:1})])),_:1}),d(I,{class:"order-icon-item",onClick:e[5]||(e[5]=s=>k.navigateTo("/pages_A/order/list?status=1"))},{default:c((()=>[d(I,{class:"icon-container"},{default:c((()=>[d(b,{src:"/h5/static/images/order-processing.png",class:"order-icon"}),l.orderCounts.processing>0?(r(),o(I,{key:0,class:"badge"},{default:c((()=>[u(g(l.orderCounts.processing),1)])),_:1})):f("",!0)])),_:1}),d(T,{class:"icon-label"},{default:c((()=>[u("处理中")])),_:1})])),_:1}),d(I,{class:"order-icon-item",onClick:e[6]||(e[6]=s=>k.navigateTo("/pages_A/order/list?status=3"))},{default:c((()=>[d(I,{class:"icon-container"},{default:c((()=>[d(b,{src:"/h5/static/images/order-completed.png",class:"order-icon"})])),_:1}),d(T,{class:"icon-label"},{default:c((()=>[u("已完成")])),_:1})])),_:1}),d(I,{class:"order-icon-item",onClick:e[7]||(e[7]=s=>k.navigateTo("/pages_A/order/list?status=4"))},{default:c((()=>[d(I,{class:"icon-container"},{default:c((()=>[d(b,{src:"/h5/static/images/order-refund.png",class:"order-icon"})])),_:1}),d(T,{class:"icon-label"},{default:c((()=>[u("退款/售后")])),_:1})])),_:1})])),_:1})])),_:1}),d(I,{class:"section-card services-card"},{default:c((()=>[d(I,{class:"section-header"},{default:c((()=>[d(T,{class:"section-title"},{default:c((()=>[u("会员服务")])),_:1})])),_:1}),d(I,{class:"services-grid"},{default:c((()=>[d(I,{class:"service-item",onClick:e[8]||(e[8]=s=>k.navigateTo("/pages_A/user/coupons"))},{default:c((()=>[d(b,{src:"/h5/static/images/service-coupon.png",class:"service-icon"}),d(T,{class:"service-label"},{default:c((()=>[u("优惠券")])),_:1})])),_:1}),d(I,{class:"service-item",onClick:e[9]||(e[9]=s=>k.navigateTo("/pages_A/user/history"))},{default:c((()=>[d(b,{src:"/h5/static/images/service-history.png",class:"service-icon"}),d(T,{class:"service-label"},{default:c((()=>[u("消费记录")])),_:1})])),_:1}),d(I,{class:"service-item",onClick:e[10]||(e[10]=s=>k.navigateTo("/pages_A/user/settings"))},{default:c((()=>[d(b,{src:"/h5/static/images/service-settings.png",class:"service-icon"}),d(T,{class:"service-label"},{default:c((()=>[u("会员设置")])),_:1})])),_:1}),d(I,{class:"service-item",onClick:e[11]||(e[11]=s=>k.navigateTo("/pages_A/user/help"))},{default:c((()=>[d(b,{src:"/h5/static/images/service-help.png",class:"service-icon"}),d(T,{class:"service-label"},{default:c((()=>[u("帮助中心")])),_:1})])),_:1})])),_:1})])),_:1}),l.isLoggedIn?(r(),o(I,{key:0,class:"logout-container"},{default:c((()=>[d(A,{class:"logout-button",onClick:k.handleLogout},{default:c((()=>[u("退出登录")])),_:1},8,["onClick"])])),_:1})):f("",!0)])),_:1})}],["__scopeId","data-v-3046ac8b"]]);export{C as default};
