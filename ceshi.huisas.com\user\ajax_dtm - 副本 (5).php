<?php
include("../includes/common.php");

// 设置CORS头部
$allowed_origins = [
    'http://localhost:5173',
    'http://127.0.0.1:5173',
    'http://ceshi.huisas.com'
];

$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: http://ceshi.huisas.com');
}

header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 修改跨域检查逻辑
function checkRefererHostWithCORS(){
    global $allowed_origins;

    // 获取请求来源
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
    $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

    // 如果是直接访问（没有 Origin 和 Referer），允许通过
    if(!$origin && !$referer) return true;

    $http_host = $_SERVER['HTTP_HOST'];
    if(strpos($http_host,':'))$http_host = substr($http_host, 0, strpos($http_host, ':'));

    // 检查 Origin
    if($origin) {
        // 检查是否在允许的跨域列表中
        if(in_array($origin, $allowed_origins)) return true;

        // 检查是否是同域名
        $origin_parts = parse_url($origin);
        if(isset($origin_parts['host']) && $origin_parts['host'] === $http_host) return true;
    }

    // 检查 Referer
    if($referer) {
        $referer_parts = parse_url($referer);
        if(isset($referer_parts['host'])) {
            // 检查是否是同域名
            if($referer_parts['host'] === $http_host) return true;

            // 构建 referer origin
            $referer_origin = $referer_parts['scheme'] . '://' . $referer_parts['host'];
            if(isset($referer_parts['port'])) {
                $referer_origin .= ':' . $referer_parts['port'];
            }

            // 检查是否在允许的跨域列表中
            if(in_array($referer_origin, $allowed_origins)) return true;
        }
    }

    return false;
}

if($islogin2==1){}else exit('{"code":-3,"msg":"No Login"}');
$act=isset($_GET['act'])?daddslashes($_GET['act']):null;

if(!checkRefererHostWithCORS())exit('{"code":403,"msg":"跨域请求被拒绝"}');

@header('Content-Type: application/json; charset=UTF-8');

switch($act){
case 'test':
	exit('{"code":0,"msg":"API连接正常","timestamp":"'.date('Y-m-d H:i:s').'"}');
	break;

case 'testpay2':
	$money=trim($_POST['money']);
	$name=trim($_POST['name']);
	$typeid=intval($_POST['typeid']);
	if($money<=0 || !is_numeric($money) || !$name || !preg_match('/^[0-9.]+$/', $money))exit('{"code":-1,"msg":"数据有误"}');
	if($conf['pay_maxmoney']>0 && $money>$conf['pay_maxmoney'])exit('{"code":-1,"msg":"最大支付金额是'.$conf['pay_maxmoney'].'元"}');
	if($conf['pay_minmoney']>0 && $money<$conf['pay_minmoney'])exit('{"code":-1,"msg":"最小支付金额是'.$conf['pay_minmoney'].'元"}');

	$trade_no=date("YmdHis").rand(11111,99999);
	$return_url=$siteurl.'user/test2.php?ok=1&trade_no='.$trade_no;
	$domain=getdomain($return_url);
	if(!$DB->exec("INSERT INTO `pay_order` (`trade_no`,`out_trade_no`,`uid`,`tid`,`addtime`,`name`,`money`,`notify_url`,`return_url`,`domain`,`ip`,`status`) VALUES (:trade_no, :out_trade_no, :uid, 3, NOW(), :name, :money, :notify_url, :return_url, :domain, :clientip, 0)", [':trade_no'=>$trade_no, ':out_trade_no'=>$trade_no, ':uid'=>$conf['test_pay_uid'], ':name'=>$name, ':money'=>$money, ':notify_url'=>$return_url, ':return_url'=>$return_url, ':domain'=>$domain, ':clientip'=>$clientip]))exit('{"code":-1,"msg":"创建订单失败，请返回重试！"}');
	$result = ['code'=>0, 'msg'=>'succ', 'url'=>'../submit2.php?typeid='.$typeid.'&trade_no='.$trade_no];
	exit(json_encode($result));
break;
case 'generate_qrcode':
	// 新增：直接生成二维码数据的接口
	$money=trim($_POST['money']);
	$name=trim($_POST['name']);
	$typeid=intval($_POST['typeid']);
	if($money<=0 || !is_numeric($money) || !$name || !preg_match('/^[0-9.]+$/', $money))exit('{"code":-1,"msg":"数据有误"}');
	if($conf['pay_maxmoney']>0 && $money>$conf['pay_maxmoney'])exit('{"code":-1,"msg":"最大支付金额是'.$conf['pay_maxmoney'].'元"}');
	if($conf['pay_minmoney']>0 && $money<$conf['pay_minmoney'])exit('{"code":-1,"msg":"最小支付金额是'.$conf['pay_minmoney'].'元"}');

	// 验证支付方式是否存在
	$paytype = $DB->getRow("SELECT id,name,status FROM pre_type WHERE id=:typeid LIMIT 1", [':typeid'=>$typeid]);
	if(!$paytype || $paytype['status']==0)exit('{"code":-1,"msg":"支付方式不存在或已禁用"}');

	try {
		// 创建订单
		$trade_no=date("YmdHis").rand(11111,99999);
		$return_url=$siteurl.'user/test2.php?ok=1&trade_no='.$trade_no;
		$domain=getdomain($return_url);

		if(!$DB->exec("INSERT INTO `pre_order` (`trade_no`,`out_trade_no`,`uid`,`tid`,`addtime`,`name`,`money`,`type`,`channel`,`notify_url`,`return_url`,`domain`,`ip`,`status`) VALUES (:trade_no, :out_trade_no, :uid, 3, NOW(), :name, :money, :type, :channel, :notify_url, :return_url, :domain, :clientip, 0)", [':trade_no'=>$trade_no, ':out_trade_no'=>$trade_no, ':uid'=>$conf['test_pay_uid'], ':name'=>$name, ':money'=>$money, ':type'=>$typeid, ':channel'=>$typeid, ':notify_url'=>$return_url, ':return_url'=>$return_url, ':domain'=>$domain, ':clientip'=>$clientip])) {
			exit('{"code":-1,"msg":"创建订单失败，请返回重试！"}');
		}

		// 模拟支付处理逻辑，生成支付URL
		// 使用authcode加密商户ID
		$encrypted_merchant = authcode($conf['test_pay_uid'], 'ENCODE', SYS_KEY);
		$payment_url = $siteurl.'paypage/?merchant='.urlencode($encrypted_merchant).'&order_id='.urlencode($trade_no).'&money='.urlencode($money).'&name='.urlencode($name).'&type='.urlencode($paytype['name']);

		// 生成二维码（使用第三方二维码API）
		$qrcode_api = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data='.urlencode($payment_url);

		$result = [
			'code' => 0,
			'msg' => 'success',
			'data' => [
				'trade_no' => $trade_no,
				'qrcode_url' => $qrcode_api,
				'payment_url' => $payment_url,
				'money' => $money,
				'name' => $name,
				'expire_time' => time() + 1800 // 30分钟过期
			]
		];
		exit(json_encode($result));

	} catch (Exception $e) {
		exit('{"code":-1,"msg":"生成二维码失败：'.$e->getMessage().'"}');
	}
break;
case 'generate_qrcode_fast':
	// 优化版本：快速生成二维码数据的接口
	$money=trim($_POST['money']);
	$name=trim($_POST['name']);
	$typeid=intval($_POST['typeid']);
	if($money<=0 || !is_numeric($money) || !$name || !preg_match('/^[0-9.]+$/', $money))exit('{"code":-1,"msg":"数据有误"}');

	// 验证支付方式是否存在
	$paytype = $DB->getRow("SELECT id,name,status FROM pre_type WHERE id=:typeid LIMIT 1", [':typeid'=>$typeid]);
	if(!$paytype || $paytype['status']==0)exit('{"code":-1,"msg":"支付方式不存在或已禁用"}');
	if($conf['pay_maxmoney']>0 && $money>$conf['pay_maxmoney'])exit('{"code":-1,"msg":"最大支付金额是'.$conf['pay_maxmoney'].'元"}');
	if($conf['pay_minmoney']>0 && $money<$conf['pay_minmoney'])exit('{"code":-1,"msg":"最小支付金额是'.$conf['pay_minmoney'].'元"}');

	try {
		// 快速生成订单号
		$trade_no='DTM'.date("YmdHis").rand(1000,9999);
		$return_url=$siteurl.'user/test2.php?ok=1&trade_no='.$trade_no;
		$domain=getdomain($return_url);

		// 异步创建订单（不等待数据库响应）
		$DB->exec("INSERT INTO `pre_order` (`trade_no`,`out_trade_no`,`uid`,`tid`,`addtime`,`name`,`money`,`type`,`channel`,`notify_url`,`return_url`,`domain`,`ip`,`status`) VALUES (:trade_no, :out_trade_no, :uid, 3, NOW(), :name, :money, :type, :channel, :notify_url, :return_url, :domain, :clientip, 0)", [':trade_no'=>$trade_no, ':out_trade_no'=>$trade_no, ':uid'=>$conf['test_pay_uid'], ':name'=>$name, ':money'=>$money, ':type'=>$typeid, ':channel'=>$typeid, ':notify_url'=>$return_url, ':return_url'=>$return_url, ':domain'=>$domain, ':clientip'=>$clientip]);

		// 直接生成支付URL
		// 使用authcode加密商户ID
		$encrypted_merchant = authcode($conf['test_pay_uid'], 'ENCODE', SYS_KEY);
		$payment_url = $siteurl.'paypage/?merchant='.urlencode($encrypted_merchant).'&order_id='.urlencode($trade_no).'&money='.urlencode($money).'&name='.urlencode($name).'&type='.urlencode($paytype['name']);

		// 优化：使用更快的二维码API，增大尺寸提高清晰度
		$qrcode_api = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&format=png&ecc=L&data='.urlencode($payment_url);

		// 立即返回结果，不等待外部API响应
		$result = [
			'code' => 0,
			'msg' => 'success',
			'data' => [
				'trade_no' => $trade_no,
				'qrcode_url' => $qrcode_api,
				'payment_url' => $payment_url,
				'money' => $money,
				'name' => $name,
				'expire_time' => time() + 1800, // 30分钟过期
				'generated_at' => time(),
				'fast_mode' => true
			]
		];
		exit(json_encode($result));

	} catch (Exception $e) {
		// 快速失败，返回错误
		exit('{"code":-1,"msg":"生成失败"}');
	}
break;
default:
	exit('{"code":-4,"msg":"No Act"}');
break;
}