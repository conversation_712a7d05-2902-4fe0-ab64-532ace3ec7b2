import{r as e,A as t,B as o,g as n,a as r,b as a,n as c,ag as s,s as i}from"./index-B1Q521gi.js";const l={baseURL:"http://ceshi.huisas.com",timeout:1e4,header:{"Content-Type":"application/x-www-form-urlencoded","X-Requested-With":"XMLHttpRequest"}},d={invoke:e=>new Promise((t=>{const o=n("token");o&&(e.header.Authorization=`Bearer ${o}`);const r=n("csrf_token");if(r)if("POST"===e.method)"object"!=typeof e.data&&(e.data={}),e.data.csrf_token=r,console.log("添加CSRF Token到POST请求:",r);else if("GET"===e.method){const t=e.url.includes("?")?"&":"?";e.url=`${e.url}${t}csrf_token=${encodeURIComponent(r)}`,console.log("添加CSRF Token到GET请求URL:",e.url)}if("POST"===e.method&&(e.header["content-type"]="application/x-www-form-urlencoded","object"==typeof e.data)){const t=Object.keys(e.data).map((t=>null!==e.data[t]&&void 0!==e.data[t]?`${encodeURIComponent(t)}=${encodeURIComponent(e.data[t])}`:"")).filter((e=>""!==e)).join("&");e.data=t,console.log("转换后的表单数据:",t)}t(e)}))},u={success(e){const{statusCode:t,data:o}=e;return 200===t?0===o.code||1===o.code?o:(function(e){const{code:t,msg:o}=e;switch(t){case-1:r({title:o||"登录失败",icon:"none"});break;case 401:a("token"),a("csrf_token"),c({url:"/pages/login/index"});break;default:r({title:o||"操作失败",icon:"none"})}}(o),Promise.reject(o)):(function(e){r({title:{400:"请求错误",401:"未授权，请重新登录",403:"拒绝访问",404:"请求错误，未找到该资源",405:"请求方法未允许",408:"请求超时",500:"服务器端出错",501:"网络未实现",502:"网络错误",503:"服务不可用",504:"网络超时",505:"http版本不支持该请求"}[e]||`连接错误${e}`,icon:"none"})}(t),Promise.reject(e))},fail:e=>(r({title:"网络连接失败，请检查网络设置",icon:"none"}),s((e=>{e.isConnected||r({title:"网络已断开",icon:"none"})})),Promise.reject(e))};const h=3e5,m="request_cache",f={get(e){try{const t=(n(m)||{})[e];return t&&t.expireTime>Date.now()?t.data:null}catch(t){return console.error("Cache get error:",t),null}},set(e,t,o=h){try{const r=n(m)||{};r[e]={data:t,expireTime:Date.now()+o},i(m,r)}catch(r){console.error("Cache set error:",r)}},clear(e){try{if(e){const t=n(m)||{};delete t[e],i(m,t)}else a(m)}catch(t){console.error("Cache clear error:",t)}}},p={queue:new Map,add(e,t){return this.queue.has(e)||this.queue.set(e,t),this.queue.get(e)},remove(e){this.queue.delete(e)},get(e){return this.queue.get(e)},generateKey:e=>`${e.method}:${e.url}:${JSON.stringify(e.data)}`};function g(n){let r;return!1!==n.loading&&((e="加载中...")=>{t({title:e,mask:!0})})(n.loadingText),r=n.url.startsWith("http")?n.url:(l.baseURL+n.url).replace(/([^:]\/)\/+/g,"$1"),console.log("发起请求:",{url:r,method:n.method||"GET",data:n.data||{},header:n.header||l.header}),new Promise(((t,a)=>{const c={...l,...n,url:r};d.invoke(c).then((r=>{if(r.cache){const e=`${r.method}:${r.url}:${JSON.stringify(r.data)}`,o=f.get(e);if(o)return t(o)}const c=p.generateKey(r);if(r.preventDuplicate&&p.get(c))return p.get(c);const s=new Promise(((t,a)=>{e({...r,success:e=>{try{const o=u.success(e);if(r.cache){const e=`${r.method}:${r.url}:${JSON.stringify(r.data)}`;f.set(e,o,r.cacheExpireTime)}t(o)}catch(o){a(o)}},fail:e=>{console.error("请求失败:",e);try{const t=u.fail(e);a(t)}catch(t){a(t)}},complete:()=>{p.remove(c),!1!==n.loading&&o()}})}));r.preventDuplicate&&p.add(c,s),s.then(t).catch(a)})).catch((e=>{console.error("拦截器错误:",e),a(e)}))}))}function $(e,t={},o={}){return g({url:e,method:"GET",data:t,...o})}function y(e,t={},o={}){return g({url:e,method:"POST",data:t,...o})}export{$ as g,y as p};
