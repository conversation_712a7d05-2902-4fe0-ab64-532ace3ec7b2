// 导航栏主题配置
export const navbarThemes = {
  // 默认主题 - 蓝色渐变
  default: {
    backgroundColor: '#4A90E2',
    textColor: '#FFFFFF',
    gradient: 'linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)',
    shadow: '0 2px 10px rgba(74, 144, 226, 0.3)'
  },
  
  // 商务主题 - 深蓝色
  business: {
    backgroundColor: '#1E3A8A',
    textColor: '#FFFFFF',
    gradient: 'linear-gradient(135deg, #1E3A8A 0%, #1E40AF 100%)',
    shadow: '0 2px 10px rgba(30, 58, 138, 0.3)'
  },
  
  // 成功主题 - 绿色
  success: {
    backgroundColor: '#10B981',
    textColor: '#FFFFFF',
    gradient: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
    shadow: '0 2px 10px rgba(16, 185, 129, 0.3)'
  },
  
  // 警告主题 - 橙色
  warning: {
    backgroundColor: '#F59E0B',
    textColor: '#FFFFFF',
    gradient: 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)',
    shadow: '0 2px 10px rgba(245, 158, 11, 0.3)'
  },
  
  // 错误主题 - 红色
  error: {
    backgroundColor: '#EF4444',
    textColor: '#FFFFFF',
    gradient: 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)',
    shadow: '0 2px 10px rgba(239, 68, 68, 0.3)'
  },
  
  // 紫色主题
  purple: {
    backgroundColor: '#8B5CF6',
    textColor: '#FFFFFF',
    gradient: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)',
    shadow: '0 2px 10px rgba(139, 92, 246, 0.3)'
  },
  
  // 粉色主题
  pink: {
    backgroundColor: '#EC4899',
    textColor: '#FFFFFF',
    gradient: 'linear-gradient(135deg, #EC4899 0%, #DB2777 100%)',
    shadow: '0 2px 10px rgba(236, 72, 153, 0.3)'
  },
  
  // 浅色主题
  light: {
    backgroundColor: '#F8FAFC',
    textColor: '#1F2937',
    gradient: 'linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%)',
    shadow: '0 2px 10px rgba(0, 0, 0, 0.1)'
  },
  
  // 深色主题
  dark: {
    backgroundColor: '#1F2937',
    textColor: '#FFFFFF',
    gradient: 'linear-gradient(135deg, #1F2937 0%, #111827 100%)',
    shadow: '0 2px 10px rgba(0, 0, 0, 0.5)'
  }
};

// 页面特定主题配置
export const pageThemes = {
  // 首页
  index: 'default',
  
  // 账单页面
  bill: 'business',
  
  // 扫码页面
  scan: 'success',
  
  // 报表页面
  report: 'purple',
  
  // 我的页面
  mine: 'dark',
  
  // 钱包页面
  wallet: 'success',
  
  // 商家信息
  merchant: 'business',
  
  // 消息通知
  message: 'warning'
};

// 获取页面主题
export function getPageTheme(pageName) {
  const themeName = pageThemes[pageName] || 'default';
  return navbarThemes[themeName];
}

// 获取主题样式
export function getThemeStyle(themeName = 'default') {
  const theme = navbarThemes[themeName];
  if (!theme) return navbarThemes.default;
  
  return {
    background: theme.gradient || theme.backgroundColor,
    color: theme.textColor,
    boxShadow: theme.shadow
  };
}

// 动态切换主题
export function applyTheme(element, themeName) {
  const theme = navbarThemes[themeName];
  if (!theme || !element) return;
  
  element.style.background = theme.gradient || theme.backgroundColor;
  element.style.color = theme.textColor;
  element.style.boxShadow = theme.shadow;
}

export default {
  navbarThemes,
  pageThemes,
  getPageTheme,
  getThemeStyle,
  applyTheme
};
