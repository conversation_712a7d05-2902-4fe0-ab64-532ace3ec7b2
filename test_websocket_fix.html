<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 WebSocket修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .warning { background: rgba(255, 152, 0, 0.3); }
        .info { background: rgba(33, 150, 243, 0.3); }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .log {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WebSocket修复测试工具</h1>
        <p>测试WebSocket连接和语音播报功能</p>

        <div id="status" class="status info">
            🔌 准备连接WebSocket服务器...
        </div>

        <div>
            <button onclick="connectWebSocket()">🔗 连接WebSocket</button>
            <button onclick="sendTestPayment()">💰 发送测试支付</button>
            <button onclick="testVoice()">🎵 测试语音播放</button>
            <button onclick="clearLog()">🗑️ 清空日志</button>
        </div>

        <div class="log" id="log">
            <div>📋 日志输出：</div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#00bcd4',
                success: '#4caf50',
                error: '#f44336',
                warning: '#ff9800'
            };
            
            logDiv.innerHTML += `<div style="color: ${colors[type]};">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function connectWebSocket() {
            if (ws && isConnected) {
                log('⚠️ WebSocket已连接', 'warning');
                return;
            }

            const wsUrl = 'ws://ceshi.huisas.com:8080';
            log(`🔗 正在连接: ${wsUrl}`, 'info');
            updateStatus('🔌 正在连接WebSocket...', 'warning');

            ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                isConnected = true;
                log('✅ WebSocket连接成功！', 'success');
                updateStatus('✅ WebSocket已连接', 'success');
                
                // 发送认证
                const authData = {
                    type: 'auth',
                    data: {
                        merchant_id: '1000',
                        staff_id: '',
                        token: 'test_token'
                    }
                };
                ws.send(JSON.stringify(authData));
                log('🔐 发送认证信息', 'info');
            };

            ws.onmessage = function(event) {
                log(`📨 收到消息: ${event.data}`, 'info');
                
                try {
                    const data = JSON.parse(event.data);
                    
                    if (data.type === 'payment_notification') {
                        log('💰 收到支付通知！', 'success');
                        handlePaymentNotification(data.data);
                    }
                } catch (error) {
                    log(`⚠️ 消息解析失败: ${error.message}`, 'warning');
                }
            };

            ws.onclose = function(event) {
                isConnected = false;
                log(`🔌 WebSocket连接关闭: code=${event.code}`, 'warning');
                updateStatus('🔌 WebSocket已断开', 'error');
            };

            ws.onerror = function(error) {
                log(`❌ WebSocket错误: ${error}`, 'error');
                updateStatus('❌ WebSocket连接错误', 'error');
            };
        }

        function sendTestPayment() {
            if (!isConnected) {
                log('❌ WebSocket未连接，无法发送测试支付', 'error');
                return;
            }

            const testPayment = {
                type: 'payment_notification',
                data: {
                    trade_no: 'TEST_' + Date.now(),
                    uid: '1000',
                    money: '0.01',
                    type: '1',
                    typename: '在线支付',
                    addtime: new Date().toISOString(),
                    api_trade_no: 'API_' + Date.now(),
                    buyer: '<EMAIL>',
                    status: 'success'
                }
            };

            ws.send(JSON.stringify(testPayment));
            log('💰 发送测试支付通知', 'info');
        }

        function handlePaymentNotification(paymentData) {
            const amount = paymentData.money || paymentData.amount || '0.00';
            log(`🎵 处理支付通知: 收款${amount}元`, 'success');
            
            // 模拟语音播放
            testVoice(amount);
        }

        function testVoice(amount = '0.01') {
            log(`🎵 测试语音播放: 收款${amount}元`, 'info');
            
            // 使用Web Speech API进行语音播报
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(`收款${amount}元`);
                utterance.lang = 'zh-CN';
                utterance.rate = 1.0;
                utterance.pitch = 1.0;
                
                utterance.onstart = function() {
                    log('🔊 语音播放开始', 'success');
                };
                
                utterance.onend = function() {
                    log('✅ 语音播放完成', 'success');
                };
                
                utterance.onerror = function(event) {
                    log(`❌ 语音播放失败: ${event.error}`, 'error');
                };
                
                speechSynthesis.speak(utterance);
            } else {
                log('❌ 浏览器不支持语音播报', 'error');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>📋 日志输出：</div>';
        }

        // 页面加载时自动连接
        window.onload = function() {
            log('🚀 WebSocket修复测试工具已加载', 'info');
            log('✅ 修复完成：首页已重新启用WebSocket Mixin', 'success');
            log('✅ 修复完成：WebSocket Mixin支持嵌套数据格式', 'success');
            log('📱 请在手机上刷新首页，然后进行0.01元支付测试', 'warning');
            setTimeout(connectWebSocket, 1000);
        };
    </script>
</body>
</html>
