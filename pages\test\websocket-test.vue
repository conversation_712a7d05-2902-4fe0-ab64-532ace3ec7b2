<template>
	<view class="container">
		<view class="header">
			<text class="title">WebSocket语音播报测试</text>
			<text class="subtitle">测试后端WebSocket连接和语音播报功能</text>
		</view>
		
		<view class="status-section">
			<view class="status-item">
				<text class="label">连接状态:</text>
				<text :class="['status', connectionStatus]">{{ getStatusText() }}</text>
			</view>
			<view class="status-item">
				<text class="label">语音状态:</text>
				<text :class="['status', voiceEnabled ? 'connected' : 'disconnected']">
					{{ voiceEnabled ? '已开启' : '已关闭' }}
				</text>
			</view>
			<view class="status-item">
				<text class="label">服务器地址:</text>
				<text class="server-url">{{ wsUrl }}</text>
			</view>
		</view>

		<view class="control-section">
			<button @click="toggleConnection" :class="['btn', connectionStatus === 'connected' ? 'btn-danger' : 'btn-primary']">
				{{ connectionStatus === 'connected' ? '断开连接' : '连接WebSocket' }}
			</button>
			
			<button @click="toggleVoice" :class="['btn', voiceEnabled ? 'btn-warning' : 'btn-success']">
				{{ voiceEnabled ? '关闭语音' : '开启语音' }}
			</button>
			
			<button @click="testVoice" class="btn btn-info" :disabled="!voiceEnabled">
				测试语音播报
			</button>
			
			<button @click="simulatePayment" class="btn btn-secondary" :disabled="connectionStatus !== 'connected'">
				模拟支付通知
			</button>
			
			<button @click="checkServerHealth" class="btn btn-outline">
				检查服务器状态
			</button>
		</view>

		<view class="stats-section">
			<view class="stats-title">服务器统计</view>
			<view class="stats-grid">
				<view class="stat-item">
					<text class="stat-value">{{ serverStats.total_connections || 0 }}</text>
					<text class="stat-label">总连接数</text>
				</view>
				<view class="stat-item">
					<text class="stat-value">{{ serverStats.current_connections || 0 }}</text>
					<text class="stat-label">当前连接</text>
				</view>
				<view class="stat-item">
					<text class="stat-value">{{ serverStats.total_messages || 0 }}</text>
					<text class="stat-label">总消息数</text>
				</view>
				<view class="stat-item">
					<text class="stat-value">{{ serverStats.payment_notifications || 0 }}</text>
					<text class="stat-label">支付通知</text>
				</view>
			</view>
		</view>

		<view class="log-section">
			<view class="log-header">
				<text class="log-title">实时日志</text>
				<button @click="clearLogs" class="btn-clear">清空</button>
			</view>
			<scroll-view class="log-container" scroll-y="true" :scroll-top="scrollTop">
				<view v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
					<text class="log-time">{{ log.time }}</text>
					<text class="log-message">{{ log.message }}</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// WebSocket配置
			wsUrl: 'ws://ceshi.huisas.com:8080',
			ws: null,
			connectionStatus: 'disconnected', // disconnected, connecting, connected, error
			
			// 语音配置
			voiceEnabled: false,
			
			// 统计数据
			serverStats: {},
			
			// 日志
			logs: [],
			scrollTop: 0,
			
			// 重连配置
			reconnectAttempts: 0,
			maxReconnectAttempts: 5,
			reconnectInterval: 3000,
			reconnectTimer: null,
			
			// 心跳配置
			heartbeatTimer: null,
			heartbeatInterval: 30000,
			
			// 用户信息
			userInfo: {
				uid: '1000', // 测试用户ID
				merchant_id: 'test_merchant'
			}
		}
	},
	
	onLoad() {
		this.addLog('info', '页面加载完成');
		this.checkServerHealth();
	},
	
	onUnload() {
		this.cleanup();
	},
	
	methods: {
		// 获取状态文本
		getStatusText() {
			const statusMap = {
				'disconnected': '未连接',
				'connecting': '连接中...',
				'connected': '已连接',
				'error': '连接错误'
			};
			return statusMap[this.connectionStatus] || '未知状态';
		},
		
		// 切换连接状态
		toggleConnection() {
			if (this.connectionStatus === 'connected') {
				this.disconnect();
			} else {
				this.connect();
			}
		},
		
		// 连接WebSocket
		connect() {
			if (this.connectionStatus === 'connecting' || this.connectionStatus === 'connected') {
				return;
			}
			
			this.connectionStatus = 'connecting';
			this.addLog('info', `正在连接到 ${this.wsUrl}...`);
			
			try {
				this.ws = new WebSocket(this.wsUrl);
				
				this.ws.onopen = this.onOpen;
				this.ws.onmessage = this.onMessage;
				this.ws.onclose = this.onClose;
				this.ws.onerror = this.onError;
				
			} catch (error) {
				this.addLog('error', `连接失败: ${error.message}`);
				this.connectionStatus = 'error';
			}
		},
		
		// 断开连接
		disconnect() {
			this.cleanup();
			this.connectionStatus = 'disconnected';
			this.addLog('info', 'WebSocket连接已断开');
		},
		
		// 清理资源
		cleanup() {
			// 清除定时器
			if (this.reconnectTimer) {
				clearTimeout(this.reconnectTimer);
				this.reconnectTimer = null;
			}
			
			if (this.heartbeatTimer) {
				clearInterval(this.heartbeatTimer);
				this.heartbeatTimer = null;
			}
			
			// 关闭WebSocket
			if (this.ws) {
				this.ws.onopen = null;
				this.ws.onmessage = null;
				this.ws.onclose = null;
				this.ws.onerror = null;
				
				if (this.ws.readyState === WebSocket.OPEN) {
					this.ws.close();
				}
				this.ws = null;
			}
			
			this.reconnectAttempts = 0;
		},
		
		// WebSocket事件处理
		onOpen() {
			this.connectionStatus = 'connected';
			this.reconnectAttempts = 0;
			this.addLog('success', 'WebSocket连接成功');
			
			// 发送认证信息
			this.authenticate();
			
			// 开始心跳
			this.startHeartbeat();
		},
		
		onMessage(event) {
			try {
				const data = JSON.parse(event.data);
				this.addLog('info', `收到消息: ${JSON.stringify(data)}`);
				
				// 处理不同类型的消息
				this.handleMessage(data);
				
			} catch (error) {
				this.addLog('error', `消息解析失败: ${error.message}`);
				this.addLog('warning', `原始消息: ${event.data}`);
			}
		},
		
		onClose(event) {
			this.connectionStatus = 'disconnected';
			this.addLog('warning', `连接关闭: code=${event.code}, reason=${event.reason}`);
			
			// 清除心跳
			if (this.heartbeatTimer) {
				clearInterval(this.heartbeatTimer);
				this.heartbeatTimer = null;
			}
			
			// 尝试重连
			this.attemptReconnect();
		},
		
		onError(error) {
			this.connectionStatus = 'error';
			this.addLog('error', `WebSocket错误: ${error.message || '未知错误'}`);
		},
		
		// 认证
		authenticate() {
			const authMessage = {
				type: 'auth',
				uid: this.userInfo.uid,
				merchant_id: this.userInfo.merchant_id,
				timestamp: Date.now()
			};
			
			this.sendMessage(authMessage);
			this.addLog('info', '发送认证信息');
		},
		
		// 发送消息
		sendMessage(message) {
			if (this.ws && this.ws.readyState === WebSocket.OPEN) {
				this.ws.send(JSON.stringify(message));
				return true;
			} else {
				this.addLog('error', 'WebSocket未连接，无法发送消息');
				return false;
			}
		},
		
		// 处理消息
		handleMessage(data) {
			switch (data.type) {
				case 'auth_success':
					this.addLog('success', '认证成功');
					this.subscribeToPayments();
					break;
					
				case 'auth_failed':
					this.addLog('error', `认证失败: ${data.message}`);
					break;
					
				case 'payment':
					this.handlePaymentNotification(data);
					break;
					
				case 'pong':
					this.addLog('info', '收到心跳响应');
					break;
					
				default:
					this.addLog('info', `未知消息类型: ${data.type}`);
			}
		},
		
		// 订阅支付通知
		subscribeToPayments() {
			const subscribeMessage = {
				type: 'subscribe',
				channel: 'payment',
				uid: this.userInfo.uid
			};
			
			this.sendMessage(subscribeMessage);
			this.addLog('info', '订阅支付通知');
		},
		
		// 处理支付通知
		handlePaymentNotification(data) {
			this.addLog('success', `收到支付通知: ${data.money}元`);
			
			// 语音播报
			if (this.voiceEnabled) {
				this.playVoiceNotification(data);
			}
		},
		
		// 语音播报
		playVoiceNotification(data) {
			try {
				const money = parseFloat(data.money || 0);
				const text = `收到${data.typename || ''}付款${money}元`;

				this.addLog('info', `语音播报: ${text}`);

				// 使用系统TTS
				if (typeof speechSynthesis !== 'undefined') {
					const utterance = new SpeechSynthesisUtterance(text);
					utterance.lang = 'zh-CN';
					utterance.rate = 1.0;
					utterance.pitch = 1.0;
					speechSynthesis.speak(utterance);
				} else {
					this.addLog('warning', '浏览器不支持语音合成');
				}

			} catch (error) {
				this.addLog('error', `语音播报失败: ${error.message}`);
			}
		},

		// 开始心跳
		startHeartbeat() {
			this.heartbeatTimer = setInterval(() => {
				if (this.ws && this.ws.readyState === WebSocket.OPEN) {
					this.sendMessage({ type: 'ping', timestamp: Date.now() });
				}
			}, this.heartbeatInterval);
		},

		// 尝试重连
		attemptReconnect() {
			if (this.reconnectAttempts >= this.maxReconnectAttempts) {
				this.addLog('error', '重连次数已达上限，停止重连');
				return;
			}

			this.reconnectAttempts++;
			this.addLog('info', `第${this.reconnectAttempts}次重连尝试...`);

			this.reconnectTimer = setTimeout(() => {
				this.connect();
			}, this.reconnectInterval);
		},

		// 切换语音
		toggleVoice() {
			this.voiceEnabled = !this.voiceEnabled;
			this.addLog('info', `语音播报${this.voiceEnabled ? '已开启' : '已关闭'}`);
		},

		// 测试语音
		testVoice() {
			if (!this.voiceEnabled) {
				this.addLog('warning', '请先开启语音播报');
				return;
			}

			const testData = {
				money: '0.01',
				typename: '支付宝'
			};

			this.playVoiceNotification(testData);
		},

		// 模拟支付通知
		simulatePayment() {
			if (this.connectionStatus !== 'connected') {
				this.addLog('warning', '请先连接WebSocket');
				return;
			}

			// 调用后端测试接口
			uni.request({
				url: 'http://ceshi.huisas.com/test_websocket_notify.php',
				method: 'GET',
				success: (res) => {
					this.addLog('success', '模拟支付通知发送成功');
				},
				fail: (err) => {
					this.addLog('error', `模拟支付通知失败: ${err.errMsg}`);
				}
			});
		},

		// 检查服务器健康状态
		checkServerHealth() {
			uni.request({
				url: 'http://ceshi.huisas.com:8080/health',
				method: 'GET',
				success: (res) => {
					if (res.data && res.data.status === 'healthy') {
						this.addLog('success', '服务器状态正常');
						this.getServerStats();
					} else {
						this.addLog('warning', '服务器状态异常');
					}
				},
				fail: (err) => {
					this.addLog('error', `服务器健康检查失败: ${err.errMsg}`);
				}
			});
		},

		// 获取服务器统计
		getServerStats() {
			uni.request({
				url: 'http://ceshi.huisas.com:8080/stats',
				method: 'GET',
				success: (res) => {
					if (res.data) {
						this.serverStats = res.data;
						this.addLog('info', '服务器统计数据已更新');
					}
				},
				fail: (err) => {
					this.addLog('error', `获取服务器统计失败: ${err.errMsg}`);
				}
			});
		},

		// 添加日志
		addLog(type, message) {
			const log = {
				type,
				message,
				time: new Date().toLocaleTimeString()
			};

			this.logs.push(log);

			// 限制日志数量
			if (this.logs.length > 100) {
				this.logs.shift();
			}

			// 自动滚动到底部
			this.$nextTick(() => {
				this.scrollTop = this.logs.length * 100;
			});
		},

		// 清空日志
		clearLogs() {
			this.logs = [];
			this.scrollTop = 0;
		}
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.subtitle {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.status-section {
	background: white;
	border-radius: 10rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.status-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.status-item:last-child {
	margin-bottom: 0;
}

.label {
	font-size: 28rpx;
	color: #333;
}

.status {
	font-size: 28rpx;
	font-weight: bold;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.status.connected {
	color: #52c41a;
	background-color: #f6ffed;
}

.status.disconnected {
	color: #ff4d4f;
	background-color: #fff2f0;
}

.status.connecting {
	color: #1890ff;
	background-color: #e6f7ff;
}

.status.error {
	color: #ff4d4f;
	background-color: #fff2f0;
}

.server-url {
	font-size: 24rpx;
	color: #666;
	font-family: monospace;
}

.control-section {
	background: white;
	border-radius: 10rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.btn {
	width: 100%;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	margin-bottom: 20rpx;
	border: none;
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
}

.btn:last-child {
	margin-bottom: 0;
}

.btn-primary {
	background-color: #1890ff;
}

.btn-danger {
	background-color: #ff4d4f;
}

.btn-success {
	background-color: #52c41a;
}

.btn-warning {
	background-color: #faad14;
}

.btn-info {
	background-color: #13c2c2;
}

.btn-secondary {
	background-color: #8c8c8c;
}

.btn-outline {
	background-color: transparent;
	border: 2rpx solid #d9d9d9;
	color: #666;
}

.btn:disabled {
	background-color: #f5f5f5;
	color: #bfbfbf;
	border-color: #d9d9d9;
}

.stats-section {
	background: white;
	border-radius: 10rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.stats-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
	text-align: center;
}

.stats-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.stat-item {
	text-align: center;
	padding: 20rpx;
	background-color: #fafafa;
	border-radius: 8rpx;
}

.stat-value {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #1890ff;
	margin-bottom: 10rpx;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666;
}

.log-section {
	background: white;
	border-radius: 10rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.log-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.log-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.btn-clear {
	padding: 10rpx 20rpx;
	background-color: #ff4d4f;
	color: white;
	border: none;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.log-container {
	height: 600rpx;
	border: 2rpx solid #f0f0f0;
	border-radius: 8rpx;
	padding: 20rpx;
	background-color: #fafafa;
}

.log-item {
	margin-bottom: 15rpx;
	padding: 15rpx;
	border-radius: 6rpx;
	border-left: 6rpx solid #d9d9d9;
}

.log-item.success {
	background-color: #f6ffed;
	border-left-color: #52c41a;
}

.log-item.error {
	background-color: #fff2f0;
	border-left-color: #ff4d4f;
}

.log-item.warning {
	background-color: #fffbe6;
	border-left-color: #faad14;
}

.log-item.info {
	background-color: #e6f7ff;
	border-left-color: #1890ff;
}

.log-time {
	font-size: 22rpx;
	color: #8c8c8c;
	display: block;
	margin-bottom: 8rpx;
}

.log-message {
	font-size: 26rpx;
	color: #333;
	word-break: break-all;
	line-height: 1.4;
}
</style>
