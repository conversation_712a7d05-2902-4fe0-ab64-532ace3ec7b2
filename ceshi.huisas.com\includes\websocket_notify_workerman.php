<?php
/**
 * WebSocket通知集成模块 (Swoole版本)
 * 用于在支付回调中发送WebSocket通知到Swoole服务器
 *
 * 注意：虽然文件名包含workerman，但实际已适配Swoole服务
 */

class WebSocketNotifyWorkerman {
    private $config;
    private $apiUrl;
    private $timeout;
    private $retryTimes;
    
    public function __construct() {
        $this->config = [
            'api_host' => 'ceshi.huisas.com',  // 服务器地址
            'api_port' => 8080,  // Swoole WebSocket服务端口
            'timeout' => 3,
            'retry_times' => 2,
            'log_file' => __DIR__ . '/../logs/websocket_integration.log',
            'use_swoole' => true  // 使用Swoole服务器
        ];

        $this->apiUrl = "http://{$this->config['api_host']}:{$this->config['api_port']}/payment/notify";
        $this->timeout = $this->config['timeout'];
        $this->retryTimes = $this->config['retry_times'];
    }
    
    /**
     * 发送支付成功通知
     * 
     * @param array $orderData 订单数据
     * @return bool 发送是否成功
     */
    public function sendPaymentNotification($orderData) {
        try {
            // 验证必要字段
            if (!$this->validateOrderData($orderData)) {
                $this->log("订单数据验证失败: " . json_encode($orderData, JSON_UNESCAPED_UNICODE));
                return false;
            }
            
            // 构建通知数据
            $notificationData = $this->buildNotificationData($orderData);
            
            // 发送通知
            $result = $this->sendNotification($notificationData);
            
            if ($result['success']) {
                $this->log("支付通知发送成功: 订单号={$orderData['trade_no']}, 金额={$orderData['money']}元, 广播客户端={$result['broadcast_count']}个");
                return true;
            } else {
                $this->log("支付通知发送失败: " . $result['error']);
                return false;
            }
            
        } catch (Exception $e) {
            $this->log("发送支付通知异常: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 验证订单数据
     */
    private function validateOrderData($orderData) {
        $requiredFields = ['trade_no', 'money'];
        
        foreach ($requiredFields as $field) {
            if (!isset($orderData[$field]) || empty($orderData[$field])) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 构建通知数据
     */
    private function buildNotificationData($orderData) {
        $merchantId = $orderData['uid'] ?? $orderData['user_id'] ?? '';

        // 构建语音播报文本
        $payType = $this->getPayTypeName($orderData['type'] ?? 'unknown');
        $amount = $orderData['money'] ?? '0.00';
        $voiceText = "{$payType}收款{$amount}元";

        // 🔒 适配Swoole服务器期望的数据格式
        return [
            // Swoole服务器必需字段
            'merchant_id' => $merchantId,
            'order_id' => $orderData['trade_no'] ?? '',
            'amount' => $amount,
            'status' => $orderData['status'] ?? 'success',

            // 扩展数据
            'extra_data' => [
                'trade_no' => $orderData['trade_no'] ?? '',
                'uid' => $merchantId,
                'money' => $amount,
                'type' => $orderData['type'] ?? $orderData['pay_type'] ?? 'unknown',
                'typename' => $orderData['typename'] ?? $payType,
                'addtime' => $orderData['addtime'] ?? date('Y-m-d H:i:s'),
                'api_trade_no' => $orderData['api_trade_no'] ?? $orderData['out_trade_no'] ?? '',
                'buyer' => $orderData['buyer'] ?? $orderData['buyer_email'] ?? '',
                'notify_time' => date('Y-m-d H:i:s'),
                'voice_text' => $voiceText,  // 语音播报文本
                'source' => 'epay_system',
                'event' => 'payment_success'
            ]
        ];
    }

    /**
     * 获取支付类型名称
     */
    private function getPayTypeName($type) {
        $typeMap = [
            '1' => '支付宝',
            '2' => '微信',
            '3' => '银联',
            '4' => 'QQ钱包',
            'alipay' => '支付宝',
            'wxpay' => '微信',
            'unionpay' => '银联',
            'qqpay' => 'QQ钱包'
        ];

        return $typeMap[$type] ?? '在线';
    }
    
    /**
     * 发送通知到WebSocket服务
     */
    private function sendNotification($data) {
        $attempts = 0;
        $lastError = '';
        
        while ($attempts < $this->retryTimes) {
            $attempts++;
            
            try {
                $result = $this->makeHttpRequest($data);
                
                if ($result !== false) {
                    $response = json_decode($result, true);

                    // 🔧 适配Swoole服务器的响应格式
                    if ($response && $response['success'] === true) {
                        return [
                            'success' => true,
                            'broadcast_count' => $response['broadcast_count'] ?? 0,
                            'response' => $response
                        ];
                    } else {
                        $lastError = $response['message'] ?? 'Unknown error';
                    }
                } else {
                    $lastError = 'HTTP request failed';
                }
                
            } catch (Exception $e) {
                $lastError = $e->getMessage();
            }
            
            // 如果不是最后一次尝试，等待一下再重试
            if ($attempts < $this->retryTimes) {
                usleep(500000); // 0.5秒
            }
        }
        
        return [
            'success' => false,
            'error' => $lastError,
            'attempts' => $attempts
        ];
    }
    
    /**
     * 发送HTTP请求
     */
    private function makeHttpRequest($data) {
        // 方法1: 使用cURL (推荐)
        if (function_exists('curl_init')) {
            return $this->sendWithCurl($data);
        }
        
        // 方法2: 使用file_get_contents
        return $this->sendWithFileGetContents($data);
    }
    
    /**
     * 使用cURL发送请求
     */
    private function sendWithCurl($data) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->apiUrl,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_CONNECTTIMEOUT => 2,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'User-Agent: EPay-WebSocket-Client/1.0'
            ],
            CURLOPT_FOLLOWLOCATION => false,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($result === false || !empty($error)) {
            throw new Exception("cURL error: " . $error);
        }
        
        if ($httpCode !== 200) {
            throw new Exception("HTTP error: " . $httpCode);
        }
        
        return $result;
    }
    
    /**
     * 使用file_get_contents发送请求
     */
    private function sendWithFileGetContents($data) {
        $postData = json_encode($data);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($postData),
                    'User-Agent: EPay-WebSocket-Client/1.0'
                ],
                'content' => $postData,
                'timeout' => $this->timeout
            ]
        ]);
        
        $result = @file_get_contents($this->apiUrl, false, $context);
        
        if ($result === false) {
            throw new Exception("file_get_contents failed");
        }
        
        return $result;
    }
    
    /**
     * 检查WebSocket服务状态
     */
    public function checkServiceStatus() {
        try {
            $statusUrl = "http://{$this->config['api_host']}:{$this->config['api_port']}/health";

            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'timeout' => 2
                ]
            ]);

            $result = @file_get_contents($statusUrl, false, $context);

            if ($result !== false) {
                $data = json_decode($result, true);
                return $data && isset($data['status']) && $data['status'] === 'healthy';
            }

            return false;

        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取服务统计信息
     */
    public function getServiceStats() {
        try {
            $statsUrl = "http://{$this->config['api_host']}:{$this->config['api_port']}/stats";

            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'timeout' => 2
                ]
            ]);

            $result = @file_get_contents($statsUrl, false, $context);

            if ($result !== false) {
                return json_decode($result, true);
            }

            return null;

        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * 发送测试通知
     */
    public function sendTestNotification() {
        $testData = [
            'trade_no' => 'TEST_' . time(),
            'uid' => '1',
            'money' => '88.88',
            'type' => 'test',
            'addtime' => date('Y-m-d H:i:s'),
            'api_trade_no' => 'TEST_API_' . time(),
            'buyer' => '<EMAIL>',
            'status' => 'success'
        ];
        
        return $this->sendPaymentNotification($testData);
    }
    
    /**
     * 记录日志
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [WebSocket-Integration] {$message}\n";
        
        // 确保日志目录存在
        $logDir = dirname($this->config['log_file']);
        if (!is_dir($logDir)) {
            @mkdir($logDir, 0755, true);
        }
        
        // 写入日志文件
        @file_put_contents($this->config['log_file'], $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 设置配置
     */
    public function setConfig($key, $value) {
        $this->config[$key] = $value;
        
        // 更新相关属性
        if ($key === 'api_host' || $key === 'api_port') {
            $this->apiUrl = "http://{$this->config['api_host']}/websocket_api.php/notify";
        }
    }
    
    /**
     * 获取配置
     */
    public function getConfig($key = null) {
        if ($key === null) {
            return $this->config;
        }
        
        return $this->config[$key] ?? null;
    }
}

/**
 * 全局函数：发送WebSocket支付通知
 * 
 * @param array $orderData 订单数据
 * @return bool 发送是否成功
 */
function sendWebSocketPaymentNotification($orderData) {
    static $notifier = null;
    
    if ($notifier === null) {
        $notifier = new WebSocketNotifyWorkerman();
    }
    
    return $notifier->sendPaymentNotification($orderData);
}

/**
 * 全局函数：检查WebSocket服务状态
 * 
 * @return bool 服务是否正常
 */
function checkWebSocketServiceStatus() {
    static $notifier = null;
    
    if ($notifier === null) {
        $notifier = new WebSocketNotifyWorkerman();
    }
    
    return $notifier->checkServiceStatus();
}

/**
 * 全局函数：发送测试WebSocket通知
 * 
 * @return bool 发送是否成功
 */
function sendTestWebSocketNotification() {
    static $notifier = null;
    
    if ($notifier === null) {
        $notifier = new WebSocketNotifyWorkerman();
    }
    
    return $notifier->sendTestNotification();
}
