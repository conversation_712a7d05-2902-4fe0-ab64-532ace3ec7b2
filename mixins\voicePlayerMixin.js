/**
 * 语音播报 Mixin - 基于CSDN文章实现
 * 参考: https://blog.csdn.net/m0_59256433/article/details/131451435
 * 在每个页面都注入websocket，切换页面之前，关闭socket，然后重新连接一下socket
 */
import voicePlayer from '@/utils/voicePlayer.js';

export const voicePlayerMixin = {
    data() {
        return {
            // WebSocket相关
            socketTask: null,
            isSocketConnected: false,
            socketUrl: '',
            
            // 语音播报相关
            voiceEnabled: true,
            voiceVolume: 1.0,
            
            // 统计信息
            receivedCount: 0,
            playedCount: 0,
            
            // WebSocket配置
            wsConfig: {
                host: 'ceshi.huisas.com',
                port: 8080,
                app_key: 'payment_websocket_2024',
                heartbeatInterval: 10000, // 10秒心跳
                reconnectInterval: 5000   // 5秒重连
            }
        };
    },

    onShow() {
        // 🔧 禁用页面级WebSocket连接，使用全局统一连接
        // setTimeout(() => {
        //     this.connectWebSocket();
        // }, 500);
        console.log('📱 页面显示 - 使用全局WebSocket连接，跳过页面级重复连接');
    },

    // 关闭websocket【必须在实例销毁之前关闭,否则会是undefined错误】
    beforeDestroy() {
        // 关闭webSocket
        this.disconnectWebSocket();
    },

    onHide() {
        // 🔧 优化：使用全局WebSocket服务，页面隐藏时无需处理连接
        // 页面隐藏时保持WebSocket连接，继续接收通知
        console.log('📱 页面隐藏 - 全局WebSocket服务继续运行');
    },

    methods: {
        /**
         * 连接WebSocket
         * 🔧 临时恢复：确保支付功能正常
         */
        connectWebSocket() {
            if (this.isSocketConnected || this.socketTask) {
                console.log('WebSocket已连接或正在连接');
                return;
            }

            this.socketUrl = `ws://${this.wsConfig.host}:${this.wsConfig.port}`;

            console.log('正在连接WebSocket:', this.socketUrl);

            this.socketTask = uni.connectSocket({
                url: this.socketUrl,
                success: () => {
                    console.log('WebSocket连接请求成功');
                },
                fail: (err) => {
                    console.error('WebSocket连接失败:', err);
                    this.handleWebSocketError(err);
                }
            });

            // 连接打开
            this.socketTask.onOpen(() => {
                console.log('WebSocket连接已建立');
                this.isSocketConnected = true;
                this.startHeartbeat();
                this.onWebSocketConnected();
            });

            // 监听消息
            this.socketTask.onMessage((res) => {
                this.handleWebSocketMessage(res);
            });

            // 连接错误
            this.socketTask.onError((err) => {
                console.error('WebSocket连接错误:', err);
                this.handleWebSocketError(err);
            });

            // 连接关闭
            this.socketTask.onClose(() => {
                console.log('WebSocket连接已关闭');
                this.isSocketConnected = false;
                this.stopHeartbeat();
                this.onWebSocketDisconnected();
            });
        },

        /**
         * 断开WebSocket连接
         * 🔧 临时恢复：确保支付功能正常
         */
        disconnectWebSocket() {
            if (this.socketTask) {
                this.socketTask.close();
                this.socketTask = null;
            }
            this.isSocketConnected = false;
            this.stopHeartbeat();
            console.log('WebSocket连接已断开');
        },

        /**
         * 发送WebSocket消息
         * @param {Object} message 消息对象
         */
        sendWebSocketMessage(message) {
            if (!this.isSocketConnected || !this.socketTask) {
                console.warn('WebSocket未连接，无法发送消息');
                return false;
            }

            try {
                this.socketTask.send({
                    data: JSON.stringify(message)
                });
                console.log('发送WebSocket消息:', message);
                return true;
            } catch (error) {
                console.error('发送WebSocket消息失败:', error);
                return false;
            }
        },

        /**
         * 启动心跳
         */
        startHeartbeat() {
            this.stopHeartbeat();
            this.heartbeatTimer = setInterval(() => {
                if (this.isSocketConnected) {
                    this.sendWebSocketMessage({ type: 'ping', timestamp: Date.now() });
                }
            }, this.wsConfig.heartbeatInterval);
        },

        /**
         * 停止心跳
         */
        stopHeartbeat() {
            if (this.heartbeatTimer) {
                clearInterval(this.heartbeatTimer);
                this.heartbeatTimer = null;
            }
        },

        /**
         * 处理WebSocket消息
         * @param {Object} res 消息响应
         */
        handleWebSocketMessage(res) {
            try {
                const data = JSON.parse(res.data);
                console.log('收到WebSocket消息:', data);
                
                this.receivedCount++;

                // 处理心跳响应
                if (data.type === 'pong') {
                    console.log('收到心跳响应');
                    return;
                }

                // 处理支付通知
                if (data.event === 'payment_success' && data.amount) {
                    this.handlePaymentNotification(data);
                }

                // 调用页面自定义处理方法
                if (typeof this.onWebSocketMessage === 'function') {
                    this.onWebSocketMessage(data);
                }

            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
            }
        },

        /**
         * 处理支付通知
         * @param {Object} data 支付数据
         */
        handlePaymentNotification(data) {
            if (!this.voiceEnabled) {
                console.log('语音播报已禁用');
                return;
            }

            const amount = data.amount;
            console.log(`收到支付通知: ${amount}元`);

            // 使用语音播报器播放
            voicePlayer.playPaymentVoice(amount).then(() => {
                this.playedCount++;
                console.log(`语音播报完成: ${amount}元`);
                
                // 播放完成后的震动反馈
                uni.vibrateShort({
                    type: 'light'
                });

                // 调用页面自定义处理方法
                if (typeof this.onPaymentVoicePlayed === 'function') {
                    this.onPaymentVoicePlayed(data);
                }
                
            }).catch((error) => {
                console.error(`语音播报失败: ${amount}元`, error);
            });

            // 调用页面自定义处理方法
            if (typeof this.onPaymentReceived === 'function') {
                this.onPaymentReceived(data);
            }
        },

        /**
         * 处理WebSocket错误
         * @param {Object} error 错误对象
         */
        handleWebSocketError(error) {
            console.error('WebSocket错误:', error);
            this.isSocketConnected = false;
            
            // 调用页面自定义处理方法
            if (typeof this.onWebSocketError === 'function') {
                this.onWebSocketError(error);
            }
        },

        /**
         * 切换语音播报开关
         */
        toggleVoiceEnabled() {
            this.voiceEnabled = !this.voiceEnabled;
            this.saveVoiceSettings();
            
            uni.showToast({
                title: this.voiceEnabled ? '语音播报已开启' : '语音播报已关闭',
                icon: this.voiceEnabled ? 'success' : 'none',
                duration: 2000
            });
        },

        /**
         * 设置语音音量
         * @param {number} volume 音量 (0-1)
         */
        setVoiceVolume(volume) {
            this.voiceVolume = Math.max(0, Math.min(1, volume));
            this.saveVoiceSettings();
        },

        /**
         * 保存语音设置
         */
        saveVoiceSettings() {
            const settings = {
                voiceEnabled: this.voiceEnabled,
                voiceVolume: this.voiceVolume
            };
            uni.setStorageSync('voiceSettings', settings);
        },

        /**
         * 加载语音设置
         */
        loadVoiceSettings() {
            try {
                const settings = uni.getStorageSync('voiceSettings');
                if (settings) {
                    this.voiceEnabled = settings.voiceEnabled !== false;
                    this.voiceVolume = settings.voiceVolume || 1.0;
                }
            } catch (error) {
                console.error('加载语音设置失败:', error);
            }
        },

        /**
         * 测试语音播报
         * @param {string|number} amount 测试金额
         */
        testVoiceNotification(amount = '123.45') {
            console.log(`测试语音播报: ${amount}元`);
            voicePlayer.playPaymentVoice(amount).then(() => {
                console.log('测试语音播报完成');
                uni.showToast({
                    title: '测试播报完成',
                    icon: 'success'
                });
            }).catch((error) => {
                console.error('测试语音播报失败:', error);
                uni.showToast({
                    title: '测试播报失败',
                    icon: 'none'
                });
            });
        },

        /**
         * 获取WebSocket状态
         */
        getWebSocketStatus() {
            return {
                connected: this.isSocketConnected,
                url: this.socketUrl,
                receivedCount: this.receivedCount,
                playedCount: this.playedCount,
                voiceEnabled: this.voiceEnabled,
                queueStatus: voicePlayer.getQueueStatus()
            };
        },

        // 以下方法可在页面中重写以自定义处理逻辑

        /**
         * WebSocket连接成功回调 - 可在页面中重写
         */
        onWebSocketConnected() {
            console.log('WebSocket连接成功 - 可在页面中重写此方法');
        },

        /**
         * WebSocket连接断开回调 - 可在页面中重写
         */
        onWebSocketDisconnected() {
            console.log('WebSocket连接断开 - 可在页面中重写此方法');
        },

        /**
         * 收到WebSocket消息回调 - 可在页面中重写
         * @param {Object} data 消息数据
         */
        onWebSocketMessage(data) {
            console.log('收到WebSocket消息 - 可在页面中重写此方法:', data);
        },

        /**
         * 收到支付通知回调 - 可在页面中重写
         * @param {Object} data 支付数据
         */
        onPaymentReceived(data) {
            console.log('收到支付通知 - 可在页面中重写此方法:', data);
        },

        /**
         * 语音播报完成回调 - 可在页面中重写
         * @param {Object} data 支付数据
         */
        onPaymentVoicePlayed(data) {
            console.log('语音播报完成 - 可在页面中重写此方法:', data);
        }
    },

    created() {
        // 加载语音设置
        this.loadVoiceSettings();
    }
};
