<?php
/**
 * Swoole WebSocket 支付通知服务器
 * 基于Swoole官方标准实现，替换现有的Workerman WebSocket服务
 *
 * 功能特性：
 * 1. 🚀 高性能协程WebSocket服务
 * 2. 🔐 商户权限隔离
 * 3. 📺 频道订阅管理
 * 4. 💓 心跳检测机制
 * 5. 🎵 支付语音播报支持
 * 6. 🌐 HTTP API支持（接收支付回调）
 */

// 检查Swoole扩展
if (!extension_loaded('swoole')) {
    die("❌ Swoole扩展未安装，请先安装Swoole扩展\n");
}

// 检查Swoole版本
if (version_compare(swoole_version(), '4.4.0', '<')) {
    die("❌ Swoole版本过低，需要4.4.0+，当前版本：" . swoole_version() . "\n");
}

require_once __DIR__ . '/config.php';

class PaymentWebSocketServer
{
    private $server;
    private $config;
    private $connections = [];           // 所有连接 [fd => connection_info]
    private $merchants = [];             // 商户连接映射 [merchant_id => [fd1, fd2, ...]]
    private $channels = [];              // 频道订阅 [channel => [fd1, fd2, ...]]
    private $stats = [
        'start_time' => 0,
        'total_connections' => 0,
        'current_connections' => 0,
        'total_messages' => 0,
        'payment_notifications' => 0
    ];

    public function __construct($config = [])
    {
        $this->config = array_merge([
            'host' => '0.0.0.0',
            'port' => 8080,
            'worker_num' => 2,
            'heartbeat_interval' => 30,
            'app_key' => 'payment_websocket_2024',
            'max_connections' => 1000
        ], $config);

        $this->initServer();
        $this->bindEvents();
    }

    /**
     * 初始化Swoole WebSocket服务器
     * 基于官方推荐配置
     */
    private function initServer()
    {
        // 创建WebSocket服务器（继承自HTTP服务器）
        $this->server = new Swoole\WebSocket\Server($this->config['host'], $this->config['port']);

        // 设置服务器参数
        $this->server->set([
            'worker_num' => $this->config['worker_num'],
            'heartbeat_check_interval' => $this->config['heartbeat_interval'],
            'heartbeat_idle_time' => $this->config['heartbeat_interval'] * 2,
            'max_connection' => $this->config['max_connections'],
            'enable_coroutine' => true,
            'log_file' => __DIR__ . '/logs/swoole_websocket.log',
            'log_level' => SWOOLE_LOG_INFO,
            'daemonize' => true, // 启用守护进程模式，防止进程意外退出
            'pid_file' => __DIR__ . '/logs/swoole_websocket.pid',
            // 增加稳定性配置
            'reload_async' => true,
            'max_wait_time' => 60,
            'enable_reuse_port' => true,
            // 错误处理
            'log_rotation' => SWOOLE_LOG_ROTATION_DAILY,
            'log_date_format' => '%Y-%m-%d %H:%M:%S',
        ]);
    }

    /**
     * 绑定事件处理器
     * 基于Swoole官方事件模型
     */
    private function bindEvents()
    {
        // WebSocket事件
        $this->server->on('start', [$this, 'onStart']);
        $this->server->on('workerStart', [$this, 'onWorkerStart']);
        $this->server->on('open', [$this, 'onOpen']);
        $this->server->on('message', [$this, 'onMessage']);
        $this->server->on('close', [$this, 'onClose']);

        // HTTP事件（用于接收支付回调）
        $this->server->on('request', [$this, 'onRequest']);
    }

    /**
     * 服务器启动事件
     */
    public function onStart($server)
    {
        echo "🚀 Swoole WebSocket支付服务器启动成功\n";
        echo "==========================================\n";
        echo "Swoole版本: " . swoole_version() . "\n";
        echo "PHP版本: " . PHP_VERSION . "\n";
        echo "监听地址: ws://{$this->config['host']}:{$this->config['port']}\n";
        echo "HTTP API: http://{$this->config['host']}:{$this->config['port']}\n";
        echo "工作进程: {$this->config['worker_num']}\n";
        echo "心跳间隔: {$this->config['heartbeat_interval']}秒\n";
        echo "最大连接: {$this->config['max_connections']}\n";
        echo "App Key: {$this->config['app_key']}\n";
        echo "==========================================\n";

        $this->stats['start_time'] = time();

        // 保存主进程PID
        file_put_contents(__DIR__ . '/logs/swoole_websocket.pid', $server->master_pid);
    }

    /**
     * Worker进程启动事件
     */
    public function onWorkerStart($server, $workerId)
    {
        echo "Worker进程 #{$workerId} 启动成功 (PID: " . posix_getpid() . ")\n";

        // 仅在第一个Worker进程中设置定时器
        if ($workerId === 0) {
            // 统计信息定时器（每分钟）
            Swoole\Timer::tick(60000, function() {
                $this->printStats();
            });

            // 清理过期连接定时器（每30秒）
            Swoole\Timer::tick(30000, function() {
                $this->cleanupConnections();
            });

            // 心跳检测定时器（每15秒）
            Swoole\Timer::tick(15000, function() {
                $this->sendHeartbeat();
            });
        }
    }

    /**
     * 客户端连接事件
     */
    public function onOpen($server, $request)
    {
        $fd = $request->fd;

        // 创建连接信息
        $clientInfo = [
            'fd' => $fd,
            'connect_time' => time(),
            'last_heartbeat' => time(),
            'authenticated' => false,
            'merchant_id' => null,
            'staff_id' => null,
            'subscribed_channels' => [],
            'ip' => $request->server['remote_addr'] ?? 'unknown',
            'user_agent' => $request->header['user-agent'] ?? 'unknown'
        ];

        $this->connections[$fd] = $clientInfo;
        $this->stats['total_connections']++;
        $this->stats['current_connections'] = count($this->connections);

        echo "✅ 新客户端连接: FD#{$fd} (IP: {$clientInfo['ip']})\n";

        // 发送欢迎消息
        $this->sendMessage($fd, [
            'type' => 'welcome',
            'data' => [
                'connection_id' => $fd,
                'server_time' => date('Y-m-d H:i:s'),
                'app_key' => $this->config['app_key'],
                'message' => '连接成功，请进行认证'
            ]
        ]);
    }

    /**
     * 处理客户端消息
     */
    public function onMessage($server, $frame)
    {
        $fd = $frame->fd;

        // 解析消息
        $message = json_decode($frame->data, true);
        if (!$message || !isset($message['type'])) {
            $this->sendError($fd, '消息格式错误，请发送JSON格式数据');
            return;
        }

        $this->stats['total_messages']++;
        $this->connections[$fd]['last_heartbeat'] = time();

        echo "📨 收到消息: FD#{$fd} -> {$message['type']}\n";

        // 路由消息处理
        switch ($message['type']) {
            case 'auth':
                $this->handleAuth($fd, $message);
                break;
            case 'subscribe':
                $this->handleSubscribe($fd, $message);
                break;
            case 'unsubscribe':
                $this->handleUnsubscribe($fd, $message);
                break;
            case 'ping':
                $this->handlePing($fd, $message);
                break;
            case 'heartbeat_response':
                $this->handleHeartbeatResponse($fd, $message);
                break;
            default:
                $this->sendError($fd, '未知消息类型: ' . $message['type']);
        }
    }

    /**
     * 处理客户端认证
     */
    private function handleAuth($fd, $message)
    {
        $merchantId = $message['data']['merchant_id'] ?? '';
        $staffId = $message['data']['staff_id'] ?? null;
        $token = $message['data']['token'] ?? '';

        // 验证必要参数
        if (empty($merchantId) || empty($token)) {
            $this->sendMessage($fd, [
                'type' => 'auth_result',
                'data' => [
                    'success' => false,
                    'message' => '认证失败：缺少merchant_id或token'
                ]
            ]);
            return;
        }

        // TODO: 这里应该验证token的有效性（查询数据库）
        // 简单验证：token长度大于10
        if (strlen($token) < 10) {
            $this->sendMessage($fd, [
                'type' => 'auth_result',
                'data' => [
                    'success' => false,
                    'message' => '认证失败：token无效'
                ]
            ]);
            return;
        }

        // 更新连接信息
        $this->connections[$fd]['authenticated'] = true;
        $this->connections[$fd]['merchant_id'] = $merchantId;
        $this->connections[$fd]['staff_id'] = $staffId;
        $this->connections[$fd]['token'] = $token;

        // 添加到商户连接映射
        if (!isset($this->merchants[$merchantId])) {
            $this->merchants[$merchantId] = [];
        }
        $this->merchants[$merchantId][$fd] = true;

        // 发送认证成功响应
        $this->sendMessage($fd, [
            'type' => 'auth_result',
            'data' => [
                'success' => true,
                'message' => '认证成功',
                'merchant_id' => $merchantId,
                'staff_id' => $staffId,
                'connection_id' => $fd
            ]
        ]);

        echo "🔐 客户端认证成功: FD#{$fd} (商户: {$merchantId}" . ($staffId ? ", 员工: {$staffId}" : "") . ")\n";
    }

    /**
     * 处理频道订阅
     */
    private function handleSubscribe($fd, $message)
    {
        if (!$this->connections[$fd]['authenticated']) {
            $this->sendError($fd, '请先进行认证');
            return;
        }

        $channel = $message['data']['channel'] ?? '';
        if (empty($channel)) {
            $this->sendError($fd, '频道名称不能为空');
            return;
        }

        // 验证频道权限
        if (!$this->validateChannelAccess($fd, $channel)) {
            $this->sendError($fd, '没有权限订阅该频道: ' . $channel);
            return;
        }

        // 检查是否已经订阅
        if (in_array($channel, $this->connections[$fd]['subscribed_channels'])) {
            $this->sendMessage($fd, [
                'type' => 'subscription_succeeded',
                'data' => [
                    'channel' => $channel,
                    'message' => '已经订阅该频道'
                ]
            ]);
            return;
        }

        // 添加订阅
        $this->connections[$fd]['subscribed_channels'][] = $channel;

        if (!isset($this->channels[$channel])) {
            $this->channels[$channel] = [];
        }
        $this->channels[$channel][$fd] = true;

        $this->sendMessage($fd, [
            'type' => 'subscription_succeeded',
            'data' => [
                'channel' => $channel,
                'message' => '订阅成功'
            ]
        ]);

        echo "📺 客户端订阅频道: FD#{$fd} -> {$channel}\n";
    }

    /**
     * 处理取消订阅
     */
    private function handleUnsubscribe($fd, $message)
    {
        $channel = $message['data']['channel'] ?? '';
        if (empty($channel)) {
            $this->sendError($fd, '频道名称不能为空');
            return;
        }

        // 从连接的订阅列表中移除
        $channels = &$this->connections[$fd]['subscribed_channels'];
        $key = array_search($channel, $channels);
        if ($key !== false) {
            unset($channels[$key]);
            $channels = array_values($channels); // 重新索引
        }

        // 从频道订阅者列表中移除
        if (isset($this->channels[$channel][$fd])) {
            unset($this->channels[$channel][$fd]);
        }

        $this->sendMessage($fd, [
            'type' => 'unsubscription_succeeded',
            'data' => [
                'channel' => $channel,
                'message' => '取消订阅成功'
            ]
        ]);

        echo "📺 客户端取消订阅: FD#{$fd} -> {$channel}\n";
    }

    /**
     * 验证频道访问权限
     */
    private function validateChannelAccess($fd, $channel)
    {
        $connection = $this->connections[$fd];
        $merchantId = $connection['merchant_id'];
        $staffId = $connection['staff_id'];

        // 商户支付频道：merchant_{merchant_id}_payment
        if ($channel === "merchant_{$merchantId}_payment") {
            return true;
        }

        // 商户系统频道：merchant_{merchant_id}_system
        if ($channel === "merchant_{$merchantId}_system") {
            return true;
        }

        // 员工频道：staff_{staff_id}_notifications
        if ($staffId && $channel === "staff_{$staffId}_notifications") {
            return true;
        }

        // 全局公告频道：所有认证用户都可以订阅
        if ($channel === 'global_announcements') {
            return true;
        }

        return false;
    }

    /**
     * 处理心跳
     */
    private function handlePing($fd, $message)
    {
        $this->connections[$fd]['last_heartbeat'] = time();

        $this->sendMessage($fd, [
            'type' => 'pong',
            'data' => [
                'timestamp' => time(),
                'server_time' => date('Y-m-d H:i:s'),
                'connection_id' => $fd
            ]
        ]);
    }

    /**
     * 处理客户端心跳响应
     */
    private function handleHeartbeatResponse($fd, $message)
    {
        $this->connections[$fd]['last_heartbeat'] = time();
        echo "💓 收到心跳响应: FD#{$fd}\n";

        // 可以在这里记录客户端的响应时间等统计信息
        if (isset($message['data']['timestamp'])) {
            $latency = time() - $message['data']['timestamp'];
            echo "📊 心跳延迟: {$latency}秒\n";
        }
    }

    /**
     * 客户端断开连接事件
     */
    public function onClose($server, $fd)
    {
        if (!isset($this->connections[$fd])) {
            return;
        }

        $connection = $this->connections[$fd];

        // 从商户映射中移除
        if ($connection['merchant_id'] && isset($this->merchants[$connection['merchant_id']][$fd])) {
            unset($this->merchants[$connection['merchant_id']][$fd]);

            // 如果商户没有连接了，清理商户映射
            if (empty($this->merchants[$connection['merchant_id']])) {
                unset($this->merchants[$connection['merchant_id']]);
            }
        }

        // 从频道订阅中移除
        foreach ($connection['subscribed_channels'] as $channel) {
            if (isset($this->channels[$channel][$fd])) {
                unset($this->channels[$channel][$fd]);

                // 如果频道没有订阅者了，清理频道
                if (empty($this->channels[$channel])) {
                    unset($this->channels[$channel]);
                }
            }
        }

        // 移除连接记录
        unset($this->connections[$fd]);
        $this->stats['current_connections'] = count($this->connections);

        $duration = time() - $connection['connect_time'];
        echo "❌ 客户端断开: FD#{$fd} (连接时长: {$duration}秒)\n";
    }

    /**
     * 处理HTTP请求（用于接收支付回调）
     */
    public function onRequest($request, $response)
    {
        // 设置CORS头
        $response->header('Content-Type', 'application/json; charset=utf-8');
        $response->header('Access-Control-Allow-Origin', '*');
        $response->header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
        $response->header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        // 处理OPTIONS预检请求
        if ($request->server['request_method'] === 'OPTIONS') {
            $response->status(200);
            $response->end();
            return;
        }

        $uri = $request->server['request_uri'];
        echo "🌐 HTTP请求: {$request->server['request_method']} {$uri}\n";

        // 路由处理
        switch ($uri) {
            case '/payment/notify':
                $this->handlePaymentNotify($request, $response);
                break;
            case '/stats':
                $this->handleStatsRequest($request, $response);
                break;
            case '/health':
                $this->handleHealthCheck($request, $response);
                break;
            default:
                $response->status(404);
                $response->end(json_encode([
                    'error' => 'Not Found',
                    'message' => '接口不存在'
                ], JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 处理支付通知
     */
    private function handlePaymentNotify($request, $response)
    {
        // 获取POST数据
        $rawData = $request->getContent();
        $data = json_decode($rawData, true);

        if (!$data) {
            $response->status(400);
            $response->end(json_encode([
                'success' => false,
                'error' => '数据格式错误'
            ], JSON_UNESCAPED_UNICODE));
            return;
        }

        // 验证必要字段
        $merchantId = $data['merchant_id'] ?? '';
        $orderId = $data['order_id'] ?? '';
        $amount = $data['amount'] ?? 0;

        if (empty($merchantId) || empty($orderId)) {
            $response->status(400);
            $response->end(json_encode([
                'success' => false,
                'error' => '缺少必要参数: merchant_id, order_id'
            ], JSON_UNESCAPED_UNICODE));
            return;
        }

        // 构造支付通知消息
        $paymentMessage = [
            'type' => 'payment_notification',
            'data' => [
                'merchant_id' => $merchantId,
                'order_id' => $orderId,
                'amount' => $amount,
                'status' => $data['status'] ?? 'success',
                'timestamp' => time(),
                'voice_text' => "收到{$amount}元支付",
                'extra_data' => $data['extra_data'] ?? []
            ]
        ];

        // 广播到商户支付频道
        $channel = "merchant_{$merchantId}_payment";
        $broadcastCount = $this->broadcastToChannel($channel, $paymentMessage);

        // 如果有员工ID，也发送到员工频道
        if (!empty($data['staff_id'])) {
            $staffChannel = "staff_{$data['staff_id']}_notifications";
            $staffBroadcastCount = $this->broadcastToChannel($staffChannel, $paymentMessage);
            $broadcastCount += $staffBroadcastCount;
        }

        $this->stats['payment_notifications']++;

        echo "💰 支付通知广播: 商户{$merchantId}, 订单{$orderId}, 金额{$amount}, 接收者{$broadcastCount}个\n";

        $response->status(200);
        $response->end(json_encode([
            'success' => true,
            'message' => '通知发送成功',
            'broadcast_count' => $broadcastCount,
            'channel' => $channel
        ], JSON_UNESCAPED_UNICODE));
    }

    /**
     * 处理统计信息请求
     */
    private function handleStatsRequest($request, $response)
    {
        $stats = array_merge($this->stats, [
            'uptime' => time() - $this->stats['start_time'],
            'merchants_online' => count($this->merchants),
            'channels_active' => count($this->channels),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true)
        ]);

        $response->status(200);
        $response->end(json_encode($stats, JSON_UNESCAPED_UNICODE));
    }

    /**
     * 处理健康检查
     */
    private function handleHealthCheck($request, $response)
    {
        $response->status(200);
        $response->end(json_encode([
            'status' => 'healthy',
            'timestamp' => time(),
            'server_time' => date('Y-m-d H:i:s'),
            'connections' => count($this->connections)
        ], JSON_UNESCAPED_UNICODE));
    }

    /**
     * 广播消息到指定频道
     */
    public function broadcastToChannel($channel, $message)
    {
        if (!isset($this->channels[$channel])) {
            return 0;
        }

        $count = 0;
        foreach ($this->channels[$channel] as $fd => $active) {
            if ($this->server->isEstablished($fd)) {
                $this->sendMessage($fd, $message);
                $count++;
            } else {
                // 清理无效连接
                unset($this->channels[$channel][$fd]);
                if (isset($this->connections[$fd])) {
                    unset($this->connections[$fd]);
                }
            }
        }

        return $count;
    }

    /**
     * 发送消息到指定客户端
     */
    private function sendMessage($fd, $message)
    {
        if ($this->server->isEstablished($fd)) {
            $jsonData = json_encode($message, JSON_UNESCAPED_UNICODE);
            $result = $this->server->push($fd, $jsonData);

            if (!$result) {
                echo "⚠️ 消息发送失败: FD#{$fd}\n";
            }

            return $result;
        }

        return false;
    }

    /**
     * 发送错误消息
     */
    private function sendError($fd, $error)
    {
        $this->sendMessage($fd, [
            'type' => 'error',
            'data' => [
                'message' => $error,
                'timestamp' => time()
            ]
        ]);
    }

    /**
     * 发送心跳到所有连接
     */
    private function sendHeartbeat()
    {
        $heartbeatMessage = [
            'type' => 'heartbeat',
            'data' => [
                'timestamp' => time(),
                'server_time' => date('Y-m-d H:i:s')
            ]
        ];

        $count = 0;
        foreach ($this->connections as $fd => $connection) {
            if ($this->server->isEstablished($fd)) {
                $this->sendMessage($fd, $heartbeatMessage);
                $count++;
            }
        }

        if ($count > 0) {
            echo "💓 发送心跳: {$count}个连接\n";
        }
    }

    /**
     * 清理过期连接
     */
    private function cleanupConnections()
    {
        $now = time();
        $timeout = $this->config['heartbeat_interval'] * 3; // 3倍心跳间隔为超时
        $cleanedCount = 0;

        foreach ($this->connections as $fd => $connection) {
            if ($now - $connection['last_heartbeat'] > $timeout) {
                echo "🧹 清理过期连接: FD#{$fd} (超时: " . ($now - $connection['last_heartbeat']) . "秒)\n";
                $this->server->close($fd);
                $cleanedCount++;
            }
        }

        if ($cleanedCount > 0) {
            echo "🧹 清理完成: {$cleanedCount}个过期连接\n";
        }
    }

    /**
     * 打印服务器统计信息
     */
    private function printStats()
    {
        $uptime = time() - $this->stats['start_time'];
        $hours = floor($uptime / 3600);
        $minutes = floor(($uptime % 3600) / 60);
        $seconds = $uptime % 60;

        echo "\n📊 ===== 服务器统计信息 =====\n";
        echo "运行时间: {$hours}小时 {$minutes}分钟 {$seconds}秒\n";
        echo "当前连接: {$this->stats['current_connections']}\n";
        echo "总连接数: {$this->stats['total_connections']}\n";
        echo "总消息数: {$this->stats['total_messages']}\n";
        echo "支付通知: {$this->stats['payment_notifications']}\n";
        echo "在线商户: " . count($this->merchants) . "\n";
        echo "活跃频道: " . count($this->channels) . "\n";
        echo "内存使用: " . round(memory_get_usage(true) / 1024 / 1024, 2) . "MB\n";
        echo "内存峰值: " . round(memory_get_peak_usage(true) / 1024 / 1024, 2) . "MB\n";
        echo "=============================\n\n";
    }

    /**
     * 启动服务器
     */
    public function start()
    {
        echo "🔧 正在启动Swoole WebSocket服务器...\n";

        // 创建日志目录
        $logDir = __DIR__ . '/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
            echo "📁 创建日志目录: {$logDir}\n";
        }

        // 启动服务器
        $this->server->start();
    }
}

// ==========================================
// 服务器启动脚本
// ==========================================

// 检查运行环境
if (php_sapi_name() !== 'cli') {
    die("❌ 请在CLI模式下运行此脚本\n");
}

// 处理命令行参数
$action = $argv[1] ?? 'start';

switch ($action) {
    case 'start':
        echo "🚀 启动Swoole WebSocket支付服务器...\n";

        $config = [
            'host' => '0.0.0.0',
            'port' => 8080,
            'worker_num' => 2,
            'heartbeat_interval' => 30,
            'max_connections' => 1000
        ];

        $server = new PaymentWebSocketServer($config);
        $server->start();
        break;

    case 'stop':
        $pidFile = __DIR__ . '/logs/swoole_websocket.pid';
        if (file_exists($pidFile)) {
            $pid = file_get_contents($pidFile);
            if ($pid && posix_kill($pid, SIGTERM)) {
                echo "✅ 服务器已停止 (PID: {$pid})\n";
                unlink($pidFile);
            } else {
                echo "❌ 停止服务器失败\n";
            }
        } else {
            echo "❌ 服务器未运行\n";
        }
        break;

    case 'restart':
        echo "🔄 重启服务器...\n";
        // 先停止
        $pidFile = __DIR__ . '/logs/swoole_websocket.pid';
        if (file_exists($pidFile)) {
            $pid = file_get_contents($pidFile);
            if ($pid && posix_kill($pid, SIGTERM)) {
                echo "✅ 服务器已停止\n";
                unlink($pidFile);
                sleep(2);
            }
        }

        // 再启动
        $config = [
            'host' => '0.0.0.0',
            'port' => 8080,
            'worker_num' => 2,
            'heartbeat_interval' => 30,
            'max_connections' => 1000
        ];

        $server = new PaymentWebSocketServer($config);
        $server->start();
        break;

    case 'status':
        $pidFile = __DIR__ . '/logs/swoole_websocket.pid';
        if (file_exists($pidFile)) {
            $pid = file_get_contents($pidFile);
            if ($pid && posix_kill($pid, 0)) {
                echo "✅ 服务器正在运行 (PID: {$pid})\n";

                // 获取统计信息
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8080/stats');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 5);
                $response = curl_exec($ch);
                curl_close($ch);

                if ($response) {
                    $stats = json_decode($response, true);
                    if ($stats) {
                        echo "当前连接: {$stats['current_connections']}\n";
                        echo "总连接数: {$stats['total_connections']}\n";
                        echo "支付通知: {$stats['payment_notifications']}\n";
                        echo "运行时间: {$stats['uptime']}秒\n";
                    }
                }
            } else {
                echo "❌ 服务器未运行\n";
            }
        } else {
            echo "❌ 服务器未运行\n";
        }
        break;

    default:
        echo "用法: php swoole_websocket_server.php {start|stop|restart|status}\n";
        echo "  start   - 启动服务器\n";
        echo "  stop    - 停止服务器\n";
        echo "  restart - 重启服务器\n";
        echo "  status  - 查看服务器状态\n";
        break;
}
?>