function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/pages-login-index.Bds5bbtX.js","assets/uni-app.es.DAfa8VxY.js","assets/_plugin-vue_export-helper.BCo6x5W8.js","assets/uni-app-PCqGMjRt.css","assets/request.DGmokXb9.js","assets/index-1W-uMN-2.css","assets/pages-index-index.DiwPS29-.js","assets/custom-navbar.DuzuSmPc.js","assets/custom-navbar-BbtLHyiQ.css","assets/shanfu.Cee0BMqH.js","assets/alipay.TVRnAsOv.js","assets/index-Dlj2ZJv-.css","assets/pages-code-index.mxKLLDzN.js","assets/index-C9D1BI1n.css","assets/pages-bill-index.BSZDuZQT.js","assets/date.5CEjHFwh.js","assets/index-Bmtkri-f.css","assets/pages-scan-index.D2or3WrE.js","assets/index-CJp4DW-C.css","assets/pages-report-index.DHpRiE0m.js","assets/share.DVl4BbzX.js","assets/index-BoMg7F7q.css","assets/pages-report-diamond.BJ80cpxu.js","assets/diamond-CHSBg0ob.css","assets/pages-mine-index.afasWBYC.js","assets/index-xg99ah8U.css","assets/pages-wallet-index.DpULw8DP.js","assets/more.BlcQRyo0.js","assets/index-Wcdk7Q0W.css","assets/pages-wallet-bank-cards.ULJ9pjbz.js","assets/back.CcwIJs7W.js","assets/bank-cards-C4S6VdLt.css","assets/pages-merchant-index.DqAEv-i4.js","assets/index-WQW3Le_T.css","assets/pages-duizhang-index.BOTzn0yc.js","assets/index-C16yymxn.css","assets/pages-jiesuan-index.SK3LjSM_.js","assets/index-cn751KzQ.css","assets/pages-xiaoxi-index.DVvIp1MN.js","assets/index-Eq4NDcrL.css","assets/pages-yuangong-index.BSBQB6_o.js","assets/index-BsF9qnBB.css","assets/pages-yingxiao-index.ulO9NaUQ.js","assets/index-C99_DbSw.css","assets/pages-bill-date-range.CV2KWAhM.js","assets/date-range-BB4ddTSQ.css","assets/pages-pay-mini-payment.CPj6FUK0.js","assets/mini-payment-DeeZOtRm.css","assets/pages-pay-result.Di3-O9b6.js","assets/result-CkVau6Cw.css","assets/pages_A-user-index.DJjUWa8i.js","assets/index-J7l129RZ.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const e={},t=function(t,n,o){let r=Promise.resolve();if(n&&n.length>0){const t=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),s=(null==i?void 0:i.nonce)||(null==i?void 0:i.getAttribute("nonce"));r=Promise.all(n.map((n=>{if((n=function(e){return"/h5/"+e}(n))in e)return;e[n]=!0;const r=n.endsWith(".css"),i=r?'[rel="stylesheet"]':"";if(!!o)for(let e=t.length-1;e>=0;e--){const o=t[e];if(o.href===n&&(!r||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${n}"]${i}`))return;const a=document.createElement("link");return a.rel=r?"stylesheet":"modulepreload",r||(a.as="script",a.crossOrigin=""),a.href=n,s&&a.setAttribute("nonce",s),document.head.appendChild(a),r?new Promise(((e,t)=>{a.addEventListener("load",e),a.addEventListener("error",(()=>t(new Error(`Unable to preload CSS for ${n}`))))})):void 0})))}return r.then((()=>t())).catch((e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}))};
/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
function n(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const o={},r=[],i=()=>{},s=()=>!1,a=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),l=e=>e.startsWith("onUpdate:"),c=Object.assign,u=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},d=Object.prototype.hasOwnProperty,f=(e,t)=>d.call(e,t),h=Array.isArray,p=e=>"[object Map]"===x(e),m=e=>"[object Set]"===x(e),g=e=>"function"==typeof e,v=e=>"string"==typeof e,y=e=>"symbol"==typeof e,b=e=>null!==e&&"object"==typeof e,_=e=>(b(e)||g(e))&&g(e.then)&&g(e.catch),w=Object.prototype.toString,x=e=>w.call(e),S=e=>"[object Object]"===x(e),T=e=>v(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,C=n(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),k=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},E=/-(\w)/g,L=k((e=>e.replace(E,((e,t)=>t?t.toUpperCase():"")))),O=/\B([A-Z])/g,P=k((e=>e.replace(O,"-$1").toLowerCase())),$=k((e=>e.charAt(0).toUpperCase()+e.slice(1))),A=k((e=>e?`on${$(e)}`:"")),M=(e,t)=>!Object.is(e,t),I=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},B=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},R=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let j;const D=()=>j||(j="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function N(e){if(h(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=v(o)?W(o):N(o);if(r)for(const e in r)t[e]=r[e]}return t}if(v(e)||b(e))return e}const F=/;(?![^(]*\))/g,V=/:([^]+)/,H=/\/\*[^]*?\*\//g;function W(e){const t={};return e.replace(H,"").split(F).forEach((e=>{if(e){const n=e.split(V);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function z(e){let t="";if(v(e))t=e;else if(h(e))for(let n=0;n<e.length;n++){const o=z(e[n]);o&&(t+=o+" ")}else if(b(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const q=n("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function U(e){return!!e||""===e}const Y=e=>v(e)?e:null==e?"":h(e)||b(e)&&(e.toString===w||!g(e.toString))?JSON.stringify(e,X,2):String(e),X=(e,t)=>t&&t.__v_isRef?X(e,t.value):p(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[G(t,o)+" =>"]=n,e)),{})}:m(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>G(e)))}:y(t)?G(t):!b(t)||h(t)||S(t)?t:String(t),G=(e,t="")=>{var n;return y(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},K=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view","location-picker","location-view"].map((e=>"uni-"+e)),J=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map((e=>"uni-"+e)),Q=["list-item"].map((e=>"uni-"+e));function Z(e){if(-1!==Q.indexOf(e))return!1;const t="uni-"+e.replace("v-uni-","");return-1!==K.indexOf(t)||-1!==J.indexOf(t)}const ee=["%","%"],te=/^([a-z-]+:)?\/\//i,ne=/^data:.*,.*/;function oe(e){return 0===e.indexOf("/")}function re(e){return oe(e)?e:"/"+e}function ie(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}const se=e=>e>9?e:"0"+e;function ae({date:e=new Date,mode:t="date"}){return"time"===t?se(e.getHours())+":"+se(e.getMinutes()):e.getFullYear()+"-"+se(e.getMonth()+1)+"-"+se(e.getDate())}function le(e,t){e=e||{},v(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?g(e.success)&&e.success(t):g(e.fail)&&e.fail(t),g(e.complete)&&e.complete(t)}let ce;function ue(){return ce||(ce=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),ce)}function de(e){return e&&(e.appContext?e.proxy:e)}function fe(e){if(!e)return;let t=e.type.name;for(;t&&Z(P(t));)t=(e=e.parent).type.name;return e.proxy}function he(e){return 1===e.nodeType}function pe(e){const t=ue();if(t&&t.UTSJSONObject&&e instanceof t.UTSJSONObject){const n={};return t.UTSJSONObject.keys(e).forEach((t=>{n[t]=e[t]})),N(n)}if(e instanceof Map){const t={};return e.forEach(((e,n)=>{t[n]=e})),N(t)}if(v(e))return W(e);if(h(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=v(o)?W(o):pe(o);if(r)for(const e in r)t[e]=r[e]}return t}return N(e)}function me(e){let t="";const n=ue();if(n&&n.UTSJSONObject&&e instanceof n.UTSJSONObject)n.UTSJSONObject.keys(e).forEach((n=>{e[n]&&(t+=n+" ")}));else if(e instanceof Map)e.forEach(((e,n)=>{e&&(t+=n+" ")}));else if(h(e))for(let o=0;o<e.length;o++){const n=me(e[o]);n&&(t+=n+" ")}else t=z(e);return t.trim()}function ge(e){return L(e.substring(5))}const ve=ie((e=>{e=e||(e=>e.tagName.startsWith("UNI-"));const t=HTMLElement.prototype,n=t.setAttribute;t.setAttribute=function(t,o){if(t.startsWith("data-")&&e(this)){(this.__uniDataset||(this.__uniDataset={}))[ge(t)]=o}n.call(this,t,o)};const o=t.removeAttribute;t.removeAttribute=function(t){this.__uniDataset&&t.startsWith("data-")&&e(this)&&delete this.__uniDataset[ge(t)],o.call(this,t)}}));function ye(e){return c({},e.dataset,e.__uniDataset)}const be=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function _e(e){return{passive:e}}function we(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:ye(e),offsetTop:n,offsetLeft:o}}function xe(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Se(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=xe(e[n])}catch(o){t[n]=e[n]}})),t}const Te=/\+/g;function Ce(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Te," ");let r=e.indexOf("="),i=xe(r<0?e:e.slice(0,r)),s=r<0?null:xe(e.slice(r+1));if(i in t){let e=t[i];h(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function ke(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);const i=()=>e.apply(this,arguments);r=o(i,t)};return i.cancel=function(){n(r)},i}class Ee{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const Le=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onShareChat","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Oe=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onShareChat","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Pe=[];const $e=ie(((e,t)=>t(e))),Ae=function(){};Ae.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var i=o.length-1;i>=0;i--)if(o[i].fn===t||o[i].fn._===t||o[i]._id===t){o.splice(i,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var Me=Ae;const Ie={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function Be(e,t,n){if(v(t)&&t.startsWith("@")){let r=e[t.replace("@","")]||t;switch(n){case"titleColor":r="black"===r?"#000000":"#ffffff";break;case"borderStyle":r=(o=r)&&o in Ie?Ie[o]:o}return r}var o;return t}function Re(e,t={},n="light"){const o=t[n],r={};return void 0!==o&&e?(Object.keys(e).forEach((i=>{const s=e[i];r[i]=S(s)?Re(s,t,n):h(s)?s.map((e=>"object"==typeof e?Re(e,t,n):Be(o,e))):Be(o,s,i)})),r):e}
/**
* @dcloudio/uni-h5-vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let je,De;class Ne{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=je,!e&&je&&(this.index=(je.scopes||(je.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=je;try{return je=this,e()}finally{je=t}}}on(){je=this}off(){je=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function Fe(e){return new Ne(e)}class Ve{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=je){t&&t.active&&t.effects.push(e)}(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,Xe();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),Ge()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=qe,t=De;try{return qe=!0,De=this,this._runnings++,He(this),this.fn()}finally{We(this),this._runnings--,De=t,qe=e}}stop(){var e;this.active&&(He(this),We(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function He(e){e._trackId++,e._depsLength=0}function We(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)ze(e.deps[t],e);e.deps.length=e._depsLength}}function ze(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let qe=!0,Ue=0;const Ye=[];function Xe(){Ye.push(qe),qe=!1}function Ge(){const e=Ye.pop();qe=void 0===e||e}function Ke(){Ue++}function Je(){for(Ue--;!Ue&&Ze.length;)Ze.shift()()}function Qe(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&ze(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const Ze=[];function et(e,t,n){Ke();for(const o of e.keys()){let n;o._dirtyLevel<t&&(null!=n?n:n=e.get(o)===o._trackId)&&(o._shouldSchedule||(o._shouldSchedule=0===o._dirtyLevel),o._dirtyLevel=t),o._shouldSchedule&&(null!=n?n:n=e.get(o)===o._trackId)&&(o.trigger(),o._runnings&&!o.allowRecurse||2===o._dirtyLevel||(o._shouldSchedule=!1,o.scheduler&&Ze.push(o.scheduler)))}Je()}const tt=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},nt=new WeakMap,ot=Symbol(""),rt=Symbol("");function it(e,t,n){if(qe&&De){let t=nt.get(e);t||nt.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=tt((()=>t.delete(n)))),Qe(De,o)}}function st(e,t,n,o,r,i){const s=nt.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&h(e)){const e=Number(o);s.forEach(((t,n)=>{("length"===n||!y(n)&&n>=e)&&a.push(t)}))}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":h(e)?T(n)&&a.push(s.get("length")):(a.push(s.get(ot)),p(e)&&a.push(s.get(rt)));break;case"delete":h(e)||(a.push(s.get(ot)),p(e)&&a.push(s.get(rt)));break;case"set":p(e)&&a.push(s.get(ot))}Ke();for(const l of a)l&&et(l,4);Je()}const at=n("__proto__,__v_isRef,__isVue"),lt=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(y)),ct=ut();function ut(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Qt(this);for(let t=0,r=this.length;t<r;t++)it(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Qt)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){Xe(),Ke();const n=Qt(this)[t].apply(this,e);return Je(),Ge(),n}})),e}function dt(e){const t=Qt(this);return it(t,0,e),t.hasOwnProperty(e)}class ft{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?Ht:Vt:r?Ft:Nt).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=h(e);if(!o){if(i&&f(ct,t))return Reflect.get(ct,t,n);if("hasOwnProperty"===t)return dt}const s=Reflect.get(e,t,n);return(y(t)?lt.has(t):at(t))?s:(o||it(e,0,t),r?s:sn(s)?i&&T(t)?s:s.value:b(s)?o?Ut(s):zt(s):s)}}class ht extends ft{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=Gt(r);if(Kt(n)||Gt(n)||(r=Qt(r),n=Qt(n)),!h(e)&&sn(r)&&!sn(n))return!t&&(r.value=n,!0)}const i=h(e)&&T(t)?Number(t)<e.length:f(e,t),s=Reflect.set(e,t,n,o);return e===Qt(o)&&(i?M(n,r)&&st(e,"set",t,n):st(e,"add",t,n)),s}deleteProperty(e,t){const n=f(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&st(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return y(t)&&lt.has(t)||it(e,0,t),n}ownKeys(e){return it(e,0,h(e)?"length":ot),Reflect.ownKeys(e)}}class pt extends ft{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const mt=new ht,gt=new pt,vt=new ht(!0),yt=e=>e,bt=e=>Reflect.getPrototypeOf(e);function _t(e,t,n=!1,o=!1){const r=Qt(e=e.__v_raw),i=Qt(t);n||(M(t,i)&&it(r,0,t),it(r,0,i));const{has:s}=bt(r),a=o?yt:n?tn:en;return s.call(r,t)?a(e.get(t)):s.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function wt(e,t=!1){const n=this.__v_raw,o=Qt(n),r=Qt(e);return t||(M(e,r)&&it(o,0,e),it(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function xt(e,t=!1){return e=e.__v_raw,!t&&it(Qt(e),0,ot),Reflect.get(e,"size",e)}function St(e){e=Qt(e);const t=Qt(this);return bt(t).has.call(t,e)||(t.add(e),st(t,"add",e,e)),this}function Tt(e,t){t=Qt(t);const n=Qt(this),{has:o,get:r}=bt(n);let i=o.call(n,e);i||(e=Qt(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?M(t,s)&&st(n,"set",e,t):st(n,"add",e,t),this}function Ct(e){const t=Qt(this),{has:n,get:o}=bt(t);let r=n.call(t,e);r||(e=Qt(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&st(t,"delete",e,void 0),i}function kt(){const e=Qt(this),t=0!==e.size,n=e.clear();return t&&st(e,"clear",void 0,void 0),n}function Et(e,t){return function(n,o){const r=this,i=r.__v_raw,s=Qt(i),a=t?yt:e?tn:en;return!e&&it(s,0,ot),i.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function Lt(e,t,n){return function(...o){const r=this.__v_raw,i=Qt(r),s=p(i),a="entries"===e||e===Symbol.iterator&&s,l="keys"===e&&s,c=r[e](...o),u=n?yt:t?tn:en;return!t&&it(i,0,l?rt:ot),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Ot(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function Pt(){const e={get(e){return _t(this,e)},get size(){return xt(this)},has:wt,add:St,set:Tt,delete:Ct,clear:kt,forEach:Et(!1,!1)},t={get(e){return _t(this,e,!1,!0)},get size(){return xt(this)},has:wt,add:St,set:Tt,delete:Ct,clear:kt,forEach:Et(!1,!0)},n={get(e){return _t(this,e,!0)},get size(){return xt(this,!0)},has(e){return wt.call(this,e,!0)},add:Ot("add"),set:Ot("set"),delete:Ot("delete"),clear:Ot("clear"),forEach:Et(!0,!1)},o={get(e){return _t(this,e,!0,!0)},get size(){return xt(this,!0)},has(e){return wt.call(this,e,!0)},add:Ot("add"),set:Ot("set"),delete:Ot("delete"),clear:Ot("clear"),forEach:Et(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=Lt(r,!1,!1),n[r]=Lt(r,!0,!1),t[r]=Lt(r,!1,!0),o[r]=Lt(r,!0,!0)})),[e,n,t,o]}const[$t,At,Mt,It]=Pt();function Bt(e,t){const n=t?e?It:Mt:e?At:$t;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(f(n,o)&&o in t?n:t,o,r)}const Rt={get:Bt(!1,!1)},jt={get:Bt(!1,!0)},Dt={get:Bt(!0,!1)},Nt=new WeakMap,Ft=new WeakMap,Vt=new WeakMap,Ht=new WeakMap;function Wt(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>x(e).slice(8,-1))(e))}function zt(e){return Gt(e)?e:Yt(e,!1,mt,Rt,Nt)}function qt(e){return Yt(e,!1,vt,jt,Ft)}function Ut(e){return Yt(e,!0,gt,Dt,Vt)}function Yt(e,t,n,o,r){if(!b(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=Wt(e);if(0===s)return e;const a=new Proxy(e,2===s?o:n);return r.set(e,a),a}function Xt(e){return Gt(e)?Xt(e.__v_raw):!(!e||!e.__v_isReactive)}function Gt(e){return!(!e||!e.__v_isReadonly)}function Kt(e){return!(!e||!e.__v_isShallow)}function Jt(e){return Xt(e)||Gt(e)}function Qt(e){const t=e&&e.__v_raw;return t?Qt(t):e}function Zt(e){return Object.isExtensible(e)&&B(e,"__v_skip",!0),e}const en=e=>b(e)?zt(e):e,tn=e=>b(e)?Ut(e):e;class nn{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new Ve((()=>e(this._value)),(()=>rn(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=Qt(this);return e._cacheable&&!e.effect.dirty||!M(e._value,e._value=e.effect.run())||rn(e,4),on(e),e.effect._dirtyLevel>=2&&rn(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function on(e){var t;qe&&De&&(e=Qt(e),Qe(De,null!=(t=e.dep)?t:e.dep=tt((()=>e.dep=void 0),e instanceof nn?e:void 0)))}function rn(e,t=4,n){const o=(e=Qt(e)).dep;o&&et(o,t)}function sn(e){return!(!e||!0!==e.__v_isRef)}function an(e){return cn(e,!1)}function ln(e){return cn(e,!0)}function cn(e,t){return sn(e)?e:new un(e,t)}class un{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Qt(e),this._value=t?e:en(e)}get value(){return on(this),this._value}set value(e){const t=this.__v_isShallow||Kt(e)||Gt(e);e=t?e:Qt(e),M(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:en(e),rn(this,4))}}function dn(e){return sn(e)?e.value:e}const fn={get:(e,t,n)=>dn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return sn(r)&&!sn(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function hn(e){return Xt(e)?e:new Proxy(e,fn)}function pn(e,t,n,o){try{return o?e(...o):e()}catch(r){gn(r,t,n)}}function mn(e,t,n,o){if(g(e)){const r=pn(e,t,n,o);return r&&_(r)&&r.catch((e=>{gn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(mn(e[i],t,n,o));return r}function gn(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void pn(s,null,10,[e,r,i])}vn(e,n,r,o)}function vn(e,t,n,o=!0){console.error(e)}let yn=!1,bn=!1;const _n=[];let wn=0;const xn=[];let Sn=null,Tn=0;const Cn=Promise.resolve();let kn=null;function En(e){const t=kn||Cn;return e?t.then(this?e.bind(this):e):t}function Ln(e){_n.length&&_n.includes(e,yn&&e.allowRecurse?wn+1:wn)||(null==e.id?_n.push(e):_n.splice(function(e){let t=wn+1,n=_n.length;for(;t<n;){const o=t+n>>>1,r=_n[o],i=An(r);i<e||i===e&&r.pre?t=o+1:n=o}return t}(e.id),0,e),On())}function On(){yn||bn||(bn=!0,kn=Cn.then(In))}function Pn(e,t,n=(yn?wn+1:0)){for(;n<_n.length;n++){const t=_n[n];if(t&&t.pre){if(e&&t.id!==e.uid)continue;_n.splice(n,1),n--,t()}}}function $n(e){if(xn.length){const e=[...new Set(xn)].sort(((e,t)=>An(e)-An(t)));if(xn.length=0,Sn)return void Sn.push(...e);for(Sn=e,Tn=0;Tn<Sn.length;Tn++)Sn[Tn]();Sn=null,Tn=0}}const An=e=>null==e.id?1/0:e.id,Mn=(e,t)=>{const n=An(e)-An(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function In(e){bn=!1,yn=!0,_n.sort(Mn);try{for(wn=0;wn<_n.length;wn++){const e=_n[wn];e&&!1!==e.active&&pn(e,null,14)}}finally{wn=0,_n.length=0,$n(),yn=!1,kn=null,(_n.length||xn.length)&&In()}}function Bn(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||o;let i=n;const s=t.startsWith("update:"),a=s&&t.slice(7);if(a&&a in r){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:s}=r[e]||o;s&&(i=n.map((e=>v(e)?e.trim():e))),t&&(i=n.map(R))}let l,c=r[l=A(t)]||r[l=A(L(t))];!c&&s&&(c=r[l=A(P(t))]),c&&mn(c,e,6,Rn(e,c,i));const u=r[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,mn(u,e,6,Rn(e,u,i))}}function Rn(e,t,n){if(1!==n.length)return n;if(g(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&f(o,"type")&&f(o,"timeStamp")&&f(o,"target")&&f(o,"currentTarget")&&f(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function jn(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},a=!1;if(!g(e)){const o=e=>{const n=jn(e,t,!0);n&&(a=!0,c(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||a?(h(i)?i.forEach((e=>s[e]=null)):c(s,i),b(e)&&o.set(e,s),s):(b(e)&&o.set(e,null),null)}function Dn(e,t){return!(!e||!a(t))&&(t=t.slice(2).replace(/Once$/,""),f(e,t[0].toLowerCase()+t.slice(1))||f(e,P(t))||f(e,t))}let Nn=null,Fn=null;function Vn(e){const t=Nn;return Nn=e,Fn=e&&e.type.__scopeId||null,t}function Hn(e,t=Nn,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Kr(-1);const r=Vn(t);let i;try{i=e(...n)}finally{Vn(r),o._d&&Kr(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function Wn(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:a,attrs:c,emit:u,render:d,renderCache:f,data:h,setupState:p,ctx:m,inheritAttrs:g}=e;let v,y;const b=Vn(e);try{if(4&n.shapeFlag){const e=r||o,t=e;v=ui(d.call(t,e,f,i,p,h,m)),y=c}else{const e=t;0,v=ui(e.length>1?e(i,{attrs:c,slots:a,emit:u}):e(i,null)),y=t.props?c:zn(c)}}catch(w){Ur.length=0,gn(w,e,1),v=si(zr)}let _=v;if(y&&!1!==g){const e=Object.keys(y),{shapeFlag:t}=_;e.length&&7&t&&(s&&e.some(l)&&(y=qn(y,s)),_=ai(_,y))}return n.dirs&&(_=ai(_),_.dirs=_.dirs?_.dirs.concat(n.dirs):n.dirs),n.transition&&(_.transition=n.transition),v=_,Vn(b),v}const zn=e=>{let t;for(const n in e)("class"===n||"style"===n||a(n))&&((t||(t={}))[n]=e[n]);return t},qn=(e,t)=>{const n={};for(const o in e)l(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function Un(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!Dn(n,i))return!0}return!1}function Yn(e,t){return Kn("components",e,!0,t)||e}const Xn=Symbol.for("v-ndc");function Gn(e){return v(e)?Kn("components",e,!1)||e:e||Xn}function Kn(e,t,n=!0,o=!1){const r=Nn||vi;if(r){const n=r.type;if("components"===e){const e=Li(n,!1);if(e&&(e===t||e===L(t)||e===$(L(t))))return n}const i=Jn(r[e]||n[e],t)||Jn(r.appContext[e],t);return!i&&o?n:i}}function Jn(e,t){return e&&(e[t]||e[L(t)]||e[$(L(t))])}const Qn=e=>e.__isSuspense;const Zn=Symbol.for("v-scx");function eo(e,t){return oo(e,null,t)}const to={};function no(e,t,n){return oo(e,t,n)}function oo(e,t,{immediate:n,deep:r,flush:s,once:a,onTrack:l,onTrigger:c}=o){if(t&&a){const e=t;t=(...t)=>{e(...t),k()}}const d=vi,f=e=>!0===r?e:so(e,!1===r?1:void 0);let p,m,v=!1,y=!1;if(sn(e)?(p=()=>e.value,v=Kt(e)):Xt(e)?(p=()=>f(e),v=!0):h(e)?(y=!0,v=e.some((e=>Xt(e)||Kt(e))),p=()=>e.map((e=>sn(e)?e.value:Xt(e)?f(e):g(e)?pn(e,d,2):void 0))):p=g(e)?t?()=>pn(e,d,2):()=>(m&&m(),mn(e,d,3,[_])):i,t&&r){const e=p;p=()=>so(e())}let b,_=e=>{m=T.onStop=()=>{pn(e,d,4),m=T.onStop=void 0}};if(Ti){if(_=i,t?n&&mn(t,d,3,[p(),y?[]:void 0,_]):p(),"sync"!==s)return i;{const e=xr(Zn);b=e.__watcherHandles||(e.__watcherHandles=[])}}let w=y?new Array(e.length).fill(to):to;const x=()=>{if(T.active&&T.dirty)if(t){const e=T.run();(r||v||(y?e.some(((e,t)=>M(e,w[t]))):M(e,w)))&&(m&&m(),mn(t,d,3,[e,w===to?void 0:y&&w[0]===to?[]:w,_]),w=e)}else T.run()};let S;x.allowRecurse=!!t,"sync"===s?S=x:"post"===s?S=()=>Rr(x,d&&d.suspense):(x.pre=!0,d&&(x.id=d.uid),S=()=>Ln(x));const T=new Ve(p,i,S),C=je,k=()=>{T.stop(),C&&u(C.effects,T)};return t?n?x():w=T.run():"post"===s?Rr(T.run.bind(T),d&&d.suspense):T.run(),b&&b.push(k),k}function ro(e,t,n){const o=this.proxy,r=v(e)?e.includes(".")?io(o,e):()=>o[e]:e.bind(o,o);let i;g(t)?i=t:(i=t.handler,n=t);const s=wi(this),a=oo(r,i.bind(o),n);return s(),a}function io(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function so(e,t,n=0,o){if(!b(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),sn(e))so(e.value,t,n,o);else if(h(e))for(let r=0;r<e.length;r++)so(e[r],t,n,o);else if(m(e)||p(e))e.forEach((e=>{so(e,t,n,o)}));else if(S(e))for(const r in e)so(e[r],t,n,o);return e}function ao(e,t){if(null===Nn)return e;const n=Ei(Nn)||Nn.proxy,r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,s,a,l=o]=t[i];e&&(g(e)&&(e={mounted:e,updated:e}),e.deep&&so(s),r.push({dir:e,instance:n,value:s,oldValue:void 0,arg:a,modifiers:l}))}return e}function lo(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let s=0;s<r.length;s++){const a=r[s];i&&(a.oldValue=i[s].value);let l=a.dir[o];l&&(Xe(),mn(l,n,8,[e.el,a,e,t]),Ge())}}const co=Symbol("_leaveCb"),uo=Symbol("_enterCb");const fo=[Function,Array],ho={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:fo,onEnter:fo,onAfterEnter:fo,onEnterCancelled:fo,onBeforeLeave:fo,onLeave:fo,onAfterLeave:fo,onLeaveCancelled:fo,onBeforeAppear:fo,onAppear:fo,onAfterAppear:fo,onAppearCancelled:fo},po={name:"BaseTransition",props:ho,setup(e,{slots:t}){const n=yi(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Vo((()=>{e.isMounted=!0})),zo((()=>{e.isUnmounting=!0})),e}();return()=>{const r=t.default&&_o(t.default(),!0);if(!r||!r.length)return;let i=r[0];if(r.length>1)for(const e of r)if(e.type!==zr){i=e;break}const s=Qt(e),{mode:a}=s;if(o.isLeaving)return vo(i);const l=yo(i);if(!l)return vo(i);const c=go(l,s,o,n);bo(l,c);const u=n.subTree,d=u&&yo(u);if(d&&d.type!==zr&&!ti(l,d)){const e=go(d,s,o,n);if(bo(d,e),"out-in"===a)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&(n.effect.dirty=!0,n.update())},vo(i);"in-out"===a&&l.type!==zr&&(e.delayLeave=(e,t,n)=>{mo(o,d)[String(d.key)]=d,e[co]=()=>{t(),e[co]=void 0,delete c.delayedLeave},c.delayedLeave=n})}return i}}};function mo(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function go(e,t,n,o){const{appear:r,mode:i,persisted:s=!1,onBeforeEnter:a,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:f,onAfterLeave:p,onLeaveCancelled:m,onBeforeAppear:g,onAppear:v,onAfterAppear:y,onAppearCancelled:b}=t,_=String(e.key),w=mo(n,e),x=(e,t)=>{e&&mn(e,o,9,t)},S=(e,t)=>{const n=t[1];x(e,t),h(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},T={mode:i,persisted:s,beforeEnter(t){let o=a;if(!n.isMounted){if(!r)return;o=g||a}t[co]&&t[co](!0);const i=w[_];i&&ti(e,i)&&i.el[co]&&i.el[co](),x(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=v||l,o=y||c,i=b||u}let s=!1;const a=e[uo]=t=>{s||(s=!0,x(t?i:o,[e]),T.delayedLeave&&T.delayedLeave(),e[uo]=void 0)};t?S(t,[e,a]):a()},leave(t,o){const r=String(e.key);if(t[uo]&&t[uo](!0),n.isUnmounting)return o();x(d,[t]);let i=!1;const s=t[co]=n=>{i||(i=!0,o(),x(n?m:p,[t]),t[co]=void 0,w[r]===e&&delete w[r])};w[r]=e,f?S(f,[t,s]):s()},clone:e=>go(e,t,n,o)};return T}function vo(e){if(Co(e))return(e=ai(e)).children=null,e}function yo(e){return Co(e)?e.children?e.children[0]:void 0:e}function bo(e,t){6&e.shapeFlag&&e.component?bo(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function _o(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let s=e[i];const a=null==n?s.key:String(n)+String(null!=s.key?s.key:i);s.type===Hr?(128&s.patchFlag&&r++,o=o.concat(_o(s.children,t,a))):(t||s.type!==zr)&&o.push(null!=a?ai(s,{key:a}):s)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}
/*! #__NO_SIDE_EFFECTS__ */function wo(e,t){return g(e)?(()=>c({name:e.name},t,{setup:e}))():e}const xo=e=>!!e.type.__asyncLoader
/*! #__NO_SIDE_EFFECTS__ */;function So(e){g(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:s=!0,onError:a}=e;let l,c=null,u=0;const d=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise(((t,n)=>{a(e,(()=>t((u++,c=null,d()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return wo({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return l},setup(){const e=vi;if(l)return()=>To(l,e);const t=t=>{c=null,gn(t,e,13,!o)};if(s&&e.suspense||Ti)return d().then((t=>()=>To(t,e))).catch((e=>(t(e),()=>o?si(o,{error:e}):null)));const a=an(!1),u=an(),f=an(!!r);return r&&setTimeout((()=>{f.value=!1}),r),null!=i&&setTimeout((()=>{if(!a.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),d().then((()=>{a.value=!0,e.parent&&Co(e.parent.vnode)&&(e.parent.effect.dirty=!0,Ln(e.parent.update))})).catch((e=>{t(e),u.value=e})),()=>a.value&&l?To(l,e):u.value&&o?si(o,{error:u.value}):n&&!f.value?si(n):void 0}})}function To(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,s=si(e,o,r);return s.ref=n,s.ce=i,delete t.vnode.ce,s}const Co=e=>e.type.__isKeepAlive;class ko{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const Eo={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=yi(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new ko(e.max);r.pruneCacheEntry=s;let i=null;function s(t){var o;!i||!ti(t,i)||"key"===e.matchBy&&t.key!==i.key?(Io(o=t),u(o,n,a,!0)):i&&Io(i)}const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=o,f=d("div");function h(t){r.forEach(((n,o)=>{const i=Ro(n,e.matchBy);!i||t&&t(i)||(r.delete(o),s(n))}))}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,I(i.ba),i.isDeactivated=e}c(e,t,n,0,a),l(i.vnode,e,t,n,i,a,o,e.slotScopeIds,r),Rr((()=>{i.isDeactivated=!1,i.a&&I(i.a);const t=e.props&&e.props.onVnodeMounted;t&&pi(t,i.parent,e)}),a)},o.deactivate=e=>{const t=e.component;t.bda&&jo(t.bda),c(e,f,null,1,a),Rr((()=>{t.bda&&t.bda.forEach((e=>e.__called=!1)),t.da&&I(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&pi(n,t.parent,e),t.isDeactivated=!0}),a)},no((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&h((t=>Oo(e,t))),t&&h((e=>!Oo(t,e)))}),{flush:"post",deep:!0});let p=null;const m=()=>{null!=p&&r.set(p,Bo(n.subTree))};return Vo(m),Wo(m),zo((()=>{r.forEach(((t,o)=>{r.delete(o),s(t);const{subTree:i,suspense:a}=n,l=Bo(i);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&I(l.component.bda),Io(l);const e=l.component.da;e&&Rr(e,a)}}))})),()=>{if(p=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!ei(o)||!(4&o.shapeFlag)&&!Qn(o.type))return i=null,o;let s=Bo(o);const a=s.type,l=Ro(s,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!Oo(c,l))||u&&l&&Oo(u,l))return i=s,o;const d=null==s.key?a:s.key,f=r.get(d);return s.el&&(s=ai(s),Qn(o.type)&&(o.ssContent=s)),p=d,f&&(s.el=f.el,s.component=f.component,s.transition&&bo(s,s.transition),s.shapeFlag|=512),s.shapeFlag|=256,i=s,Qn(o.type)?o:s}}},Lo=Eo;function Oo(e,t){return h(e)?e.some((e=>Oo(e,t))):v(e)?e.split(",").includes(t):"[object RegExp]"===x(e)&&e.test(t)}function Po(e,t){Ao(e,"a",t)}function $o(e,t){Ao(e,"da",t)}function Ao(e,t,n=vi){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,Do(t,o,n),n){let e=n.parent;for(;e&&e.parent;)Co(e.parent.vnode)&&Mo(o,t,n,e),e=e.parent}}function Mo(e,t,n,o){const r=Do(t,e,o,!0);qo((()=>{u(o[t],r)}),n)}function Io(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Bo(e){return Qn(e.type)?e.ssContent:e}function Ro(e,t){if("name"===t){const t=e.type;return Li(xo(e)?t.__asyncResolved||{}:t)}return String(e.key)}function jo(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function Do(e,t,n=vi,o=!1){if(n){if(r=e,Le.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return["onLoad","onShow"].indexOf(e)>-1}(e))){const o=n.proxy;mn(t.bind(o),n,e,"onLoad"===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Xe();const r=wi(n),i=mn(t,n,e,o);return r(),Ge(),i});return o?i.unshift(s):i.push(s),s}var r}const No=e=>(t,n=vi)=>(!Ti||"sp"===e)&&Do(e,((...e)=>t(...e)),n),Fo=No("bm"),Vo=No("m"),Ho=No("bu"),Wo=No("u"),zo=No("bum"),qo=No("um"),Uo=No("sp"),Yo=No("rtg"),Xo=No("rtc");function Go(e,t=vi){Do("ec",e,t)}function Ko(e,t,n,o){let r;const i=n&&n[o];if(h(e)||v(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i&&i[n])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if(b(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,s=n.length;o<s;o++){const s=n[o];r[o]=t(e[s],s,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function Jo(e,t){for(let n=0;n<t.length;n++){const o=t[n];if(h(o))for(let t=0;t<o.length;t++)e[o[t].name]=o[t].fn;else o&&(e[o.name]=o.key?(...e)=>{const t=o.fn(...e);return t&&(t.key=o.key),t}:o.fn)}return e}function Qo(e,t,n={},o,r){if(Nn.isCE||Nn.parent&&xo(Nn.parent)&&Nn.parent.isCE)return"default"!==t&&(n.name=t),si("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),Xr();const s=i&&Zo(i(n)),a=Zr(Hr,{key:n.key||s&&s.key||`_${t}`},s||(o?o():[]),s&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function Zo(e){return e.some((e=>!ei(e)||e.type!==zr&&!(e.type===Hr&&!Zo(e.children))))?e:null}const er=e=>{if(!e)return null;if(Si(e)){return Ei(e)||e.proxy}return er(e.parent)},tr=c(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>er(e.parent),$root:e=>er(e.root),$emit:e=>e.emit,$options:e=>cr(e),$forceUpdate:e=>e.f||(e.f=(e=>function(){e.effect.dirty=!0,Ln(e.update)})(e)),$nextTick:e=>e.n||(e.n=En.bind(e.proxy)),$watch:e=>ro.bind(e)}),nr=(e,t)=>e!==o&&!e.__isScriptSetup&&f(e,t),or={get({_:e},t){const{ctx:n,setupState:r,data:i,props:s,accessCache:a,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=a[t];if(void 0!==l)switch(l){case 1:return r[t];case 2:return i[t];case 4:return n[t];case 3:return s[t]}else{if(nr(r,t))return a[t]=1,r[t];if(i!==o&&f(i,t))return a[t]=2,i[t];if((u=e.propsOptions[0])&&f(u,t))return a[t]=3,s[t];if(n!==o&&f(n,t))return a[t]=4,n[t];ir&&(a[t]=0)}}const d=tr[t];let h,p;return d?("$attrs"===t&&it(e,0,t),d(e)):(h=l.__cssModules)&&(h=h[t])?h:n!==o&&f(n,t)?(a[t]=4,n[t]):(p=c.config.globalProperties,f(p,t)?p[t]:void 0)},set({_:e},t,n){const{data:r,setupState:i,ctx:s}=e;return nr(i,t)?(i[t]=n,!0):r!==o&&f(r,t)?(r[t]=n,!0):!f(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(s[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:i,propsOptions:s}},a){let l;return!!n[a]||e!==o&&f(e,a)||nr(t,a)||(l=s[0])&&f(l,a)||f(r,a)||f(tr,a)||f(i.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:f(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function rr(e){return h(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let ir=!0;function sr(e){const t=cr(e),n=e.proxy,o=e.ctx;ir=!1,t.beforeCreate&&ar(t.beforeCreate,e,"bc");const{data:r,computed:s,methods:a,watch:l,provide:c,inject:u,created:d,beforeMount:f,mounted:p,beforeUpdate:m,updated:v,activated:y,deactivated:_,beforeDestroy:w,beforeUnmount:x,destroyed:S,unmounted:T,render:C,renderTracked:k,renderTriggered:E,errorCaptured:L,serverPrefetch:O,expose:P,inheritAttrs:$,components:A,directives:M,filters:I}=t;if(u&&function(e,t,n=i){h(e)&&(e=hr(e));for(const o in e){const n=e[o];let r;r=b(n)?"default"in n?xr(n.from||o,n.default,!0):xr(n.from||o):xr(n),sn(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[o]=r}}(u,o,null),a)for(const i in a){const e=a[i];g(e)&&(o[i]=e.bind(n))}if(r){const t=r.call(n,n);b(t)&&(e.data=zt(t))}if(ir=!0,s)for(const h in s){const e=s[h],t=g(e)?e.bind(n,n):g(e.get)?e.get.bind(n,n):i,r=!g(e)&&g(e.set)?e.set.bind(n):i,a=Oi({get:t,set:r});Object.defineProperty(o,h,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(l)for(const i in l)lr(l[i],o,n,i);if(c){const e=g(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{wr(t,e[t])}))}function B(e,t){h(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&ar(d,e,"c"),B(Fo,f),B(Vo,p),B(Ho,m),B(Wo,v),B(Po,y),B($o,_),B(Go,L),B(Xo,k),B(Yo,E),B(zo,x),B(qo,T),B(Uo,O),h(P))if(P.length){const t=e.exposed||(e.exposed={});P.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});C&&e.render===i&&(e.render=C),null!=$&&(e.inheritAttrs=$),A&&(e.components=A),M&&(e.directives=M);const R=e.appContext.config.globalProperties.$applyOptions;R&&R(t,e,n)}function ar(e,t,n){mn(h(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function lr(e,t,n,o){const r=o.includes(".")?io(n,o):()=>n[o];if(v(e)){const n=t[e];g(n)&&no(r,n)}else if(g(e))no(r,e.bind(n));else if(b(e))if(h(e))e.forEach((e=>lr(e,t,n,o)));else{const o=g(e.handler)?e.handler.bind(n):t[e.handler];g(o)&&no(r,o,e)}}function cr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:r.length||n||o?(l={},r.length&&r.forEach((e=>ur(l,e,s,!0))),ur(l,t,s)):l=t,b(t)&&i.set(t,l),l}function ur(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&ur(e,i,n,!0),r&&r.forEach((t=>ur(e,t,n,!0)));for(const s in t)if(o&&"expose"===s);else{const o=dr[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const dr={data:fr,props:gr,emits:gr,methods:mr,computed:mr,beforeCreate:pr,created:pr,beforeMount:pr,mounted:pr,beforeUpdate:pr,updated:pr,beforeDestroy:pr,beforeUnmount:pr,destroyed:pr,unmounted:pr,activated:pr,deactivated:pr,errorCaptured:pr,serverPrefetch:pr,components:mr,directives:mr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=c(Object.create(null),e);for(const o in t)n[o]=pr(e[o],t[o]);return n},provide:fr,inject:function(e,t){return mr(hr(e),hr(t))}};function fr(e,t){return t?e?function(){return c(g(e)?e.call(this,this):e,g(t)?t.call(this,this):t)}:t:e}function hr(e){if(h(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function pr(e,t){return e?[...new Set([].concat(e,t))]:t}function mr(e,t){return e?c(Object.create(null),e,t):t}function gr(e,t){return e?h(e)&&h(t)?[...new Set([...e,...t])]:c(Object.create(null),rr(e),rr(null!=t?t:{})):t}function vr(){return{app:null,config:{isNativeTag:s,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let yr=0;function br(e,t){return function(n,o=null){g(n)||(n=c({},n)),null==o||b(o)||(o=null);const r=vr(),i=new WeakSet;let s=!1;const a=r.app={_uid:yr++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:$i,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&g(e.install)?(i.add(e),e.install(a,...t)):g(e)&&(i.add(e),e(a,...t))),a),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),a),component:(e,t)=>t?(r.components[e]=t,a):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,a):r.directives[e],mount(i,l,c){if(!s){const u=si(n,o);return u.appContext=r,!0===c?c="svg":!1===c&&(c=void 0),l&&t?t(u,i):e(u,i,c),s=!0,a._container=i,i.__vue_app__=a,a._instance=u.component,Ei(u.component)||u.component.proxy}},unmount(){s&&(e(null,a._container),delete a._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,a),runWithContext(e){const t=_r;_r=a;try{return e()}finally{_r=t}}};return a}}let _r=null;function wr(e,t){if(vi){let n=vi.provides;const o=vi.parent&&vi.parent.provides;o===n&&(n=vi.provides=Object.create(o)),n[e]=t,"app"===vi.type.mpType&&vi.appContext.app.provide(e,t)}else;}function xr(e,t,n=!1){const o=vi||Nn;if(o||_r){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:_r._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&g(t)?t.call(o&&o.proxy):t}}function Sr(e,t,n,r){const[i,s]=e.propsOptions;let a,l=!1;if(t)for(let o in t){if(C(o))continue;const c=t[o];let u;i&&f(i,u=L(o))?s&&s.includes(u)?(a||(a={}))[u]=c:n[u]=c:Dn(e.emitsOptions,o)||o in r&&c===r[o]||(r[o]=c,l=!0)}if(s){const t=Qt(n),r=a||o;for(let o=0;o<s.length;o++){const a=s[o];n[a]=Tr(i,t,a,r[a],e,!f(r,a))}}return l}function Tr(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=f(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&!s.skipFactory&&g(e)){const{propsDefaults:i}=r;if(n in i)o=i[n];else{const s=wi(r);o=i[n]=e.call(null,t),s()}}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==P(n)||(o=!0))}return o}function Cr(e,t,n=!1){const i=t.propsCache,s=i.get(e);if(s)return s;const a=e.props,l={},u=[];let d=!1;if(!g(e)){const o=e=>{d=!0;const[n,o]=Cr(e,t,!0);c(l,n),o&&u.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!a&&!d)return b(e)&&i.set(e,r),r;if(h(a))for(let r=0;r<a.length;r++){const e=L(a[r]);kr(e)&&(l[e]=o)}else if(a)for(const o in a){const e=L(o);if(kr(e)){const t=a[o],n=l[e]=h(t)||g(t)?{type:t}:c({},t);if(n){const t=Or(Boolean,n.type),o=Or(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||f(n,"default"))&&u.push(e)}}}const p=[l,u];return b(e)&&i.set(e,p),p}function kr(e){return"$"!==e[0]&&!C(e)}function Er(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function Lr(e,t){return Er(e)===Er(t)}function Or(e,t){return h(t)?t.findIndex((t=>Lr(t,e))):g(t)&&Lr(t,e)?0:-1}const Pr=e=>"_"===e[0]||"$stable"===e,$r=e=>h(e)?e.map(ui):[ui(e)],Ar=(e,t,n)=>{if(t._n)return t;const o=Hn(((...e)=>$r(t(...e))),n);return o._c=!1,o},Mr=(e,t,n)=>{const o=e._ctx;for(const r in e){if(Pr(r))continue;const n=e[r];if(g(n))t[r]=Ar(0,n,o);else if(null!=n){const e=$r(n);t[r]=()=>e}}},Ir=(e,t)=>{const n=$r(t);e.slots.default=()=>n};function Br(e,t,n,r,i=!1){if(h(e))return void e.forEach(((e,o)=>Br(e,t&&(h(t)?t[o]:t),n,r,i)));if(xo(r)&&!i)return;const s=4&r.shapeFlag?Ei(r.component)||r.component.proxy:r.el,a=i?null:s,{i:l,r:c}=e,d=t&&t.r,p=l.refs===o?l.refs={}:l.refs,m=l.setupState;if(null!=d&&d!==c&&(v(d)?(p[d]=null,f(m,d)&&(m[d]=null)):sn(d)&&(d.value=null)),g(c))pn(c,l,12,[a,p]);else{const t=v(c),o=sn(c);if(t||o){const r=()=>{if(e.f){const n=t?f(m,c)?m[c]:p[c]:c.value;i?h(n)&&u(n,s):h(n)?n.includes(s)||n.push(s):t?(p[c]=[s],f(m,c)&&(m[c]=p[c])):(c.value=[s],e.k&&(p[e.k]=c.value))}else t?(p[c]=a,f(m,c)&&(m[c]=a)):o&&(c.value=a,e.k&&(p[e.k]=a))};a?(r.id=-1,Rr(r,n)):r()}}}const Rr=function(e,t){var n;t&&t.pendingBranch?h(e)?t.effects.push(...e):t.effects.push(e):(h(n=e)?xn.push(...n):Sn&&Sn.includes(n,n.allowRecurse?Tn+1:Tn)||xn.push(n),On())};function jr(e){return function(e,t){D().__VUE__=!0;const{insert:n,remove:s,patchProp:a,forcePatchProp:l,createElement:u,createText:d,createComment:h,setText:p,setElementText:m,parentNode:g,nextSibling:v,setScopeId:y=i,insertStaticContent:b}=e,w=(e,t,n,o=null,r=null,i=null,s,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!ti(e,t)&&(o=te(e),K(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case Wr:x(e,t,n,o);break;case zr:S(e,t,n,o);break;case qr:null==e&&T(t,n,o,s);break;case Hr:F(e,t,n,o,r,i,s,a,l);break;default:1&d?O(e,t,n,o,r,i,s,a,l):6&d?V(e,t,n,o,r,i,s,a,l):(64&d||128&d)&&c.process(e,t,n,o,r,i,s,a,l,re)}null!=u&&r&&Br(u,e&&e.ref,i,t||e,!t)},x=(e,t,o,r)=>{if(null==e)n(t.el=d(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&p(n,t.children)}},S=(e,t,o,r)=>{null==e?n(t.el=h(t.children||""),o,r):t.el=e.el},T=(e,t,n,o)=>{[e.el,e.anchor]=b(e.children,t,n,o,e.el,e.anchor)},k=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=v(e),n(e,o,r),e=i;n(t,o,r)},E=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=v(e),s(e),e=n;s(t)},O=(e,t,n,o,r,i,s,a,l)=>{"svg"===t.type?s="svg":"math"===t.type&&(s="mathml"),null==e?$(t,n,o,r,i,s,a,l):R(e,t,r,i,s,a,l)},$=(e,t,o,r,i,s,l,c)=>{let d,f;const{props:h,shapeFlag:p,transition:g,dirs:v}=e;if(d=e.el=u(e.type,s,h&&h.is,h),8&p?m(d,e.children):16&p&&M(e.children,d,null,r,i,Dr(e,s),l,c),v&&lo(e,null,r,"created"),A(d,e,e.scopeId,l,r),h){for(const t in h)"value"===t||C(t)||a(d,t,null,h[t],s,e.children,r,i,ee);"value"in h&&a(d,"value",null,h.value,s),(f=h.onVnodeBeforeMount)&&pi(f,r,e)}Object.defineProperty(d,"__vueParentComponent",{value:r,enumerable:!1}),v&&lo(e,null,r,"beforeMount");const y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(i,g);y&&g.beforeEnter(d),n(d,t,o),((f=h&&h.onVnodeMounted)||y||v)&&Rr((()=>{f&&pi(f,r,e),y&&g.enter(d),v&&lo(e,null,r,"mounted")}),i)},A=(e,t,n,o,r)=>{if(n&&y(e,n),o)for(let i=0;i<o.length;i++)y(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;A(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},M=(e,t,n,o,r,i,s,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?di(e[c]):ui(e[c]);w(null,l,t,n,o,r,i,s,a)}},R=(e,t,n,r,i,s,c)=>{const u=t.el=e.el;let{patchFlag:d,dynamicChildren:f,dirs:h}=t;d|=16&e.patchFlag;const p=e.props||o,g=t.props||o;let v;if(n&&Nr(n,!1),(v=g.onVnodeBeforeUpdate)&&pi(v,n,t,e),h&&lo(t,e,n,"beforeUpdate"),n&&Nr(n,!0),f?j(e.dynamicChildren,f,u,n,r,Dr(t,i),s):c||U(e,t,u,null,n,r,Dr(t,i),s,!1),d>0){if(16&d)N(u,t,p,g,n,r,i);else if(2&d&&p.class!==g.class&&a(u,"class",null,g.class,i),4&d&&a(u,"style",p.style,g.style,i),8&d){const o=t.dynamicProps;for(let t=0;t<o.length;t++){const s=o[t],c=p[s],d=g[s];(d!==c||"value"===s||l&&l(u,s))&&a(u,s,c,d,i,e.children,n,r,ee)}}1&d&&e.children!==t.children&&m(u,t.children)}else c||null!=f||N(u,t,p,g,n,r,i);((v=g.onVnodeUpdated)||h)&&Rr((()=>{v&&pi(v,n,t,e),h&&lo(t,e,n,"updated")}),r)},j=(e,t,n,o,r,i,s)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===Hr||!ti(l,c)||70&l.shapeFlag)?g(l.el):n;w(l,c,u,null,o,r,i,s,!0)}},N=(e,t,n,r,i,s,c)=>{if(n!==r){if(n!==o)for(const o in n)C(o)||o in r||a(e,o,n[o],null,c,t.children,i,s,ee);for(const o in r){if(C(o))continue;const u=r[o],d=n[o];(u!==d&&"value"!==o||l&&l(e,o))&&a(e,o,d,u,c,t.children,i,s,ee)}"value"in r&&a(e,"value",n.value,r.value,c)}},F=(e,t,o,r,i,s,a,l,c)=>{const u=t.el=e?e.el:d(""),f=t.anchor=e?e.anchor:d("");let{patchFlag:h,dynamicChildren:p,slotScopeIds:m}=t;m&&(l=l?l.concat(m):m),null==e?(n(u,o,r),n(f,o,r),M(t.children||[],o,f,i,s,a,l,c)):h>0&&64&h&&p&&e.dynamicChildren?(j(e.dynamicChildren,p,o,i,s,a,l),(null!=t.key||i&&t===i.subTree)&&Fr(e,t,!0)):U(e,t,o,f,i,s,a,l,c)},V=(e,t,n,o,r,i,s,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,s,l):H(t,n,o,r,i,s,l):W(e,t,l)},H=(e,t,n,r,i,s,a)=>{const l=e.component=function(e,t,n){const r=e.type,i=(t?t.appContext:e.appContext)||mi,s={uid:gi++,vnode:e,type:r,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,scope:new Ne(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Cr(r,i),emitsOptions:jn(r,i),emit:null,emitted:null,propsDefaults:o,inheritAttrs:r.inheritAttrs,ctx:o,data:o,props:o,attrs:o,slots:o,refs:o,setupState:o,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};s.ctx={_:s},s.root=t?t.root:s,s.emit=Bn.bind(null,s),s.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(s);return s}(e,r,i);if(Co(e)&&(l.ctx.renderer=re),function(e,t=!1){t&&_i(t);const{props:n,children:o}=e.vnode,r=Si(e);(function(e,t,n,o=!1){const r={},i={};B(i,ni,1),e.propsDefaults=Object.create(null),Sr(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);n?e.props=o?r:qt(r):e.type.props?e.props=r:e.props=i,e.attrs=i})(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=Qt(t),B(t,"_",n)):Mr(t,e.slots={})}else e.slots={},t&&Ir(e,t);B(e.slots,ni,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Zt(new Proxy(e.ctx,or));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(it(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,r=wi(e);Xe();const i=pn(o,e,0,[e.props,n]);if(Ge(),r(),_(i)){if(i.then(xi,xi),t)return i.then((n=>{Ci(e,n,t)})).catch((t=>{gn(t,e,0)}));e.asyncDep=i}else Ci(e,i,t)}else ki(e,t)}(e,t):void 0;t&&_i(!1)}(l),l.asyncDep){if(i&&i.registerDep(l,z),!e.el){const e=l.subTree=si(zr);S(null,e,t,n)}}else z(l,e,t,n,i,s,a)},W=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:s,children:a,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!a||a&&a.$stable)||o!==s&&(o?!s||Un(o,s,c):!!s);if(1024&l)return!0;if(16&l)return o?Un(o,s,c):!!s;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(s[n]!==o[n]&&!Dn(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void q(o,t,n);o.next=t,function(e){const t=_n.indexOf(e);t>wn&&_n.splice(t,1)}(o.update),o.effect.dirty=!0,o.update()}else t.el=e.el,o.vnode=t},z=(e,t,n,o,r,s,a)=>{const l=()=>{if(e.isMounted){let{next:t,bu:n,u:o,parent:i,vnode:c}=e;{const n=Vr(e);if(n)return t&&(t.el=c.el,q(e,t,a)),void n.asyncDep.then((()=>{e.isUnmounted||l()}))}let u,d=t;Nr(e,!1),t?(t.el=c.el,q(e,t,a)):t=c,n&&I(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&pi(u,i,t,c),Nr(e,!0);const f=Wn(e),h=e.subTree;e.subTree=f,w(h,f,g(h.el),te(h),e,r,s),t.el=f.el,null===d&&function({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,f.el),o&&Rr(o,r),(u=t.props&&t.props.onVnodeUpdated)&&Rr((()=>pi(u,i,t,c)),r)}else{let i;const{el:a,props:l}=t,{bm:c,m:u,parent:d}=e,f=xo(t);if(Nr(e,!1),c&&I(c),!f&&(i=l&&l.onVnodeBeforeMount)&&pi(i,d,t),Nr(e,!0),a&&se){const n=()=>{e.subTree=Wn(e),se(a,e.subTree,e,r,null)};f?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const i=e.subTree=Wn(e);w(null,i,n,o,e,r,s),t.el=i.el}if(u&&Rr(u,r),!f&&(i=l&&l.onVnodeMounted)){const e=t;Rr((()=>pi(i,d,e)),r)}(256&t.shapeFlag||d&&xo(d.vnode)&&256&d.vnode.shapeFlag)&&(e.ba&&jo(e.ba),e.a&&Rr(e.a,r)),e.isMounted=!0,t=n=o=null}},c=e.effect=new Ve(l,i,(()=>Ln(u)),e.scope),u=e.update=()=>{c.dirty&&c.run()};u.id=e.uid,Nr(e,!0),u()},q=(e,t,n)=>{t.component=e;const r=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,a=Qt(r),[l]=e.propsOptions;let c=!1;if(!(o||s>0)||16&s){let o;Sr(e,t,r,i)&&(c=!0);for(const i in a)t&&(f(t,i)||(o=P(i))!==i&&f(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=Tr(l,a,i,void 0,e,!0)):delete r[i]);if(i!==a)for(const e in i)t&&f(t,e)||(delete i[e],c=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if(Dn(e.emitsOptions,s))continue;const u=t[s];if(l)if(f(i,s))u!==i[s]&&(i[s]=u,c=!0);else{const t=L(s);r[t]=Tr(l,a,t,u,e,!1)}else u!==i[s]&&(i[s]=u,c=!0)}}c&&st(e,"set","$attrs")}(e,t.props,r,n),((e,t,n)=>{const{vnode:r,slots:i}=e;let s=!0,a=o;if(32&r.shapeFlag){const e=t._;e?n&&1===e?s=!1:(c(i,t),n||1!==e||delete i._):(s=!t.$stable,Mr(t,i)),a=t}else t&&(Ir(e,t),a={default:1});if(s)for(const o in i)Pr(o)||null!=a[o]||delete i[o]})(e,t.children,n),Xe(),Pn(e),Ge()},U=(e,t,n,o,r,i,s,a,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:f,shapeFlag:h}=t;if(f>0){if(128&f)return void X(c,d,n,o,r,i,s,a,l);if(256&f)return void Y(c,d,n,o,r,i,s,a,l)}8&h?(16&u&&ee(c,r,i),d!==c&&m(n,d)):16&u?16&h?X(c,d,n,o,r,i,s,a,l):ee(c,r,i,!0):(8&u&&m(n,""),16&h&&M(d,n,o,r,i,s,a,l))},Y=(e,t,n,o,i,s,a,l,c)=>{t=t||r;const u=(e=e||r).length,d=t.length,f=Math.min(u,d);let h;for(h=0;h<f;h++){const o=t[h]=c?di(t[h]):ui(t[h]);w(e[h],o,n,null,i,s,a,l,c)}u>d?ee(e,i,s,!0,!1,f):M(t,n,o,i,s,a,l,c,f)},X=(e,t,n,o,i,s,a,l,c)=>{let u=0;const d=t.length;let f=e.length-1,h=d-1;for(;u<=f&&u<=h;){const o=e[u],r=t[u]=c?di(t[u]):ui(t[u]);if(!ti(o,r))break;w(o,r,n,null,i,s,a,l,c),u++}for(;u<=f&&u<=h;){const o=e[f],r=t[h]=c?di(t[h]):ui(t[h]);if(!ti(o,r))break;w(o,r,n,null,i,s,a,l,c),f--,h--}if(u>f){if(u<=h){const e=h+1,r=e<d?t[e].el:o;for(;u<=h;)w(null,t[u]=c?di(t[u]):ui(t[u]),n,r,i,s,a,l,c),u++}}else if(u>h)for(;u<=f;)K(e[u],i,s,!0),u++;else{const p=u,m=u,g=new Map;for(u=m;u<=h;u++){const e=t[u]=c?di(t[u]):ui(t[u]);null!=e.key&&g.set(e.key,u)}let v,y=0;const b=h-m+1;let _=!1,x=0;const S=new Array(b);for(u=0;u<b;u++)S[u]=0;for(u=p;u<=f;u++){const o=e[u];if(y>=b){K(o,i,s,!0);continue}let r;if(null!=o.key)r=g.get(o.key);else for(v=m;v<=h;v++)if(0===S[v-m]&&ti(o,t[v])){r=v;break}void 0===r?K(o,i,s,!0):(S[r-m]=u+1,r>=x?x=r:_=!0,w(o,t[r],n,null,i,s,a,l,c),y++)}const T=_?function(e){const t=e.slice(),n=[0];let o,r,i,s,a;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,s=n.length-1;i<s;)a=i+s>>1,e[n[a]]<l?i=a+1:s=a;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,s=n[i-1];for(;i-- >0;)n[i]=s,s=t[s];return n}(S):r;for(v=T.length-1,u=b-1;u>=0;u--){const e=m+u,r=t[e],f=e+1<d?t[e+1].el:o;0===S[u]?w(null,r,n,f,i,s,a,l,c):_&&(v<0||u!==T[v]?G(r,n,f,2):v--)}}},G=(e,t,o,r,i=null)=>{const{el:s,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void G(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void a.move(e,t,o,re);if(a===Hr){n(s,t,o);for(let e=0;e<c.length;e++)G(c[e],t,o,r);return void n(e.anchor,t,o)}if(a===qr)return void k(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(s),n(s,t,o),Rr((()=>l.enter(s)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,a=()=>n(s,t,o),c=()=>{e(s,(()=>{a(),i&&i()}))};r?r(s,a,c):c()}else n(s,t,o)},K=(e,t,n,o=!1,r=!1)=>{const{type:i,props:s,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:f}=e;if(null!=a&&Br(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const h=1&u&&f,p=!xo(e);let m;if(p&&(m=s&&s.onVnodeBeforeUnmount)&&pi(m,t,e),6&u)Z(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);h&&lo(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,re,o):c&&(i!==Hr||d>0&&64&d)?ee(c,t,n,!1,!0):(i===Hr&&384&d||!r&&16&u)&&ee(l,t,n),o&&J(e)}(p&&(m=s&&s.onVnodeUnmounted)||h)&&Rr((()=>{m&&pi(m,t,e),h&&lo(e,null,t,"unmounted")}),n)},J=e=>{const{type:t,el:n,anchor:o,transition:r}=e;if(t===Hr)return void Q(n,o);if(t===qr)return void E(e);const i=()=>{s(n),r&&!r.persisted&&r.afterLeave&&r.afterLeave()};if(1&e.shapeFlag&&r&&!r.persisted){const{leave:t,delayLeave:o}=r,s=()=>t(n,i);o?o(e.el,i,s):s()}else i()},Q=(e,t)=>{let n;for(;e!==t;)n=v(e),s(e),e=n;s(t)},Z=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:s,um:a}=e;o&&I(o),r.stop(),i&&(i.active=!1,K(s,e,t,n)),a&&Rr(a,t),Rr((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let s=i;s<e.length;s++)K(e[s],t,n,o,r)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():v(e.anchor||e.el);let ne=!1;const oe=(e,t,n)=>{null==e?t._vnode&&K(t._vnode,null,null,!0):w(t._vnode||null,e,t,null,null,null,n),ne||(ne=!0,Pn(),$n(),ne=!1),t._vnode=e},re={p:w,um:K,m:G,r:J,mt:H,mc:M,pc:U,pbc:j,n:te,o:e};let ie,se;t&&([ie,se]=t(re));return{render:oe,hydrate:ie,createApp:br(oe,ie)}}(e)}function Dr({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Nr({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Fr(e,t,n=!1){const o=e.children,r=t.children;if(h(o)&&h(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=di(r[i]),t.el=e.el),n||Fr(e,t)),t.type===Wr&&(t.el=e.el)}}function Vr(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Vr(t)}const Hr=Symbol.for("v-fgt"),Wr=Symbol.for("v-txt"),zr=Symbol.for("v-cmt"),qr=Symbol.for("v-stc"),Ur=[];let Yr=null;function Xr(e=!1){Ur.push(Yr=e?null:[])}let Gr=1;function Kr(e){Gr+=e}function Jr(e){return e.dynamicChildren=Gr>0?Yr||r:null,Ur.pop(),Yr=Ur[Ur.length-1]||null,Gr>0&&Yr&&Yr.push(e),e}function Qr(e,t,n,o,r,i){return Jr(ii(e,t,n,o,r,i,!0))}function Zr(e,t,n,o,r){return Jr(si(e,t,n,o,r,!0))}function ei(e){return!!e&&!0===e.__v_isVNode}function ti(e,t){return e.type===t.type&&e.key===t.key}const ni="__vInternal",oi=({key:e})=>null!=e?e:null,ri=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?v(e)||sn(e)||g(e)?{i:Nn,r:e,k:t,f:!!n}:e:null);function ii(e,t=null,n=null,o=0,r=null,i=(e===Hr?0:1),s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&oi(t),ref:t&&ri(t),scopeId:Fn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Nn};return a?(fi(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=v(n)?8:16),Gr>0&&!s&&Yr&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&Yr.push(l),l}const si=function(e,t=null,n=null,o=0,r=null,i=!1){e&&e!==Xn||(e=zr);if(ei(e)){const o=ai(e,t,!0);return n&&fi(o,n),Gr>0&&!i&&Yr&&(6&o.shapeFlag?Yr[Yr.indexOf(e)]=o:Yr.push(o)),o.patchFlag|=-2,o}s=e,g(s)&&"__vccOpts"in s&&(e=e.__vccOpts);var s;if(t){t=function(e){return e?Jt(e)||ni in e?c({},e):e:null}(t);let{class:e,style:n}=t;e&&!v(e)&&(t.class=me(e)),b(n)&&(Jt(n)&&!h(n)&&(n=c({},n)),t.style=pe(n))}const a=v(e)?1:Qn(e)?128:(e=>e.__isTeleport)(e)?64:b(e)?4:g(e)?2:0;return ii(e,t,n,o,r,a,i,!0)};function ai(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:s}=e,a=t?hi(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&oi(a),ref:t&&t.ref?n&&r?h(r)?r.concat(ri(t)):[r,ri(t)]:ri(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Hr?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ai(e.ssContent),ssFallback:e.ssFallback&&ai(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function li(e=" ",t=0){return si(Wr,null,e,t)}function ci(e="",t=!1){return t?(Xr(),Zr(zr,null,e)):si(zr,null,e)}function ui(e){return null==e||"boolean"==typeof e?si(zr):h(e)?si(Hr,null,e.slice()):"object"==typeof e?di(e):si(Wr,null,String(e))}function di(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:ai(e)}function fi(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(h(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),fi(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||ni in t?3===o&&Nn&&(1===Nn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Nn}}else g(t)?(t={default:t,_ctx:Nn},n=32):(t=String(t),64&o?(n=16,t=[li(t)]):n=8);e.children=t,e.shapeFlag|=n}function hi(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=me([t.class,o.class]));else if("style"===e)t.style=pe([t.style,o.style]);else if(a(e)){const n=t[e],r=o[e];!r||n===r||h(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function pi(e,t,n,o=null){mn(e,t,7,[n,o])}const mi=vr();let gi=0;let vi=null;const yi=()=>vi||Nn;let bi,_i;{const e=D(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach((t=>t(e))):o[0](e)}};bi=t("__VUE_INSTANCE_SETTERS__",(e=>vi=e)),_i=t("__VUE_SSR_SETTERS__",(e=>Ti=e))}const wi=e=>{const t=vi;return bi(e),e.scope.on(),()=>{e.scope.off(),bi(t)}},xi=()=>{vi&&vi.scope.off(),bi(null)};function Si(e){return 4&e.vnode.shapeFlag}let Ti=!1;function Ci(e,t,n){g(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:b(t)&&(e.setupState=hn(t)),ki(e,n)}function ki(e,t,n){const o=e.type;e.render||(e.render=o.render||i);{const t=wi(e);Xe();try{sr(e)}finally{Ge(),t()}}}function Ei(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(hn(Zt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in tr?tr[n](e):void 0,has:(e,t)=>t in e||t in tr}))}function Li(e,t=!0){return g(e)?e.displayName||e.name:e.name||t&&e.__name}const Oi=(e,t)=>{const n=function(e,t,n=!1){let o,r;const s=g(e);return s?(o=e,r=i):(o=e.get,r=e.set),new nn(o,r,s||!r,n)}(e,0,Ti);return n};function Pi(e,t,n){const o=arguments.length;return 2===o?b(t)&&!h(t)?ei(t)?si(e,null,[t]):si(e,t):si(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&ei(n)&&(n=[n]),si(e,t,n))}const $i="3.4.21",Ai="undefined"!=typeof document?document:null,Mi=Ai&&Ai.createElement("template"),Ii={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r="svg"===t?Ai.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?Ai.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?Ai.createElement(e,{is:n}):Ai.createElement(e);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>Ai.createTextNode(e),createComment:e=>Ai.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ai.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const s=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{Mi.innerHTML="svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e;const r=Mi.content;if("svg"===o||"mathml"===o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Bi="transition",Ri=Symbol("_vtc"),ji=(e,{slots:t})=>Pi(po,function(e){const t={};for(const c in e)c in Di||(t[c]=e[c]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:s=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:u=s,appearToClass:d=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:h=`${n}-leave-active`,leaveToClass:p=`${n}-leave-to`}=e,m=function(e){if(null==e)return null;if(b(e))return[Vi(e.enter),Vi(e.leave)];{const t=Vi(e);return[t,t]}}(r),g=m&&m[0],v=m&&m[1],{onBeforeEnter:y,onEnter:_,onEnterCancelled:w,onLeave:x,onLeaveCancelled:S,onBeforeAppear:T=y,onAppear:C=_,onAppearCancelled:k=w}=t,E=(e,t,n)=>{Wi(e,t?d:a),Wi(e,t?u:s),n&&n()},L=(e,t)=>{e._isLeaving=!1,Wi(e,f),Wi(e,p),Wi(e,h),t&&t()},O=e=>(t,n)=>{const r=e?C:_,s=()=>E(t,e,n);Ni(r,[t,s]),zi((()=>{Wi(t,e?l:i),Hi(t,e?d:a),Fi(r)||Ui(t,o,g,s)}))};return c(t,{onBeforeEnter(e){Ni(y,[e]),Hi(e,i),Hi(e,s)},onBeforeAppear(e){Ni(T,[e]),Hi(e,l),Hi(e,u)},onEnter:O(!1),onAppear:O(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>L(e,t);Hi(e,f),document.body.offsetHeight,Hi(e,h),zi((()=>{e._isLeaving&&(Wi(e,f),Hi(e,p),Fi(x)||Ui(e,o,v,n))})),Ni(x,[e,n])},onEnterCancelled(e){E(e,!1),Ni(w,[e])},onAppearCancelled(e){E(e,!0),Ni(k,[e])},onLeaveCancelled(e){L(e),Ni(S,[e])}})}(e),t);ji.displayName="Transition";const Di={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};ji.props=c({},ho,Di);const Ni=(e,t=[])=>{h(e)?e.forEach((e=>e(...t))):e&&e(...t)},Fi=e=>!!e&&(h(e)?e.some((e=>e.length>1)):e.length>1);function Vi(e){const t=(e=>{const t=v(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function Hi(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[Ri]||(e[Ri]=new Set)).add(t)}function Wi(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const n=e[Ri];n&&(n.delete(t),n.size||(e[Ri]=void 0))}function zi(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let qi=0;function Ui(e,t,n,o){const r=e._endId=++qi,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:s,timeout:a,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o("transitionDelay"),i=o("transitionDuration"),s=Yi(r,i),a=o("animationDelay"),l=o("animationDuration"),c=Yi(a,l);let u=null,d=0,f=0;t===Bi?s>0&&(u=Bi,d=s,f=i.length):"animation"===t?c>0&&(u="animation",d=c,f=l.length):(d=Math.max(s,c),u=d>0?s>c?Bi:"animation":null,f=u?u===Bi?i.length:l.length:0);const h=u===Bi&&/\b(transform|all)(,|$)/.test(o("transitionProperty").toString());return{type:u,timeout:d,propCount:f,hasTransform:h}}(e,t);if(!s)return o();const c=s+"end";let u=0;const d=()=>{e.removeEventListener(c,f),i()},f=t=>{t.target===e&&++u>=l&&d()};setTimeout((()=>{u<l&&d()}),a+1),e.addEventListener(c,f)}function Yi(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>Xi(t)+Xi(e[n]))))}function Xi(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}const Gi=Symbol("_vod"),Ki=Symbol("_vsh"),Ji={beforeMount(e,{value:t},{transition:n}){e[Gi]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Qi(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Qi(e,!0),o.enter(e)):o.leave(e,(()=>{Qi(e,!1)})):Qi(e,t))},beforeUnmount(e,{value:t}){Qi(e,t)}};function Qi(e,t){e.style.display=t?e[Gi]:"none",e[Ki]=!t}const Zi=Symbol(""),es=/(^|;)\s*display\s*:/;const ts=/\s*!important$/;function ns(e,t,n){if(h(n))n.forEach((n=>ns(e,t,n)));else if(null==n&&(n=""),n=fs(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=rs[t];if(n)return n;let o=L(t);if("filter"!==o&&o in e)return rs[t]=o;o=$(o);for(let r=0;r<os.length;r++){const n=os[r]+o;if(n in e)return rs[t]=n}return t}(e,t);ts.test(n)?e.setProperty(P(o),n.replace(ts,""),"important"):e[o]=n}}const os=["Webkit","Moz","ms"],rs={};const{unit:is,unitRatio:ss,unitPrecision:as}={unit:"rem",unitRatio:10/320,unitPrecision:5},ls=(cs=is,us=ss,ds=as,e=>e.replace(be,((e,t)=>{if(!t)return e;if(1===us)return`${t}${cs}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*us,ds);return 0===n?"0":`${n}${cs}`})));var cs,us,ds;const fs=e=>v(e)?ls(e):e,hs="http://www.w3.org/1999/xlink";const ps=Symbol("_vei");function ms(e,t,n,o,r=null){const i=e[ps]||(e[ps]={}),s=i[t];if(o&&s)s.value=o;else{const[n,a]=function(e){let t;if(gs.test(e)){let n;for(t={};n=e.match(gs);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):P(e.slice(2)),t]}(t);if(o){const s=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&h(i)){const n=bs(e,i);for(let o=0;o<n.length;o++){const i=n[o];mn(i,t,5,i.__wwe?[e]:r(e))}}else mn(bs(e,n.value),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=(()=>vs||(ys.then((()=>vs=0)),vs=Date.now()))(),n}(o,r);!function(e,t,n,o){e.addEventListener(t,n,o)}(e,n,s,a)}else s&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,s,a),i[t]=void 0)}}const gs=/(?:Once|Passive|Capture)$/;let vs=0;const ys=Promise.resolve();function bs(e,t){if(h(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const _s=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const ws=["ctrl","shift","alt","meta"],xs={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>ws.some((n=>e[`${n}Key`]&&!t.includes(n)))},Ss=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(n,...o)=>{for(let e=0;e<t.length;e++){const o=xs[t[e]];if(o&&o(n,t))return}return e(n,...o)})},Ts=c({patchProp:(e,t,n,o,r,i,s,c,u)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,s=i[r],a=(e.__wxsProps||(e.__wxsProps={}))[r];if(a===s)return;e.__wxsProps[r]=s;const l=o.proxy;En((()=>{n(s,a,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,s);const d="svg"===r;"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e[Ri];i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,d):"style"===t?function(e,t,n){const o=e.style,r=v(n);let i=!1;if(n&&!r){if(t)if(v(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&ns(o,t,"")}else for(const e in t)null==n[e]&&ns(o,e,"");for(const e in n)"display"===e&&(i=!0),ns(o,e,n[e])}else if(r){if(t!==n){const e=o[Zi];e&&(n+=";"+e),o.cssText=n,i=es.test(n)}}else t&&e.removeAttribute("style");Gi in e&&(e[Gi]=i?o.display:"",e[Ki]&&(o.display="none"));const{__wxsStyle:s}=e;if(s)for(const a in s)ns(o,a,s[a])}(e,n,o):a(t)?l(t)||ms(e,t,0,o,s):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&_s(t)&&g(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(_s(t)&&v(n))return!1;return t in e}(e,t,o,d))?function(e,t,n,o,r,i,s){if("innerHTML"===t||"textContent"===t)return o&&s(o,r,i),void(e[t]=null==n?"":n);const a=e.tagName;if("value"===t&&"PROGRESS"!==a&&!a.includes("-")){const o=null==n?"":n;return("OPTION"===a?e.getAttribute("value")||"":e.value)===o&&"_value"in e||(e.value=o),null==n&&e.removeAttribute(t),void(e._value=n)}let l=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=U(n):null==n&&"string"===o?(n="",l=!0):"number"===o&&(n=0,l=!0)}try{e[t]=n}catch(c){}l&&e.removeAttribute(t)}(e,t,o,i,s,c,u):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,r){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(hs,t.slice(6,t.length)):e.setAttributeNS(hs,t,n);else{const o=q(t);null==n||o&&!U(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,d))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},Ii);let Cs;const ks=(...e)=>{const t=(Cs||(Cs=jr(Ts))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(v(e)){return document.querySelector(e)}return e}
/*!
  * vue-router v4.3.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */(e);if(!o)return;const r=t._component;g(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const Es="undefined"!=typeof document;const Ls=Object.assign;function Os(e,t){const n={};for(const o in t){const r=t[o];n[o]=$s(r)?r.map(e):e(r)}return n}const Ps=()=>{},$s=Array.isArray,As=/#/g,Ms=/&/g,Is=/\//g,Bs=/=/g,Rs=/\?/g,js=/\+/g,Ds=/%5B/g,Ns=/%5D/g,Fs=/%5E/g,Vs=/%60/g,Hs=/%7B/g,Ws=/%7C/g,zs=/%7D/g,qs=/%20/g;function Us(e){return encodeURI(""+e).replace(Ws,"|").replace(Ds,"[").replace(Ns,"]")}function Ys(e){return Us(e).replace(js,"%2B").replace(qs,"+").replace(As,"%23").replace(Ms,"%26").replace(Vs,"`").replace(Hs,"{").replace(zs,"}").replace(Fs,"^")}function Xs(e){return null==e?"":function(e){return Us(e).replace(As,"%23").replace(Rs,"%3F")}(e).replace(Is,"%2F")}function Gs(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Ks=/\/$/;function Js(e,t,n="/"){let o,r={},i="",s="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),r=e(i)),a>-1&&(o=o||t.slice(0,a),s=t.slice(a,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let i,s,a=n.length-1;for(i=0;i<o.length;i++)if(s=o[i],"."!==s){if(".."!==s)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+o.slice(i).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+s,path:o,query:r,hash:Gs(s)}}function Qs(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Zs(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ea(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!ta(e[n],t[n]))return!1;return!0}function ta(e,t){return $s(e)?na(e,t):$s(t)?na(t,e):e===t}function na(e,t){return $s(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}var oa,ra,ia,sa;function aa(e){if(!e)if(Es){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Ks,"")}(ra=oa||(oa={})).pop="pop",ra.push="push",(sa=ia||(ia={})).back="back",sa.forward="forward",sa.unknown="";const la=/^[^#]+#/;function ca(e,t){return e.replace(la,"#")+t}const ua=()=>({left:window.scrollX,top:window.scrollY});function da(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function fa(e,t){return(history.state?history.state.position-t:-1)+e}const ha=new Map;function pa(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),Qs(n,"")}return Qs(n,e)+o+r}function ma(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?ua():null}}function ga(e){const{history:t,location:n}=window,o={value:pa(e,n)},r={value:t.state};function i(o,i,s){const a=e.indexOf("#"),l=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+o:location.protocol+"//"+location.host+e+o;try{t[s?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){console.error(c),n[s?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const s=Ls({},r.value,t.state,{forward:e,scroll:ua()});i(s.current,s,!0),i(e,Ls({},ma(o.value,e,null),{position:s.position+1},n),!1),o.value=e},replace:function(e,n){i(e,Ls({},t.state,ma(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function va(e){const t=ga(e=aa(e)),n=function(e,t,n,o){let r=[],i=[],s=null;const a=({state:i})=>{const a=pa(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=a,t.value=i,s&&s===l)return void(s=null);u=c?i.position-c.position:0}else o(a);r.forEach((e=>{e(n.value,l,{delta:u,type:oa.pop,direction:u?u>0?ia.forward:ia.back:ia.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(Ls({},e.state,{scroll:ua()}),"")}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){s=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=Ls({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:ca.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function ya(e){return"string"==typeof e||"symbol"==typeof e}const ba={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},_a=Symbol("");var wa,xa;function Sa(e,t){return Ls(new Error,{type:e,[_a]:!0},t)}function Ta(e,t){return e instanceof Error&&_a in e&&(null==t||!!(e.type&t))}(xa=wa||(wa={}))[xa.aborted=4]="aborted",xa[xa.cancelled=8]="cancelled",xa[xa.duplicated=16]="duplicated";const Ca={sensitive:!1,strict:!1,start:!0,end:!0},ka=/[.+*?^${}()[\]/\\]/g;function Ea(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function La(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=Ea(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(Oa(o))return 1;if(Oa(r))return-1}return r.length-o.length}function Oa(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Pa={type:0,value:""},$a=/[a-zA-Z0-9_]/;function Aa(e,t,n){const o=function(e,t){const n=Ls({},Ca,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let s=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(ka,"\\$&"),s+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const d=u||"[^/]+?";if("[^/]+?"!==d){s+=10;try{new RegExp(`(${d})`)}catch(a){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+a.message)}}let f=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(f=c&&l.length<2?`(?:/${f})`:"/"+f),c&&(f+="?"),r+=f,s+=20,c&&(s+=-8),n&&(s+=-20),".*"===d&&(s+=-50)}e.push(s)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const s=new RegExp(r,n.sensitive?"":"i");return{re:s,score:o,keys:i,parse:function(e){const t=e.match(s),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:s,optional:a}=e,l=i in t?t[i]:"";if($s(l)&&!s)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=$s(l)?l.join("/"):l;if(!c){if(!a)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Pa]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function s(){i&&r.push(i),i=[]}let a,l=0,c="",u="";function d(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),c="")}function f(){c+=a}for(;l<e.length;)if(a=e[l++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(c&&d(),s()):":"===a?(d(),n=1):f();break;case 4:f(),n=o;break;case 1:"("===a?n=2:$a.test(a)?f():(d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--);break;case 2:")"===a?"\\"==u[u.length-1]?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),d(),s(),r}(e.path),n),r=Ls(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Ma(e,t){const n=[],o=new Map;function r(e,n,o){const a=!o,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:Ba(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);l.aliasOf=o&&o.record;const c=Da(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(Ls({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l}))}let d,f;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(d=Aa(t,n,c),o?o.alias.push(d):(f=f||d,f!==d&&f.alias.push(d),a&&e.name&&!Ra(d)&&i(e.name)),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],d,o&&o.children[t])}o=o||d,(d.record.components&&Object.keys(d.record.components).length||d.record.name||d.record.redirect)&&s(d)}return f?()=>{i(f)}:Ps}function i(e){if(ya(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function s(e){let t=0;for(;t<n.length&&La(e,n[t])>=0&&(e.record.path!==n[t].record.path||!Na(e,n[t]));)t++;n.splice(t,0,e),e.record.name&&!Ra(e)&&o.set(e.record.name,e)}return t=Da({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,i,s,a={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw Sa(1,{location:e});s=r.record.name,a=Ls(Ia(t.params,r.keys.filter((e=>!e.optional)).concat(r.parent?r.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&Ia(e.params,r.keys.map((e=>e.name)))),i=r.stringify(a)}else if(null!=e.path)i=e.path,r=n.find((e=>e.re.test(i))),r&&(a=r.parse(i),s=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw Sa(1,{location:e,currentLocation:t});s=r.record.name,a=Ls({},t.params,e.params),i=r.stringify(a)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:s,path:i,params:a,matched:l,meta:ja(l)}},removeRoute:i,getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function Ia(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function Ba(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function Ra(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ja(e){return e.reduce(((e,t)=>Ls(e,t.meta)),{})}function Da(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function Na(e,t){return t.children.some((t=>t===e||Na(e,t)))}function Fa(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(js," "),r=e.indexOf("="),i=Gs(r<0?e:e.slice(0,r)),s=r<0?null:Gs(e.slice(r+1));if(i in t){let e=t[i];$s(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function Va(e){let t="";for(let n in e){const o=e[n];if(n=Ys(n).replace(Bs,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}($s(o)?o.map((e=>e&&Ys(e))):[o&&Ys(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Ha(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=$s(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const Wa=Symbol(""),za=Symbol(""),qa=Symbol(""),Ua=Symbol(""),Ya=Symbol("");function Xa(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Ga(e,t,n,o,r,i=(e=>e())){const s=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((a,l)=>{const c=e=>{var i;!1===e?l(Sa(4,{from:n,to:t})):e instanceof Error?l(e):"string"==typeof(i=e)||i&&"object"==typeof i?l(Sa(2,{from:t,to:e})):(s&&o.enterCallbacks[r]===s&&"function"==typeof e&&s.push(e),a())},u=i((()=>e.call(o&&o.instances[r],t,n,c)));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch((e=>l(e)))}))}function Ka(e,t,n,o,r=(e=>e())){const i=[];for(const a of e)for(const e in a.components){let l=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if("object"==typeof(s=l)||"displayName"in s||"props"in s||"__vccOpts"in s){const s=(l.__vccOpts||l)[t];s&&i.push(Ga(s,n,o,a,e,r))}else{let s=l();i.push((()=>s.then((i=>{if(!i)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${a.path}"`));const s=(l=i).__esModule||"Module"===l[Symbol.toStringTag]?i.default:i;var l;a.components[e]=s;const c=(s.__vccOpts||s)[t];return c&&Ga(c,n,o,a,e,r)()}))))}}var s;return i}function Ja(e){const t=xr(qa),n=xr(Ua),o=Oi((()=>t.resolve(dn(e.to)))),r=Oi((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const s=i.findIndex(Zs.bind(null,r));if(s>-1)return s;const a=Za(e[t-2]);return t>1&&Za(r)===a&&i[i.length-1].path!==a?i.findIndex(Zs.bind(null,e[t-2])):s})),i=Oi((()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!$s(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,o.value.params))),s=Oi((()=>r.value>-1&&r.value===n.matched.length-1&&ea(n.params,o.value.params)));return{route:o,href:Oi((()=>o.value.href)),isActive:i,isExactActive:s,navigate:function(n={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)?t[dn(e.replace)?"replace":"push"](dn(e.to)).catch(Ps):Promise.resolve()}}}const Qa=wo({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Ja,setup(e,{slots:t}){const n=zt(Ja(e)),{options:o}=xr(qa),r=Oi((()=>({[el(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[el(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:Pi("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}});function Za(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const el=(e,t,n)=>null!=e?e:null!=t?t:n;function tl(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const nl=wo({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=xr(Ya),r=Oi((()=>e.route||o.value)),i=xr(za,0),s=Oi((()=>{let e=dn(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),a=Oi((()=>r.value.matched[s.value]));wr(za,Oi((()=>s.value+1))),wr(Wa,a),wr(Ya,r);const l=an();return no((()=>[l.value,a.value,e.name]),(([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&Zs(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,i=e.name,s=a.value,c=s&&s.components[i];if(!c)return tl(n.default,{Component:c,route:o});const u=s.props[i],d=u?!0===u?o.params:"function"==typeof u?u(o):u:null,f=Pi(c,Ls({},d,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(s.instances[i]=null)},ref:l}));return tl(n.default,{Component:f,route:o})||f}}});function ol(e){const t=Ma(e.routes,e),n=e.parseQuery||Fa,o=e.stringifyQuery||Va,r=e.history,i=Xa(),s=Xa(),a=Xa(),l=ln(ba);let c=ba;Es&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Os.bind(null,(e=>""+e)),d=Os.bind(null,Xs),f=Os.bind(null,Gs);function h(e,i){if(i=Ls({},i||l.value),"string"==typeof e){const o=Js(n,e,i.path),s=t.resolve({path:o.path},i),a=r.createHref(o.fullPath);return Ls(o,s,{params:f(s.params),hash:Gs(o.hash),redirectedFrom:void 0,href:a})}let s;if(null!=e.path)s=Ls({},e,{path:Js(n,e.path,i.path).path});else{const t=Ls({},e.params);for(const e in t)null==t[e]&&delete t[e];s=Ls({},e,{params:d(t)}),i.params=d(i.params)}const a=t.resolve(s,i),c=e.hash||"";a.params=u(f(a.params));const h=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,Ls({},e,{hash:(p=c,Us(p).replace(Hs,"{").replace(zs,"}").replace(Fs,"^")),path:a.path}));var p;const m=r.createHref(h);return Ls({fullPath:h,hash:c,query:o===Va?Ha(e.query):e.query||{}},a,{redirectedFrom:void 0,href:m})}function p(e){return"string"==typeof e?Js(n,e,l.value.path):Ls({},e)}function m(e,t){if(c!==e)return Sa(8,{from:t,to:e})}function g(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=p(o):{path:o},o.params={}),Ls({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function y(e,t){const n=c=h(e),r=l.value,i=e.state,s=e.force,a=!0===e.replace,u=v(n);if(u)return y(Ls(p(u),{state:"object"==typeof u?Ls({},i,u.state):i,force:s,replace:a}),t||n);const d=n;let f;return d.redirectedFrom=t,!s&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&Zs(t.matched[o],n.matched[r])&&ea(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(f=Sa(16,{to:d,from:r}),$(r,r,!0,!1)),(f?Promise.resolve(f):w(d,r)).catch((e=>Ta(e)?Ta(e,2)?e:P(e):O(e,d,r))).then((e=>{if(e){if(Ta(e,2))return y(Ls({replace:a},p(e.to),{state:"object"==typeof e.to?Ls({},i,e.to.state):i,force:s}),t||d)}else e=S(d,r,!0,a,i);return x(d,r,e),e}))}function b(e,t){const n=m(e,t);return n?Promise.reject(n):Promise.resolve()}function _(e){const t=I.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function w(e,t){let n;const[o,r,a]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let s=0;s<i;s++){const i=t.matched[s];i&&(e.matched.find((e=>Zs(e,i)))?o.push(i):n.push(i));const a=e.matched[s];a&&(t.matched.find((e=>Zs(e,a)))||r.push(a))}return[n,o,r]}(e,t);n=Ka(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach((o=>{n.push(Ga(o,e,t))}));const l=b.bind(null,e,t);return n.push(l),R(n).then((()=>{n=[];for(const o of i.list())n.push(Ga(o,e,t));return n.push(l),R(n)})).then((()=>{n=Ka(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(Ga(o,e,t))}));return n.push(l),R(n)})).then((()=>{n=[];for(const o of a)if(o.beforeEnter)if($s(o.beforeEnter))for(const r of o.beforeEnter)n.push(Ga(r,e,t));else n.push(Ga(o.beforeEnter,e,t));return n.push(l),R(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Ka(a,"beforeRouteEnter",e,t,_),n.push(l),R(n)))).then((()=>{n=[];for(const o of s.list())n.push(Ga(o,e,t));return n.push(l),R(n)})).catch((e=>Ta(e,8)?e:Promise.reject(e)))}function x(e,t,n){a.list().forEach((o=>_((()=>o(e,t,n)))))}function S(e,t,n,o,i){const s=m(e,t);if(s)return s;const a=t===ba,c=Es?history.state:{};n&&(o||a?r.replace(e.fullPath,Ls({scroll:a&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,$(e,t,n,a),P()}let T;function C(){T||(T=r.listen(((e,t,n)=>{if(!B.listening)return;const o=h(e),i=v(o);if(i)return void y(Ls(i,{replace:!0}),o).catch(Ps);c=o;const s=l.value;var a,u;Es&&(a=fa(s.fullPath,n.delta),u=ua(),ha.set(a,u)),w(o,s).catch((e=>Ta(e,12)?e:Ta(e,2)?(y(e.to,o).then((e=>{Ta(e,20)&&!n.delta&&n.type===oa.pop&&r.go(-1,!1)})).catch(Ps),Promise.reject()):(n.delta&&r.go(-n.delta,!1),O(e,o,s)))).then((e=>{(e=e||S(o,s,!1))&&(n.delta&&!Ta(e,8)?r.go(-n.delta,!1):n.type===oa.pop&&Ta(e,20)&&r.go(-1,!1)),x(o,s,e)})).catch(Ps)})))}let k,E=Xa(),L=Xa();function O(e,t,n){P(e);const o=L.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function P(e){return k||(k=!e,C(),E.list().forEach((([t,n])=>e?n(e):t())),E.reset()),e}function $(t,n,o,r){const{scrollBehavior:i}=e;if(!Es||!i)return Promise.resolve();const s=!o&&function(e){const t=ha.get(e);return ha.delete(e),t}(fa(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return En().then((()=>i(t,n,s))).then((e=>e&&da(e))).catch((e=>O(e,t,n)))}const A=e=>r.go(e);let M;const I=new Set,B={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return ya(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:h,options:e,push:g,replace:function(e){return g(Ls(p(e),{replace:!0}))},go:A,back:()=>A(-1),forward:()=>A(1),beforeEach:i.add,beforeResolve:s.add,afterEach:a.add,onError:L.add,isReady:function(){return k&&l.value!==ba?Promise.resolve():new Promise(((e,t)=>{E.add([e,t])}))},install(e){e.component("RouterLink",Qa),e.component("RouterView",nl),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>dn(l)}),Es&&!M&&l.value===ba&&(M=!0,g(r.location).catch((e=>{})));const t={};for(const o in ba)Object.defineProperty(t,o,{get:()=>l.value[o],enumerable:!0});e.provide(qa,this),e.provide(Ua,qt(t)),e.provide(Ya,l);const n=e.unmount;I.add(e),e.unmount=function(){I.delete(e),I.size<1&&(c=ba,T&&T(),T=null,l.value=ba,M=!1,k=!1),n()}}};function R(e){return e.reduce(((e,t)=>e.then((()=>_(t)))),Promise.resolve())}return B}function rl(){return xr(Ua)}const il=["{","}"];const sl=/^(?:\d)+/,al=/^(?:\w)+/;const ll=Object.prototype.hasOwnProperty,cl=(e,t)=>ll.call(e,t),ul=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=il){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let s=e[r++];if(s===t){i&&o.push({type:"text",value:i}),i="";let t="";for(s=e[r++];void 0!==s&&s!==n;)t+=s,s=e[r++];const a=s===n,l=sl.test(t)?"list":a&&al.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=s}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function dl(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class fl{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||ul,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=dl(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{cl(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=dl(t,this.messages))&&(o=this.messages[t]):n=t,cl(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function hl(e,t={},n,o){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e="undefined"!=typeof uni&&Xd?Xd():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const r=new fl({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=Tm().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}function pl(e,t){return e.indexOf(t[0])>-1}const ml=ie((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let gl;function vl(e){return pl(e,ee)?_l().f(e,function(){const e=Xd(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}(),ee):e}function yl(e,t){if(1===t.length){if(e){const n=e=>v(e)&&pl(e,ee),o=t[0];let r=[];if(h(e)&&(r=e.filter((e=>n(e[o])))).length)return r;const i=e[t[0]];if(n(i))return e}return}const n=t.shift();return yl(e&&e[n],t)}function bl(e,t){const n=yl(e,t);if(!n)return!1;const o=t[t.length-1];if(h(n))n.forEach((e=>bl(e,[o])));else{let e=n[o];Object.defineProperty(n,o,{get:()=>vl(e),set(t){e=t}})}return!0}function _l(){if(!gl){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,gl=hl(e),ml()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>gl.add(e,__uniConfig.locales[e]))),gl.setLocale(e)}}return gl}function wl(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const xl=ie((()=>{const e="uni.async.",t=["error"];_l().add("en",wl(e,t,["The connection timed out, click the screen to try again."]),!1),_l().add("es",wl(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),_l().add("fr",wl(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),_l().add("zh-Hans",wl(e,t,["连接服务器超时，点击屏幕重试"]),!1),_l().add("zh-Hant",wl(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),Sl=ie((()=>{const e="uni.showActionSheet.",t=["cancel"];_l().add("en",wl(e,t,["Cancel"]),!1),_l().add("es",wl(e,t,["Cancelar"]),!1),_l().add("fr",wl(e,t,["Annuler"]),!1),_l().add("zh-Hans",wl(e,t,["取消"]),!1),_l().add("zh-Hant",wl(e,t,["取消"]),!1)})),Tl=ie((()=>{const e="uni.showToast.",t=["unpaired"];_l().add("en",wl(e,t,["Please note showToast must be paired with hideToast"]),!1),_l().add("es",wl(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),_l().add("fr",wl(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),_l().add("zh-Hans",wl(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),_l().add("zh-Hant",wl(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),Cl=ie((()=>{const e="uni.showLoading.",t=["unpaired"];_l().add("en",wl(e,t,["Please note showLoading must be paired with hideLoading"]),!1),_l().add("es",wl(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),_l().add("fr",wl(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),_l().add("zh-Hans",wl(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),_l().add("zh-Hant",wl(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),kl=ie((()=>{const e="uni.showModal.",t=["cancel","confirm"];_l().add("en",wl(e,t,["Cancel","OK"]),!1),_l().add("es",wl(e,t,["Cancelar","OK"]),!1),_l().add("fr",wl(e,t,["Annuler","OK"]),!1),_l().add("zh-Hans",wl(e,t,["取消","确定"]),!1),_l().add("zh-Hant",wl(e,t,["取消","確定"]),!1)})),El=ie((()=>{const e="uni.chooseFile.",t=["notUserActivation"];_l().add("en",wl(e,t,["File chooser dialog can only be shown with a user activation"]),!1),_l().add("es",wl(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),_l().add("fr",wl(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),_l().add("zh-Hans",wl(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),_l().add("zh-Hant",wl(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)})),Ll=ie((()=>{const e="uni.picker.",t=["done","cancel"];_l().add("en",wl(e,t,["Done","Cancel"]),!1),_l().add("es",wl(e,t,["OK","Cancelar"]),!1),_l().add("fr",wl(e,t,["OK","Annuler"]),!1),_l().add("zh-Hans",wl(e,t,["完成","取消"]),!1),_l().add("zh-Hant",wl(e,t,["完成","取消"]),!1)}));function Ol(e){const t=new Me;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}let Pl=1;const $l=Object.create(null);function Al(e,t){return e+"."+t}function Ml(e,t,n){t=Al(e,t),$l[t]||($l[t]=n)}function Il({id:e,name:t,args:n},o){t=Al(o,t);const r=t=>{e&&Av.publishHandler("invokeViewApi."+e,t)},i=$l[t];i?i(n,r):r({})}const Bl=c(Ol("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=Av,i=n?Pl++:0;n&&o("invokeServiceApi."+i,n,!0),r("invokeServiceApi",{id:i,name:e,args:t})}}),Rl=_e(!0);let jl;function Dl(){jl&&(clearTimeout(jl),jl=null)}let Nl=0,Fl=0;function Vl(e){if(Dl(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];Nl=t,Fl=n,jl=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function Hl(e){if(!jl)return;if(1!==e.touches.length)return Dl();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Nl)>10||Math.abs(n-Fl)>10?Dl():void 0}function Wl(e,t){const n=Number(e);return isNaN(n)?t:n}function zl(){const e=__uniConfig.globalStyle||{},t=Wl(e.rpxCalcMaxDeviceWidth,960),n=Wl(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function ql(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ul,Yl,Xl=["top","left","right","bottom"],Gl={};function Kl(){return Yl="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function Jl(){if(Yl="string"==typeof Yl?Yl:Kl()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(a){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),Xl.forEach((function(e){s(o,e)})),document.body.appendChild(o),i(),Ul=!0}else Xl.forEach((function(e){Gl[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function s(e,n){var o=document.createElement("div"),s=document.createElement("div"),a=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Yl+"(safe-area-inset-"+n+")"};r(o,c),r(s,c),r(a,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(a),s.appendChild(l),e.appendChild(o),e.appendChild(s),i((function(){o.scrollTop=s.scrollTop=1e4;var e=o.scrollTop,r=s.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=s.scrollTop=1e4,e=o.scrollTop,r=s.scrollTop,function(e){Zl.length||setTimeout((function(){var e={};Zl.forEach((function(t){e[t]=Gl[t]})),Zl.length=0,ec.forEach((function(t){t(e)}))}),0);Zl.push(e)}(n))}o.addEventListener("scroll",i,t),s.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(Gl,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function Ql(e){return Ul||Jl(),Gl[e]}var Zl=[];var ec=[];const tc=ql({get support(){return 0!=("string"==typeof Yl?Yl:Kl()).length},get top(){return Ql("top")},get left(){return Ql("left")},get right(){return Ql("right")},get bottom(){return Ql("bottom")},onChange:function(e){Kl()&&(Ul||Jl(),"function"==typeof e&&ec.push(e))},offChange:function(e){var t=ec.indexOf(e);t>=0&&ec.splice(t,1)}}),nc=Ss((()=>{}),["prevent"]),oc=Ss((e=>{}),["stop"]);function rc(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function ic(){const e=rc(document.documentElement.style,"--window-top");return e?e+tc.top:0}function sc(){const e=document.documentElement.style,t=ic(),n=rc(e,"--window-bottom"),o=rc(e,"--window-left"),r=rc(e,"--window-right"),i=rc(e,"--top-window-height");return{top:t,bottom:n?n+tc.bottom:0,left:o?o+tc.left:0,right:r?r+tc.right:0,topWindowHeight:i||0}}function ac(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function lc(e){return ac(e)}function cc(e){return Symbol(e)}function uc(e){return e.$page}function dc(e){return 0===e.tagName.indexOf("UNI-")}const fc="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",hc="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z";function pc(e,t="#000",n=27){return si("svg",{width:n,height:n,viewBox:"0 0 32 32"},[si("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function mc(){{const{$pageInstance:e}=yi();return e&&Sc(e.proxy)}}function gc(e){const t=de(e);if(t.$page)return Sc(t);if(!t.$)return;{const{$pageInstance:e}=t.$;if(e)return Sc(e.proxy)}const n=t.$.root.proxy;return n&&n.$page?Sc(n):void 0}function vc(){const e=nh(),t=e.length;if(t)return e[t-1]}function yc(){var e;const t=null==(e=vc())?void 0:e.$page;if(t)return t.meta}function bc(){const e=yc();return e?e.id:-1}function _c(){const e=vc();if(e)return e.$vm}const wc=["navigationBar","pullToRefresh"];function xc(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=c({id:t},n,e);wc.forEach((t=>{o[t]=c({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function Sc(e){var t,n;return(null==(t=e.$page)?void 0:t.id)||(null==(n=e.$basePage)?void 0:n.id)}function Tc(e,t,n){if(v(e))n=t,t=e,e=_c();else if("number"==typeof e){const t=nh().find((t=>uc(t).id===e));e=t?t.$vm:_c()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function Cc(e){e.preventDefault()}let kc,Ec=0;function Lc({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const s=()=>{function s(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,s=Math.abs(e-Ec)>n;return!i||r&&!s?(!i&&r&&(r=!1),!1):(Ec=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(s()||(kc=setTimeout(s,300))),o=!1};return function(){clearTimeout(kc),o||requestAnimationFrame(s),o=!0}}function Oc(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return Oc(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),re(i.concat(n).join("/"))}function Pc(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}function $c(){zl(),ve(dc),window.addEventListener("touchstart",Vl,Rl),window.addEventListener("touchmove",Hl,Rl),window.addEventListener("touchend",Dl,Rl),window.addEventListener("touchcancel",Dl,Rl)}class Ac{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(he(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&he(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=Rc(this.$el.querySelector(e));return t?Mc(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=Rc(n[o]);e&&t.push(Mc(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||v(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:P(n);(v(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(v(e)&&(e=W(e)),S(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];g(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&Av.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Mc(e,t=!0){if(t&&e&&(e=fe(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Ac(e)),e.$el.__wxsComponentDescriptor}function Ic(e,t){return Mc(e,t)}function Bc(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>Ic(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=fe(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,Ic(r,!1)]}}function Rc(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function jc(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e;let s,a;s=we(t?r:function(e){for(;!dc(e);)e=e.parentElement;return e}(r)),a=we(i);const l={type:n,timeStamp:o,target:s,detail:{},currentTarget:a};return e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),function(e,t){c(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(l,e),l}function Dc(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Nc(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:s,clientX:a,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:s-t,clientX:a,clientY:l-t,force:c||0})}return n}const Fc=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=!dc(o);if(r)return Bc(e,t,n,!1)||[e];const i=jc(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=ic();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[Dc(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=ic();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Dc(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=ic();i.touches=Nc(e.touches,t),i.changedTouches=Nc(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(i,t,{get:()=>e[t]})}))}return Bc(i,t,n)||[i]},createNativeEvent:jc},Symbol.toStringTag,{value:"Module"});function Vc(e){!function(e){const t=e.globalProperties;c(t,Fc),t.$gcd=Ic}(e._context.config)}let Hc=1;function Wc(e){return(e||bc())+".invokeViewApi"}const zc=c(Ol("view"),{invokeOnCallback:(e,t)=>Mv.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=Mv,s=o?Hc++:0;o&&r("invokeViewApi."+s,o,!0),i(Wc(n),{id:s,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:s}=Mv,a=Hc++,l="invokeViewApi."+a;return r(l,n),s(Wc(o),{id:a,name:e,args:t},o),()=>{i(l)}}});function qc(e){Tc(vc(),"onResize",e),Mv.invokeOnCallback("onWindowResize",e)}function Uc(e){const t=vc();Tc(Tm(),"onShow",e),Tc(t,"onShow")}function Yc(){Tc(Tm(),"onHide"),Tc(vc(),"onHide")}const Xc=["onPageScroll","onReachBottom"];function Gc(){Xc.forEach((e=>Mv.subscribe(e,function(e){return(t,n)=>{Tc(parseInt(n),e,t)}}(e))))}function Kc(){!function(){const{on:e}=Mv;e("onResize",qc),e("onAppEnterForeground",Uc),e("onAppEnterBackground",Yc)}(),Gc()}function Jc(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new Ee(this.$page.id)),e.eventChannel}}function Qc(e){e._context.config.globalProperties.getOpenerEventChannel=Jc}function Zc(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function eu(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${pd(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function tu(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],s=t.option.transition,a=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,s=e.option,a=s.transition,l={},c=[];return i.forEach((e=>{let i=e.type,s=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?s=s.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(s=s.map(eu)),n.indexOf(i)>=0&&(s.length=1),c.push(`${i}(${s.join(",")})`);else if(o.concat(r).includes(s[0])){i=s[0];const e=s[1];l[i]=r.includes(i)?eu(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${a.duration}ms ${a.timingFunction} ${a.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=s.transformOrigin,l}(t);Object.keys(a).forEach((t=>{e.$el.style[t]=a[t]})),n+=1,n<r&&setTimeout(i,s.duration+s.delay)}setTimeout((()=>{i()}),0)}const nu={props:["animation"],watch:{animation:{deep:!0,handler(){tu(this)}}},mounted(){tu(this)}},ou=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(nu),ru(e)},ru=e=>(e.__reserved=!0,e.compatConfig={MODE:3},wo(e));function iu(e){return e.__wwe=!0,e}function su(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){let r;return r=we(n),{type:o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const au={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function lu(e){const t=an(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function s(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function a(){r=!1,t.value&&i()}function l(){a(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:iu((function(e){e.touches.length>1||s(e)})),onMousedown:iu((function(e){r||(s(e),window.addEventListener("mouseup",l))})),onTouchend:iu((function(){a()})),onMouseup:iu((function(){r&&l()})),onTouchcancel:iu((function(){r=!1,t.value=!1,clearTimeout(n)}))}}}function cu(e,t){return v(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}const uu=cc("uf"),du=cc("ul");function fu(e,t){hu(e.id,t),no((()=>e.id),((e,n)=>{pu(n,t,!0),hu(e,t,!0)})),qo((()=>{pu(e.id,t)}))}function hu(e,t,n){const o=mc();n&&!e||S(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Av.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Av.on(r,t[r]):e&&Av.on(`uni-${r}-${o}-${e}`,t[r])}))}function pu(e,t,n){const o=mc();n&&!e||S(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Av.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Av.off(r,t[r]):e&&Av.off(`uni-${r}-${o}-${e}`,t[r])}))}const mu=ou({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=an(null),o=xr(uu,!1),{hovering:r,binding:i}=lu(e),s=iu(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),a=xr(du,!1);return a&&(a.addHandler(s),zo((()=>{a.removeHandler(s)}))),fu(e,{"label-click":s}),()=>{const o=e.hoverClass,a=cu(e,"disabled"),l=cu(e,"loading"),c=cu(e,"plain"),u=o&&"none"!==o;return si("uni-button",hi({ref:n,onClick:s,id:e.id,class:u&&r.value?o:""},u&&i,a,l,c),[t.default&&t.default()],16,["onClick","id"])}}}),gu=cc("upm");function vu(){return xr(gu)}function yu(e){const t=function(e){return zt(function(e){if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==nh().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(xc(rl().meta,e)))))}(e);return wr(gu,t),t}function bu(){return rl()}function _u(){return history.state&&history.state.__id__||1}const wu=["original","compressed"],xu=["album","camera"],Su=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function Tu(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function Cu(e,t){return!h(e)||0===e.length||e.find((e=>-1===t.indexOf(e)))?t:e}function ku(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let Eu=1;const Lu={};function Ou(e,t,n,o=!1){return Lu[e]={name:t,keepAlive:o,callback:n},e}function Pu(e,t,n){if("number"==typeof e){const o=Lu[e];if(o)return o.keepAlive||delete Lu[e],o.callback(t,n)}return t}function $u(e){for(const t in Lu)if(Lu[t].name===e)return!0;return!1}const Au="success",Mu="fail",Iu="complete";function Bu(e,t={},{beforeAll:n,beforeSuccess:o}={}){S(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];g(o)&&(t[n]=ku(o),delete e[n])}return t}(t),a=g(r),l=g(i),c=g(s),u=Eu++;return Ou(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),g(n)&&n(u),u.errMsg===e+":ok"?(g(o)&&o(u,t),a&&r(u)):l&&i(u),c&&s(u)})),u}const Ru="success",ju="fail",Du="complete",Nu={},Fu={};function Vu(e,t){return function(n){return e(n,t)||n}}function Hu(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(Vu(i,n));else{const e=i(t,n);if(_(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function Wu(e,t={}){return[Ru,ju,Du].forEach((n=>{const o=e[n];if(!h(o))return;const r=t[n];t[n]=function(e){Hu(o,e,t).then((e=>g(r)&&r(e)||e))}})),t}function zu(e,t){const n=[];h(Nu.returnValue)&&n.push(...Nu.returnValue);const o=Fu[e];return o&&h(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function qu(e){const t=Object.create(null);Object.keys(Nu).forEach((e=>{"returnValue"!==e&&(t[e]=Nu[e].slice())}));const n=Fu[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Uu(e,t,n,o){const r=qu(e);if(r&&Object.keys(r).length){if(h(r.invoke)){return Hu(r.invoke,n).then((n=>t(Wu(qu(e),n),...o)))}return t(Wu(r,n),...o)}return t(n,...o)}function Yu(e,t){return(n={},...o)=>function(e){return!(!S(e)||![Au,Mu,Iu].find((t=>g(e[t]))))}(n)?zu(e,Uu(e,t,n,o)):zu(e,new Promise(((r,i)=>{Uu(e,t,c(n,{success:r,fail:i}),o)})))}function Xu(e,t,n,o={}){const r=t+":fail";let i="";return i=n?0===n.indexOf(r)?n:r+" "+n:r,delete o.errCode,Pu(e,c({errMsg:i},o))}function Gu(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(v(e))return e}const r=function(e,t){const n=e[0];if(!t||!t.formatArgs||!S(t.formatArgs)&&S(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],s=o[t];if(g(s)){const o=s(e[0][t],n);if(v(o))return o}else f(n,t)||(n[t]=s)}}(t,o);if(r)return r}function Ku(e){if(!g(e))throw new Error('Invalid args: type check failed for args "callback". Expected Function')}function Ju(e,t,n){return o=>{Ku(o);const r=Gu(0,[o],0,n);if(r)throw new Error(r);const i=!$u(e);!function(e,t){Ou(Eu++,e,t,!0)}(e,o),i&&(!function(e){Mv.on("api."+e,(t=>{for(const n in Lu){const o=Lu[n];o.name===e&&o.callback(t)}}))}(e),t())}}function Qu(e,t,n){return o=>{Ku(o);const r=Gu(0,[o],0,n);if(r)throw new Error(r);!function(e,t){for(const n in Lu){const o=Lu[n];o.callback===t&&o.name===e&&delete Lu[n]}}(e=e.replace("off","on"),o);$u(e)||(!function(e){Mv.off("api."+e)}(e),t())}}function Zu(e,t,n,o){return n=>{const r=Bu(e,n,o),i=Gu(0,[n],0,o);return i?Xu(r,e,i):t(n,{resolve:t=>function(e,t,n){return Pu(e,c(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>Xu(r,e,function(e){return!e||v(e)?e:e.stack?("undefined"!=typeof globalThis&&globalThis.harmonyChannel||console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function ed(e,t,n){return Ju(e,t,n)}function td(e,t,n){return Qu(e,t,n)}function nd(e,t,n,o){return Yu(e,Zu(e,t,0,o))}function od(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=Gu(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function rd(e,t,n,o){return Yu(e,function(e,t,n,o){return Zu(e,t,0,o)}(e,t,0,o))}function id(e){return(t,{reject:n})=>n(function(e){return`method 'uni.${e}' not supported`}(e))}let sd=!1,ad=0,ld=0,cd=960,ud=375,dd=750;function fd(){const{windowWidth:e,pixelRatio:t,platform:n}=function(){const e=kh(),t=Oh(Lh(e,Eh(e)));return{platform:wh?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();ad=e,ld=t,sd="ios"===n}function hd(e,t){const n=Number(e);return isNaN(n)?t:n}const pd=od(0,((e,t)=>{if(0===ad&&(fd(),function(){const e=__uniConfig.globalStyle||{};cd=hd(e.rpxCalcMaxDeviceWidth,960),ud=hd(e.rpxCalcBaseDeviceWidth,375),dd=hd(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||ad;n=e===dd||n<=cd?n:ud;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==ld&&sd?.5:1),e<0?-o:o}));function md(e,t){Object.keys(t).forEach((n=>{g(t[n])&&(e[n]=function(e,t){const n=t?e?e.concat(t):h(t)?t:[t]:e;return n?function(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}const gd=od(0,((e,t)=>{v(e)&&S(t)?md(Fu[e]||(Fu[e]={}),t):S(e)&&md(Nu,e)})),vd=[.5,.8,1,1.25,1.5,2];const yd=(e,t,n,o)=>{!function(e,t,n,o,r){Mv.invokeViewMethod("map."+e,{type:n,data:o},t,r)}(e,t,n,o,(e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)}))};function bd(e,t){return function(n,o){n?o[e]=Math.round(n):void 0!==t&&(o[e]=t)}}const _d=bd("width"),wd=bd("height"),xd={PNG:"png",JPG:"jpg",JPEG:"jpg"},Sd={formatArgs:{x:bd("x",0),y:bd("y",0),width:_d,height:wd,destWidth:bd("destWidth"),destHeight:bd("destHeight"),fileType(e,t){e=(e||"").toUpperCase();let n=xd[e];n||(n=xd.PNG),t.fileType=n},quality(e,t){t.quality=e&&e>0&&e<1?e:1}}};function Td(e,t,n,o,r){Mv.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,(e=>{r&&r(e)}))}var Cd=["scale","rotate","translate","setTransform","transform"],kd=["drawImage","fillText","fill","stroke","fillRect","strokeRect","clearRect","strokeText"],Ed=["setFillStyle","setTextAlign","setStrokeStyle","setGlobalAlpha","setShadow","setFontSize","setLineCap","setLineJoin","setLineWidth","setMiterLimit","setTextBaseline","setLineDash"];const Ld={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function Od(e){let t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e){return Math.min(255,parseInt(e.trim()))})).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))}));var n=e.toLowerCase();if(f(Ld,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(Ld[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let i=parseInt(t[1].slice(6,8),16);return i=i>=0?i:255,[e,o,r,i]}return console.error("unsupported color:"+e),[0,0,0,255]}class Pd{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,Od(t)])}}class $d{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class Ad{constructor(e){this.width=e}}class Md{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}setFillStyle(e){console.log("initCanvasContextProperty implemented.")}setStrokeStyle(e){console.log("initCanvasContextProperty implemented.")}setShadow(e,t,n,o){console.log("initCanvasContextProperty implemented.")}addColorStop(e,t){console.log("initCanvasContextProperty implemented.")}setLineWidth(e){console.log("initCanvasContextProperty implemented.")}setLineCap(e){console.log("initCanvasContextProperty implemented.")}setLineJoin(e){console.log("initCanvasContextProperty implemented.")}setLineDash(e,t){console.log("initCanvasContextProperty implemented.")}setMiterLimit(e){console.log("initCanvasContextProperty implemented.")}fillRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}strokeRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}clearRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}fill(){console.log("initCanvasContextProperty implemented.")}stroke(){console.log("initCanvasContextProperty implemented.")}scale(e,t){console.log("initCanvasContextProperty implemented.")}rotate(e){console.log("initCanvasContextProperty implemented.")}translate(e,t){console.log("initCanvasContextProperty implemented.")}setFontSize(e){console.log("initCanvasContextProperty implemented.")}fillText(e,t,n,o){console.log("initCanvasContextProperty implemented.")}setTextAlign(e){console.log("initCanvasContextProperty implemented.")}setTextBaseline(e){console.log("initCanvasContextProperty implemented.")}drawImage(e,t,n,o,r,i,s,a,l){console.log("initCanvasContextProperty implemented.")}setGlobalAlpha(e){console.log("initCanvasContextProperty implemented.")}strokeText(e,t,n,o){console.log("initCanvasContextProperty implemented.")}setTransform(e,t,n,o,r,i){console.log("initCanvasContextProperty implemented.")}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],Td(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new Pd("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new Pd("radial",[e,t,n])}createPattern(e,t){if(void 0===t)console.error("Failed to execute 'createPattern' on 'CanvasContext': 2 arguments required, but only 1 present.");else{if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new $d(e,t);console.error("Failed to execute 'createPattern' on 'CanvasContext': The provided type ('"+t+"') is not one of 'repeat', 'no-repeat', 'repeat-x', or 'repeat-y'.")}}measureText(e,t){let n=0;return n=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new Ad(n)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,r,i){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,r,i]}),this.subpath.push([r,i])}arc(e,t,n,o,r,i=!1){this.path.push({method:"arc",data:[e,t,n,o,r,i]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,r){this.path.push({method:"arcTo",data:[e,t,n,o,r]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),r=parseFloat(n[3]),i=n[7],s=[];o.forEach((function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(s.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal"].indexOf(e)>-1?(s.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(s.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&a()})),1===o.length&&a(),o=s.map((function(e){return e.data[0]})).join(" "),this.state.fontSize=r,this.state.fontFamily=i,this.actions.push({method:"setFont",data:[`${o} ${r}px ${i}`]})}else console.warn("Failed to set 'font' on 'CanvasContext': invalid format.");function a(){s.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}}const Id=ie((()=>{[...Cd,...kd].forEach((function(e){Md.prototype[e]=function(e){switch(e){case"fill":case"stroke":return function(){this.actions.push({method:e+"Path",data:[...this.path]})};case"fillRect":return function(e,t,n,o){this.actions.push({method:"fillPath",data:[{method:"rect",data:[e,t,n,o]}]})};case"strokeRect":return function(e,t,n,o){this.actions.push({method:"strokePath",data:[{method:"rect",data:[e,t,n,o]}]})};case"fillText":case"strokeText":return function(t,n,o,r){var i=[t.toString(),n,o];"number"==typeof r&&i.push(r),this.actions.push({method:e,data:i})};case"drawImage":return function(t,n,o,r,i,s,a,l,c){var u;function d(e){return"number"==typeof e}void 0===c&&(s=n,a=o,l=r,c=i,n=void 0,o=void 0,r=void 0,i=void 0),u=d(n)&&d(o)&&d(r)&&d(i)?[t,s,a,l,c,n,o,r,i]:d(l)&&d(c)?[t,s,a,l,c]:[t,s,a],this.actions.push({method:e,data:u})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)})),Ed.forEach((function(e){Md.prototype[e]=function(e){switch(e){case"setFillStyle":case"setStrokeStyle":return function(t){"object"!=typeof t?this.actions.push({method:e,data:["normal",Od(t)]}):this.actions.push({method:e,data:[t.type,t.data,t.colorStop]})};case"setGlobalAlpha":return function(t){t=Math.floor(255*parseFloat(t)),this.actions.push({method:e,data:[t]})};case"setShadow":return function(t,n,o,r){r=Od(r),this.actions.push({method:e,data:[t,n,o,r]}),this.state.shadowBlur=o,this.state.shadowColor=r,this.state.shadowOffsetX=t,this.state.shadowOffsetY=n};case"setLineDash":return function(t,n){t=t||[0,0],n=n||0,this.actions.push({method:e,data:[t,n]}),this.state.lineDash=t};case"setFontSize":return function(t){this.state.font=this.state.font.replace(/\d+\.?\d*px/,t+"px"),this.state.fontSize=t,this.actions.push({method:e,data:[t]})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)}))})),Bd=od(0,((e,t)=>{if(Id(),t)return new Md(e,gc(t));const n=gc(_c());if(n)return new Md(e,n);Mv.emit("onError","createCanvasContext:fail")})),Rd=rd("canvasToTempFilePath",(({x:e=0,y:t=0,width:n,height:o,destWidth:r,destHeight:i,canvasId:s,fileType:a,quality:l},{resolve:c,reject:u})=>{var d=gc(_c());if(!d)return void u();Td(s,d,"toTempFilePath",{x:e,y:t,width:n,height:o,destWidth:r,destHeight:i,fileType:a,quality:l,dirname:"/canvas"},(e=>{e.errMsg&&-1!==e.errMsg.indexOf("fail")?u("",e):c(e)}))}),0,Sd),jd=["onCanplay","onPlay","onPause","onStop","onEnded","onTimeUpdate","onError","onWaiting","onSeeking","onSeeked"],Dd=["offCanplay","offPlay","offPause","offStop","offEnded","offTimeUpdate","offError","offWaiting","offSeeking","offSeeked"];let Nd=0,Fd={};const Vd={canvas:Md,map:class{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){yd(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){yd(this.id,this.pageId,"moveToLocation",e)}getScale(e){yd(this.id,this.pageId,"getScale",e)}getRegion(e){yd(this.id,this.pageId,"getRegion",e)}includePoints(e){yd(this.id,this.pageId,"includePoints",e)}translateMarker(e){yd(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){yd(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){yd(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){yd(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){yd(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){yd(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){yd(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){yd(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){yd(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){yd(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){yd(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){yd(this.id,this.pageId,"openMapApp",e)}on(e,t){yd(this.id,this.pageId,"on",{name:e,callback:t})}},video:class{constructor(e,t){this.id=e,this.pageId=t}play(){Ph(this.id,this.pageId,"play")}pause(){Ph(this.id,this.pageId,"pause")}stop(){Ph(this.id,this.pageId,"stop")}seek(e){Ph(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){Ph(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~vd.indexOf(e)||(e=1),Ph(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){Ph(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){Ph(this.id,this.pageId,"exitFullScreen")}showStatusBar(){Ph(this.id,this.pageId,"showStatusBar")}hideStatusBar(){Ph(this.id,this.pageId,"hideStatusBar")}},editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){!function(e,t,n,o){const r={options:o},i=o&&("success"in o||"fail"in o||"complete"in o);if(i){const e=String(Nd++);r.callbackId=e,Fd[e]=o}Mv.invokeViewMethod(`editor.${e}`,{type:n,data:r},t,(({callbackId:e,data:t})=>{i&&(le(Fd[e],t),delete Fd[e])}))}(this.id,this.pageId,e,t)}}};function Hd(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,r=Vd[n];e.context=new r(t,o),delete e.contextInfo}}class Wd{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery._push(this._selector,this._component,this._single,{node:!0},e),this._selectorQuery}}class zd{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return function(e,t,n){const o=[];t.forEach((({component:t,selector:n,single:r,fields:i})=>{null===t?o.push(function(e){const t={};e.id&&(t.id="");e.dataset&&(t.dataset={});e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0);e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight);if(e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(i)):o.push(function(e,t,n,o,r){const i=function(e,t){if(!e)return t.$el;return e.$el}(t,e),s=i.parentElement;if(!s)return o?null:[];const{nodeType:a}=i,l=3===a||8===a;if(o){const e=l?s.querySelector(n):Ah(i,n)?i:i.querySelector(n);return e?$h(e,r):null}{let e=[];const t=(l?s:i).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,(t=>{e.push($h(t,r))})),!l&&Ah(i,n)&&e.unshift($h(i,r)),e}}(e,t,n,r,i))})),n(o)}(this._page,this._queue,(t=>{const n=this._queueCb;t.forEach(((e,t)=>{h(e)?e.forEach(Hd):Hd(e);const o=n[t];g(o)&&o.call(this,e)})),g(e)&&e.call(this,t)})),this._nodesRef}in(e){return this._component=de(e),this}select(e){return this._nodesRef=new Wd(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new Wd(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new Wd(this,null,"",!0)}_push(e,t,n,o,r){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(r)}}const qd=od(0,(e=>((e=de(e))&&!gc(e)&&(e=null),new zd(e||_c())))),Ud=ed("onWindowResize",(()=>{})),Yd=td("offWindowResize",(()=>{})),Xd=od(0,(()=>{const e=Tm();return e&&e.$vm?e.$vm.$locale:_l().getLocale()})),Gd={onUnhandledRejection:[],onPageNotFound:[],onError:[],onShow:[],onHide:[]};const Kd=od(0,(()=>c({},Dh)));let Jd,Qd,Zd;const ef=[];const tf=rd("getPushClientId",((e,{resolve:t,reject:n})=>{Promise.resolve().then((()=>{var e,o;void 0===Zd&&(Zd=!1,Jd="",Qd="uniPush is not enabled"),ef.push(((e,o)=>{e?t({cid:e}):n(o)})),void 0!==Jd&&(e=Jd,o=Qd,ef.forEach((t=>{t(e,o)})),ef.length=0)}))})),nf=e=>{},of=e=>{},rf={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=Cu(e,wu)},sourceType(e,t){t.sourceType=Cu(e,xu)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},sf={formatArgs:{sourceType(e,t){t.sourceType=Cu(e,xu)},compressed:!0,maxDuration:60,camera:"back",extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},af=(Boolean,["all","image","video"]),lf={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=100)},sourceType(e,t){t.sourceType=Cu(e,xu)},type(e,t){t.type=Tu(e,af)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||("all"!==t.type&&t.type?t.extension=["*"]:t.extension=[""])}}},cf="json",uf=["text","arraybuffer"],df=encodeURIComponent;ArrayBuffer,Boolean;const ff={formatArgs:{method(e,t){t.method=Tu((e||"").toUpperCase(),Su)},data(e,t){t.data=e||""},url(e,t){t.method===Su[0]&&S(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),s={};i.forEach((e=>{const t=e.split("=");s[t[0]]=t[1]}));for(const a in t)if(f(t,a)){let e=t[a];null==e?e="":S(e)&&(e=JSON.stringify(e)),s[df(a)]=df(e)}return r=Object.keys(s).map((e=>`${e}=${s[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==Su[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||cf).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===uf.indexOf(t.responseType)&&(t.responseType="text")}}},hf={formatArgs:{filePath(e,t){e&&(t.filePath=yh(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}},pf={formatArgs:{header(e,t){t.header=e||{}},method(e,t){t.method=Tu((e||"").toUpperCase(),Su)},protocols(e,t){v(e)&&(t.protocols=[e])}}};const mf={url:{type:String,required:!0}},gf=(_f(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),_f(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),Sf("navigateTo")),vf=Sf("redirectTo"),yf=Sf("reLaunch"),bf={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(nh().length-1,e)}}};function _f(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let wf;function xf(){wf=""}function Sf(e){return{formatArgs:{url:Tf(e)},beforeAll:xf}}function Tf(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/")||0===e.indexOf("uni:"))return e;let t="";const n=nh();return n.length&&(t=uc(n[n.length-1]).route),Oc(t,e)}(t)).split("?")[0],r=Pc(o,!0);if(!r)return"page `"+t+"` is not found";if("navigateTo"===e||"redirectTo"===e){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if("switchTab"===e&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if("switchTab"!==e&&"preloadPage"!==e||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!v(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if("preloadPage"!==e){if(wf===t&&"appLaunch"!==n.openType)return`${wf} locked`;__uniConfig.ready&&(wf=t)}else if(r.meta.isTabBar){const e=nh(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}const Cf={formatArgs:{itemColor:"#000"}},kf=(Boolean,{formatArgs:{title:"",mask:!1}}),Ef=(Boolean,{beforeInvoke(){kl()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!f(t,"cancelText")){const{t:e}=_l();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!f(t,"confirmText")){const{t:e}=_l();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}}),Lf=["success","loading","none","error"],Of=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=Tu(e,Lf)},image(e,t){t.image=e?yh(e):""},duration:1500,mask:!1}});function Pf(){const e=_c();if(!e)return;const t=th(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:rh(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,Tc(e,"onHide"))}function $f(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}function Af(e){const t=th().values();for(const n of t){const t=Gf(n);if($f(e,t))return n.$.__isActive=!0,t.id}}const Mf=rd("switchTab",(({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(Kf.handledBeforeEntryPageRoutes)return Pf(),Df({type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},Af(e)).then(o).catch(r);Qf.push({args:{type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},resolve:o,reject:r})}),0,Sf("switchTab"));function If(){const e=vc();if(!e)return;const t=Gf(e);rh(lh(t.path,t.id))}const Bf=rd("redirectTo",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(Kf.handledBeforeEntryPageRoutes)return If(),Df({type:"redirectTo",url:e,isAutomatedTesting:t}).then(n).catch(o);Zf.push({args:{type:"redirectTo",url:e,isAutomatedTesting:t},resolve:n,reject:o})}),0,vf);function Rf(){const e=th().keys();for(const t of e)rh(t)}const jf=rd("reLaunch",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(Kf.handledBeforeEntryPageRoutes)return Rf(),Df({type:"reLaunch",url:e,isAutomatedTesting:t}).then(n).catch(o);eh.push({args:{type:"reLaunch",url:e,isAutomatedTesting:t},resolve:n,reject:o})}),0,yf);function Df({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:r},i){const s=Tm().$router,{path:a,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:Ce(n||"")}}(t);return new Promise(((t,c)=>{const u=function(e,t){return{__id__:t||++ih,__type__:e}}(e,i);s["navigateTo"===e?"push":"replace"]({path:a,query:l,state:u,force:!0}).then((i=>{if(Ta(i))return c(i.message);if("switchTab"===e&&(s.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=s.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new Ee(u.__id__,o),t(r?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return r?t({__id__:u.__id__}):t()}))}))}function Nf(){if(Kf.handledBeforeEntryPageRoutes)return;Kf.handledBeforeEntryPageRoutes=!0;const e=[...Jf];Jf.length=0,e.forEach((({args:e,resolve:t,reject:n})=>Df(e).then(t).catch(n)));const t=[...Qf];Qf.length=0,t.forEach((({args:e,resolve:t,reject:n})=>(Pf(),Df(e,Af(e.url)).then(t).catch(n))));const n=[...Zf];Zf.length=0,n.forEach((({args:e,resolve:t,reject:n})=>(If(),Df(e).then(t).catch(n))));const o=[...eh];eh.length=0,o.forEach((({args:e,resolve:t,reject:n})=>(Rf(),Df(e).then(t).catch(n))))}let Ff;function Vf(){var e;return Ff||(Ff=__uniConfig.tabBar&&zt((e=__uniConfig.tabBar,ml()&&e.list&&e.list.forEach((e=>{bl(e,["text"])})),e))),Ff}function Hf(e){const t=window.CSS&&window.CSS.supports;return t&&(t(e)||t.apply(window.CSS,e.split(":")))}const Wf=Hf("top:env(a)"),zf=Hf("top:constant(a)"),qf=Hf("backdrop-filter:blur(10px)"),Uf=(()=>Wf?"env":zf?"constant":"")();function Yf(e){return Uf?`calc(${e}px + ${Uf}(safe-area-inset-bottom))`:`${e}px`}const Xf=new Map;function Gf(e){return e.$page}const Kf={handledBeforeEntryPageRoutes:!1},Jf=[],Qf=[],Zf=[],eh=[];function th(){return Xf}function nh(){return oh()}function oh(){const e=[],t=Xf.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function rh(e,t=!0){const n=Xf.get(e);n.$.__isUnload=!0,Tc(n,"onUnload"),Xf.delete(e),t&&function(e){const t=ch.get(e);t&&(ch.delete(e),uh.pruneCacheEntry(t))}(e)}let ih=_u();function sh(e){const t=vu();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,o,r,i){const{id:s,route:a}=o,l=Re(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:s,path:re(a),route:a,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",n,{},t)}function ah(e){const t=sh(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",e.$fontFamilySet=new Set,t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),Xf.set(lh(t.path,t.id),e),1===Xf.size&&setTimeout((()=>{Nf()}),0)}function lh(e,t){return e+"$$"+t}const ch=new Map,uh={get:e=>ch.get(e),set(e,t){!function(e){const t=parseInt(e.split("$$")[1]);if(!t)return;uh.forEach(((e,n)=>{const o=parseInt(n.split("$$")[1]);if(o&&o>t){if(function(e){return"tabBar"===e.props.type}(e))return;uh.delete(n),uh.pruneCacheEntry(e),En((()=>{Xf.forEach(((e,t)=>{e.$.isUnmounted&&Xf.delete(t)}))}))}}))}(e),ch.set(e,t)},delete(e){ch.get(e)&&ch.delete(e)},forEach(e){ch.forEach(e)}};function dh(e,t){!function(e){const t=hh(e),{body:n}=document;ph&&n.removeAttribute(ph),t&&n.setAttribute(t,""),ph=t}(e),function(e){let t=0;if(e.isTabBar){const e=Vf();e.shown&&(t=parseInt(e.height))}var n;lc({"--window-top":(n=0,Uf?`calc(${n}px + ${Uf}(safe-area-inset-top))`:`${n}px`),"--window-bottom":Yf(t)})}(t),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),gh(e,t)}function fh(e){const t=hh(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function hh(e){return e.type.__scopeId}let ph,mh;function gh(e,t){if(document.removeEventListener("touchmove",Cc),mh&&document.removeEventListener("scroll",mh),t.disableScroll)return document.addEventListener("touchmove",Cc);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!(null==n?void 0:n.length)&&!(null==o?void 0:o.length)&&!r)return;const i={},s=Gf(e.proxy).id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&Av.publishHandler("onPageScroll",{scrollTop:o},e),n&&Av.emit(e+".onPageScroll",{scrollTop:o})}}(s,n,r)),(null==o?void 0:o.length)&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>Av.publishHandler("onReachBottom",{},s)),mh=Lc(i),requestAnimationFrame((()=>document.addEventListener("scroll",mh)))}function vh(e){const{base:t}=__uniConfig.router;return 0===re(e).indexOf(t)?re(e):t+e}function yh(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0!==e.indexOf("./")||!e.includes("/static/")&&0!==e.indexOf("./"+(n||"assets")+"/")||(e=e.slice(1))),0===e.indexOf("/")){if(0!==e.indexOf("//"))return vh(e.slice(1));e="https:"+e}if(te.test(e)||ne.test(e)||0===e.indexOf("blob:"))return e;const o=oh();return o.length?vh(Oc(Gf(o[o.length-1]).route,e).slice(1)):e}const bh=navigator.userAgent,_h=/android/i.test(bh),wh=/iphone|ipad|ipod/i.test(bh),xh=bh.match(/Windows NT ([\d|\d.\d]*)/i),Sh=/Macintosh|Mac/i.test(bh),Th=/Linux|X11/i.test(bh),Ch=Sh&&navigator.maxTouchPoints>0;function kh(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function Eh(e){return e&&90===Math.abs(window.orientation)}function Lh(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function Oh(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function Ph(e,t,n,o){Mv.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function $h(e,t){const n={},{top:o,topWindowHeight:r}=sc();if(t.node){const t=e.tagName.split("-")[1]||e.tagName;t&&(n.node=e.querySelector(t))}if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=ye(e)),t.rect||t.size){const i=e.getBoundingClientRect();t.rect&&(n.left=i.left,n.right=i.right,n.top=i.top-o-r,n.bottom=i.bottom-o-r),t.size&&(n.width=i.width,n.height=i.height)}if(h(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(h(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=o[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function Ah(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}const Mh={};function Ih(e,t){const n=Mh[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",r=atob(t[1]);let i=r.length;const s=new Uint8Array(i);for(;i--;)s[i]=r.charCodeAt(i);return Bh(s,o)}(e)):t?Promise.reject(new Error("not find")):new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()}))}function Bh(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const r=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],r,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||r}}return n}function Rh(e){for(const n in Mh)if(f(Mh,n)){if(Mh[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return Mh[t]=e,t}function jh(e){(window.URL||window.webkitURL).revokeObjectURL(e),delete Mh[e]}const Dh=Zc(),Nh=Zc();const Fh=ou({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=an(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=zt({width:-1,height:-1});return no((()=>c({},o)),(e=>t("resize",e))),()=>{const t=e.value;t&&(o.width=t.offsetWidth,o.height=t.offsetHeight,n())}}(n,t,o);return function(e,t,n,o){Po(o),Vo((()=>{t.initial&&En(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>si("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[si("div",{onScroll:r},[si("div",null,null)],40,["onScroll"]),si("div",{onScroll:r},[si("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});const Vh=function(){if(navigator.userAgent.includes("jsdom"))return 1;const e=document.createElement("canvas");e.height=e.width=0;const t=e.getContext("2d"),n=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/n}();function Hh(e,t=!0){const n=t?Vh:1;e.width=e.offsetWidth*n,e.height=e.offsetHeight*n,e.getContext("2d").__hidpi__=t}let Wh=!1;function zh(){if(Wh)return;Wh=!0;const e={fillRect:"all",clearRect:"all",strokeRect:"all",moveTo:"all",lineTo:"all",arc:[0,1,2],arcTo:"all",bezierCurveTo:"all",isPointinPath:"all",isPointinStroke:"all",quadraticCurveTo:"all",rect:"all",translate:"all",createRadialGradient:"all",createLinearGradient:"all",transform:[4,5],setTransform:[4,5]},t=CanvasRenderingContext2D.prototype;t.drawImageByCanvas=function(e){return function(t,n,o,r,i,s,a,l,c,u){if(!this.__hidpi__)return e.apply(this,arguments);n*=Vh,o*=Vh,r*=Vh,i*=Vh,s*=Vh,a*=Vh,l=u?l*Vh:l,c=u?c*Vh:c,e.call(this,t,n,o,r,i,s,a,l,c)}}(t.drawImage),1!==Vh&&(!function(e,t){for(const n in e)f(e,n)&&t(e[n],n)}(e,(function(e,n){t[n]=function(t){return function(){if(!this.__hidpi__)return t.apply(this,arguments);let n=Array.prototype.slice.call(arguments);if("all"===e)n=n.map((function(e){return e*Vh}));else if(Array.isArray(e))for(let t=0;t<e.length;t++)n[e[t]]*=Vh;return t.apply(this,n)}}(t[n])})),t.stroke=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.lineWidth*=Vh,e.apply(this,arguments),this.lineWidth/=Vh}}(t.stroke),t.fillText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);const t=Array.prototype.slice.call(arguments);t[1]*=Vh,t[2]*=Vh,t[3]&&"number"==typeof t[3]&&(t[3]*=Vh);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*Vh+n})),e.apply(this,t),this.font=n}}(t.fillText),t.strokeText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var t=Array.prototype.slice.call(arguments);t[1]*=Vh,t[2]*=Vh,t[3]&&"number"==typeof t[3]&&(t[3]*=Vh);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*Vh+n})),e.apply(this,t),this.font=n}}(t.strokeText),t.drawImage=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.scale(Vh,Vh),e.apply(this,arguments),this.scale(1/Vh,1/Vh)}}(t.drawImage))}const qh=ie((()=>zh()));function Uh(e){return e?yh(e):e}function Yh(e){return(e=e.slice(0))[3]=e[3]/255,"rgba("+e.join(",")+")"}function Xh(e,t){Array.from(t).forEach((t=>{t.x=t.clientX-e.left,t.y=t.clientY-e.top}))}let Gh;function Kh(e=0,t=0){return Gh||(Gh=document.createElement("canvas")),Gh.width=e,Gh.height=t,Gh}const Jh=ou({inheritAttrs:!1,name:"Canvas",compatConfig:{MODE:3},props:{canvasId:{type:String,default:""},disableScroll:{type:[Boolean,String],default:!1},hidpi:{type:Boolean,default:!0}},computed:{id(){return this.canvasId}},setup(e,{emit:t,slots:n}){qh();const o=an(null),r=an(null),i=an(null),s=an(!1),a=function(e){return(t,n)=>{e(t,jc(n))}}(t),{$attrs:l,$excludeAttrs:u,$listeners:d}=$p({excludeListeners:!0}),{_listeners:h}=function(e,t,n){const o=Oi((()=>{let o=["onTouchstart","onTouchmove","onTouchend"],r=t.value,i=c({},(()=>{let e={};for(const t in r)if(f(r,t)){const n=r[t];e[t]=n}return e})());return o.forEach((t=>{let o=[];i[t]&&o.push(iu((e=>{const o=e.currentTarget.getBoundingClientRect();Xh(o,e.touches),Xh(o,e.changedTouches),n(t.replace("on","").toLocaleLowerCase(),e)}))),e.disableScroll&&"onTouchmove"===t&&o.push(nc),i[t]=o})),i}));return{_listeners:o}}(e,d,a),{_handleSubscribe:p,_resize:m}=function(e,t,n){let o=[],r={};const i=Oi((()=>e.hidpi?Vh:1));function s(n){let o=t.value;if(!n||o.width!==Math.floor(n.width*i.value)||o.height!==Math.floor(n.height*i.value))if(o.width>0&&o.height>0){let t=o.getContext("2d"),n=t.getImageData(0,0,o.width,o.height);Hh(o,e.hidpi),t.putImageData(n,0,0)}else Hh(o,e.hidpi)}function a({actions:e,reserve:i},s){if(!e)return;if(n.value)return void o.push([e,i]);let a=t.value,c=a.getContext("2d");i||(c.fillStyle="#000000",c.strokeStyle="#000000",c.shadowColor="#000000",c.shadowBlur=0,c.shadowOffsetX=0,c.shadowOffsetY=0,c.setTransform(1,0,0,1,0,0),c.clearRect(0,0,a.width,a.height)),l(e);for(let t=0;t<e.length;t++){const n=e[t];let o=n.method;const i=n.data,a=i[0];if(/^set/.test(o)&&"setTransform"!==o){const n=o[3].toLowerCase()+o.slice(4);let r;if("fillStyle"===n||"strokeStyle"===n){if("normal"===a)r=Yh(i[1]);else if("linear"===a){const e=c.createLinearGradient(...i[1]);i[2].forEach((function(t){const n=t[0],o=Yh(t[1]);e.addColorStop(n,o)})),r=e}else if("radial"===a){let e=i[1];const t=e[0],n=e[1],o=e[2],s=c.createRadialGradient(t,n,0,t,n,o);i[2].forEach((function(e){const t=e[0],n=Yh(e[1]);s.addColorStop(t,n)})),r=s}else if("pattern"===a){if(!u(i[1],e.slice(t+1),s,(function(e){e&&(c[n]=c.createPattern(e,i[2]))})))break;continue}c[n]=r}else if("globalAlpha"===n)c[n]=Number(a)/255;else if("shadow"===n){let e=["shadowOffsetX","shadowOffsetY","shadowBlur","shadowColor"];i.forEach((function(t,n){c[e[n]]="shadowColor"===e[n]?Yh(t):t}))}else if("fontSize"===n){const e=c.__font__||c.font;c.__font__=c.font=e.replace(/\d+\.?\d*px/,a+"px")}else"lineDash"===n?(c.setLineDash(a),c.lineDashOffset=i[1]||0):"textBaseline"===n?("normal"===a&&(i[0]="alphabetic"),c[n]=a):"font"===n?c.__font__=c.font=a:c[n]=a}else if("fillPath"===o||"strokePath"===o)o=o.replace(/Path/,""),c.beginPath(),i.forEach((function(e){c[e.method].apply(c,e.data)})),c[o]();else if("fillText"===o)c.fillText.apply(c,i);else if("drawImage"===o){if("break"===function(){let n=[...i],o=n[0],a=n.slice(1);if(r=r||{},!u(o,e.slice(t+1),s,(function(e){e&&c.drawImage.apply(c,[e].concat([...a.slice(4,8)],[...a.slice(0,4)]))})))return"break"}())break}else"clip"===o?(i.forEach((function(e){c[e.method].apply(c,e.data)})),c.clip()):c[o].apply(c,i)}n.value||s({errMsg:"drawCanvas:ok"})}function l(e){e.forEach((function(e){let t=e.method,n=e.data,o="";function i(){const e=r[o]=new Image;e.onload=function(){e.ready=!0},function(e){const t=document.createElement("a");return t.href=e,t.origin===location.origin?Promise.resolve(e):Ih(e).then(Rh)}(o).then((t=>{e.src=t})).catch((()=>{e.src=o}))}"drawImage"===t?(o=n[0],o=Uh(o),n[0]=o):"setFillStyle"===t&&"pattern"===n[0]&&(o=n[1],o=Uh(o),n[1]=o),o&&!r[o]&&i()}))}function u(e,t,i,s){let l=r[e];return l.ready?(s(l),!0):(o.unshift([t,!0]),n.value=!0,l.onload=function(){l.ready=!0,s(l),n.value=!1;let e=o.slice(0);o=[];for(let t=e.shift();t;)a({actions:t[0],reserve:t[1]},i),t=e.shift()},!1)}function d({x:e=0,y:n=0,width:o,height:r,destWidth:s,destHeight:a,hidpi:l=!0,dataType:c,quality:u=1,type:d="png"},f){const h=t.value;let p;const m=h.offsetWidth-e;o=o?Math.min(o,m):m;const g=h.offsetHeight-n;r=r?Math.min(r,g):g,l?(s=o,a=r):s||a?s?a||(a=Math.round(r/o*s)):(a||(a=Math.round(r*i.value)),s=Math.round(o/r*a)):(s=Math.round(o*i.value),a=Math.round(r*i.value));const v=Kh(s,a),y=v.getContext("2d");let b;"jpeg"!==d&&"jpg"!==d||(d="jpeg",y.fillStyle="#fff",y.fillRect(0,0,s,a)),y.__hidpi__=!0,y.drawImageByCanvas(h,e,n,o,r,0,0,s,a,!1);try{let e;if("base64"===c)p=v.toDataURL(`image/${d}`,u);else{const e=y.getImageData(0,0,s,a);p=Array.prototype.slice.call(e.data)}b={data:p,compressed:e,width:s,height:a}}catch(_){b={errMsg:`canvasGetImageData:fail ${_}`}}if(v.height=v.width=0,y.__hidpi__=!1,!f)return b;f(b)}function f({data:e,x:n,y:o,width:r,height:i,compressed:s},a){try{0,i||(i=Math.round(e.length/4/r));const s=Kh(r,i);s.getContext("2d").putImageData(new ImageData(new Uint8ClampedArray(e),r,i),0,0),t.value.getContext("2d").drawImage(s,n,o,r,i),s.height=s.width=0}catch(l){return void a({errMsg:"canvasPutImageData:fail"})}a({errMsg:"canvasPutImageData:ok"})}function h({x:e=0,y:t=0,width:n,height:o,destWidth:r,destHeight:i,fileType:s,quality:a,dirname:l},c){const u=d({x:e,y:t,width:n,height:o,destWidth:r,destHeight:i,hidpi:!1,dataType:"base64",type:s,quality:a});var f;u.errMsg?c({errMsg:u.errMsg.replace("canvasPutImageData","toTempFilePath")}):(f=u.data,((e,t)=>{let n="toTempFilePath:"+(e?"fail":"ok");e&&(n+=` ${e.message}`),c({errMsg:n,tempFilePath:t})})(null,f))}const p={actionsChanged:a,getImageData:d,putImageData:f,toTempFilePath:h};function m(e,t,n){let o=p[e];0!==e.indexOf("_")&&g(o)&&o(t,n)}return c(p,{_resize:s,_handleSubscribe:m})}(e,r,s);return function(e,t,n,o){const r=yi().proxy;Vo((()=>{tm(t||em(r),e,o),!n&&t||no((()=>r.id),((t,n)=>{tm(em(r,t),e,o),nm(n&&em(r,n))}))})),zo((()=>{nm(t||em(r),o)}))}(p,function(e){const t=mc(),n=yi().proxy,o=n.$options.name.toLowerCase(),r=e||n.id||"context"+om++;return Vo((()=>{n.$el.__uniContextInfo={id:r,type:o,page:t}})),`${o}.${r}`}(e.canvasId),!0),Vo((()=>{m()})),()=>{const{canvasId:t,disableScroll:s}=e;return si("uni-canvas",hi({ref:o,"canvas-id":t,"disable-scroll":s},l.value,u.value,h.value),[si("canvas",{ref:r,class:"uni-canvas-canvas",width:"300",height:"150"},null,512),si("div",{style:"position: absolute;top: 0;left: 0;width: 100%;height: 100%;overflow: hidden;"},[n.default&&n.default()]),si(Fh,{ref:i,onResize:m},null,8,["onResize"])],16,["canvas-id","disable-scroll"])}}});const Qh=cc("ucg"),Zh=ou({name:"Checkbox",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},value:{type:String,default:""},color:{type:String,default:"#007aff"},backgroundColor:{type:String,default:""},borderColor:{type:String,default:""},activeBackgroundColor:{type:String,default:""},activeBorderColor:{type:String,default:""},iconColor:{type:String,default:""},foreColor:{type:String,default:""}},setup(e,{slots:t}){const n=an(null),o=an(e.checked),r=Oi((()=>"true"===o.value||!0===o.value)),i=an(e.value);const s=Oi((()=>function(t){if(e.disabled)return{backgroundColor:"#E1E1E1",borderColor:"#D1D1D1"};const n={};return t?(e.activeBorderColor&&(n.borderColor=e.activeBorderColor),e.activeBackgroundColor&&(n.backgroundColor=e.activeBackgroundColor)):(e.borderColor&&(n.borderColor=e.borderColor),e.backgroundColor&&(n.backgroundColor=e.backgroundColor)),n}(r.value)));no([()=>e.checked,()=>e.value],(([e,t])=>{o.value=e,i.value=t}));const{uniCheckGroup:a,uniLabel:l}=function(e,t,n){const o=Oi((()=>({checkboxChecked:Boolean(e.value),value:t.value}))),r={reset:n},i=xr(Qh,!1);i&&i.addField(o);const s=xr(uu,!1);s&&s.addField(r);const a=xr(du,!1);return zo((()=>{i&&i.removeField(o),s&&s.removeField(r)})),{uniCheckGroup:i,uniForm:s,uniLabel:a}}(o,i,(()=>{o.value=!1})),c=t=>{e.disabled||(o.value=!o.value,a&&a.checkboxChange(t),t.stopPropagation())};return l&&(l.addHandler(c),zo((()=>{l.removeHandler(c)}))),fu(e,{"label-click":c}),()=>{const r=cu(e,"disabled");let i;return i=o.value,si("uni-checkbox",hi(r,{id:e.id,onClick:c,ref:n}),[si("div",{class:"uni-checkbox-wrapper",style:{"--HOVER-BD-COLOR":e.activeBorderColor}},[si("div",{class:["uni-checkbox-input",{"uni-checkbox-input-disabled":e.disabled}],style:s.value},[i?pc(fc,e.disabled?"#ADADAD":e.foreColor||e.iconColor||e.color,22):""],6),t.default&&t.default()],4)],16,["id","onClick"])}}});function ep(){}const tp={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function np(e,t,n){function o(e){const t=Oi((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(undefined),document.addEventListener("click",ep,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",ep,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}no((()=>t.value),(e=>e&&o(e)))}const op={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},rp={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},ip={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},sp=ou({name:"Image",props:op,setup(e,{emit:t}){const n=an(null),o=function(e,t){const n=an(""),o=Oi((()=>{let e="auto",o="";const r=ip[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=zt({rootEl:e,src:Oi((()=>t.src?yh(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return Vo((()=>{const t=e.value;r.origWidth=t.clientWidth||0,r.origHeight=t.clientHeight||0})),r}(n,e),r=su(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=rp[o];if(!r)return;const{origWidth:i,origHeight:s}=n,a=i&&s?i/s:0;if(!a)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){ap&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,a))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return no((()=>t.mode),((e,t)=>{rp[t]&&r(),rp[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,s;const a=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void a();i=i||new Image,i.onload=e=>{const{width:u,height:d}=i;a(u,d,l),En((()=>{o()})),i.draggable=t.draggable,s&&s.remove(),s=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:d})},i.onerror=t=>{a(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};no((()=>e.src),(e=>l(e))),no((()=>e.imgSrc),(e=>{!e&&s&&(s.remove(),s=null)})),Vo((()=>l(e.src))),zo((()=>c()))}(o,e,n,i,r),()=>si("uni-image",{ref:n},[si("div",{style:o.modeStyle},null,4),rp[e.mode]?si(Fh,{onResize:i},null,8,["onResize"]):si("span",null,null)],512)}});const ap="Google Inc."===navigator.vendor;const lp=_e(!0),cp=[];let up=0,dp=!1;const fp=e=>cp.forEach((t=>t.userAction=e));function hp(e={userAction:!1}){if(!dp){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!up&&fp(!0),up++,setTimeout((()=>{!--up&&fp(!1)}),0)}),lp)})),dp=!0}cp.push(e)}const pp=()=>!!up;function mp(){const e=zt({userAction:!1});return Vo((()=>{hp(e)})),zo((()=>{!function(e){const t=cp.indexOf(e);t>=0&&cp.splice(t,1)}(e)})),{state:e}}function gp(){const e=zt({attrs:{}});return Vo((()=>{let t=yi();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function vp(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}function yp(e,t,n){"number"===t&&isNaN(Number(e))&&(e="");return null==e?"":String(e)}const bp=["none","text","decimal","numeric","tel","search","email","url"],_p=c({},{name:{type:String,default:""},modelValue:{type:[String,Number]},value:{type:[String,Number]},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~bp.indexOf(e)},cursorColor:{type:String,default:""}},tp),wp=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function xp(e,t,n,o){let r=null;r=ke((n=>{t.value=yp(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout}),no((()=>e.modelValue),r),no((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const s=Date.now();clearTimeout(n),o=()=>{o=null,r=s,e.apply(this,i)},s-r<t?n=setTimeout(o,t-(s-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return Fo((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function Sp(e,t){mp();const n=Oi((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}no((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),Vo((()=>{n.value&&En(o)}))}function Tp(e,t,n,o){Ml(bc(),"getSelectedTextRange",vp);const{fieldRef:r,state:i,trigger:s}=function(e,t,n){const o=an(null),r=su(t,n),i=Oi((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),s=Oi((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),a=Oi((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=Oi((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t}));let c="";c=yp(e.modelValue,e.type)||yp(e.value,e.type);const u=zt({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:s,cursor:a});return no((()=>u.focus),(e=>n("update:focus",e))),no((()=>u.maxlength),(e=>u.value=u.value.slice(0,e)),{immediate:!1}),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:a}=xp(e,i,n,s);Sp(e,r),np(0,r);const{state:l}=gp();!function(e,t){const n=xr(uu,!1);if(!n)return;const o=yi(),r={submit(){const n=o.proxy;return[n[e],v(t)?n[t]:t.value]},reset(){v(t)?o.proxy[t]="":t.value=""}};n.addField(r),zo((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function s(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function a(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}no([()=>t.selectionStart,()=>t.selectionEnd],s),no((()=>t.cursor),a),no((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),g(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function d(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),s(),a()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),d(e)})),c.addEventListener("compositionupdate",d)}))}(r,i,e,s,a,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:s}}const Cp=c({},_p,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),kp=ie((()=>{{const e=navigator.userAgent;let t="";const n=e.match(/OS\s([\w_]+)\slike/);if(n)t=n[1].replace(/_/g,".");else if(/Macintosh|Mac/i.test(e)&&navigator.maxTouchPoints>0){const n=e.match(/Version\/(\S*)\b/);n&&(t=n[1])}return!!t&&parseInt(t)>=16&&parseFloat(t)<17.2}}));function Ep(e,t,n,o,r){if(t.value)if("."===e.data){if("."===t.value.slice(-1))return n.value=o.value=t.value=t.value.slice(0,-1),!1;if(t.value&&!t.value.includes("."))return t.value+=".",r&&(r.fn=()=>{n.value=o.value=t.value=t.value.slice(0,-1),o.removeEventListener("blur",r.fn)},o.addEventListener("blur",r.fn)),!1}else if("deleteContentBackward"===e.inputType&&kp()&&"."===t.value.slice(-2,-1))return t.value=n.value=o.value=t.value.slice(0,-2),!0}const Lp=ou({name:"Input",props:Cp,emits:["confirm",...wp],setup(e,{emit:t,expose:n}){const o=["text","number","idcard","digit","password","tel"],r=["off","one-time-code"],i=Oi((()=>{let t="";switch(e.type){case"text":t="text","search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=o.includes(e.type)?e.type:"text"}return e.password?"password":t})),s=Oi((()=>{const t=r.indexOf(e.textContentType),n=r.indexOf(P(e.textContentType));return r[-1!==t?t:-1!==n?n:0]}));let a=function(e,t){if("number"===t.value){const t=void 0===e.modelValue?e.value:e.modelValue,n=an(null!=t?t.toLocaleString():"");return no((()=>e.modelValue),(e=>{n.value=null!=e?e.toLocaleString():""})),no((()=>e.value),(e=>{n.value=null!=e?e.toLocaleString():""})),n}return an("")}(e,i),l={fn:null};const c=an(null),{fieldRef:u,state:d,scopedAttrsState:f,fixDisabledColor:h,trigger:p}=Tp(e,c,t,((t,n)=>{const o=t.target;if("number"===i.value){if(l.fn&&(o.removeEventListener("blur",l.fn),l.fn=null),o.validity&&!o.validity.valid){if((!a.value||!o.value)&&"-"===t.data||"-"===a.value[0]&&"deleteContentBackward"===t.inputType)return a.value="-",n.value="",l.fn=()=>{a.value=o.value=""},o.addEventListener("blur",l.fn),!1;const e=Ep(t,a,n,o,l);return"boolean"==typeof e?e:(a.value=n.value=o.value="-"===a.value?"":a.value,!1)}{const e=Ep(t,a,n,o,l);if("boolean"==typeof e)return e;a.value=o.value}const r=n.maxlength;if(r>0&&o.value.length>r){o.value=o.value.slice(0,r),n.value=o.value;return(void 0!==e.modelValue&&null!==e.modelValue?e.modelValue.toString():"")!==o.value}}}));no((()=>d.value),(t=>{"number"!==e.type||"-"===a.value&&""===t||(a.value=t.toString())}));const m=["number","digit"],g=Oi((()=>m.includes(e.type)?e.step:""));function v(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),p("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return n({$triggerInput:e=>{t("update:modelValue",e.value),t("update:value",e.value),d.value=e.value}}),()=>{let t=e.disabled&&h?si("input",{key:"disabled-input",ref:u,value:d.value,tabindex:"-1",readonly:!!e.disabled,type:i.value,maxlength:d.maxlength,step:g.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):si("input",{key:"input",ref:u,value:d.value,onInput:e=>{d.value=e.target.value.toString()},disabled:!!e.disabled,type:i.value,maxlength:d.maxlength,step:g.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:s.value,onKeyup:v,inputmode:e.inputmode},null,44,["value","onInput","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return si("uni-input",{ref:c},[si("div",{class:"uni-input-wrapper"},[ao(si("div",hi(f.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Ji,!(d.value.length||"-"===a.value||a.value.includes("."))]]),"search"===e.confirmType?si("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});const Op=["class","style"],Pp=/^on[A-Z]+/,$p=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,o=yi(),r=ln({}),i=ln({}),s=ln({}),a=n.concat(Op);return o.attrs=zt(o.attrs),eo((()=>{const e=(n=o.attrs,Object.keys(n).map((e=>[e,n[e]]))).reduce(((e,[n,o])=>(a.includes(n)?e.exclude[n]=o:Pp.test(n)?(t||(e.attrs[n]=o),e.listeners[n]=o):e.attrs[n]=o,e)),{exclude:{},attrs:{},listeners:{}});var n;r.value=e.attrs,i.value=e.listeners,s.value=e.exclude})),{$attrs:r,$listeners:i,$excludeAttrs:s}};function Ap(e){const t=[];return h(e)&&e.forEach((e=>{ei(e)?e.type===Hr?t.push(...Ap(e.children)):t.push(e):h(e)&&t.push(...Ap(e))})),t}const Mp=function(e,t,n,o){e.addEventListener(t,(e=>{g(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let Ip,Bp;function Rp(e,t,n){zo((()=>{document.removeEventListener("mousemove",Ip),document.removeEventListener("mouseup",Bp)}));let o=0,r=0,i=0,s=0;const a=function(e,n,a,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:a,y:l,dx:a-o,dy:l-r,ddx:a-i,ddy:l-s,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;Mp(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=s=e.touches[0].pageY,a(e,"start",o,r)})),Mp(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=s=e.pageY,a(e,"start",o,r)})),Mp(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=a(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,s=e.touches[0].pageY,t}}));const d=Ip=function(e){if(!l&&c&&u){const t=a(e,"move",e.pageX,e.pageY);return i=e.pageX,s=e.pageY,t}};document.addEventListener("mousemove",d),Mp(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,a(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const f=Bp=function(e){if(c=!1,!l&&u)return u=null,a(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",f),Mp(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,a(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}const jp=ou({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return h(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,{slots:t,emit:n}){const o=an(null),r=an(null),i=su(o,n),s=function(e){const t=zt([...e.value]),n=zt({value:t,height:34});return no((()=>e.value),((e,t)=>{n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)}))})),n}(e),a=an(null);Vo((()=>{const e=a.value;e&&(s.height=e.$el.offsetHeight)}));let l=an([]),c=an([]);function u(e){let t=c.value;t=t.filter((e=>e.type!==zr));let n=t.indexOf(e);return-1!==n?n:l.value.indexOf(e)}return wr("getPickerViewColumn",(function(e){return Oi({get(){const t=u(e.vnode);return s.value[t]||0},set(t){const o=u(e.vnode);if(o<0)return;if(s.value[o]!==t){s.value[o]=t;const e=s.value.map((e=>e));n("update:value",e),i("change",{},{value:e})}}})})),wr("pickerViewProps",e),wr("pickerViewState",s),()=>{const e=t.default&&t.default();{const t=Ap(e);l.value=t,En((()=>{c.value=t}))}return si("uni-picker-view",{ref:o},[si(Fh,{ref:a,onResize:({height:e})=>s.height=e},null,8,["onResize"]),si("div",{ref:r,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class Dp{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function Np(e,t,n){return e>t-n&&e<t+n}function Fp(e,t){return Np(e,0,t)}class Vp{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,s=t/(r*e);return{x:function(e){return(i+s*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+s*e)*t+s*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),s=(-n+Math.sqrt(i))/(2*o),a=(t-r*e)/(s-r),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*r*t+a*s*n}}}const s=Math.sqrt(4*o*r-n*n)/(2*o),a=-n/2*o,l=e,c=(t-a*e)/s;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(s*e)+c*Math.sin(s*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(s*e),o=Math.sin(s*e);return t*(c*s*n-l*s*o)+a*t*(c*o+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!Fp(t,.4)){t=t||0;let o=this._endPosition;this._solution&&(Fp(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),Fp(t,.4)&&(t=0),Fp(o,.4)&&(o=0),o+=this._endPosition),this._solution&&Fp(o-e,.4)&&Fp(t,.4)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),Np(this.x(),this._endPosition,.4)&&Fp(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class Hp{constructor(e,t,n){this._extent=e,this._friction=t||new Dp(.01),this._spring=n||new Vp(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class Wp{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new Hp(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let o;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;o=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,o<=0&&o>=-this._extent&&this._scroll.setVelocityByEnd(o)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);const i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}(this._scroll,(()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const o=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/o),this._lastTime=e)}),(()=>{this._enableSnap&&(o<=0&&o>=-this._extent&&(this._position=o,this.updatePosition()),g(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1}))}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),g(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(g(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let o=0;const r=this._position;this._enableX?(o=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(o=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-o?this._position=-o:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),g(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=o,this._scroll._extent=o}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}function zp(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new Wp(e,t);function r(e){const t=e,o=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:o.screenX-n.x,y:o.screenY-n.y}}return{scroller:o,handleTouchStart:function(e){const t=e,r=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=r.screenX,n.y=r.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||r.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,o=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const i=r(e);if(i){for(n.maxDy=Math.max(n.maxDy,Math.abs(i.y)),n.maxDx=Math.max(n.maxDx,Math.abs(i.x)),n.historyX.push(i.x),n.historyY.push(i.y),n.historyTime.push(t.detail.timeStamp||o.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(i.x,i.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=r(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const o={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,r=n.historyTime[t],i=n.historyX[t],s=n.historyY[t];t>0;){t--;const e=r-n.historyTime[t];if(e>30&&e<50){o.x=(i-n.historyX[t])/(e/1e3),o.y=(s-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,o)}}}}}const qp=ou({name:"PickerViewColumn",setup(e,{slots:t,emit:n}){const o=an(null),r=an(null),i=xr("getPickerViewColumn"),s=yi(),a=i?i(s):an(0),l=xr("pickerViewProps"),c=xr("pickerViewState"),u=an(34),d=an(null);Vo((()=>{const e=d.value;u.value=e.$el.offsetHeight}));const f=Oi((()=>(c.height-u.value)/2)),{state:h}=gp();let p;const m=zt({current:a.value,length:0});let g;function v(){p&&!g&&(g=!0,En((()=>{g=!1;let e=Math.min(m.current,m.length-1);e=Math.max(e,0),p.update(e*u.value,void 0,u.value)})))}no((()=>a.value),(e=>{e!==m.current&&(m.current=e,v())})),no((()=>m.current),(e=>a.value=e)),no([()=>u.value,()=>m.length,()=>c.height],v);let y=0;function b(e){const t=y+e.deltaY;if(Math.abs(t)>10){y=0;let e=Math.min(m.current+(t<0?-1:1),m.length-1);m.current=e=Math.max(e,0),p.scrollTo(e*u.value)}else y=t;e.preventDefault()}function _({clientY:e}){const t=o.value;if(!p.isScrolling()){const n=e-t.getBoundingClientRect().top-c.height/2,o=u.value/2;if(!(Math.abs(n)<=o)){const e=Math.ceil((Math.abs(n)-o)/u.value),t=n<0?-e:e;let r=Math.min(m.current+t,m.length-1);m.current=r=Math.max(r,0),p.scrollTo(r*u.value)}}}return Vo((()=>{const e=o.value,t=r.value,{scroller:n,handleTouchStart:i,handleTouchMove:s,handleTouchEnd:a}=zp(t,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:u.value,friction:new Dp(1e-4),spring:new Vp(2,90,20),onSnap:e=>{isNaN(e)||e===m.current||(m.current=e)}});p=n,Rp(e,(e=>{switch(e.detail.state){case"start":i(e);break;case"move":s(e),e.stopPropagation();break;case"end":case"cancel":a(e)}}),!0),function(e){let t=0,n=0;e.addEventListener("touchstart",(e=>{const o=e.changedTouches[0];t=o.clientX,n=o.clientY})),e.addEventListener("touchend",(e=>{const o=e.changedTouches[0];if(Math.abs(o.clientX-t)<20&&Math.abs(o.clientY-n)<20){const t={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},n=new CustomEvent("click",t);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{n[e]=o[e]})),e.target.dispatchEvent(n)}}))}(e),v()})),()=>{const e=t.default&&t.default();m.length=Ap(e).length;const n=`${f.value}px 0`;return si("uni-picker-view-column",{ref:o},[si("div",{onWheel:b,onClick:_,class:"uni-picker-view-group"},[si("div",hi(h.attrs,{class:["uni-picker-view-mask",l.maskClass],style:`background-size: 100% ${f.value}px;${l.maskStyle}`}),null,16),si("div",hi(h.attrs,{class:["uni-picker-view-indicator",l.indicatorClass],style:l.indicatorStyle}),[si(Fh,{ref:d,onResize:({height:e})=>u.value=e},null,8,["onResize"])],16),si("div",{ref:r,class:["uni-picker-view-content"],style:{padding:n,"--picker-view-column-indicator-height":`${u.value}px`}},[e],4)],40,["onWheel","onClick"])],512)}}}),Up=ou({name:"Refresher",props:{refreshState:{type:String,default:""},refresherHeight:{type:Number,default:0},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"}},setup(e,{slots:t}){const n=an(null),o=Oi((()=>{const t={backgroundColor:e.refresherBackground};switch(e.refreshState){case"pulling":t.height=e.refresherHeight+"px";break;case"refreshing":t.height=e.refresherThreshold+"px",t.transition="height 0.3s";break;case"":case"refresherabort":case"restore":t.height="0px",t.transition="height 0.3s"}return t})),r=Oi((()=>{const t=e.refresherHeight/e.refresherThreshold;return 360*(t>1?1:t)}));return()=>{const{refreshState:i,refresherDefaultStyle:s,refresherThreshold:a}=e;return si("div",{ref:n,style:o.value,class:"uni-scroll-view-refresher"},["none"!==s?si("div",{class:"uni-scroll-view-refresh"},[si("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==i?si("svg",{key:"refresh__icon",style:{transform:"rotate("+r.value+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[si("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),si("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==i?si("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[si("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"===s?si("div",{class:"uni-scroll-view-refresher-container",style:{height:`${a}px`}},[t.default&&t.default()]):null],4)}}}),Yp=_e(!0),Xp=ou({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n,expose:o}){const r=an(null),i=an(null),s=an(null),a=an(null),l=su(r,t),{state:c,scrollTopNumber:u,scrollLeftNumber:d}=function(e){const t=Oi((()=>Number(e.scrollTop)||0)),n=Oi((()=>Number(e.scrollLeft)||0));return{state:zt({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshState:""}),scrollTopNumber:t,scrollLeftNumber:n}}(e),{realScrollX:f,realScrollY:h,_scrollLeftChanged:p,_scrollTopChanged:m}=function(e,t,n,o,r,i,s,a,l){let c=!1,u=0,d=!1,f=()=>{};const h=Oi((()=>e.scrollX)),p=Oi((()=>e.scrollY)),m=Oi((()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t})),g=Oi((()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function v(e,t){const n=s.value;let o=0,r="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let i=a.value;i.style.transition="transform .3s ease-out",i.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?r="translateX("+o+"px) translateZ(0)":"y"===t&&(r="translateY("+o+"px) translateZ(0)"),i.removeEventListener("transitionend",f),i.removeEventListener("webkitTransitionEnd",f),f=()=>x(e,t),i.addEventListener("transitionend",f),i.addEventListener("webkitTransitionEnd",f),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),i.style.transform=r,i.style.webkitTransform=r}function y(e){const n=e.target;r("scroll",e,{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop,scrollHeight:n.scrollHeight,scrollWidth:n.scrollWidth,deltaX:t.lastScrollLeft-n.scrollLeft,deltaY:t.lastScrollTop-n.scrollTop}),p.value&&(n.scrollTop<=m.value&&t.lastScrollTop-n.scrollTop>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"top"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollTop+n.offsetHeight+g.value>=n.scrollHeight&&t.lastScrollTop-n.scrollTop<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"bottom"}),t.lastScrollToLowerTime=e.timeStamp)),h.value&&(n.scrollLeft<=m.value&&t.lastScrollLeft-n.scrollLeft>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"left"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollLeft+n.offsetWidth+g.value>=n.scrollWidth&&t.lastScrollLeft-n.scrollLeft<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"right"}),t.lastScrollToLowerTime=e.timeStamp)),t.lastScrollTop=n.scrollTop,t.lastScrollLeft=n.scrollLeft}function b(t){p.value&&(e.scrollWithAnimation?v(t,"y"):s.value.scrollTop=t)}function _(t){h.value&&(e.scrollWithAnimation?v(t,"x"):s.value.scrollLeft=t)}function w(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error(`id error: scroll-into-view=${t}`);let n=i.value.querySelector("#"+t);if(n){let t=s.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(h.value){let n=o.left-t.left,r=s.value.scrollLeft+n;e.scrollWithAnimation?v(r,"x"):s.value.scrollLeft=r}if(p.value){let n=o.top-t.top,r=s.value.scrollTop+n;e.scrollWithAnimation?v(r,"y"):s.value.scrollTop=r}}}}function x(e,t){a.value.style.transition="",a.value.style.webkitTransition="",a.value.style.transform="",a.value.style.webkitTransform="";let n=s.value;"x"===t?(n.style.overflowX=h.value?"auto":"hidden",n.scrollLeft=e):"y"===t&&(n.style.overflowY=p.value?"auto":"hidden",n.scrollTop=e),a.value.removeEventListener("transitionend",f),a.value.removeEventListener("webkitTransitionEnd",f)}function S(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,r("refresherpulling",{},{deltaY:t.refresherHeight,dy:t.refresherHeight}),r("refresherrefresh",{},{dy:C.y-T.y}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(d=!1,r("refresherrestore",{},{dy:C.y-T.y})),"refresherabort"===n&&d&&(d=!1,r("refresherabort",{},{dy:C.y-T.y}))}t.refreshState=n}}let T={x:0,y:0},C={x:0,y:e.refresherThreshold};return Vo((()=>{En((()=>{b(n.value),_(o.value)})),w(e.scrollIntoView);let i=function(e){e.preventDefault(),e.stopPropagation(),y(e)},a=null,l=function(n){if(null===T)return;let o=n.touches[0].pageX,i=n.touches[0].pageY,l=s.value;if(Math.abs(o-T.x)>Math.abs(i-T.y))if(h.value){if(0===l.scrollLeft&&o>T.x)return void(a=!1);if(l.scrollWidth===l.offsetWidth+l.scrollLeft&&o<T.x)return void(a=!1);a=!0}else a=!1;else if(p.value)if(0===l.scrollTop&&i>T.y)a=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(l.scrollHeight===l.offsetHeight+l.scrollTop&&i<T.y)return void(a=!1);a=!0}else a=!1;if(a&&n.stopPropagation(),0===l.scrollTop&&1===n.touches.length&&S("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=i-T.y;0===u&&(u=i),c?(t.refresherHeight=o+e.refresherThreshold,d=!1):(t.refresherHeight=i-u,t.refresherHeight>0&&(d=!0,r("refresherpulling",n,{deltaY:o,dy:o})))}},f=function(e){1===e.touches.length&&(T={x:e.touches[0].pageX,y:e.touches[0].pageY})},m=function(n){C={x:n.changedTouches[0].pageX,y:n.changedTouches[0].pageY},t.refresherHeight>=e.refresherThreshold?S("refreshing"):S("refresherabort"),T={x:0,y:0},C={x:0,y:e.refresherThreshold}};s.value.addEventListener("touchstart",f,Yp),s.value.addEventListener("touchmove",l,_e(!1)),s.value.addEventListener("scroll",i,_e(!1)),s.value.addEventListener("touchend",m,Yp),zo((()=>{s.value.removeEventListener("touchstart",f),s.value.removeEventListener("touchmove",l),s.value.removeEventListener("scroll",i),s.value.removeEventListener("touchend",m)}))})),Po((()=>{p.value&&(s.value.scrollTop=t.lastScrollTop),h.value&&(s.value.scrollLeft=t.lastScrollLeft)})),no(n,(e=>{b(e)})),no(o,(e=>{_(e)})),no((()=>e.scrollIntoView),(e=>{w(e)})),no((()=>e.refresherTriggered),(e=>{!0===e?S("refreshing"):!1===e&&S("restore")})),{realScrollX:h,realScrollY:p,_scrollTopChanged:b,_scrollLeftChanged:_}}(e,c,u,d,l,r,i,a,t),g=Oi((()=>{let e="";return f.value?e+="overflow-x:auto;":e+="overflow-x:hidden;",h.value?e+="overflow-y:auto;":e+="overflow-y:hidden;",e})),v=Oi((()=>{let t="uni-scroll-view";return!1===e.showScrollbar&&(t+=" uni-scroll-view-scrollbar-hidden"),t}));return o({$getMain:()=>i.value}),()=>{const{refresherEnabled:t,refresherBackground:o,refresherDefaultStyle:l,refresherThreshold:u}=e,{refresherHeight:d,refreshState:f}=c;return si("uni-scroll-view",{ref:r},[si("div",{ref:s,class:"uni-scroll-view"},[si("div",{ref:i,style:g.value,class:v.value},[t?si(Up,{refreshState:f,refresherHeight:d,refresherThreshold:u,refresherDefaultStyle:l,refresherBackground:o},{default:()=>["none"==l?n.refresher&&n.refresher():null]},8,["refreshState","refresherHeight","refresherThreshold","refresherDefaultStyle","refresherBackground"]):null,si("div",{ref:a,class:"uni-scroll-view-content"},[n.default&&n.default()],512)],6)],512)],512)}}});const Gp=ou({name:"Switch",props:{name:{type:String,default:""},checked:{type:[Boolean,String],default:!1},type:{type:String,default:"switch"},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:""}},emits:["change"],setup(e,{emit:t}){const n=an(null),o=an(e.checked),r=function(e,t){const n=xr(uu,!1),o=xr(du,!1),r={submit:()=>{const n=["",null];return e.name&&(n[0]=e.name,n[1]=t.value),n},reset:()=>{t.value=!1}};n&&(n.addField(r),qo((()=>{n.removeField(r)})));return o}(e,o),i=su(n,t);no((()=>e.checked),(e=>{o.value=e}));const s=t=>{e.disabled||(o.value=!o.value,i("change",t,{value:o.value}))};return r&&(r.addHandler(s),zo((()=>{r.removeHandler(s)}))),fu(e,{"label-click":s}),()=>{const{color:t,type:r}=e,i=cu(e,"disabled"),a={};let l;return t&&o.value&&(a.backgroundColor=t,a.borderColor=t),l=o.value,si("uni-switch",hi({id:e.id,ref:n},i,{onClick:s}),[si("div",{class:"uni-switch-wrapper"},[ao(si("div",{class:["uni-switch-input",[o.value?"uni-switch-input-checked":""]],style:a},null,6),[[Ji,"switch"===r]]),ao(si("div",{class:"uni-checkbox-input"},[l?pc(fc,e.color,22):""],512),[[Ji,"checkbox"===r]])])],16,["id","onClick"])}}});const Kp={ensp:" ",emsp:" ",nbsp:" "};function Jp(e,t){return function(e,{space:t,decode:n}){let o="",r=!1;for(let i of e)t&&Kp[t]&&" "===i&&(i=Kp[t]),r?(o+="n"===i?"\n":"\\"===i?"\\":"\\"+i,r=!1):"\\"===i?r=!0:o+=i;return n?o.replace(/&nbsp;/g,Kp.nbsp).replace(/&ensp;/g,Kp.ensp).replace(/&emsp;/g,Kp.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):o}(e,t).split("\n")}const Qp=ou({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=an(null);return()=>{const o=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==zr){const n=Jp(t.children,{space:e.space,decode:e.decode}),r=n.length-1;n.forEach(((e,t)=>{(0!==t||e)&&o.push(li(e)),t!==r&&o.push(si("br"))}))}else o.push(t)})),si("uni-text",{ref:n,selectable:!!e.selectable||null},[si("span",null,o)],8,["selectable"])}}}),Zp=ou({name:"View",props:c({},au),setup(e,{slots:t}){const n=an(null),{hovering:o,binding:r}=lu(e);return()=>{const i=e.hoverClass;return i&&"none"!==i?si("uni-view",hi({class:o.value?i:"",ref:n},r),[Qo(t,"default")],16):si("uni-view",{ref:n},[Qo(t,"default")],512)}}});function em(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function tm(e,t,n){e&&Ml(n||bc(),e,(({type:e,data:n},o)=>{t(e,n,o)}))}function nm(e,t){e&&function(e,t){t=Al(e,t),delete $l[t]}(t||bc(),e)}let om=0;function rm(e,t,n,o){g(t)&&Do(e,t.bind(n),o)}function im(e,t,n){const o=e.mpType||n.$mpType;if(o&&"component"!==o&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!g(t))&&(Oe.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];h(r)?r.forEach((e=>rm(o,e,n,t))):rm(o,r,n,t)}})),"page"===o)){t.__isVisible=!0;try{let e=t.attrs.__pageQuery;0,Tc(n,"onLoad",e),delete t.attrs.__pageQuery;const o=n.$page;"preloadPage"!==(null==o?void 0:o.openType)&&Tc(n,"onShow")}catch(r){console.error(r.message+"\n"+r.stack)}}}function sm(e,t,n){im(e,t,n)}function am(e,t,n){return e[t]=n}function lm(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function cm(e){const t=e.config.errorHandler;return function(n,o,r){t&&t(n,o,r);const i=e._instance;if(!i||!i.proxy)throw n;i.onError?Tc(i.proxy,"onError",n):vn(n,0,o&&o.$.vnode,!1)}}function um(e,t){return e?[...new Set([].concat(e,t))]:t}function dm(e){const t=e.config;var n;t.errorHandler=$e(e,cm),n=t.optionMergeStrategies,Oe.forEach((e=>{n[e]=um}));const o=t.globalProperties;o.$set=am,o.$applyOptions=sm,o.$callMethod=lm,function(e){Pe.forEach((t=>t(e)))}(e)}function fm(e){const t=ol({history:mm(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:pm});t.beforeEach(((e,t)=>{var n;e&&t&&e.meta.isTabBar&&t.meta.isTabBar&&(n=t.meta.tabBarIndex,"undefined"!=typeof window&&(hm[n]={left:window.pageXOffset,top:window.pageYOffset}))})),e.router=t,e.use(t)}let hm=Object.create(null);const pm=(e,t,n)=>{if(n)return n;if(e&&t&&e.meta.isTabBar&&t.meta.isTabBar){const t=(o=e.meta.tabBarIndex,hm[o]);if(t)return t}return{left:0,top:0};var o};function mm(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=(n=e,(n=location.host?n||location.pathname+location.search:"").includes("#")||(n+="#"),va(n));var n;return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=oh(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=Gf(t[r]);rh(lh(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const gm={install(e){dm(e),Vc(e),Qc(e),e.config.warnHandler||(e.config.warnHandler=vm),fm(e)}};function vm(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const ym={class:"uni-async-loading"},bm=si("i",{class:"uni-loading"},null,-1),_m=ru({name:"AsyncLoading",render:()=>(Xr(),Zr("div",ym,[bm]))});function wm(){window.location.reload()}const xm=ru({name:"AsyncError",setup(){xl();const{t:e}=_l();return()=>si("div",{class:"uni-async-error",onClick:wm},[e("uni.async.error")],8,["onClick"])}});let Sm;function Tm(){return Sm}function Cm(e){Sm=e,Object.defineProperty(Sm.$.ctx,"$children",{get:()=>oh().map((e=>e.$vm))});const t=Sm.$.appContext.app;t.component(_m.name)||t.component(_m.name,_m),t.component(xm.name)||t.component(xm.name,xm),function(e){e.$vm=e,e.$mpType="app";const t=an(_l().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(Sm),function(e,t){const n=e.$options||{};n.globalData=c(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(Sm),Kc(),$c()}function km(e,{clone:t,init:n,setup:o,before:r}){t&&(e=c({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=yi();if(n(r.proxy),o(r),i)return i(e,t)},e}function Em(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?km(e.default,t):km(e,t)}function Lm(e){return Em(e,{clone:!0,init:ah,setup(e){e.$pageInstance=e;const t=bu(),n=Se(t.query);e.attrs.__pageQuery=n,Gf(e.proxy).options=n,e.proxy.options=n;const o=vu();var r,i;return e.onReachBottom=zt([]),e.onPageScroll=zt([]),no([e.onReachBottom,e.onPageScroll],(()=>{const t=vc();e.proxy===t&&gh(e,o)}),{once:!0}),Fo((()=>{dh(e,o)})),Vo((()=>{fh(e);const{onReady:n}=e;n&&I(n),Am(t)})),Ao((()=>{if(!e.__isVisible){dh(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&I(n),En((()=>{Am(t)}))}}),"ba",r),function(e,t){Ao(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;{const{onHide:t}=e;t&&I(t)}}})),i=o.id,Av.subscribe(Al(i,"invokeViewApi"),Il),zo((()=>{!function(e){Av.unsubscribe(Al(e,"invokeViewApi")),Object.keys($l).forEach((t=>{0===t.indexOf(e+".")&&delete $l[t]}))}(o.id)})),n}})}function Om(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=Ym(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";Mv.emit("onResize",{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function Pm(e){S(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&Mv.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function $m(){const{emit:e}=Mv;"visible"===document.visibilityState?e("onAppEnterForeground",c({},Nh)):e("onAppEnterBackground")}function Am(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&Tc("onTabItemTap",{index:n,text:t,pagePath:o})}const Mm=ie((()=>{jd.forEach((e=>{Im.prototype[e]=function(t){g(t)&&this._events[e].push(t)}})),Dd.forEach((e=>{Im.prototype[e]=function(t){var n=this._events[e.replace("off","on")],o=n.indexOf(t);o>=0&&n.splice(o,1)}}))}));class Im{constructor(){this._src="";var e=this._audio=new Audio;this._stoping=!1;["src","autoplay","loop","duration","currentTime","paused","volume"].forEach((t=>{Object.defineProperty(this,t,{set:"src"===t?t=>(e.src=yh(t),this._src=t,t):n=>(e[t]=n,n),get:"src"===t?()=>this._src:()=>e[t]})})),this.startTime=0,Object.defineProperty(this,"obeyMuteSwitch",{set:()=>!1,get:()=>!1}),Object.defineProperty(this,"buffered",{get(){var t=e.buffered;return t.length?t.end(t.length-1):0}}),this._events={},jd.forEach((e=>{this._events[e]=[]})),e.addEventListener("loadedmetadata",(()=>{var t=Number(this.startTime)||0;t>0&&(e.currentTime=t)}));var t=["canplay","pause","seeking","seeked","timeUpdate"];t.concat(["play","ended","error","waiting"]).forEach((n=>{e.addEventListener(n.toLowerCase(),(()=>{if(this._stoping&&t.indexOf(n)>=0)return;const e=`on${n.slice(0,1).toUpperCase()}${n.slice(1)}`;this._events[e].forEach((e=>{e()}))}),!1)})),Mm()}play(){this._stoping=!1,this._audio.play()}pause(){this._audio.pause()}stop(){this._stoping=!0,this._audio.pause(),this._audio.currentTime=0,this._events.onStop.forEach((e=>{e()}))}seek(e){this._stoping=!1,"number"!=typeof(e=Number(e))||isNaN(e)||(this._audio.currentTime=e)}destroy(){this.stop()}}const Bm=od(0,(()=>new Im)),Rm=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let jm;function Dm(){if(jm=jm||Rm.__DC_STAT_UUID,!jm){jm=Date.now()+""+Math.floor(1e7*Math.random());try{Rm.__DC_STAT_UUID=jm}catch(e){}}return jm}function Nm(){if(!0!==__uniConfig.darkmode)return v(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function Fm(){let e,t="0",n="",o="phone";const r=navigator.language;if(wh){e="iOS";const o=bh.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=bh.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(_h){e="Android";const o=bh.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=bh.match(/\((.+?)\)/),i=r?r[1].split(";"):bh.split(" "),s=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<s.length;e++)if(s[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(Ch){if(n="iPad",e="iOS",o="pad",t=g(window.BigInt)?"14.0":"13.0",14===parseInt(t)){const e=bh.match(/Version\/(\S*)\b/);e&&(t=e[1])}}else if(xh||Sh||Th){n="PC",e="PC",o="pc",t="0";let r=bh.match(/\((.+?)\)/)[1];if(xh){switch(e="Windows",xh[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(Sh){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(Th){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,s=e.toLocaleLowerCase();let a="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)a="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(bh)&&(a=t[n],l=bh.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:s,browserName:a.toLocaleLowerCase(),browserVersion:l,language:r,deviceType:o,ua:bh,osname:e,osversion:t,theme:Nm()}}const Vm=od(0,(()=>{const e=window.devicePixelRatio,t=kh(),n=Eh(t),o=Lh(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=Oh(o);let s=window.innerHeight;const a=tc.top,l={left:tc.left,right:i-tc.right,top:tc.top,bottom:s-tc.bottom,width:i-tc.left-tc.right,height:s-tc.top-tc.bottom},{top:c,bottom:u}=sc();return s-=c,s-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:s,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:a,safeArea:l,safeAreaInsets:{top:tc.top,right:tc.right,bottom:tc.bottom,left:tc.left},screenTop:r-s}}));let Hm,Wm=!0;function zm(){Wm&&(Hm=Fm())}const qm=od(0,(()=>{zm();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:s,deviceType:a,osname:l,osversion:u}=Hm;return c({brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:Dm(),deviceOrientation:s,deviceType:a,model:o,platform:r,system:i,osName:l?l.toLocaleLowerCase():void 0,osVersion:u})})),Um=od(0,(()=>{zm();const{theme:e,language:t,browserName:n,browserVersion:o}=Hm;return c({appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Xd?Xd():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:"",uniPlatform:"web",isUniAppX:!1,uniCompileVersion:__uniConfig.compilerVersion,uniCompilerVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion},{})})),Ym=od(0,(()=>{Wm=!0,zm(),Wm=!1;const e=Vm(),t=qm(),n=Um();Wm=!0;const{ua:o,browserName:r,browserVersion:i,osname:s,osversion:a}=Hm,l=c(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:s.toLocaleLowerCase(),osVersion:a,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return S(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)})),Xm=rd("getSystemInfo",((e,{resolve:t})=>t(Ym())));function Gm(){Qm().then((({networkType:e})=>{Mv.invokeOnCallback("onNetworkStatusChange",{isConnected:"none"!==e,networkType:e})}))}function Km(){return navigator.connection||navigator.webkitConnection||navigator.mozConnection}const Jm=ed("onNetworkStatusChange",(()=>{const e=Km();e?e.addEventListener("change",Gm):(window.addEventListener("offline",Gm),window.addEventListener("online",Gm))})),Qm=rd("getNetworkType",((e,{resolve:t})=>{const n=Km();let o="unknown";return n?(o=n.type,"cellular"===o&&n.effectiveType?o=n.effectiveType.replace("slow-",""):!o&&n.effectiveType?o=n.effectiveType:["none","wifi"].includes(o)||(o="unknown")):!1===navigator.onLine&&(o="none"),t({networkType:o})}));const Zm=od(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)}));function eg(e){const t=localStorage&&localStorage.getItem(e);if(!v(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=v(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const tg=od(0,(e=>{try{return eg(e)}catch(t){return""}})),ng=od(0,(e=>{localStorage&&localStorage.removeItem(e)})),og=od(0,(()=>{localStorage&&localStorage.clear()})),rg={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function ig({count:e,sourceType:t,type:n,extension:o}){hp();const r=document.createElement("input");return r.type="file",function(e,t){for(const n in t)e.style[n]=t[n]}(r,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),r.accept=o.map((e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${rg[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`})).join(","),e&&e>1&&(r.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&r.setAttribute("capture","camera"),r}let sg=null;const ag=rd("chooseFile",(({count:e,sourceType:t,type:n,extension:o},{resolve:r,reject:i})=>{El();const{t:s}=_l();sg&&(document.body.removeChild(sg),sg=null),sg=ig({count:e,sourceType:t,type:n,extension:o}),document.body.appendChild(sg),sg.addEventListener("change",(function(t){const n=t.target,o=[];if(n&&n.files){const t=n.files.length;for(let r=0;r<t;r++){const t=n.files[r];let i;Object.defineProperty(t,"path",{get:()=>(i=i||Rh(t),i)}),r<e&&o.push(t)}}r({get tempFilePaths(){return o.map((({path:e})=>e))},tempFiles:o})})),sg.click(),pp()||console.warn(s("uni.chooseFile.notUserActivation"))}),0,lf);let lg=null;const cg=rd("chooseImage",(({count:e,sourceType:t,extension:n},{resolve:o,reject:r})=>{El();const{t:i}=_l();lg&&(document.body.removeChild(lg),lg=null),lg=ig({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(lg),lg.addEventListener("change",(function(t){const n=t.target,r=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let i;Object.defineProperty(t,"path",{get:()=>(i=i||Rh(t),i)}),o<e&&r.push(t)}}o({get tempFilePaths(){return r.map((({path:e})=>e))},tempFiles:r})})),lg.click(),pp()||console.warn(i("uni.chooseFile.notUserActivation"))}),0,rf),ug={esc:["Esc","Escape"],enter:["Enter"]},dg=Object.keys(ug);function fg(){const e=an(""),t=an(!1),n=n=>{if(t.value)return;const o=dg.find((e=>-1!==ug[e].indexOf(n.key)));o&&(e.value=o),En((()=>e.value=""))};return Vo((()=>{document.addEventListener("keyup",n)})),zo((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}const hg=si("div",{class:"uni-mask"},null,-1);function pg(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),ks(wo({setup:()=>()=>(Xr(),Zr(e,t,null,16))}))}function mg(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function gg(e,{onEsc:t,onEnter:n}){const o=an(e.visible),{key:r,disable:i}=fg();return no((()=>e.visible),(e=>o.value=e)),no((()=>o.value),(e=>i.value=!e)),eo((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}let vg=null;const yg=rd("chooseVideo",(({sourceType:e,extension:t},{resolve:n,reject:o})=>{El();const{t:r}=_l();vg&&(document.body.removeChild(vg),vg=null),vg=ig({sourceType:e,extension:t,type:"video"}),document.body.appendChild(vg),vg.addEventListener("change",(function(e){const t=e.target.files[0];let o="";const r={tempFilePath:o,tempFile:t,size:t.size,duration:0,width:0,height:0,name:t.name};Object.defineProperty(r,"tempFilePath",{get(){return o=o||Rh(this.tempFile),o}});const i=document.createElement("video");if(void 0!==i.onloadedmetadata){const e=Rh(t);i.onloadedmetadata=function(){jh(e),n(c(r,{duration:i.duration||0,width:i.videoWidth||0,height:i.videoHeight||0}))},setTimeout((()=>{i.onloadedmetadata=null,jh(e),n(r)}),300),i.src=e}else n(r)})),vg.click(),pp()||console.warn(r("uni.chooseFile.notUserActivation"))}),0,sf),bg=nd("request",(({url:e,data:t,header:n={},method:o,dataType:r,responseType:i,withCredentials:s,timeout:a=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const d=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(v(t)||t instanceof ArrayBuffer)u=t;else if("json"===d)try{u=JSON.stringify(t)}catch(g){u=t.toString()}else if("urlencoded"===d){const e=[];for(const n in t)f(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const h=new XMLHttpRequest,p=new _g(h);h.open(o,e);for(const v in n)f(n,v)&&h.setRequestHeader(v,n[v]);const m=setTimeout((function(){h.onload=h.onabort=h.onerror=null,p.abort(),c("timeout",{errCode:5})}),a);return h.responseType=i,h.onload=function(){clearTimeout(m);const e=h.status;let t="text"===i?h.responseText:h.response;if("text"===i&&"json"===r)try{t=JSON.parse(t)}catch(g){}l({data:t,statusCode:e,header:wg(h.getAllResponseHeaders()),cookies:[]})},h.onabort=function(){clearTimeout(m),c("abort",{errCode:600003})},h.onerror=function(){clearTimeout(m),c(void 0,{errCode:5})},h.withCredentials=s,h.send(u),p}),0,ff);class _g{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function wg(e){const t={};return e.split("\n").forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}class xg{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){g(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const Sg=nd("uploadFile",(({url:e,file:t,filePath:n,name:o,files:r,header:i={},formData:s={},timeout:a=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new xg;return h(r)&&r.length||(r=[{name:o,file:t,uri:n}]),Promise.all(r.map((({file:e,uri:t})=>e instanceof Blob?Promise.resolve(Bh(e)):Ih(t)))).then((function(t){var n,o=new XMLHttpRequest,d=new FormData;Object.keys(s).forEach((e=>{d.append(e,s[e])})),Object.values(r).forEach((({name:e},n)=>{const o=t[n];d.append(e||"file",o,o.name||`file-${Date.now()}`)})),o.open("POST",e),Object.keys(i).forEach((e=>{o.setRequestHeader(e,i[e])})),o.upload.onprogress=function(e){u._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})}))},o.onerror=function(){clearTimeout(n),c("",{errCode:602001})},o.onabort=function(){clearTimeout(n),c("abort",{errCode:600003})},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort",{errCode:600003}):(n=setTimeout((function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout",{errCode:5})}),a),o.send(d),u._xhr=o)})).catch((()=>{setTimeout((()=>{c("file error")}),0)})),u}),0,hf),Tg=[],Cg={open:"",close:"",error:"",message:""};class kg{constructor(e,t,n){let o;this._callbacks={open:[],close:[],error:[],message:[]};try{const n=this._webSocket=new WebSocket(e,t);n.binaryType="arraybuffer";["open","close","error","message"].forEach((e=>{this._callbacks[e]=[],n.addEventListener(e,(t=>{const{data:n,code:o,reason:r}=t,i="message"===e?{data:n}:"close"===e?{code:o,reason:r}:{};if(this._callbacks[e].forEach((t=>{try{t(i)}catch(n){console.error(`thirdScriptError\n${n};at socketTask.on${$(e)} callback function\n`,n)}})),this===Tg[0]&&Cg[e]&&Mv.invokeOnCallback(Cg[e],i),"error"===e||"close"===e){const e=Tg.indexOf(this);e>=0&&Tg.splice(e,1)}}))}));["CLOSED","CLOSING","CONNECTING","OPEN","readyState"].forEach((e=>{Object.defineProperty(this,e,{get:()=>n[e]})}))}catch(r){o=r}n&&n(o,this)}send(e){const t=(e||{}).data,n=this._webSocket;try{if(n.readyState!==n.OPEN)throw le(e,{errMsg:"sendSocketMessage:fail SocketTask.readyState is not OPEN",errCode:10002}),new Error("SocketTask.readyState is not OPEN");n.send(t),le(e,"sendSocketMessage:ok")}catch(o){le(e,{errMsg:`sendSocketMessage:fail ${o}`,errCode:602001})}}close(e={}){const t=this._webSocket;try{const n=e.code||1e3,o=e.reason;v(o)?t.close(n,o):t.close(n),le(e,"closeSocket:ok")}catch(n){le(e,`closeSocket:fail ${n}`)}}onOpen(e){this._callbacks.open.push(e)}onMessage(e){this._callbacks.message.push(e)}onError(e){this._callbacks.error.push(e)}onClose(e){this._callbacks.close.push(e)}}const Eg=nd("connectSocket",(({url:e,protocols:t},{resolve:n,reject:o})=>new kg(e,t,((e,t)=>{e?o(e.toString(),{errCode:600009}):(Tg.push(t),n())}))),0,pf),Lg=rd("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===Tc("onBackPress",{from:e.from||"navigateBack"})&&(o=!1),o?(Tm().$router.go(-e.delta),t()):n("onBackPress")}),0,bf),Og=rd("navigateTo",(({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(Kf.handledBeforeEntryPageRoutes)return Df({type:"navigateTo",url:e,events:t,isAutomatedTesting:n}).then(o).catch(r);Jf.push({args:{type:"navigateTo",url:e,events:t,isAutomatedTesting:n},resolve:o,reject:r})}),0,gf);function Pg(e){__uniConfig.darkmode&&Mv.on("onThemeChange",e)}function $g(e){Mv.off("onThemeChange",e)}function Ag(e){let t={};return __uniConfig.darkmode&&(t=Re(e,__uniConfig.themeConfig,Nm())),__uniConfig.darkmode?t:e}const Mg={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},Ig=wo({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=an(""),o=()=>s.value=!1,r=()=>(o(),t("close","cancel")),i=()=>(o(),t("close","confirm",n.value)),s=gg(e,{onEsc:r,onEnter:()=>{!e.editable&&i()}}),a=function(e){const t=an(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=Mg[e].cancelColor})(e,t)};return eo((()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===Nm()&&n({theme:"dark"}),Pg(n))):$g(n)})),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:d,placeholderText:f}=e;return n.value=o,si(ji,{name:"uni-fade"},{default:()=>[ao(si("uni-modal",{onTouchmove:nc},[hg,si("div",{class:"uni-modal"},[t?si("div",{class:"uni-modal__hd"},[si("strong",{class:"uni-modal__title",textContent:t||""},null,8,["textContent"])]):null,d?si("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:f,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):si("div",{class:"uni-modal__bd",onTouchmovePassive:oc,textContent:o},null,40,["onTouchmovePassive","textContent"]),si("div",{class:"uni-modal__ft"},[l&&si("div",{style:{color:a.value},class:"uni-modal__btn uni-modal__btn_default",onClick:r},[e.cancelText],12,["onClick"]),si("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:i},[c],12,["onClick"])])])],40,["onTouchmove"]),[[Ji,s.value]])]})}}});let Bg;const Rg=ie((()=>{Mv.on("onHidePopup",(()=>Bg.visible=!1))}));let jg;function Dg(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&Bg.editable&&(o.content=t),jg&&jg(o)}const Ng=rd("showModal",((e,{resolve:t})=>{Rg(),jg=t,Bg?(c(Bg,e),Bg.visible=!0):(Bg=zt(e),En((()=>(pg(Ig,Bg,Dg).mount(mg("u-a-m")),En((()=>Bg.visible=!0))))))}),0,Ef),Fg={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==Lf.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},Vg={light:"#fff",dark:"rgba(255,255,255,0.9)"},Hg=e=>Vg[e],Wg=wo({name:"Toast",props:Fg,setup(e){Tl(),Cl();const{Icon:t}=function(e){const t=an(Hg(Nm())),n=({theme:e})=>t.value=Hg(e);eo((()=>{e.visible?Pg(n):$g(n)}));return{Icon:Oi((()=>{switch(e.icon){case"success":return si(pc(fc,t.value,38),{class:"uni-toast__icon"});case"error":return si(pc(hc,t.value,38),{class:"uni-toast__icon"});case"loading":return si("i",{class:["uni-toast__icon","uni-loading"]},null,2);default:return null}}))}}(e),n=gg(e,{});return()=>{const{mask:o,duration:r,title:i,image:s}=e;return si(ji,{name:"uni-fade"},{default:()=>[ao(si("uni-toast",{"data-duration":r},[o?si("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:nc},null,40,["onTouchmove"]):"",s||t.value?si("div",{class:"uni-toast"},[s?si("img",{src:s,class:"uni-toast__icon"},null,10,["src"]):t.value,si("p",{class:"uni-toast__content"},[i])]):si("div",{class:"uni-sample-toast"},[si("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[Ji,n.value]])]})}}});let zg,qg,Ug="";const Yg=Fe();function Xg(e){zg?c(zg,e):(zg=zt(c(e,{visible:!1})),En((()=>{Yg.run((()=>{no([()=>zg.visible,()=>zg.duration],(([e,t])=>{if(e){if(qg&&clearTimeout(qg),"onShowLoading"===Ug)return;qg=setTimeout((()=>{Zg("onHideToast")}),t)}else qg&&clearTimeout(qg)}))})),Mv.on("onHidePopup",(()=>Zg("onHidePopup"))),pg(Wg,zg,(()=>{})).mount(mg("u-a-t"))}))),setTimeout((()=>{zg.visible=!0}),10)}const Gg=rd("showToast",((e,{resolve:t,reject:n})=>{Xg(e),Ug="onShowToast",t()}),0,Of),Kg={icon:"loading",duration:1e8,image:""},Jg=rd("showLoading",((e,{resolve:t,reject:n})=>{c(e,Kg),Xg(e),Ug="onShowLoading",t()}),0,kf),Qg=rd("hideLoading",((e,{resolve:t,reject:n})=>{Zg("onHideLoading"),t()}));function Zg(e){const{t:t}=_l();if(!Ug)return;let n="";if("onHideToast"===e&&"onShowToast"!==Ug?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==Ug&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);Ug="",setTimeout((()=>{zg.visible=!1}),10)}function ev(e){const t=an(0),n=an(0),o=Oi((()=>t.value>=500&&n.value>=500)),r=Oi((()=>{const t={content:{transform:"",left:"",top:"",bottom:""},triangle:{left:"",top:"",bottom:"","border-width":"","border-color":""}},r=t.content,i=t.triangle,s=e.popover;function a(e){return Number(e)||0}if(o.value&&s){c(i,{position:"absolute",width:"0",height:"0","margin-left":"-6px","border-style":"solid"});const e=a(s.left),t=a(s.width),o=a(s.top),l=a(s.height),u=e+t/2;r.transform="none !important";const d=Math.max(0,u-150);r.left=`${d}px`;let f=Math.max(12,u-d);f=Math.min(288,f),i.left=`${f}px`;const h=n.value/2;o+l-h>h-o?(r.top="auto",r.bottom=n.value-o+6+"px",i.bottom="-6px",i["border-width"]="6px 6px 0 6px",i["border-color"]="#fcfcfd transparent transparent transparent"):(r.top=`${o+l+6}px`,i.top="-6px",i["border-width"]="0 6px 6px 6px",i["border-color"]="transparent transparent #fcfcfd transparent")}return t}));return Vo((()=>{const e=()=>{const{windowWidth:e,windowHeight:o,windowTop:r}=Ym();t.value=e,n.value=o+(r||0)};window.addEventListener("resize",e),e(),qo((()=>{window.removeEventListener("resize",e)}))})),{isDesktop:o,popupStyle:r}}const tv={light:{listItemColor:"#000000",cancelItemColor:"#000000"},dark:{listItemColor:"rgba(255, 255, 255, 0.8)",cancelItemColor:"rgba(255, 255, 255)"}};const nv=wo({name:"ActionSheet",props:{title:{type:String,default:""},itemList:{type:Array,default:()=>[]},itemColor:{type:String,default:"#000000"},popover:{type:Object,default:null},visible:{type:Boolean,default:!1}},emits:["close"],setup(e,{emit:t}){Sl();const n=an(260),o=an(0),r=an(0),i=an(0),s=an(0),a=an(null),l=an(null),{t:c}=_l(),{_close:u}=function(e,t){function n(e){t("close",e)}const{key:o,disable:r}=fg();return no((()=>e.visible),(e=>r.value=!e)),eo((()=>{const{value:e}=o;"esc"===e&&n&&n(-1)})),{_close:n}}(e,t),{popupStyle:d}=ev(e);let f;function h(e){const t=i.value+e.deltaY;Math.abs(t)>10?(s.value+=t/3,s.value=s.value>=o.value?o.value:s.value<=0?0:s.value,f.scrollTo(s.value)):i.value=t,e.preventDefault()}Vo((()=>{const{scroller:e,handleTouchStart:t,handleTouchMove:n,handleTouchEnd:o}=zp(a.value,{enableY:!0,friction:new Dp(1e-4),spring:new Vp(2,90,20),onScroll:e=>{s.value=e.target.scrollTop}});f=e,Rp(a.value,(r=>{if(e)switch(r.detail.state){case"start":t(r);break;case"move":n(r);break;case"end":case"cancel":o(r)}}),!0)})),no((()=>e.visible),(()=>{En((()=>{e.title&&(r.value=document.querySelector(".uni-actionsheet__title").offsetHeight),f.update(),a.value&&(o.value=a.value.clientHeight-n.value),document.querySelectorAll(".uni-actionsheet__cell").forEach((e=>{!function(e){const t=20;let n=0,o=0;e.addEventListener("touchstart",(e=>{const t=e.changedTouches[0];n=t.clientX,o=t.clientY})),e.addEventListener("touchend",(e=>{const r=e.changedTouches[0];if(Math.abs(r.clientX-n)<t&&Math.abs(r.clientY-o)<t){const t=e.target,n=e.currentTarget,o=new CustomEvent("click",{bubbles:!0,cancelable:!0,target:t,currentTarget:n});["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{o[e]=r[e]})),e.target.dispatchEvent(o)}}))}(e)}))}))}));const p=function(e){const t=zt({listItemColor:"#000",cancelItemColor:"#000"}),n=({theme:e})=>{!function(e,t){["listItemColor","cancelItemColor"].forEach((n=>{t[n]=tv[e][n]}))}(e,t)};return eo((()=>{e.visible?(t.listItemColor=t.cancelItemColor=e.itemColor,"#000"===e.itemColor&&(n({theme:Nm()}),Pg(n))):$g(n)})),t}(e);return()=>si("uni-actionsheet",{onTouchmove:nc},[si(ji,{name:"uni-fade"},{default:()=>[ao(si("div",{class:"uni-mask uni-actionsheet__mask",onClick:()=>u(-1)},null,8,["onClick"]),[[Ji,e.visible]])]}),si("div",{class:["uni-actionsheet",{"uni-actionsheet_toggle":e.visible}],style:d.value.content},[si("div",{ref:l,class:"uni-actionsheet__menu",onWheel:h},[e.title?si(Hr,null,[si("div",{class:"uni-actionsheet__cell",style:{height:`${r.value}px`}},null),si("div",{class:"uni-actionsheet__title"},[e.title])]):"",si("div",{style:{maxHeight:`${n.value}px`,overflow:"hidden"}},[si("div",{ref:a},[e.itemList.map(((e,t)=>si("div",{key:t,style:{color:p.listItemColor},class:"uni-actionsheet__cell",onClick:()=>u(t)},[e],12,["onClick"])))],512)])],40,["onWheel"]),si("div",{class:"uni-actionsheet__action"},[si("div",{style:{color:p.cancelItemColor},class:"uni-actionsheet__cell",onClick:()=>u(-1)},[c("uni.showActionSheet.cancel")],12,["onClick"])]),si("div",{style:d.value.triangle},null,4)],6)],40,["onTouchmove"])}});let ov,rv,iv;const sv=ie((()=>{Mv.on("onHidePopup",(()=>iv.visible=!1))}));function av(e){-1===e?rv&&rv("cancel"):ov&&ov({tapIndex:e})}const lv=rd("showActionSheet",((e,{resolve:t,reject:n})=>{sv(),ov=t,rv=n,iv?(c(iv,e),iv.visible=!0):(iv=zt(e),En((()=>(pg(nv,iv,av).mount(mg("u-s-a-s")),En((()=>iv.visible=!0))))))}),0,Cf),cv=rd("loadFontFace",(({family:e,source:t,desc:n},{resolve:o,reject:r})=>{(function(e,t,n){const o=document.fonts;if(o){const r=new FontFace(e,t,n);return r.load().then((()=>{o.add&&o.add(r)}))}return new Promise((o=>{const r=document.createElement("style"),i=[];if(n){const{style:e,weight:t,stretch:o,unicodeRange:r,variant:s,featureSettings:a}=n;e&&i.push(`font-style:${e}`),t&&i.push(`font-weight:${t}`),o&&i.push(`font-stretch:${o}`),r&&i.push(`unicode-range:${r}`),s&&i.push(`font-variant:${s}`),a&&i.push(`font-feature-settings:${a}`)}r.innerText=`@font-face{font-family:"${e}";src:${t};${i.join(";")}}`,document.head.appendChild(r),o()}))})(e,t=t.startsWith('url("')||t.startsWith("url('")?`url('${yh(t.substring(5,t.length-2))}')`:t.startsWith("url(")?`url('${yh(t.substring(4,t.length-1))}')`:yh(t),n).then((()=>{o()})).catch((e=>{r(`loadFontFace:fail ${e}`)}))}));function uv(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,Mv.emit("onNavigationBarChange",{titleText:t})}eo(t),Po(t)}const dv=rd("stopPullDownRefresh",((e,{resolve:t})=>{Mv.invokeViewMethod("stopPullDownRefresh",{},bc()),t()})),fv=ru({name:"TabBar",setup(){const e=an([]),t=Vf(),n=function(e,t){const n=Xt(e),o=n?zt(Ag(e)):Ag(e);return __uniConfig.darkmode&&n&&no(e,(e=>{const t=Ag(e);for(const n in t)o[n]=t[n]})),t&&Pg(t),o}(t,(()=>{const e=Ag(t);n.backgroundColor=e.backgroundColor,n.borderStyle=e.borderStyle,n.color=e.color,n.selectedColor=e.selectedColor,n.blurEffect=e.blurEffect,n.midButton=e.midButton,e.list&&e.list.length&&e.list.forEach(((e,t)=>{n.list[t].iconPath=e.iconPath,n.list[t].selectedIconPath=e.selectedIconPath}))}));!function(e,t){function n(){let n=[];n=e.list.filter((e=>!1!==e.visible)),t.value=n}an(c({type:"midButton"},e.midButton)),eo(n)}(n,e),function(e){no((()=>e.shown),(t=>{lc({"--window-bottom":Yf(t?parseInt(e.height):0)})}))}(n);const o=function(e,t,n){return eo((()=>{const o=e.meta;if(o.isTabBar){const e=o.route,r=n.value.findIndex((t=>t.pagePath===e));t.selectedIndex=r}})),(t,n)=>()=>{const{pagePath:o,text:r}=t;let i=re(o);i===__uniRoutes[0].alias&&(i="/"),e.path!==i?Mf({from:"tabBar",url:i,tabBarText:r}):Tc("onTabItemTap",{index:n,text:r,pagePath:o})}}(rl(),n,e),{style:r,borderStyle:i,placeholderStyle:s}=function(e){const t=Oi((()=>{let t=e.backgroundColor;const n=e.blurEffect;return t||qf&&n&&"none"!==n&&(t=hv[n]),{backgroundColor:t||"#f7f7fa",backdropFilter:"none"!==n?"blur(10px)":n}})),n=Oi((()=>{const{borderStyle:t,borderColor:n}=e;return n&&v(n)?{backgroundColor:n}:{backgroundColor:pv[t]||pv.black}})),o=Oi((()=>({height:e.height})));return{style:t,borderStyle:n,placeholderStyle:o}}(n);return Vo((()=>{n.iconfontSrc&&cv({family:"UniTabbarIconFont",source:`url("${n.iconfontSrc}")`})})),()=>{const t=function(e,t,n){const{selectedIndex:o,selectedColor:r,color:i}=e;return n.value.map(((n,s)=>{const a=o===s;return function(e,t,n,o,r,i,s,a){return si("div",{key:s,class:"uni-tabbar__item",onClick:a(r,s)},[mv(e,t||"",n,o,r,i)],8,["onClick"])}(a?r:i,a&&n.selectedIconPath||n.iconPath||"",n.iconfont?a&&n.iconfont.selectedText||n.iconfont.text:void 0,n.iconfont?a&&n.iconfont.selectedColor||n.iconfont.color:void 0,n,e,s,t)}))}(n,o,e);return si("uni-tabbar",{class:"uni-tabbar-"+n.position},[si("div",{class:"uni-tabbar",style:r.value},[si("div",{class:"uni-tabbar-border",style:i.value},null,4),t],4),si("div",{class:"uni-placeholder",style:s.value},null,4)],2)}}});const hv={dark:"rgb(0, 0, 0, 0.8)",light:"rgb(250, 250, 250, 0.8)",extralight:"rgb(250, 250, 250, 0.8)"},pv={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function mv(e,t,n,o,r,i){const{height:s}=i;return si("div",{class:"uni-tabbar__bd",style:{height:s}},[n?vv(n,o||"rgb(0, 0, 0, 0.8)",r,i):t&&gv(t,r,i),r.text&&yv(e,r,i),r.redDot&&bv(r.badge)],4)}function gv(e,t,n){const{type:o,text:r}=t,{iconWidth:i}=n;return si("div",{class:"uni-tabbar__icon"+(r?" uni-tabbar__icon__diff":""),style:{width:i,height:i}},["midButton"!==o&&si("img",{src:yh(e)},null,8,["src"])],6)}function vv(e,t,n,o){var r;const{type:i,text:s}=n,{iconWidth:a}=o,l="uni-tabbar__icon"+(s?" uni-tabbar__icon__diff":""),c={width:a,height:a},u={fontSize:(null==(r=n.iconfont)?void 0:r.fontSize)||a,color:t};return si("div",{class:l,style:c},["midButton"!==i&&si("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function yv(e,t,n){const{iconPath:o,text:r}=t,{fontSize:i,spacing:s}=n;return si("div",{class:"uni-tabbar__label",style:{color:e,fontSize:i,lineHeight:o?"normal":1.8,marginTop:o?s:"inherit"}},[r],4)}function bv(e){return si("div",{class:"uni-tabbar__reddot"+(e?" uni-tabbar__badge":"")},[e],2)}const _v=ru({name:"Layout",setup(e,{emit:t}){const n=an(null);ac({"--status-bar-height":"0px","--top-window-height":"0px","--window-left":"0px","--window-right":"0px","--window-margin":"0px","--tab-bar-height":"0px"});const o=function(){const e=rl();return{routeKey:Oi((()=>lh("/"+e.meta.route,_u()))),isTabBar:Oi((()=>e.meta.isTabBar)),routeCache:uh}}(),{layoutState:r,windowState:i}=function(){bu();{const e=zt({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return no((()=>e.marginWidth),(e=>ac({"--window-margin":e+"px"}))),no((()=>e.leftWindowWidth+e.marginWidth),(e=>{ac({"--window-left":e+"px"})})),no((()=>e.rightWindowWidth+e.marginWidth),(e=>{ac({"--window-right":e+"px"})})),{layoutState:e,windowState:Oi((()=>({})))}}}();!function(e,t){const n=bu();function o(){const o=document.body.clientWidth,r=oh();let i={};if(r.length>0){i=Gf(r[r.length-1]).meta}else{const e=Pc(n.path,!0);e&&(i=e.meta)}const s=parseInt(String((f(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let a=!1;a=o>s,a&&s?(e.marginWidth=(o-s)/2,En((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+s+"px;margin:0 auto;")}))):(e.marginWidth=0,En((()=>{const e=t.value;e&&e.removeAttribute("style")})))}no([()=>n.path],o),Vo((()=>{o(),window.addEventListener("resize",o)}))}(r,n);const s=function(e){const t=bu(),n=Vf(),o=Oi((()=>t.meta.isTabBar&&n.shown));return ac({"--tab-bar-height":n.height}),o}(),a=function(e){const t=an(!1);return Oi((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(s);return()=>{const e=function(e,t,n,o,r,i){return function({routeKey:e,isTabBar:t,routeCache:n}){return si(nl,null,{default:Hn((({Component:o})=>[(Xr(),Zr(Lo,{matchBy:"key",cache:n},[(Xr(),Zr(Gn(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e)}(o),t=function(e){return ao(si(fv,null,null,512),[[Ji,e.value]])}(s);return si("uni-app",{ref:n,class:a.value},[e,t],2)}}});const wv=rd("scanCode",id("scanCode")),xv=rd("login",id("login"));function Sv(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!ei(e)}function Tv(e){if(e.mode===Ev.TIME)return"00:00";if(e.mode===Ev.DATE){const t=(new Date).getFullYear()-150;switch(e.fields){case Lv.YEAR:return t.toString();case Lv.MONTH:return t+"-01";default:return t+"-01-01"}}return""}function Cv(e){if(e.mode===Ev.TIME)return"23:59";if(e.mode===Ev.DATE){const t=(new Date).getFullYear()+150;switch(e.fields){case Lv.YEAR:return t.toString();case Lv.MONTH:return t+"-12";default:return t+"-12-31"}}return""}function kv(e,t,n,o){const r=e.mode===Ev.DATE?"-":":",i=e.mode===Ev.DATE?t.dateArray:t.timeArray;let s;if(e.mode===Ev.TIME)s=2;else switch(e.fields){case Lv.YEAR:s=1;break;case Lv.MONTH:s=2;break;default:s=3}const a=String(n).split(r);let l=[];for(let c=0;c<s;c++){const e=a[c];l.push(i[c].indexOf(e))}return l.indexOf(-1)>=0&&(l=o?kv(e,t,o):l.map((()=>0))),l}const Ev={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date"},Lv={YEAR:"year",MONTH:"month",DAY:"day"},Ov={PICKER:"picker",SELECT:"select"},Pv=ou({name:"Picker",compatConfig:{MODE:3},props:{name:{type:String,default:""},range:{type:Array,default:()=>[]},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:Ev.SELECTOR,validator:e=>Object.values(Ev).includes(e)},fields:{type:String,default:""},start:{type:String,default:e=>Tv(e)},end:{type:String,default:e=>Cv(e)},disabled:{type:[Boolean,String],default:!1},selectorType:{type:String,default:""}},emits:["change","cancel","columnchange"],setup(e,{emit:t,slots:n}){Ll();const{t:o}=_l(),r=an(null),i=an(null),s=an(null),a=an(null),l=an(!1),{state:c,rangeArray:u}=function(e){const t=zt({valueSync:void 0,visible:!1,contentVisible:!1,popover:null,valueChangeSource:"",timeArray:[],dateArray:[],valueArray:[],oldValueArray:[],isDesktop:!1,popupStyle:{content:{},triangle:{}}}),n=Oi((()=>{let n=e.range;switch(e.mode){case Ev.SELECTOR:return[n];case Ev.MULTISELECTOR:return n;case Ev.TIME:return t.timeArray;case Ev.DATE:{const n=t.dateArray;switch(e.fields){case Lv.YEAR:return[n[0]];case Lv.MONTH:return[n[0],n[1]];default:return[n[0],n[1],n[2]]}}}return[]}));return{state:t,rangeArray:n}}(e),d=su(r,t),{system:f,selectorTypeComputed:p,_show:m,_l10nColumn:g,_l10nItem:v,_input:y,_fixInputPosition:b,_pickerViewChange:_,_cancel:w,_change:x,_resetFormData:S,_getFormData:T,_createTime:C,_createDate:k,_setValueSync:E}=function(e,t,n,o,r,i,s){const a=function(){const e=an(!1);return e.value=(()=>0===String(navigator.vendor).indexOf("Apple")&&navigator.maxTouchPoints>0)(),e}(),l=function(){const e=an("");return e.value=(()=>{if(/win|mac/i.test(navigator.platform)){if("Google Inc."===navigator.vendor)return"chrome";if(/Firefox/.test(navigator.userAgent))return"firefox"}return""})(),e}(),c=Oi((()=>{const t=e.selectorType;return Object.values(Ov).includes(t)?t:a.value?Ov.PICKER:Ov.SELECT})),u=Oi((()=>e.mode===Ev.DATE&&!Object.values(Lv).includes(e.fields)&&t.isDesktop?l.value:"")),d=Oi((()=>kv(e,t,e.start,Tv(e)))),f=Oi((()=>kv(e,t,e.end,Cv(e))));function p(n){if(e.disabled)return;t.valueChangeSource="";let o=r.value,i=n.currentTarget;o.remove(),(document.querySelector("uni-app")||document.body).appendChild(o),o.style.display="block";const s=i.getBoundingClientRect();t.popover={top:s.top,left:s.left,width:s.width,height:s.height},setTimeout((()=>{t.visible=!0}),20)}function m(){return{value:t.valueSync,key:e.name}}function g(){switch(e.mode){case Ev.SELECTOR:t.valueSync=0;break;case Ev.MULTISELECTOR:t.valueSync=e.value.map((e=>0));break;case Ev.DATE:case Ev.TIME:t.valueSync=""}}function v(){let e=[],n=[];for(let t=0;t<24;t++)e.push((t<10?"0":"")+t);for(let t=0;t<60;t++)n.push((t<10?"0":"")+t);t.timeArray.push(e,n)}function y(){let t=(new Date).getFullYear(),n=t-150,o=t+150;if(e.start){const t=new Date(e.start).getFullYear();!isNaN(t)&&t<n&&(n=t)}if(e.end){const t=new Date(e.end).getFullYear();!isNaN(t)&&t>o&&(o=t)}return{start:n,end:o}}function b(){let e=[];const n=y();for(let t=n.start,i=n.end;t<=i;t++)e.push(String(t));let o=[];for(let t=1;t<=12;t++)o.push((t<10?"0":"")+t);let r=[];for(let t=1;t<=31;t++)r.push((t<10?"0":"")+t);t.dateArray.push(e,o,r)}function _(e){return 60*e[0]+e[1]}function w(e){const t=31;return e[0]*t*12+(e[1]||0)*t+(e[2]||0)}function x(e,t){for(let n=0;n<e.length&&n<t.length;n++)e[n]=t[n]}function S(){let n=e.value;switch(e.mode){case Ev.MULTISELECTOR:{h(n)||(n=t.valueArray),h(t.valueSync)||(t.valueSync=[]);const o=t.valueSync.length=Math.max(n.length,e.range.length);for(let r=0;r<o;r++){const o=Number(n[r]),i=Number(t.valueSync[r]),s=isNaN(o)?isNaN(i)?0:i:o,a=e.range[r]?e.range[r].length-1:0;t.valueSync.splice(r,1,s<0||s>a?0:s)}}break;case Ev.TIME:case Ev.DATE:t.valueSync=String(n);break;default:{const e=Number(n);t.valueSync=e<0?0:e;break}}}function T(){let n,o=t.valueSync;switch(e.mode){case Ev.MULTISELECTOR:n=[...o];break;case Ev.TIME:n=kv(e,t,o,ae({mode:Ev.TIME}));break;case Ev.DATE:n=kv(e,t,o,ae({mode:Ev.DATE}));break;default:n=[o]}t.oldValueArray=[...n],t.valueArray=[...n]}function C(){let n=t.valueArray;switch(e.mode){case Ev.SELECTOR:return n[0];case Ev.MULTISELECTOR:return n.map((e=>e));case Ev.TIME:return t.valueArray.map(((e,n)=>t.timeArray[n][e])).join(":");case Ev.DATE:return t.valueArray.map(((e,n)=>t.dateArray[n][e])).join("-")}}function k(){L(),t.valueChangeSource="click";const e=C();t.valueSync=h(e)?e.map((e=>e)):e,n("change",{},{value:e})}function E(e){if("firefox"===u.value&&e){const{top:n,left:o,width:r,height:i}=t.popover,{pageX:s,pageY:a}=e;if(s>o&&s<o+r&&a>n&&a<n+i)return}L(),n("cancel",{},{})}function L(){t.visible=!1,setTimeout((()=>{let e=r.value;e.remove(),o.value.prepend(e),e.style.display="none"}),260)}function O(){e.mode===Ev.SELECTOR&&c.value===Ov.SELECT&&(i.value.scrollTop=34*t.valueArray[0])}function P(e){const n=e.target;t.valueSync=n.value,En((()=>{k()}))}function $(e){if("chrome"===u.value){const t=o.value.getBoundingClientRect(),n=32;s.value.style.left=e.clientX-t.left-1.5*n+"px",s.value.style.top=e.clientY-t.top-.5*n+"px"}}function A(e){t.valueArray=M(e.detail.value,!0)}function M(t,n){const{getLocale:o}=_l();if(e.mode===Ev.DATE){const r=o();if(!r.startsWith("zh"))switch(e.fields){case Lv.YEAR:return t;case Lv.MONTH:return[t[1],t[0]];default:switch(r){case"es":case"fr":return[t[2],t[1],t[0]];default:return n?[t[2],t[0],t[1]]:[t[1],t[2],t[0]]}}}return t}function I(t,n){const{getLocale:o}=_l();if(e.mode===Ev.DATE){const r=o();if(r.startsWith("zh")){return t+["年","月","日"][n]}if(e.fields!==Lv.YEAR&&n===(e.fields===Lv.MONTH||"es"!==r&&"fr"!==r?0:1)){let e;switch(r){case"es":e=["enero","febrero","marzo","abril","mayo","junio","​​julio","agosto","septiembre","octubre","noviembre","diciembre"];break;case"fr":e=["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"];break;default:e=["January","February","March","April","May","June","July","August","September","October","November","December"]}return e[Number(t)-1]}}return t}return no((()=>t.visible),(e=>{e?(clearTimeout($v),t.contentVisible=e,O()):$v=setTimeout((()=>{t.contentVisible=e}),300)})),no([()=>e.mode,()=>e.value,()=>e.range],S,{deep:!0}),no((()=>t.valueSync),T,{deep:!0}),no((()=>t.valueArray),(o=>{if(e.mode===Ev.TIME||e.mode===Ev.DATE){const n=e.mode===Ev.TIME?_:w,o=t.valueArray,r=d.value,i=f.value;if(e.mode===Ev.DATE){const e=t.dateArray,n=e[2].length,r=Number(e[2][o[2]])||1,i=new Date(`${e[0][o[0]]}/${e[1][o[1]]}/${r}`).getDate();i<r&&(o[2]-=i+n-r)}n(o)<n(r)?x(o,r):n(o)>n(i)&&x(o,i)}o.forEach(((o,r)=>{o!==t.oldValueArray[r]&&(t.oldValueArray[r]=o,e.mode===Ev.MULTISELECTOR&&n("columnchange",{},{column:r,value:o}))}))})),{selectorTypeComputed:c,system:u,_show:p,_cancel:E,_change:k,_l10nColumn:M,_l10nItem:I,_input:P,_resetFormData:g,_getFormData:m,_createTime:v,_createDate:b,_setValueSync:S,_fixInputPosition:$,_pickerViewChange:A}}(e,c,d,r,i,s,a);!function(e,t,n){const{key:o,disable:r}=fg();eo((()=>{r.value=!e.visible})),no(o,(e=>{"esc"===e?t():"enter"===e&&n()}))}(c,w,x),function(e,t){const n=xr(uu,!1);if(n){const o={reset:e,submit:()=>{const e=["",null],{key:n,value:o}=t();return""!==n&&(e[0]=n,e[1]=o),e}};n.addField(o),zo((()=>{n.removeField(o)}))}}(S,T),C(),k(),E();const L=ev(c);return eo((()=>{c.isDesktop=L.isDesktop.value,c.popupStyle=L.popupStyle.value})),zo((()=>{i.value&&i.value.remove()})),Vo((()=>{l.value=!0})),()=>{let t;const{visible:d,contentVisible:h,valueArray:S,popupStyle:T,valueSync:C}=c,{rangeKey:k,mode:E,start:L,end:O}=e,P=cu(e,"disabled");return si("uni-picker",hi({ref:r},P,{onClick:iu(m)}),[l.value?si("div",{ref:i,class:["uni-picker-container",`uni-${E}-${p.value}`],onWheel:nc,onTouchmove:nc},[si(ji,{name:"uni-fade"},{default:()=>[ao(si("div",{class:"uni-mask uni-picker-mask",onClick:iu(w),onMousemove:b},null,40,["onClick","onMousemove"]),[[Ji,d]])]}),f.value?null:si("div",{class:[{"uni-picker-toggle":d},"uni-picker-custom"],style:T.content},[si("div",{class:"uni-picker-header",onClick:oc},[si("div",{class:"uni-picker-action uni-picker-action-cancel",onClick:iu(w)},[o("uni.picker.cancel")],8,["onClick"]),si("div",{class:"uni-picker-action uni-picker-action-confirm",onClick:x},[o("uni.picker.done")],8,["onClick"])],8,["onClick"]),h?si(jp,{value:g(S),class:"uni-picker-content",onChange:_},Sv(t=Ko(g(u.value),((e,t)=>{let n;return si(qp,{key:t},Sv(n=Ko(e,((e,n)=>si("div",{key:n,class:"uni-picker-item"},["object"==typeof e?e[k]||"":v(e,t)]))))?n:{default:()=>[n],_:1})})))?t:{default:()=>[t],_:1},8,["value","onChange"]):null,si("div",{ref:s,class:"uni-picker-select",onWheel:oc,onTouchmove:oc},[Ko(u.value[0],((e,t)=>si("div",{key:t,class:["uni-picker-item",{selected:S[0]===t}],onClick:()=>{S[0]=t,x()}},["object"==typeof e?e[k]||"":e],10,["onClick"])))],40,["onWheel","onTouchmove"]),si("div",{style:T.triangle},null,4)],6)],40,["onWheel","onTouchmove"]):null,si("div",null,[n.default&&n.default()]),f.value?si("div",{class:"uni-picker-system",onMousemove:iu(b)},[si("input",{class:["uni-picker-system_input",f.value],ref:a,value:C,type:E,tabindex:"-1",min:L,max:O,onChange:e=>{y(e),oc(e)}},null,42,["value","type","min","max","onChange"])],40,["onMousemove"]):null],16,["onClick"])}}});let $v;const Av=c(Bl,{publishHandler(e,t,n){Mv.subscribeHandler(e,t,n)}}),Mv=c(zc,{publishHandler(e,t,n){Av.subscribeHandler(e,t,n)}}),Iv=ru({name:"PageBody",setup(e,t){const n=an(null),o=an(null);return no((()=>false.enablePullDownRefresh),(()=>{o.value=null}),{immediate:!0}),()=>si(Hr,null,[!1,si("uni-page-wrapper",hi({ref:n},o.value),[si("uni-page-body",null,[Qo(t.slots,"default")]),null],16)])}}),Bv=ru({name:"Page",setup(e,t){let n=yu(_u());n.navigationBar;const o={};return uv(n),()=>si("uni-page",{"data-page":n.route,style:o},[Rv(t),null])}});function Rv(e){return Xr(),Zr(Iv,{key:0},{default:Hn((()=>[Qo(e.slots,"page")])),_:3})}const jv={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=pd;const Dv=Object.assign({}),Nv=Object.assign;window.__uniConfig=Nv({globalStyle:{backgroundColor:"#F8F8F8",navigationBar:{backgroundColor:"#5145F7",titleText:"商家收款",type:"default",titleColor:"#ffffff"},isNVue:!1},easycom:{autoscan:!0,custom:{"^uni-(.*)":"@/uni_modules/uni-$1/components/uni-$1/uni-$1.vue","^custom-(.*)":"@/components/custom-$1.vue"}},tabBar:{position:"bottom",color:"#999999",selectedColor:"#5145F7",borderStyle:"black",blurEffect:"none",fontSize:"10px",iconWidth:"24px",spacing:"3px",height:"50px",list:[{pagePath:"pages/index/index",text:"首页",iconPath:"/static/tab/home.png",selectedIconPath:"/static/tab/home-active.png"},{pagePath:"pages/bill/index",text:"账单",iconPath:"/static/tab/bill.png",selectedIconPath:"/static/tab/bill-active.png"},{pagePath:"pages/scan/index",text:"一码通",iconPath:"/static/tab/code.png",selectedIconPath:"/static/tab/code-active.png"},{pagePath:"pages/report/index",text:"报表",iconPath:"/static/tab/report.png",selectedIconPath:"/static/tab/report-active.png"},{pagePath:"pages/mine/index",text:"我的",iconPath:"/static/tab/mine.png",selectedIconPath:"/static/tab/mine-active.png"}],custom:!1,backgroundColor:"#FFFFFF",selectedIndex:0,shown:!0},uniIdRouter:{},compilerVersion:"4.57"},{appId:"__UNI__41DB369",appName:"apps",appVersion:"1.0.0",appVersionCode:"100",async:jv,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(Dv).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return Nv(e[n]||(e[n]={}),Dv[t].default),e}),{}),router:{mode:"hash",base:"/h5/",assets:"assets",routerBase:"/h5/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const Fv={delay:jv.delay,timeout:jv.timeout,suspensible:jv.suspensible};jv.loading&&(Fv.loadingComponent={name:"SystemAsyncLoading",render:()=>si(Yn(jv.loading))}),jv.error&&(Fv.errorComponent={name:"SystemAsyncError",render:()=>si(Yn(jv.error))});const Vv=()=>t((()=>import("./pages-login-index.Bds5bbtX.js")),__vite__mapDeps([0,1,2,3,4,5])).then((e=>Lm(e.default||e))),Hv=So(Nv({loader:Vv},Fv)),Wv=()=>t((()=>import("./pages-index-index.DiwPS29-.js")),__vite__mapDeps([6,7,1,2,3,8,9,10,11])).then((e=>Lm(e.default||e))),zv=So(Nv({loader:Wv},Fv)),qv=()=>t((()=>import("./pages-code-index.mxKLLDzN.js")),__vite__mapDeps([12,1,2,3,7,8,9,10,13])).then((e=>Lm(e.default||e))),Uv=So(Nv({loader:qv},Fv)),Yv=()=>t((()=>import("./pages-bill-index.BSZDuZQT.js")),__vite__mapDeps([14,7,1,2,3,8,15,16])).then((e=>Lm(e.default||e))),Xv=So(Nv({loader:Yv},Fv)),Gv=()=>t((()=>import("./pages-scan-index.D2or3WrE.js")),__vite__mapDeps([17,1,2,3,7,8,18])).then((e=>Lm(e.default||e))),Kv=So(Nv({loader:Gv},Fv)),Jv=()=>t((()=>import("./pages-report-index.DHpRiE0m.js")),__vite__mapDeps([19,7,1,2,3,8,20,21])).then((e=>Lm(e.default||e))),Qv=So(Nv({loader:Jv},Fv)),Zv=()=>t((()=>import("./pages-report-diamond.BJ80cpxu.js")),__vite__mapDeps([22,2,20,23])).then((e=>Lm(e.default||e))),ey=So(Nv({loader:Zv},Fv)),ty=()=>t((()=>import("./pages-mine-index.afasWBYC.js")),__vite__mapDeps([24,7,1,2,3,8,25])).then((e=>Lm(e.default||e))),ny=So(Nv({loader:ty},Fv)),oy=()=>t((()=>import("./pages-wallet-index.DpULw8DP.js")),__vite__mapDeps([26,7,1,2,3,8,27,10,28])).then((e=>Lm(e.default||e))),ry=So(Nv({loader:oy},Fv)),iy=()=>t((()=>import("./pages-wallet-bank-cards.ULJ9pjbz.js")),__vite__mapDeps([29,30,27,10,2,31])).then((e=>Lm(e.default||e))),sy=So(Nv({loader:iy},Fv)),ay=()=>t((()=>import("./pages-merchant-index.DqAEv-i4.js")),__vite__mapDeps([32,1,2,3,7,8,33])).then((e=>Lm(e.default||e))),ly=So(Nv({loader:ay},Fv)),cy=()=>t((()=>import("./pages-duizhang-index.BOTzn0yc.js")),__vite__mapDeps([34,1,2,3,9,10,35])).then((e=>Lm(e.default||e))),uy=So(Nv({loader:cy},Fv)),dy=()=>t((()=>import("./pages-jiesuan-index.SK3LjSM_.js")),__vite__mapDeps([36,1,2,3,7,8,37])).then((e=>Lm(e.default||e))),fy=So(Nv({loader:dy},Fv)),hy=()=>t((()=>import("./pages-xiaoxi-index.DVvIp1MN.js")),__vite__mapDeps([38,7,1,2,3,8,39])).then((e=>Lm(e.default||e))),py=So(Nv({loader:hy},Fv)),my=()=>t((()=>import("./pages-yuangong-index.BSBQB6_o.js")),__vite__mapDeps([40,1,2,3,7,8,41])).then((e=>Lm(e.default||e))),gy=So(Nv({loader:my},Fv)),vy=()=>t((()=>import("./pages-yingxiao-index.ulO9NaUQ.js")),__vite__mapDeps([42,1,2,3,7,8,43])).then((e=>Lm(e.default||e))),yy=So(Nv({loader:vy},Fv)),by=()=>t((()=>import("./pages-bill-date-range.CV2KWAhM.js")),__vite__mapDeps([44,30,15,2,45])).then((e=>Lm(e.default||e))),_y=So(Nv({loader:by},Fv)),wy=()=>t((()=>import("./pages-pay-mini-payment.CPj6FUK0.js")),__vite__mapDeps([46,7,1,2,3,8,47])).then((e=>Lm(e.default||e))),xy=So(Nv({loader:wy},Fv)),Sy=()=>t((()=>import("./pages-pay-result.Di3-O9b6.js")),__vite__mapDeps([48,7,1,2,3,8,49])).then((e=>Lm(e.default||e))),Ty=So(Nv({loader:Sy},Fv)),Cy=()=>t((()=>import("./pages_A-user-index.DJjUWa8i.js")),__vite__mapDeps([50,7,1,2,3,8,51])).then((e=>Lm(e.default||e))),ky=So(Nv({loader:Cy},Fv));function Ey(e,t){return Xr(),Zr(Bv,null,{page:Hn((()=>[si(e,Nv({},t,{ref:"page"}),null,512)])),_:1})}window.__uniRoutes=[{path:"/",alias:"/pages/login/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(Hv,t)}},loader:Vv,meta:{isQuit:!0,isEntry:!0,navigationBar:{titleText:"登录",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/index/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(zv,t)}},loader:Wv,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:0,navigationBar:{titleText:"商家收款",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/code/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(Uv,t)}},loader:qv,meta:{navigationBar:{titleText:"收款码",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/bill/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(Xv,t)}},loader:Yv,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:1,navigationBar:{titleText:"交易记录",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/scan/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(Kv,t)}},loader:Gv,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:2,navigationBar:{titleText:"一码通",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/report/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(Qv,t)}},loader:Jv,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:3,navigationBar:{titleText:"报表",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/report/diamond",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(ey,t)}},loader:Zv,meta:{navigationBar:{titleText:"多维数据分析",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/mine/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(ny,t)}},loader:ty,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:4,navigationBar:{titleText:"我的",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/wallet/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(ry,t)}},loader:oy,meta:{navigationBar:{titleText:"我的钱包",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/wallet/bank-cards",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(sy,t)}},loader:iy,meta:{navigationBar:{titleText:"银行卡管理",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/merchant/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(ly,t)}},loader:ay,meta:{navigationBar:{titleText:"商家信息",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/duizhang/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(uy,t)}},loader:cy,meta:{navigationBar:{titleText:"对账单",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/jiesuan/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(fy,t)}},loader:dy,meta:{navigationBar:{titleText:"结算记录",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/xiaoxi/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(py,t)}},loader:hy,meta:{navigationBar:{titleText:"消息通知",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/yuangong/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(gy,t)}},loader:my,meta:{navigationBar:{titleText:"员工管理",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/yingxiao/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(yy,t)}},loader:vy,meta:{navigationBar:{titleText:"营销工具",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/bill/date-range",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(_y,t)}},loader:by,meta:{navigationBar:{titleText:"日期范围选择",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/pay/mini-payment",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(xy,t)}},loader:wy,meta:{navigationBar:{titleText:"收银台",style:"custom",type:"default"},isNVue:!1}},{path:"/pages/pay/result",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(Ty,t)}},loader:Sy,meta:{navigationBar:{titleText:"支付结果",style:"custom",type:"default"},isNVue:!1}},{path:"/pages_A/user/index",component:{setup(){const e=Tm(),t=e&&e.$route&&e.$route.query||{};return()=>Ey(ky,t)}},loader:Cy,meta:{navigationBar:{titleText:"会员中心",style:"custom",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const Ly={onLaunch:function(){console.log("App Launch")},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")}};Em(Ly,{init:Cm,setup(e){const t=bu(),n=()=>{var n;n=e,Object.keys(Gd).forEach((e=>{Gd[e].forEach((t=>{Do(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i}=e,s=function({path:e,query:t}){return c(Dh,{path:e,query:t}),c(Nh,Dh),c({},Dh)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:Se(t.query)});if(o&&I(o,s),r&&I(r,s),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};Nf(),i&&I(i,e)}};return xr(qa).isReady().then(n),Vo((()=>{window.addEventListener("resize",ke(Om,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",Pm),document.addEventListener("visibilitychange",$m),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{Mv.emit("onThemeChange",{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(Xr(),Zr(_v));e.setup=(e,o)=>{const r=t&&t(e,o);return g(r)?n:r},e.render=n}});let Oy={baseUrl:"/ajax.php",headers:{"content-type":"application/x-www-form-urlencoded","X-Requested-With":"XMLHttpRequest"},timeout:1e4,notifyUrl:"http://ceshi.huisas.com/notify_url.php",returnUrl:"http://ceshi.huisas.com/return_url.php",merchant:{id:"",key:""}};Oy={baseUrl:"http://ceshi.huisas.com",headers:{"content-type":"application/x-www-form-urlencoded","X-Requested-With":"XMLHttpRequest"},timeout:1e4,notifyUrl:"http://ceshi.huisas.com/notify_url.php",returnUrl:"http://ceshi.huisas.com/return_url.php",merchant:{id:"",key:""}};const Py=Oy;(function(){const e=ks(Ly);return e.config.globalProperties.$baseUrl=Py.baseUrl,e.config.globalProperties.$config=Py,{app:e}})().app.use(gm).mount("#app");export{Rd as $,Jg as A,Qg as B,Qr as C,Ko as D,Ng as E,Hr as F,cg as G,yg as H,Lp as I,nh as J,gd as K,ag as L,nf as M,of as N,Sg as O,og as P,Eg as Q,Ym as R,Bf as S,Xm as T,tf as U,Xd as V,Kd as W,Ud as X,Yd as Y,qd as Z,Bd as _,Gg as a,ao as a0,pe as a1,Ji as a2,Jh as a3,Pv as a4,dv as a5,lv as a6,Gp as a7,Xp as a8,Qo as a9,qp as aa,jp as ab,xv as ac,t as ad,Jo as ae,Py as af,Jm as ag,ng as b,jf as c,Gn as d,Zr as e,si as f,tg as g,li as h,Zp as i,ci as j,sp as k,Qp as l,Zh as m,Og as n,Xr as o,mu as p,Mf as q,bg as r,Zm as s,Y as t,wv as u,Yn as v,Hn as w,Bm as x,Lg as y,me as z};
