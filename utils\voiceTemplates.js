// utils/voiceTemplates.js - 语音播报模板系统

import { getPaymentType, formatAmountForVoice } from './voiceConfig.js';

// 语音模板类
class VoiceTemplateManager {
  constructor() {
    // 模板定义
    this.templates = {
      // 标准模板
      normal: {
        id: 'normal',
        name: '标准模式',
        template: '收款成功，{payType}，金额{amount}元',
        description: '简洁明了的播报内容',
        example: '收款成功，微信支付，金额88.88元'
      },
      
      // 详细模板
      detailed: {
        id: 'detailed',
        name: '详细模式',
        template: '收款成功，{payType}，{productName}，金额{amount}元',
        description: '包含商品名称的详细播报',
        example: '收款成功，支付宝，咖啡，金额25元'
      },
      
      // 反扫模板
      scan: {
        id: 'scan',
        name: '反扫模式',
        template: '反扫收款成功，{payType}，金额{amount}元',
        description: '专用于反扫收款场景',
        example: '反扫收款成功，微信支付，金额100元'
      },
      
      // 大额模板
      large: {
        id: 'large',
        name: '大额提醒',
        template: '大额收款成功，{payType}，金额{amount}元，请注意核实',
        description: '大额收款的特殊提醒',
        example: '大额收款成功，支付宝，金额1500元，请注意核实'
      },
      
      // 简洁模板
      simple: {
        id: 'simple',
        name: '简洁模式',
        template: '收款{amount}元',
        description: '最简洁的播报内容',
        example: '收款88.88元'
      },
      
      // 动态收款模板
      dynamic: {
        id: 'dynamic',
        name: '动态收款',
        template: '动态收款成功，{payType}，{productName}，金额{amount}元',
        description: '动态收款码专用模板',
        example: '动态收款成功，支付宝，商品名称，金额50元'
      },
      
      // 欢迎模式
      welcome: {
        id: 'welcome',
        name: '欢迎模式',
        template: '欢迎光临，{payType}收款成功，金额{amount}元，谢谢惠顾',
        description: '带有欢迎语的友好播报',
        example: '欢迎光临，微信支付收款成功，金额88元，谢谢惠顾'
      }
    };
    
    // 场景模板映射
    this.sceneTemplates = {
      'pages/index/index': 'normal',
      'pages/scan/index': 'normal',
      'pages/dynamic-code/index': 'dynamic',
      'pages/pay/mini-payment': 'scan',
      'pages/bill/index': 'normal'
    };
  }
  
  // 获取模板
  getTemplate(templateId) {
    return this.templates[templateId] || this.templates.normal;
  }
  
  // 获取所有模板
  getAllTemplates() {
    return Object.values(this.templates);
  }
  
  // 根据场景获取推荐模板
  getSceneTemplate(pagePath) {
    const templateId = this.sceneTemplates[pagePath] || 'normal';
    return this.getTemplate(templateId);
  }
  
  // 格式化语音文本
  formatVoiceText(templateId, order, context = {}) {
    const template = this.getTemplate(templateId);
    const paymentType = getPaymentType(order.type);
    
    // 准备替换变量
    const variables = {
      payType: paymentType.voiceName,
      amount: formatAmountForVoice(order.money),
      productName: this.formatProductName(order.name),
      orderNo: order.trade_no,
      time: this.formatTime(order.addtime),
      ...context // 允许传入额外的上下文变量
    };
    
    // 执行模板替换
    let text = template.template;
    Object.keys(variables).forEach(key => {
      const regex = new RegExp(`\\{${key}\\}`, 'g');
      text = text.replace(regex, variables[key] || '');
    });
    
    return text;
  }
  
  // 智能选择模板
  smartSelectTemplate(order, pagePath, options = {}) {
    const amount = parseFloat(order.money || 0);
    
    // 大额收款优先
    if (options.largeAmountAlert && amount >= (options.largeAmountThreshold || 1000)) {
      return 'large';
    }
    
    // 根据页面场景选择
    switch(pagePath) {
      case 'pages/dynamic-code/index':
        return 'dynamic';
      case 'pages/pay/mini-payment':
        return 'scan';
      default:
        return options.defaultTemplate || 'normal';
    }
  }
  
  // 格式化商品名称
  formatProductName(productName) {
    if (!productName || productName.trim() === '') {
      return '商品';
    }
    
    // 限制长度，避免播报过长
    const maxLength = 10;
    if (productName.length > maxLength) {
      return productName.substring(0, maxLength);
    }
    
    return productName;
  }
  
  // 格式化时间
  formatTime(timeString) {
    if (!timeString) return '';
    
    const date = new Date(timeString);
    const now = new Date();
    
    // 如果是今天
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
    
    return date.toLocaleDateString('zh-CN');
  }
  
  // 预览模板效果
  previewTemplate(templateId, sampleData = null) {
    const sampleOrder = sampleData || {
      trade_no: 'TEST123456',
      money: '88.88',
      type: 2, // 微信支付
      name: '测试商品',
      addtime: new Date().toISOString()
    };
    
    return this.formatVoiceText(templateId, sampleOrder);
  }
  
  // 验证模板格式
  validateTemplate(templateString) {
    const requiredVariables = ['payType', 'amount'];
    const errors = [];
    
    requiredVariables.forEach(variable => {
      if (!templateString.includes(`{${variable}}`)) {
        errors.push(`缺少必需变量: {${variable}}`);
      }
    });
    
    // 检查是否有未知变量
    const variableRegex = /\{([^}]+)\}/g;
    const knownVariables = ['payType', 'amount', 'productName', 'orderNo', 'time'];
    let match;
    
    while ((match = variableRegex.exec(templateString)) !== null) {
      const variable = match[1];
      if (!knownVariables.includes(variable)) {
        errors.push(`未知变量: {${variable}}`);
      }
    }
    
    return {
      valid: errors.length === 0,
      errors: errors
    };
  }
  
  // 添加自定义模板
  addCustomTemplate(id, template) {
    const validation = this.validateTemplate(template.template);
    if (!validation.valid) {
      throw new Error(`模板验证失败: ${validation.errors.join(', ')}`);
    }
    
    this.templates[id] = {
      id: id,
      custom: true,
      ...template
    };
    
    return true;
  }
  
  // 删除自定义模板
  removeCustomTemplate(id) {
    if (this.templates[id] && this.templates[id].custom) {
      delete this.templates[id];
      return true;
    }
    return false;
  }
  
  // 获取模板统计信息
  getTemplateStats() {
    const total = Object.keys(this.templates).length;
    const custom = Object.values(this.templates).filter(t => t.custom).length;
    const builtin = total - custom;
    
    return {
      total,
      builtin,
      custom
    };
  }
}

// 创建全局实例
const voiceTemplateManager = new VoiceTemplateManager();

// 导出便捷函数
export function formatVoiceText(templateId, order, context = {}) {
  return voiceTemplateManager.formatVoiceText(templateId, order, context);
}

export function smartSelectTemplate(order, pagePath, options = {}) {
  return voiceTemplateManager.smartSelectTemplate(order, pagePath, options);
}

export function previewTemplate(templateId, sampleData = null) {
  return voiceTemplateManager.previewTemplate(templateId, sampleData);
}

export function getAllTemplates() {
  return voiceTemplateManager.getAllTemplates();
}

export function getTemplate(templateId) {
  return voiceTemplateManager.getTemplate(templateId);
}

// 默认导出
export { voiceTemplateManager };
export default voiceTemplateManager;
