// utils/sign.js - 签名工具
// 使用自定义的md5实现
import md5 from './md5.js';

/**
 * MD5签名函数
 * @param {Object} params - 参数对象
 * @param {String} key - 商户密钥
 * @returns {String} MD5签名
 */
export function signParams(params, key) {
  // 1. 参数按照键名升序排序
  const sortedKeys = Object.keys(params).sort();
  
  // 2. 拼接为key=value&形式
  let stringA = '';
  sortedKeys.forEach(k => {
    if (k !== 'sign' && k !== 'sign_type' && params[k] !== '' && params[k] !== null && params[k] !== undefined) {
      stringA += `${k}=${params[k]}&`;
    }
  });
  
  // 3. 添加商户密钥
  const stringSignTemp = stringA + `key=${key}`;
  
  // 4. MD5加密并转为大写
  return md5(stringSignTemp).toUpperCase();
}

/**
 * RSA签名函数（如果后端支持RSA签名）
 * 注意：实际实现可能需要依赖第三方RSA库，这里仅提供示例框架
 * @param {Object} params - 参数对象
 * @param {String} privateKey - 商户RSA私钥
 * @returns {String} RSA签名
 */
export function signParamsRSA(params, privateKey) {
  // 1. 参数按照键名升序排序
  const sortedKeys = Object.keys(params).sort();
  
  // 2. 拼接为key=value&形式
  let stringA = '';
  sortedKeys.forEach(k => {
    if (k !== 'sign' && k !== 'sign_type' && params[k] !== '' && params[k] !== null && params[k] !== undefined) {
      stringA += `${k}=${params[k]}&`;
    }
  });
  
  // 3. 去掉最后的&
  if (stringA.endsWith('&')) {
    stringA = stringA.substring(0, stringA.length - 1);
  }
  
  // 4. 使用RSA私钥签名
  // 这里需要实际的RSA签名实现，可以使用第三方库
  // 例如：jsrsasign, node-forge等
  // 以下为伪代码示例
  /*
  const rsa = new RSAKey();
  rsa.readPrivateKeyFromPEMString(privateKey);
  const signature = rsa.sign(stringA, 'sha256');
  return signature;
  */
  
  // 临时使用MD5签名代替
  console.warn('警告：使用了MD5签名代替RSA签名，请在生产环境实现正确的RSA签名！');
  return md5(stringA).toUpperCase();
}

/**
 * 验证签名
 * @param {Object} params - 参数对象
 * @param {String} key - 商户密钥
 * @returns {Boolean} 验证结果
 */
export function verifySign(params, key) {
  const sign = params.sign;
  if (!sign) return false;
  
  const signType = params.sign_type || 'MD5';
  
  if (signType.toUpperCase() === 'MD5') {
    const calculatedSign = signParams(params, key);
    return sign === calculatedSign;
  } else if (signType.toUpperCase() === 'RSA') {
    // 这里需要实现RSA验签逻辑
    console.warn('警告：RSA验签功能尚未实现！');
    return false;
  }
  
  return false;
}

/**
 * 生成随机字符串
 * @param {Number} length - 长度
 * @returns {String} 随机字符串
 */
export function generateNonceStr(length = 32) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成订单号
 * @param {String} prefix - 前缀
 * @returns {String} 订单号
 */
export function generateOrderNo(prefix = '') {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  
  return `${prefix}${year}${month}${day}${hour}${minute}${second}${random}`;
} 