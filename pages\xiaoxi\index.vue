<template>
  <view class="container">
    <!-- 🎯 优雅现代导航栏 -->
    <view class="elegant-navbar">
      <!-- 状态栏占位 -->
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

      <!-- 导航栏主体 -->
      <view class="navbar-main">
        <!-- 左侧返回和标题 -->
        <view class="navbar-left">
          <view class="back-btn" @click="goBack">
            <text class="back-icon">←</text>
          </view>
          <view class="navbar-title">
            <text class="title-text">系统消息</text>
          </view>
        </view>

        <!-- 右侧操作 -->
        <view class="navbar-actions">
          <!-- 全部已读按钮 -->
          <view class="mark-all-btn" @click="markAllAsRead">
            <text class="mark-all-text">全部已读</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 导航栏占位 -->
    <view class="navbar-placeholder" :style="{ height: navbarPlaceholderHeight + 'px' }"></view>

    <!-- 🎨 微信风格消息列表 -->
    <scroll-view
      scroll-y
      class="message-list"
      @scrolltolower="onScrollToLower"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <template v-if="systemMessages.today.length > 0 || systemMessages.yesterday.length > 0 || systemMessages.older.length > 0">

        <!-- 今天的消息 -->
        <template v-if="systemMessages.today.length > 0">
          <view class="date-divider">
            <text class="date-text">今天</text>
          </view>
          <view
            v-for="(item, index) in systemMessages.today"
            :key="'today-'+index"
            class="wechat-message-item"
            :class="{ 'expanded': item.expanded }"
          >
            <!-- 消息头部 -->
            <view class="message-header">
              <view class="system-avatar">
                <text class="avatar-icon">🔔</text>
              </view>
              <view class="message-info">
                <view class="sender-info">
                  <text class="sender-name">系统消息</text>
                  <text class="message-time">{{ formatTime(item.time) }}</text>
                  <view class="unread-dot" v-if="item.is_read === '0'"></view>
                </view>
                <view class="message-preview" @click="toggleMessage(item)">
                  <text class="message-title">{{ item.title }}</text>
                </view>
              </view>
            </view>

            <!-- 消息完整内容 -->
            <view class="message-full-content" v-if="item.expanded">
              <view class="content-wrapper">
                <text class="content-text">{{ item.content }}</text>
              </view>
              <view class="message-actions">
                <view class="action-btn" @click="markAsRead(item)" v-if="item.is_read === '0'">
                  <text class="action-text">标记已读</text>
                </view>
              </view>
            </view>
          </view>
        </template>

        <!-- 昨天的消息 -->
        <template v-if="systemMessages.yesterday.length > 0">
          <view class="date-divider">
            <text class="date-text">昨天</text>
          </view>
          <view
            v-for="(item, index) in systemMessages.yesterday"
            :key="'yesterday-'+index"
            class="wechat-message-item"
            :class="{ 'expanded': item.expanded }"
          >
            <!-- 消息头部 -->
            <view class="message-header">
              <view class="system-avatar">
                <text class="avatar-icon">🔔</text>
              </view>
              <view class="message-info">
                <view class="sender-info">
                  <text class="sender-name">系统消息</text>
                  <text class="message-time">{{ formatTime(item.time) }}</text>
                  <view class="unread-dot" v-if="item.is_read === '0'"></view>
                </view>
                <view class="message-preview" @click="toggleMessage(item)">
                  <text class="message-title">{{ item.title }}</text>
                </view>
              </view>
            </view>

            <!-- 消息完整内容 -->
            <view class="message-full-content" v-if="item.expanded">
              <view class="content-wrapper">
                <text class="content-text">{{ item.content }}</text>
              </view>
              <view class="message-actions">
                <view class="action-btn" @click="markAsRead(item)" v-if="item.is_read === '0'">
                  <text class="action-text">标记已读</text>
                </view>
              </view>
            </view>
          </view>
        </template>

        <!-- 更早的消息 -->
        <template v-if="systemMessages.older.length > 0">
          <view class="date-divider">
            <text class="date-text">更早</text>
          </view>
          <view
            v-for="(item, index) in systemMessages.older"
            :key="'older-'+index"
            class="wechat-message-item"
            :class="{ 'expanded': item.expanded }"
          >
            <!-- 消息头部 -->
            <view class="message-header">
              <view class="system-avatar">
                <text class="avatar-icon">🔔</text>
              </view>
              <view class="message-info">
                <view class="sender-info">
                  <text class="sender-name">系统消息</text>
                  <text class="message-time">{{ formatTime(item.time) }}</text>
                  <view class="unread-dot" v-if="item.is_read === '0'"></view>
                </view>
                <view class="message-preview" @click="toggleMessage(item)">
                  <text class="message-title">{{ item.title }}</text>
                </view>
              </view>
            </view>

            <!-- 消息完整内容 -->
            <view class="message-full-content" v-if="item.expanded">
              <view class="content-wrapper">
                <text class="content-text">{{ item.content }}</text>
              </view>
              <view class="message-actions">
                <view class="action-btn" @click="markAsRead(item)" v-if="item.is_read === '0'">
                  <text class="action-text">标记已读</text>
                </view>
              </view>
            </view>
          </view>
        </template>

        <!-- 加载更多按钮 -->
        <view class="load-more-btn" @click="loadMoreMessages" v-if="hasMoreMessages && !isLoading">
          <text class="load-more-text">查看更早消息</text>
        </view>

        <!-- 加载中提示 -->
        <view class="loading-indicator" v-if="isLoading">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
      </template>

      <!-- 无数据状态 -->
      <view class="empty-state" v-else>
        <view class="empty-icon">📭</view>
        <text class="empty-text">暂无系统消息</text>
        <text class="empty-desc">系统消息将在这里显示</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getAnnouncementList, getMessageList, clearAllMessages, getUnreadMessageCount } from '@/api/message.js'

export default {
  data() {
    return {
      statusBarHeight: 0, // 状态栏高度
      isRefreshing: false,
      hasMoreMessages: true,
      isLoading: false,
      // 🎨 只保存系统消息数据
      systemMessages: {
        today: [],
        yesterday: [],
        older: []
      },
      // 📊 分页信息
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        pages: 0
      },
      // 📈 统计信息
      unreadCount: 0
    }
  },
  computed: {
    // 📏 导航栏占位高度
    navbarPlaceholderHeight() {
      // 状态栏高度(px) + 导航栏总高度
      // 增加一些额外高度确保完全不遮挡
      return this.statusBarHeight + 80;
    },


  },
  onLoad() {
    // 🚀 页面加载时获取消息
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight || 0;

    this.initPage();
  },
  methods: {
    // 🎨 切换消息展开/收起状态
    toggleMessage(message) {
      console.log('🎨 切换消息状态:', message);
      // 使用 Vue.set 确保响应式更新
      this.$set(message, 'expanded', !message.expanded);

      // 如果展开消息且未读，自动标记为已读
      if (message.expanded && message.is_read === '0') {
        this.markAsRead(message);
      }
    },

    // 📝 标记单个消息为已读
    async markAsRead(message) {
      try {
        // 调用API标记消息为已读
        // const response = await markMessageAsRead(message.id);

        // 更新本地状态
        this.$set(message, 'is_read', '1');

        // 更新未读计数
        if (this.unreadCount > 0) {
          this.unreadCount--;
        }

        uni.showToast({
          title: '已标记为已读',
          icon: 'success',
          duration: 1500
        });
      } catch (error) {
        console.error('❌ 标记已读失败:', error);
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },

    // 📝 全部已读
    async markAllAsRead() {
      try {
        uni.showLoading({ title: '处理中...' });

        // 更新本地数据
        const updateMessages = (messages) => {
          messages.forEach(msg => {
            this.$set(msg, 'is_read', '1');
          });
        };

        updateMessages(this.systemMessages.today);
        updateMessages(this.systemMessages.yesterday);
        updateMessages(this.systemMessages.older);

        // 重置未读计数
        this.unreadCount = 0;

        uni.hideLoading();
        uni.showToast({
          title: '全部已读',
          icon: 'success'
        });
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },

    // 🚀 初始化页面
    async initPage() {
      try {
        // 并行加载数据
        await Promise.all([
          this.fetchMessages(),
          this.fetchUnreadCount()
        ]);
      } catch (error) {
        console.error('❌ 初始化页面失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    },



    // 🏷️ 获取类型文本
    getTypeText(type) {
      const typeMap = {
        'system': '系统',
        'transaction': '交易',
        'activity': '活动',
        'staff': '客服',
        'client': '客户端'
      };
      return typeMap[type] || '其他';
    },

    // ⏰ 格式化时间
    formatTime(time) {
      if (!time) return '';
      const date = new Date(time);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

      if (messageDate.getTime() === today.getTime()) {
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      } else {
        return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
      }
    },

    // 📝 获取消息摘要
    getMessageSummary(content) {
      if (!content) return '';
      // 显示前50个字符，超出部分用...表示
      return content.length > 50 ? content.substring(0, 50) + '...' : content;
    },

    // 📥 获取系统消息数据
    async fetchMessages(isLoadMore = false) {
      try {
        if (!isLoadMore) {
          this.isLoading = true;
        }

        console.log('📥 获取系统消息列表...');

        // 调用API获取公告列表（系统消息）
        const response = await getAnnouncementList();

        console.log('📥 系统消息响应:', response);

        if (response && response.code === 0) {
          const messageList = response.data || [];

          // 🔍 只保留系统消息类型
          const systemMessageList = messageList.filter(msg => {
            const type = msg.type || 'system';
            return type === 'system' || type === 'client'; // client类型也归类为系统消息
          });

          // 🕒 按时间分组系统消息
          this.groupSystemMessagesByTime(systemMessageList, isLoadMore);

          // 更新分页信息
          if (response.data && response.data.pagination) {
            this.pagination = response.data.pagination;
          }

          console.log('✅ 系统消息加载成功，共', systemMessageList.length, '条');
        } else {
          throw new Error(response?.msg || '获取消息失败');
        }

      } catch (error) {
        console.error('❌ 获取消息失败:', error);

        // 显示错误提示
        uni.showToast({
          title: error.message || '获取消息失败',
          icon: 'none'
        });

        // 设置空数据
        if (!isLoadMore) {
          this.systemMessages = { today: [], yesterday: [], older: [] };
        }
      } finally {
        this.isLoading = false;
        this.isRefreshing = false;
      }
    },

    // 🕒 按时间分组系统消息
    groupSystemMessagesByTime(messageList, isLoadMore = false) {
      console.log('🕒 开始分组系统消息:', messageList);

      const today = new Date();
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

      const todayStr = this.formatDate(today);
      const yesterdayStr = this.formatDate(yesterday);

      console.log('📅 今天:', todayStr, '昨天:', yesterdayStr);

      const grouped = {
        today: [],
        yesterday: [],
        older: []
      };

      messageList.forEach(message => {
        console.log('📝 处理系统消息:', message);
        console.log('📅 消息时间:', message.addtime);

        // 🎨 为每个消息添加展开状态
        const processedMessage = {
          ...message,
          expanded: false, // 默认收起状态
          time: message.addtime || message.time || new Date().toISOString()
        };

        // 处理时间格式 - 支持多种格式
        let messageDate;
        if (message.addtime) {
          // 如果是时间戳
          if (typeof message.addtime === 'number') {
            messageDate = this.formatDate(new Date(message.addtime * 1000));
          } else {
            // 如果是字符串格式的时间
            messageDate = this.formatDate(new Date(message.addtime));
          }
        } else {
          messageDate = this.formatDate(new Date());
        }

        console.log('📅 格式化后的消息日期:', messageDate);

        if (messageDate === todayStr) {
          grouped.today.push(processedMessage);
          console.log('📅 归类到今天');
        } else if (messageDate === yesterdayStr) {
          grouped.yesterday.push(processedMessage);
          console.log('📅 归类到昨天');
        } else {
          grouped.older.push(processedMessage);
          console.log('📅 归类到更早');
        }
      });

      console.log('📊 系统消息分组结果:', grouped);
      console.log('📊 今天消息数:', grouped.today.length);
      console.log('📊 昨天消息数:', grouped.yesterday.length);
      console.log('📊 更早消息数:', grouped.older.length);

      if (isLoadMore) {
        // 加载更多时，追加到现有数据
        this.systemMessages.older = [...this.systemMessages.older, ...grouped.older];
      } else {
        // 首次加载或刷新时，替换所有数据
        this.systemMessages = grouped;
      }

      console.log('📊 最终系统消息数据:', this.systemMessages);
    },

    // 📅 格式化日期为 YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 📊 获取未读消息数量
    async fetchUnreadCount() {
      try {
        const response = await getUnreadMessageCount();
        if (response && response.code === 0) {
          this.unreadCount = response.data.unread_count || 0;
        }
      } catch (error) {
        console.error('❌ 获取未读消息数量失败:', error);
      }
    },
    
    goBack() {
      uni.navigateBack();
    },
    


    // 🗑️ 清空消息
    clearMessages() {
      uni.showModal({
        title: '确认清空',
        content: '是否清空所有消息通知？',
        success: async (res) => {
          if (res.confirm) {
            try {
              // 调用清空API
              const response = await clearAllMessages();

              if (response && response.code === 0) {
                // 清空本地数据
                this.systemMessages = { today: [], yesterday: [], older: [] };
                this.unreadCount = 0;

                uni.showToast({
                  title: '消息已清空',
                  icon: 'success'
                });
              } else {
                throw new Error(response?.msg || '清空失败');
              }
            } catch (error) {
              console.error('❌ 清空消息失败:', error);
              uni.showToast({
                title: error.message || '清空失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    
    getIconClass(type) {
      const classMap = {
        'system': 'icon-system',
        'transaction': 'icon-transaction',
        'activity': 'icon-activity',
        'staff': 'icon-staff'
      };
      return classMap[type] || '';
    },
    
    getIconSrc(type) {
      const iconMap = {
        'system': '/static/message/system.png',
        'transaction': '/static/message/transaction.png',
        'activity': '/static/message/activity.png',
        'staff': '/static/message/staff.png'
      };
      return iconMap[type] || '';
    },
    
    // 📄 加载更多消息
    async loadMoreMessages() {
      if (this.isLoading || !this.hasMoreMessages) return;

      try {
        this.isLoading = true;

        // 增加页码
        this.pagination.page += 1;

        // 加载更多数据
        await this.fetchMessages(true);

        // 检查是否还有更多数据
        if (this.pagination.page >= this.pagination.pages) {
          this.hasMoreMessages = false;
        }

      } catch (error) {
        console.error('❌ 加载更多消息失败:', error);
        // 恢复页码
        this.pagination.page -= 1;

        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },

    // 📜 滚动到底部
    onScrollToLower() {
      if (this.hasMoreMessages && !this.isLoading) {
        this.loadMoreMessages();
      }
    },

    // 🔄 下拉刷新
    onRefresh() {
      this.isRefreshing = true;
      // 重置分页
      this.pagination.page = 1;
      this.hasMoreMessages = true;
      // 刷新消息列表
      this.fetchMessages();
    }
  }
}
</script>

<style>
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Arial, sans-serif;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 🎯 优雅现代导航栏样式 */
.elegant-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 50%, #8B5CF6 100%);
  box-shadow: 0 4rpx 20rpx rgba(81, 69, 247, 0.15);
}

.status-bar {
  width: 100%;
}

.navbar-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  min-height: 88rpx;
}

.navbar-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.back-icon {
  font-size: 36rpx;
  color: #fff;
  font-weight: 600;
}

.navbar-title {
  flex: 1;
}

.title-text {
  font-size: 36rpx;
  color: #fff;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.mark-all-btn {
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
}

.mark-all-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}

/* 导航栏占位 */
.navbar-placeholder {
  width: 100%;
  background: transparent;
  flex-shrink: 0;
  /* 高度通过内联样式动态设置 */
}



/* 🎨 微信风格消息列表 */
.message-list {
  flex: 1;
  height: calc(100vh - 88px); /* 减去导航栏高度 */
  background-color: #EDEDED;
}

/* 📅 日期分隔符 */
.date-divider {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  background-color: #EDEDED;
}

.date-text {
  background-color: rgba(0, 0, 0, 0.1);
  color: #999;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  backdrop-filter: blur(10rpx);
}

/* 🎨 微信风格消息项 */
.wechat-message-item {
  background-color: #fff;
  margin: 0 24rpx 2rpx;
  border-radius: 0;
  border-bottom: 1rpx solid #E5E5E5;
  transition: background-color 0.2s ease;
}

.wechat-message-item:last-child {
  border-bottom: none;
}

.wechat-message-item.expanded {
  background-color: #F7F7F7;
}

/* 📱 消息头部 */
.message-header {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  border-bottom: none;
}

/* 🔔 系统头像 */
.system-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

.avatar-icon {
  font-size: 32rpx;
  color: #fff;
}

/* 📝 消息信息区域 */
.message-info {
  flex: 1;
  min-width: 0;
}

/* 👤 发送者信息 */
.sender-info {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.sender-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.message-time {
  font-size: 24rpx;
  color: #999;
  margin-right: 16rpx;
}

.unread-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: #FF3B30;
  border-radius: 50%;
  flex-shrink: 0;
}

/* 📄 消息预览 */
.message-preview {
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.message-preview:active {
  opacity: 0.7;
}

.message-title {
  font-size: 30rpx;
  color: #333;
  line-height: 1.4;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 📖 消息完整内容 */
.message-full-content {
  padding: 0 24rpx 24rpx 128rpx; /* 左边距对齐头像右侧 */
  animation: expandContent 0.3s ease-out;
}

@keyframes expandContent {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 1000rpx;
  }
}

.content-wrapper {
  background-color: #F8F8F8;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  border-left: 6rpx solid #007AFF;
}

.content-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-word;
  white-space: pre-wrap;
}

/* 🔘 消息操作按钮 */
.message-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  border-radius: 20rpx;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

.action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.action-text {
  font-size: 24rpx;
  color: #fff;
  font-weight: 500;
}

/* 🔄 加载更多按钮 */
.load-more-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx;
  margin: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  border: 2rpx solid #E5E5E5;
  cursor: pointer;
  transition: all 0.2s ease;
}

.load-more-btn:active {
  background-color: #F7F7F7;
  transform: scale(0.98);
}

.load-more-text {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: 500;
}

/* 🔄 加载指示器 */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  background-color: #EDEDED;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E5E5E5;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 📭 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background-color: #EDEDED;
  min-height: 400rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  line-height: 1.4;
}
</style>

