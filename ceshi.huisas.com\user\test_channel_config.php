<?php
/**
 * 测试商户支付通道配置
 */
include("../includes/common.php");

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: text/html; charset=utf-8');

if($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查登录状态
if(!$islogin) {
    echo "<h2>未登录</h2>";
    echo "<p>请先登录商户后台，然后再访问此页面</p>";
    echo "<p>登录地址: <a href='/user/login.php'>/user/login.php</a></p>";
    echo "<p>当前登录状态: " . ($islogin ? '已登录' : '未登录') . "</p>";
    echo "<p>Session UID: " . ($_SESSION['user_uid'] ?? 'null') . "</p>";
    exit();
}

// 获取商户信息
$userrow = $DB->getRow("SELECT * FROM pre_user WHERE uid='$uid' LIMIT 1");
if(!$userrow) {
    exit('{"code":-1,"msg":"商户不存在"}');
}

echo "<h2>商户支付通道配置检查</h2>";
echo "<p>商户ID: {$userrow['uid']}</p>";
echo "<p>商户组: {$userrow['gid']}</p>";
echo "<p>商户状态: " . ($userrow['status'] == 1 ? '正常' : '禁用') . "</p>";

// 检查支付方式
echo "<h3>支付方式配置:</h3>";
$types = $DB->getAll("SELECT * FROM pre_type ORDER BY id");
foreach($types as $type) {
    echo "<p>ID: {$type['id']}, 名称: {$type['name']}, 显示名: {$type['showname']}, 状态: " . ($type['status'] == 1 ? '启用' : '禁用') . "</p>";
}

// 检查支付通道
echo "<h3>支付通道配置:</h3>";
$channels = $DB->getAll("SELECT * FROM pre_channel WHERE status=1 ORDER BY id");
foreach($channels as $channel) {
    echo "<p>通道ID: {$channel['id']}, 名称: {$channel['name']}, 插件: {$channel['plugin']}, 支付方式: {$channel['type']}</p>";
}

// 测试各支付方式的通道获取
echo "<h3>支付方式通道测试:</h3>";
$test_types = ['alipay', 'wxpay', 'qqpay', 'bank'];
foreach($test_types as $pay_type) {
    try {
        $submitData = \lib\Channel::submit($pay_type, $userrow['uid'], $userrow['gid'], 1.00);
        if($submitData) {
            echo "<p style='color:green;'>{$pay_type}: 可用 - 通道ID: {$submitData['channel']}, 插件: {$submitData['plugin']}</p>";
        } else {
            echo "<p style='color:red;'>{$pay_type}: 不可用 - 无可用通道</p>";
        }
    } catch(Exception $e) {
        echo "<p style='color:red;'>{$pay_type}: 错误 - {$e->getMessage()}</p>";
    }
}

// 检查商户组配置
echo "<h3>商户组配置:</h3>";
if($userrow['gid'] > 0) {
    $groupinfo = $DB->getColumn("SELECT info FROM pre_group WHERE gid='{$userrow['gid']}' LIMIT 1");
    if($groupinfo) {
        $info = json_decode($groupinfo, true);
        echo "<pre>" . print_r($info, true) . "</pre>";
    } else {
        echo "<p>商户组无特殊配置</p>";
    }
} else {
    echo "<p>使用默认商户组</p>";
}
?>
