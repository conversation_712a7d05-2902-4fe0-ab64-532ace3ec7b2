<?php
/**
 * 预制码功能安装程序
 * 文件路径: epay_release_99009/admin/install.php
 */
include("../includes/common.php");
$title='预制码功能安装';
include './head.php';
if($islogin==1){}else exit("<script language='javascript'>window.location.href='./login.php';</script>");

$install_status = [];
$errors = [];

// 检查generatePreCode函数是否存在
if(function_exists('generatePreCode')) {
    $install_status['function'] = true;
} else {
    $install_status['function'] = false;
    $errors[] = 'generatePreCode函数不存在，请在includes/functions.php中添加该函数';
}

// 检查并创建预制码表
try {
    $create_precode_sql = "CREATE TABLE IF NOT EXISTS `{$dbconfig['dbqz']}_precode` (
        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `code` varchar(50) NOT NULL COMMENT '预制码',
        `uid` int(11) DEFAULT NULL COMMENT '绑定的商户ID',
        `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0未绑定 1已绑定',
        `addtime` datetime DEFAULT NULL COMMENT '生成时间',
        `bindtime` datetime DEFAULT NULL COMMENT '绑定时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `code` (`code`),
        KEY `uid` (`uid`),
        KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预制收款码表';";
    
    $DB->exec($create_precode_sql);
    $install_status['precode_table'] = true;
} catch(Exception $e) {
    $install_status['precode_table'] = false;
    $errors[] = '预制码表创建失败: ' . $e->getMessage();
}

// 检查并创建收款码配置表
try {
    $create_qrconfig_sql = "CREATE TABLE IF NOT EXISTS `{$dbconfig['dbqz']}_qrcode_config` (
        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `uid` int(11) NOT NULL COMMENT '商户ID',
        `name` varchar(100) NOT NULL COMMENT '收款码名称',
        `style` varchar(50) DEFAULT 'default' COMMENT '收款码样式',
        `staff_id` int(11) DEFAULT NULL COMMENT '绑定员工ID，NULL表示商户自己',
        `fixed_amount` decimal(10,2) DEFAULT NULL COMMENT '固定金额，NULL表示不固定',
        `description` text COMMENT '描述信息',
        `precode` varchar(50) DEFAULT NULL COMMENT '关联的预制码',
        `qr_url` text COMMENT '二维码URL',
        `qr_style` varchar(30) DEFAULT 'native' COMMENT '二维码样式',
        `amount` decimal(10,2) DEFAULT NULL COMMENT '金额',
        `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1启用 0禁用',
        `addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `updatetime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`id`),
        KEY `uid` (`uid`),
        KEY `staff_id` (`staff_id`),
        KEY `precode` (`precode`),
        KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收款码配置表';";
    
    $DB->exec($create_qrconfig_sql);
    $install_status['qrconfig_table'] = true;
} catch(Exception $e) {
    $install_status['qrconfig_table'] = false;
    $errors[] = '收款码配置表创建失败: ' . $e->getMessage();
}

// 检查员工表是否存在
try {
    $staff_exists = $DB->getColumn("SHOW TABLES LIKE '{$dbconfig['dbqz']}_staff'");
    if($staff_exists) {
        $install_status['staff_table'] = true;
    } else {
        $install_status['staff_table'] = false;
        $errors[] = '员工表不存在，请先创建员工表';
    }
} catch(Exception $e) {
    $install_status['staff_table'] = false;
    $errors[] = '检查员工表失败: ' . $e->getMessage();
}

// 测试generatePreCode函数
if($install_status['function'] && $install_status['precode_table']) {
    try {
        $test_code = generatePreCode();
        if($test_code && strlen($test_code) == 8) {
            $install_status['function_test'] = true;
        } else {
            $install_status['function_test'] = false;
            $errors[] = 'generatePreCode函数测试失败，返回值不正确';
        }
    } catch(Exception $e) {
        $install_status['function_test'] = false;
        $errors[] = 'generatePreCode函数测试失败: ' . $e->getMessage();
    }
} else {
    $install_status['function_test'] = false;
}

// 检查API接口
$api_checks = [
    'saveQRConfig' => false,
    'getQRConfigList' => false,
    'generateQRUrl' => false,
    'editQRConfig' => false,
    'deleteQRConfig' => false,
    'bindPreCode' => false
];

// 读取staff.php文件检查API
$staff_file = '../user/staff.php';
if(file_exists($staff_file)) {
    $staff_content = file_get_contents($staff_file);
    foreach($api_checks as $api => $status) {
        if(strpos($staff_content, "case '$api'") !== false) {
            $api_checks[$api] = true;
        }
    }
}

$all_success = true;
foreach($install_status as $status) {
    if(!$status) {
        $all_success = false;
        break;
    }
}
?>

<div class="container" style="padding-top:70px;">
    <div class="row">
        <div class="col-md-12">
            <h3>预制码功能安装检查</h3>
            
            <?php if($all_success): ?>
            <div class="alert alert-success">
                <h4><i class="fa fa-check"></i> 安装检查完成！</h4>
                <p>所有必需的组件都已正确安装，您可以开始使用预制码功能了。</p>
                <a href="./precode.php" class="btn btn-primary">进入预制码管理</a>
            </div>
            <?php else: ?>
            <div class="alert alert-warning">
                <h4><i class="fa fa-warning"></i> 安装检查发现问题</h4>
                <p>请根据下面的检查结果修复相关问题。</p>
            </div>
            <?php endif; ?>
            
            <!-- 安装状态检查 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4>核心组件检查</h4>
                </div>
                <div class="panel-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>检查项目</th>
                                <th>状态</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>generatePreCode函数</td>
                                <td>
                                    <?php if($install_status['function']): ?>
                                        <span class="label label-success">正常</span>
                                    <?php else: ?>
                                        <span class="label label-danger">缺失</span>
                                    <?php endif; ?>
                                </td>
                                <td>生成预制码的核心函数</td>
                            </tr>
                            <tr>
                                <td>预制码表</td>
                                <td>
                                    <?php if($install_status['precode_table']): ?>
                                        <span class="label label-success">正常</span>
                                    <?php else: ?>
                                        <span class="label label-danger">失败</span>
                                    <?php endif; ?>
                                </td>
                                <td>pay_precode表创建状态</td>
                            </tr>
                            <tr>
                                <td>收款码配置表</td>
                                <td>
                                    <?php if($install_status['qrconfig_table']): ?>
                                        <span class="label label-success">正常</span>
                                    <?php else: ?>
                                        <span class="label label-danger">失败</span>
                                    <?php endif; ?>
                                </td>
                                <td>pay_qrcode_config表创建状态</td>
                            </tr>
                            <tr>
                                <td>员工表</td>
                                <td>
                                    <?php if($install_status['staff_table']): ?>
                                        <span class="label label-success">存在</span>
                                    <?php else: ?>
                                        <span class="label label-warning">缺失</span>
                                    <?php endif; ?>
                                </td>
                                <td>pay_staff表存在状态</td>
                            </tr>
                            <tr>
                                <td>函数测试</td>
                                <td>
                                    <?php if($install_status['function_test']): ?>
                                        <span class="label label-success">通过</span>
                                    <?php else: ?>
                                        <span class="label label-danger">失败</span>
                                    <?php endif; ?>
                                </td>
                                <td>generatePreCode函数功能测试</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- API接口检查 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4>API接口检查</h4>
                </div>
                <div class="panel-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>API接口</th>
                                <th>状态</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($api_checks as $api => $status): ?>
                            <tr>
                                <td><?php echo $api; ?></td>
                                <td>
                                    <?php if($status): ?>
                                        <span class="label label-success">存在</span>
                                    <?php else: ?>
                                        <span class="label label-warning">缺失</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $descriptions = [
                                        'saveQRConfig' => '保存收款码配置',
                                        'getQRConfigList' => '获取收款码列表',
                                        'generateQRUrl' => '生成收款码URL',
                                        'editQRConfig' => '编辑收款码配置',
                                        'deleteQRConfig' => '删除收款码配置',
                                        'bindPreCode' => '绑定预制码'
                                    ];
                                    echo $descriptions[$api];
                                    ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 错误信息 -->
            <?php if($errors): ?>
            <div class="panel panel-danger">
                <div class="panel-heading">
                    <h4>错误信息</h4>
                </div>
                <div class="panel-body">
                    <ul>
                        <?php foreach($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- 安装说明 -->
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h4>安装说明</h4>
                </div>
                <div class="panel-body">
                    <h5>如果检查失败，请按以下步骤修复：</h5>
                    <ol>
                        <li><strong>generatePreCode函数缺失</strong>：在 includes/functions.php 文件末尾添加 generatePreCode 函数</li>
                        <li><strong>数据库表创建失败</strong>：检查数据库连接和权限，确保有CREATE TABLE权限</li>
                        <li><strong>员工表不存在</strong>：先创建员工管理功能相关的数据库表</li>
                        <li><strong>API接口缺失</strong>：在 user/staff.php 文件中添加相应的API接口</li>
                    </ol>
                    
                    <h5>完整安装步骤：</h5>
                    <ol>
                        <li>上传所有必需的PHP文件到服务器</li>
                        <li>在数据库中执行SQL创建语句</li>
                        <li>访问此页面检查安装状态</li>
                        <li>修复发现的问题</li>
                        <li>重新检查直到所有项目都显示正常</li>
                    </ol>
                </div>
            </div>
            
            <div class="text-center">
                <a href="?" class="btn btn-default">重新检查</a>
                <a href="./precode.php" class="btn btn-primary">进入预制码管理</a>
                <a href="./" class="btn btn-default">返回首页</a>
            </div>
            
        </div>
    </div>
</div>

</body>
</html>
