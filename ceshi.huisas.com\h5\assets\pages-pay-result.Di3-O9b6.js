function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/order.ZBwgueDH.js","assets/index-B1Q521gi.js","assets/index-BaSB2f3O.css","assets/request.DGmokXb9.js"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
import{A as s,ad as a,a as e,B as t,y as l,d as o,e as r,w as n,i as d,o as c,f as u,C as i,h as f,t as _,F as m,j as h,l as p,p as I}from"./index-B1Q521gi.js";import{_ as v}from"./custom-navbar.DuzuSmPc.js";import{r as b}from"./uni-app.es.DAfa8VxY.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";const g=y({data:()=>({orderId:"",status:"success",failReason:"",orderInfo:null}),onLoad(s){s.orderId&&(this.orderId=s.orderId),s.status&&(this.status=s.status),s.reason&&(this.failReason=decodeURIComponent(s.reason)),this.orderId&&"success"===this.status&&this.loadOrderDetail()},methods:{loadOrderDetail(){s({title:"加载中..."}),a((()=>import("./order.ZBwgueDH.js").then((s=>s.o))),__vite__mapDeps([0,1,2,3])).then((({getOrder:s})=>{s(this.orderId).then((s=>{0===s.code?this.orderInfo={amount:s.money||"--",merchantName:s.name||"--",orderId:s.trade_no||this.orderId,payTime:s.endtime||"--"}:e({title:s.msg||"获取订单详情失败",icon:"none"})})).catch((()=>{e({title:"网络异常，请重试",icon:"none"})})).finally((()=>{t()}))})).catch((s=>{console.error("加载订单API失败",s),t(),e({title:"系统异常，请稍后再试",icon:"none"})}))},goBack(){"success"===this.status?uni.exitMiniProgram():l()}}},[["render",function(s,a,e,t,l,y){const g=b(o("custom-navbar"),v),j=p,k=d,w=I;return c(),r(k,{class:"container"},{default:n((()=>[u(g,{title:"支付结果",shadow:!0}),u(k,{class:"result-content"},{default:n((()=>["success"===l.status?(c(),i(m,{key:0},[u(k,{class:"icon-wrapper success"},{default:n((()=>[u(j,{class:"iconfont icon-success"},{default:n((()=>[f("✓")])),_:1})])),_:1}),u(k,{class:"result-title"},{default:n((()=>[f("支付成功")])),_:1}),u(k,{class:"result-desc"},{default:n((()=>[f("您的订单已支付成功")])),_:1}),u(k,{class:"order-info"},{default:n((()=>[u(k,{class:"info-item"},{default:n((()=>[u(j,{class:"label"},{default:n((()=>[f("订单金额")])),_:1}),u(j,{class:"value"},{default:n((()=>{var s;return[f("¥"+_((null==(s=l.orderInfo)?void 0:s.amount)||"--"),1)]})),_:1})])),_:1}),u(k,{class:"info-item"},{default:n((()=>[u(j,{class:"label"},{default:n((()=>[f("商户名称")])),_:1}),u(j,{class:"value"},{default:n((()=>{var s;return[f(_((null==(s=l.orderInfo)?void 0:s.merchantName)||"--"),1)]})),_:1})])),_:1}),u(k,{class:"info-item"},{default:n((()=>[u(j,{class:"label"},{default:n((()=>[f("订单编号")])),_:1}),u(j,{class:"value"},{default:n((()=>{var s;return[f(_((null==(s=l.orderInfo)?void 0:s.orderId)||"--"),1)]})),_:1})])),_:1}),u(k,{class:"info-item"},{default:n((()=>[u(j,{class:"label"},{default:n((()=>[f("支付时间")])),_:1}),u(j,{class:"value"},{default:n((()=>{var s;return[f(_((null==(s=l.orderInfo)?void 0:s.payTime)||"--"),1)]})),_:1})])),_:1})])),_:1})],64)):"fail"===l.status?(c(),i(m,{key:1},[u(k,{class:"icon-wrapper fail"},{default:n((()=>[u(j,{class:"iconfont icon-fail"},{default:n((()=>[f("✕")])),_:1})])),_:1}),u(k,{class:"result-title"},{default:n((()=>[f("支付失败")])),_:1}),u(k,{class:"result-desc"},{default:n((()=>[f(_(l.failReason||"支付未完成，请重新支付"),1)])),_:1})],64)):h("",!0),u(k,{class:"action-buttons"},{default:n((()=>[u(w,{class:"btn secondary",onClick:y.goBack},{default:n((()=>[f(_("success"===l.status?"关闭":"重新支付"),1)])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-de12e447"]]);export{g as default};
