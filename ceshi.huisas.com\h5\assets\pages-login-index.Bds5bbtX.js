import{g as e,r as s,s as o,a,b as t,c as l,n as i,d as n,e as r,w as c,i as d,o as p,f as u,h,j as g,t as m,k as f,l as w,I as _,m as k,p as C}from"./index-B1Q521gi.js";import{r as b,_ as y}from"./uni-app.es.DAfa8VxY.js";import{g as x,p as F}from"./request.DGmokXb9.js";import{_ as T}from"./_plugin-vue_export-helper.BCo6x5W8.js";const V={LOGIN:"/user/ajax.php?act=login",GET_CSRF:"/user/ajax.php?act=getcsrf",CAPTCHA:"/user/ajax.php?act=captcha",SENDCODE:"/user/ajax.php?act=sendcode",REG:"/user/ajax.php?act=reg",FORGOT:"/user/ajax.php?act=sendcode2"};const P=T({data:()=>({loginForm:{username:"",password:"",type:1},rememberPassword:!1,passwordVisible:!1,loading:!1,captchaObj:null,showCaptcha:!1,captchaResult:{}}),onLoad(){const s=e("remembered_username");s&&(this.loginForm.username=s,this.rememberPassword=!0),this.fetchCSRFToken()},methods:{fetchCSRFToken(){console.log("正在获取CSRF Token..."),s({url:"/ajax.php?act=getcsrf",method:"GET",success:e=>{console.log("CSRF Token获取响应:",e),200===e.statusCode&&e.data&&0===e.data.code&&e.data.token?(o("csrf_token",e.data.token),console.log("成功获取CSRF Token:",e.data.token)):console.error("获取CSRF Token失败:",e.data)},fail:e=>{console.error("CSRF Token请求错误:",e)}})},initCaptcha(){this.showCaptcha=!0,async function(){const e=await x(V.CAPTCHA);if(1===e.success)return e;throw new Error("获取验证码失败")}().then((e=>{1===e.success&&(1===e.version?this.initGeetestV4(e.gt):this.initGeetestV3(e.gt,e.challenge,e.new_captcha))})).catch((e=>{a({title:"验证码加载失败",icon:"none"})}))},initGeetestV4(e){console.log("初始化GeetestV4验证码")},initGeetestV3(e,s,o){console.log("初始化GeetestV3验证码")},togglePasswordVisibility(){this.passwordVisible=!this.passwordVisible},async handleLogin(){try{if(!this.loginForm.username)return void a({title:"请输入账号",icon:"none"});if(!this.loginForm.password)return void a({title:"请输入密码",icon:"none"});this.loading=!0,console.log("开始登录处理，登录数据:",this.loginForm);const i=e("csrf_token");s({url:"/ajax.php?act=login",method:"POST",data:{type:1,user:this.loginForm.username,pass:this.loginForm.password,csrf_token:i||""},header:{"content-type":"application/x-www-form-urlencoded"},success:e=>{var s;console.log("登录响应:",e),200===e.statusCode&&e.data&&0===e.data.code?(a({title:e.data.msg||"登录成功",icon:"success"}),e.data.token&&o("token",e.data.token),this.rememberPassword?o("remembered_username",this.loginForm.username):t("remembered_username"),setTimeout((()=>{l({url:e.data.url||"/pages/index/index"})}),1500)):(e.data&&e.data.captcha&&(this.showCaptcha=!0,this.initCaptcha()),a({title:(null==(s=e.data)?void 0:s.msg)||"登录失败",icon:"none"}))},fail:e=>{console.error("登录请求失败:",e),a({title:"网络错误，请稍后重试",icon:"none"})},complete:()=>{this.loading=!1}})}catch(i){console.error("登录操作异常:",i),a({title:"登录异常，请重试",icon:"none"}),this.loading=!1}},goToRegister(){i({url:"/pages/register/index"})},goToForgotPassword(){i({url:"/pages/login/forgot-password"})},handleThirdPartyLogin(e){(function(e,s=!1){return F("/ajax.php?act=connect",{type:e,bind:s?"1":""})})(e).then((e=>{0===e.code&&e.url?i({url:"/pages/login/oauth?url="+encodeURIComponent(e.url)}):a({title:e.msg||"登录方式不可用",icon:"none"})})).catch((e=>{a({title:"网络错误，请稍后重试",icon:"none"})}))}}},[["render",function(e,s,o,a,t,l){const i=f,x=w,F=d,T=b(n("uni-icons"),y),V=_,P=k,j=C;return p(),r(F,{class:"login-container"},{default:c((()=>[u(F,{class:"logo-section"},{default:c((()=>[u(i,{class:"logo-image",src:"/h5/assets/logo-CxwrX2FE.png",mode:"aspectFit"}),u(x,{class:"app-title"},{default:c((()=>[h("商家收款助手")])),_:1}),u(x,{class:"app-subtitle"},{default:c((()=>[h("专业的商家收款解决方案")])),_:1})])),_:1}),u(F,{class:"form-section"},{default:c((()=>[u(F,{class:"input-item"},{default:c((()=>[u(T,{type:"phone",size:"20",color:"#999"}),u(V,{class:"input-field",type:"text",modelValue:t.loginForm.username,"onUpdate:modelValue":s[0]||(s[0]=e=>t.loginForm.username=e),placeholder:"手机号/邮箱/商户ID"},null,8,["modelValue"])])),_:1}),u(F,{class:"input-item"},{default:c((()=>[u(T,{type:"locked",size:"20",color:"#999"}),u(V,{class:"input-field",type:t.passwordVisible?"text":"password",modelValue:t.loginForm.password,"onUpdate:modelValue":s[1]||(s[1]=e=>t.loginForm.password=e),placeholder:"密码"},null,8,["type","modelValue"]),u(T,{type:t.passwordVisible?"eye-filled":"eye",size:"20",color:"#bbb",onClick:l.togglePasswordVisibility},null,8,["type","onClick"])])),_:1}),t.showCaptcha?(p(),r(F,{key:0,class:"captcha-container"},{default:c((()=>[u(F,{id:"captcha-box"})])),_:1})):g("",!0),u(F,{class:"remember-section"},{default:c((()=>[u(P,{class:"remember-checkbox",checked:t.rememberPassword,onClick:s[2]||(s[2]=e=>t.rememberPassword=!t.rememberPassword)},null,8,["checked"]),u(x,{class:"remember-text"},{default:c((()=>[h("记住账号")])),_:1}),u(x,{class:"forgot-text",onClick:l.goToForgotPassword},{default:c((()=>[h("忘记密码?")])),_:1},8,["onClick"])])),_:1}),u(j,{class:"login-button",loading:t.loading,disabled:t.loading,onClick:l.handleLogin},{default:c((()=>[h(m(t.loading?"登录中...":"登录"),1)])),_:1},8,["loading","disabled","onClick"]),u(F,{class:"register-section"},{default:c((()=>[u(x,{class:"register-text"},{default:c((()=>[h("还没有账号?")])),_:1}),u(x,{class:"register-link",onClick:l.goToRegister},{default:c((()=>[h("立即注册")])),_:1},8,["onClick"])])),_:1})])),_:1}),u(F,{class:"other-login-section"},{default:c((()=>[u(F,{class:"divider"},{default:c((()=>[u(F,{class:"divider-line"}),u(x,{class:"divider-text"},{default:c((()=>[h("其他登录方式")])),_:1}),u(F,{class:"divider-line"})])),_:1}),u(F,{class:"login-methods"},{default:c((()=>[u(F,{class:"login-method wechat",onClick:s[3]||(s[3]=e=>l.handleThirdPartyLogin("wx"))},{default:c((()=>[u(T,{type:"weixin",size:"30",color:"#2aab5d"})])),_:1}),u(F,{class:"login-method qq",onClick:s[4]||(s[4]=e=>l.handleThirdPartyLogin("qq"))},{default:c((()=>[u(T,{type:"notification",size:"30",color:"#3f8cf7"})])),_:1}),u(F,{class:"login-method alipay",onClick:s[5]||(s[5]=e=>l.handleThirdPartyLogin("alipay"))},{default:c((()=>[u(T,{type:"smartphone",size:"30",color:"#fa5151"})])),_:1})])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-c9f51e0b"]]);export{P as default};
