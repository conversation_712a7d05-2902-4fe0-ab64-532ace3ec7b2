<template>
	<view class="container">
		<custom-navbar :config="navbarConfig" @back="onNavbarBack"></custom-navbar>
		
		<scroll-view class="scroll-content" scroll-y="true">
			<view class="test-section">
				<text class="section-title">API测试 - 商户统计数据</text>
				<text class="section-desc">测试后端真实数据接口</text>
			</view>

			<!-- getcount接口测试 -->
			<view class="test-card">
				<view class="test-header">
					<text class="test-title">1. getcount接口测试</text>
					<button class="test-btn" @click="testGetCount" :disabled="loading">
						{{ loading ? '测试中...' : '测试接口' }}
					</button>
				</view>
				
				<view class="result-box" v-if="getcountResult">
					<text class="result-title">返回结果:</text>
					<text class="result-content">{{ getcountResult }}</text>
				</view>
			</view>

			<!-- 数据展示 -->
			<view class="stats-section" v-if="statsData">
				<text class="section-title">2. 数据展示效果</text>
				
				<view class="stats-grid">
					<view class="stat-card today">
						<text class="stat-label">今日收款(元)</text>
						<text class="stat-value">¥{{ statsData.todayIncome }}</text>
					</view>
					
					<view class="stat-card yesterday">
						<text class="stat-label">昨日收款(元)</text>
						<text class="stat-value">¥{{ statsData.yesterdayIncome }}</text>
					</view>
					
					<view class="stat-card month">
						<text class="stat-label">本月收款(元)</text>
						<text class="stat-value">¥{{ statsData.monthIncome }}</text>
					</view>
					
					<view class="stat-card orders">
						<text class="stat-label">今日订单数</text>
						<text class="stat-value">{{ statsData.todayOrders }}</text>
					</view>
				</view>
			</view>

			<!-- 接口说明 -->
			<view class="info-section">
				<text class="section-title">3. 接口说明</text>
				<view class="info-item">
					<text class="info-label">接口地址:</text>
					<text class="info-value">/user/ajax2.php?act=getcount</text>
				</view>
				<view class="info-item">
					<text class="info-label">请求方式:</text>
					<text class="info-value">POST</text>
				</view>
				<view class="info-item">
					<text class="info-label">认证方式:</text>
					<text class="info-value">Cookie Token验证</text>
				</view>
			</view>

			<!-- 预期数据格式 -->
			<view class="expected-section">
				<text class="section-title">4. 预期数据格式</text>
				<text class="expected-code">{{expectedFormat}}</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import CustomNavbar from '@/components/custom-navbar.vue'
	import { getUserInfo, formatAmount } from '@/api/home.js'

	export default {
		components: {
			CustomNavbar
		},
		data() {
			return {
				navbarConfig: {
					title: 'API测试',
					showBack: true,
					showNotification: false
				},
				loading: false,
				getcountResult: '',
				statsData: null,
				expectedFormat: `{
  "code": 0,
  "orders": 5,
  "orders_today": 5,
  "settle_money": "60.64",
  "order_today_all": "60.64",
  "order_lastday_all": "0.00",
  "channels": [...]
}`
			}
		},

		methods: {
			// 测试getcount接口
			async testGetCount() {
				this.loading = true;
				this.getcountResult = '';
				this.statsData = null;

				try {
					console.log('开始测试getcount接口...');
					
					const response = await getUserInfo();
					console.log('API返回:', response);

					// 显示原始返回数据
					this.getcountResult = JSON.stringify(response, null, 2);

					// 处理统计数据
					if (response) {
						this.statsData = {
							todayIncome: formatAmount(response.order_today_all || 0),
							yesterdayIncome: formatAmount(response.order_lastday_all || 0),
							monthIncome: formatAmount(response.settle_money || 0),
							todayOrders: response.orders_today || 0
						};

						uni.showToast({
							title: '测试成功',
							icon: 'success'
						});
					}

				} catch (error) {
					console.error('测试失败:', error);
					
					this.getcountResult = `测试失败: ${error.message}

可能的原因:
1. 未登录或登录状态过期
2. 网络连接问题
3. 服务器错误

建议:
1. 先登录系统
2. 检查网络连接
3. 查看控制台详细错误信息`;

					uni.showToast({
						title: '测试失败',
						icon: 'error'
					});
				} finally {
					this.loading = false;
				}
			},

			// 导航栏返回
			onNavbarBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #f5f7fa;
		min-height: 100vh;
	}

	.scroll-content {
		height: calc(100vh - 88px);
		padding: 20rpx;
	}

	.test-section {
		text-align: center;
		margin-bottom: 40rpx;

		.section-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #2E3A59;
			display: block;
			margin-bottom: 16rpx;
		}

		.section-desc {
			font-size: 28rpx;
			color: #8F9BB3;
			display: block;
		}
	}

	.test-card {
		background: white;
		border-radius: 16rpx;
		padding: 32rpx;
		margin-bottom: 32rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

		.test-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 24rpx;

			.test-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #2E3A59;
			}

			.test-btn {
				background: #007aff;
				color: white;
				border: none;
				padding: 16rpx 32rpx;
				border-radius: 8rpx;
				font-size: 28rpx;

				&[disabled] {
					background: #ccc;
				}
			}
		}

		.result-box {
			background: #f8f9fa;
			border-radius: 8rpx;
			padding: 24rpx;

			.result-title {
				font-size: 28rpx;
				font-weight: 600;
				color: #333;
				display: block;
				margin-bottom: 16rpx;
			}

			.result-content {
				font-family: monospace;
				font-size: 24rpx;
				color: #666;
				white-space: pre-wrap;
				word-break: break-all;
				display: block;
			}
		}
	}

	.stats-section {
		margin-bottom: 32rpx;

		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #2E3A59;
			display: block;
			margin-bottom: 24rpx;
		}

		.stats-grid {
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: 24rpx;

			.stat-card {
				background: white;
				border-radius: 16rpx;
				padding: 32rpx 24rpx;
				text-align: center;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

				&.today {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
				}

				.stat-label {
					font-size: 24rpx;
					opacity: 0.8;
					display: block;
					margin-bottom: 12rpx;
				}

				.stat-value {
					font-size: 36rpx;
					font-weight: bold;
					display: block;
				}
			}
		}
	}

	.info-section, .expected-section {
		background: white;
		border-radius: 16rpx;
		padding: 32rpx;
		margin-bottom: 32rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #2E3A59;
			display: block;
			margin-bottom: 24rpx;
		}

		.info-item {
			display: flex;
			margin-bottom: 16rpx;

			.info-label {
				font-size: 28rpx;
				color: #8F9BB3;
				width: 200rpx;
			}

			.info-value {
				font-size: 28rpx;
				color: #2E3A59;
				flex: 1;
			}
		}

		.expected-code {
			font-family: monospace;
			font-size: 24rpx;
			color: #666;
			background: #f8f9fa;
			padding: 24rpx;
			border-radius: 8rpx;
			white-space: pre-wrap;
			display: block;
		}
	}
</style>
