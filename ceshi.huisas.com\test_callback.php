<?php
// 测试回调处理
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== 回调测试开始 ===\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n";

// 模拟回调数据
$_POST = [
    'orderNo' => '2025072605062650741',
    'orderSub' => 'test order',
    'orderAmount' => '1.00',
    'orderStatus' => '1',
    'payTime' => date('Y-m-d H:i:s'),
    'sign' => 'test_sign'
];

echo "模拟POST数据:\n";
print_r($_POST);

// 包含系统文件
try {
    include("./includes/common.php");
    echo "系统文件加载成功\n";
    
    echo "siteurl: " . $siteurl . "\n";
    echo "localurl: " . $conf['localurl'] . "\n";
    
    // 检查插件
    $plugin_file = PLUGIN_ROOT . 'easypay/easypay_plugin.php';
    if (file_exists($plugin_file)) {
        echo "easypay插件文件存在\n";
        
        // 模拟回调处理
        define('TRADE_NO', '2025072605062650741');
        define('PAY_ROOT', ROOT);
        
        include($plugin_file);
        
        $easypay = new easypay_plugin();
        echo "easypay插件实例化成功\n";
        
        // 测试notify方法
        if (method_exists($easypay, 'notify')) {
            echo "notify方法存在，开始测试...\n";
            $result = $easypay->notify();
            echo "notify返回结果: " . var_export($result, true) . "\n";
        } else {
            echo "notify方法不存在\n";
        }
        
    } else {
        echo "easypay插件文件不存在: " . $plugin_file . "\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈: " . $e->getTraceAsString() . "\n";
}

echo "=== 回调测试结束 ===\n";
?>
