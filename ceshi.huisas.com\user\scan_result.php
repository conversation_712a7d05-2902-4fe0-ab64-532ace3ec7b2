<?php
/**
 * 反扫支付结果页面
 */
include("../includes/common.php");

$trade_no = trim($_GET['trade_no']);
if(empty($trade_no)) {
    sysmsg('订单号不能为空');
}

$order = $DB->getRow("SELECT * FROM pre_order WHERE trade_no='{$trade_no}' LIMIT 1");
if(!$order) {
    sysmsg('订单不存在');
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付结果</title>
    <link href="//cdn.staticfile.org/twitter-bootstrap/3.4.1/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .result-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
        }
        .success-icon {
            color: #5cb85c;
            font-size: 48px;
            margin-bottom: 20px;
        }
        .fail-icon {
            color: #d9534f;
            font-size: 48px;
            margin-bottom: 20px;
        }
        .order-info {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .order-info .row {
            margin-bottom: 10px;
        }
        .order-info .row:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="result-container">
            <?php if($order['status'] == 1): ?>
                <div class="success-icon">
                    <i class="glyphicon glyphicon-ok-circle"></i>
                </div>
                <h3 class="text-success">支付成功！</h3>
                <div class="order-info">
                    <div class="row">
                        <div class="col-xs-4 text-right"><strong>订单号：</strong></div>
                        <div class="col-xs-8 text-left"><?php echo $order['trade_no']; ?></div>
                    </div>
                    <div class="row">
                        <div class="col-xs-4 text-right"><strong>商品名称：</strong></div>
                        <div class="col-xs-8 text-left"><?php echo $order['name']; ?></div>
                    </div>
                    <div class="row">
                        <div class="col-xs-4 text-right"><strong>支付金额：</strong></div>
                        <div class="col-xs-8 text-left text-success"><strong>￥<?php echo $order['money']; ?></strong></div>
                    </div>
                    <div class="row">
                        <div class="col-xs-4 text-right"><strong>支付时间：</strong></div>
                        <div class="col-xs-8 text-left"><?php echo $order['endtime']; ?></div>
                    </div>
                </div>
                <button type="button" class="btn btn-success" onclick="window.close();">关闭窗口</button>
            <?php else: ?>
                <div class="fail-icon">
                    <i class="glyphicon glyphicon-remove-circle"></i>
                </div>
                <h3 class="text-danger">支付失败或处理中...</h3>
                <div class="order-info">
                    <div class="row">
                        <div class="col-xs-4 text-right"><strong>订单号：</strong></div>
                        <div class="col-xs-8 text-left"><?php echo $order['trade_no']; ?></div>
                    </div>
                    <div class="row">
                        <div class="col-xs-4 text-right"><strong>商品名称：</strong></div>
                        <div class="col-xs-8 text-left"><?php echo $order['name']; ?></div>
                    </div>
                    <div class="row">
                        <div class="col-xs-4 text-right"><strong>支付金额：</strong></div>
                        <div class="col-xs-8 text-left">￥<?php echo $order['money']; ?></div>
                    </div>
                    <div class="row">
                        <div class="col-xs-4 text-right"><strong>创建时间：</strong></div>
                        <div class="col-xs-8 text-left"><?php echo $order['addtime']; ?></div>
                    </div>
                </div>
                <button type="button" class="btn btn-default" onclick="history.back();">返回重试</button>
                <button type="button" class="btn btn-primary" onclick="location.reload();">刷新状态</button>
            <?php endif; ?>
        </div>
    </div>

    <script src="//cdn.staticfile.org/jquery/3.4.1/jquery.min.js"></script>
    <script src="//cdn.staticfile.org/twitter-bootstrap/3.4.1/js/bootstrap.min.js"></script>
</body>
</html>
