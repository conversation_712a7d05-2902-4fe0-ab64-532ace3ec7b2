<?php
include("../includes/common.php");

// 设置CORS头部
$allowed_origins = [
    'http://localhost:5173',
    'http://127.0.0.1:5173',
    'http://ceshi.huisas.com'
];

$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
if (in_array($origin, $allowed_origins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: http://ceshi.huisas.com');
}

header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 修改跨域检查逻辑
function checkRefererHostWithCORS(){
    global $allowed_origins;

    // 获取请求来源
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
    $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

    // 如果是直接访问（没有 Origin 和 Referer），允许通过
    if(!$origin && !$referer) return true;

    $http_host = $_SERVER['HTTP_HOST'];
    if(strpos($http_host,':'))$http_host = substr($http_host, 0, strpos($http_host, ':'));

    // 检查 Origin
    if($origin) {
        // 检查是否在允许的跨域列表中
        if(in_array($origin, $allowed_origins)) return true;

        // 检查是否是同域名
        $origin_parts = parse_url($origin);
        if(isset($origin_parts['host']) && $origin_parts['host'] === $http_host) return true;
    }

    // 检查 Referer
    if($referer) {
        $referer_parts = parse_url($referer);
        if(isset($referer_parts['host'])) {
            // 检查是否是同域名
            if($referer_parts['host'] === $http_host) return true;

            // 构建 referer origin
            $referer_origin = $referer_parts['scheme'] . '://' . $referer_parts['host'];
            if(isset($referer_parts['port'])) {
                $referer_origin .= ':' . $referer_parts['port'];
            }

            // 检查是否在允许的跨域列表中
            if(in_array($referer_origin, $allowed_origins)) return true;
        }
    }

    return false;
}

if($islogin2==1){}else exit('{"code":-3,"msg":"No Login"}');
$act=isset($_GET['act'])?daddslashes($_GET['act']):null;

if(!checkRefererHostWithCORS())exit('{"code":403,"msg":"跨域请求被拒绝"}');

@header('Content-Type: application/json; charset=UTF-8');

switch($act){
case 'testpay2':
	$money=trim($_POST['money']);
	$name=trim($_POST['name']);
	$typeid=intval($_POST['typeid']);
	if($money<=0 || !is_numeric($money) || !$name || !preg_match('/^[0-9.]+$/', $money))exit('{"code":-1,"msg":"数据有误"}');
	if($conf['pay_maxmoney']>0 && $money>$conf['pay_maxmoney'])exit('{"code":-1,"msg":"最大支付金额是'.$conf['pay_maxmoney'].'元"}');
	if($conf['pay_minmoney']>0 && $money<$conf['pay_minmoney'])exit('{"code":-1,"msg":"最小支付金额是'.$conf['pay_minmoney'].'元"}');

	$trade_no=date("YmdHis").rand(11111,99999);
	$return_url=$siteurl.'user/test2.php?ok=1&trade_no='.$trade_no;
	$domain=getdomain($return_url);
	// 🔍 使用当前登录用户的真实商户ID，而不是测试商户ID
	global $userrow;
	$real_uid = $userrow['uid']; // 当前登录的商户ID

	if(!$DB->exec("INSERT INTO `pre_order` (`trade_no`,`out_trade_no`,`uid`,`tid`,`addtime`,`name`,`money`,`type`,`channel`,`notify_url`,`return_url`,`domain`,`ip`,`status`) VALUES (:trade_no, :out_trade_no, :uid, 3, NOW(), :name, :money, :type, :channel, :notify_url, :return_url, :domain, :clientip, 0)", [':trade_no'=>$trade_no, ':out_trade_no'=>$trade_no, ':uid'=>$real_uid, ':name'=>$name, ':money'=>$money, ':type'=>$typeid, ':channel'=>$typeid, ':notify_url'=>$return_url, ':return_url'=>$return_url, ':domain'=>$domain, ':clientip'=>$clientip]))exit('{"code":-1,"msg":"创建订单失败，请返回重试！"}');
	$result = ['code'=>0, 'msg'=>'succ', 'url'=>'../submit2.php?typeid='.$typeid.'&trade_no='.$trade_no];
	exit(json_encode($result));
break;
case 'generate_qrcode':
	// 新增：直接生成二维码数据的接口
	$money=trim($_POST['money']);
	$name=trim($_POST['name']);
	$typeid=intval($_POST['typeid']);
	if($money<=0 || !is_numeric($money) || !$name || !preg_match('/^[0-9.]+$/', $money))exit('{"code":-1,"msg":"数据有误"}');
	if($conf['pay_maxmoney']>0 && $money>$conf['pay_maxmoney'])exit('{"code":-1,"msg":"最大支付金额是'.$conf['pay_maxmoney'].'元"}');
	if($conf['pay_minmoney']>0 && $money<$conf['pay_minmoney'])exit('{"code":-1,"msg":"最小支付金额是'.$conf['pay_minmoney'].'元"}');

	// 验证支付方式是否存在
	$paytype = $DB->getRow("SELECT id,name,status FROM pre_type WHERE id=:typeid LIMIT 1", [':typeid'=>$typeid]);
	if(!$paytype || $paytype['status']==0)exit('{"code":-1,"msg":"支付方式不存在或已禁用"}');

	try {
		// 创建订单
		$trade_no=date("YmdHis").rand(11111,99999);
		$return_url=$siteurl.'user/test2.php?ok=1&trade_no='.$trade_no;
		$domain=getdomain($return_url);

		// 🔍 使用当前登录用户的真实商户ID，而不是测试商户ID
		global $userrow;
		$real_uid = $userrow['uid']; // 当前登录的商户ID

		if(!$DB->exec("INSERT INTO `pre_order` (`trade_no`,`out_trade_no`,`uid`,`tid`,`addtime`,`name`,`money`,`type`,`channel`,`notify_url`,`return_url`,`domain`,`ip`,`status`) VALUES (:trade_no, :out_trade_no, :uid, 3, NOW(), :name, :money, :type, :channel, :notify_url, :return_url, :domain, :clientip, 0)", [':trade_no'=>$trade_no, ':out_trade_no'=>$trade_no, ':uid'=>$real_uid, ':name'=>$name, ':money'=>$money, ':type'=>$typeid, ':channel'=>$typeid, ':notify_url'=>$return_url, ':return_url'=>$return_url, ':domain'=>$domain, ':clientip'=>$clientip])) {
			exit('{"code":-1,"msg":"创建订单失败，请返回重试！"}');
		}

		// 模拟支付处理逻辑，生成支付URL
		// 使用authcode加密商户ID
		$encrypted_merchant = authcode($conf['test_pay_uid'], 'ENCODE', SYS_KEY);
		$payment_url = $siteurl.'paypage/?merchant='.urlencode($encrypted_merchant).'&order_id='.urlencode($trade_no).'&money='.urlencode($money).'&name='.urlencode($name).'&type='.urlencode($paytype['name']);

		// 生成二维码（使用第三方二维码API）
		$qrcode_api = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data='.urlencode($payment_url);

		$result = [
			'code' => 0,
			'msg' => 'success',
			'data' => [
				'trade_no' => $trade_no,
				'qrcode_url' => $qrcode_api,
				'payment_url' => $payment_url,
				'money' => $money,
				'name' => $name,
				'expire_time' => time() + 1800 // 30分钟过期
			]
		];
		exit(json_encode($result));

	} catch (Exception $e) {
		exit('{"code":-1,"msg":"生成二维码失败：'.$e->getMessage().'"}');
	}
break;
case 'generate_qrcode_fast':
	// 直接调用支付插件alipay方法生成真正的支付宝二维码
	$money=trim($_POST['money']);
	$name=trim($_POST['name']);
	$typeid=intval($_POST['typeid']);
	if($money<=0 || !is_numeric($money) || !$name || !preg_match('/^[0-9.]+$/', $money))exit('{"code":-1,"msg":"数据有误"}');

	// 验证支付方式是否存在
	$paytype = $DB->getRow("SELECT id,name,status FROM pre_type WHERE id=:typeid LIMIT 1", [':typeid'=>$typeid]);
	if(!$paytype || $paytype['status']==0)exit('{"code":-1,"msg":"支付方式不存在或已禁用"}');
	if($conf['pay_maxmoney']>0 && $money>$conf['pay_maxmoney'])exit('{"code":-1,"msg":"最大支付金额是'.$conf['pay_maxmoney'].'元"}');
	if($conf['pay_minmoney']>0 && $money<$conf['pay_minmoney'])exit('{"code":-1,"msg":"最小支付金额是'.$conf['pay_minmoney'].'元"}');

	try {
		// 生成订单号（必须是纯数字，符合Plugin.php的正则验证）
		$trade_no=date("YmdHis").rand(100000,999999);
		$return_url=$siteurl.'user/test2.php?ok=1&trade_no='.$trade_no;
		$domain=getdomain($return_url);

		// 🔍 使用真实的登录商户ID，而不是测试商户ID
		// 在common.php中，登录用户信息存储在全局变量中
		global $userrow;
		$real_uid = $userrow['uid']; // 当前登录的商户ID
		$debug_steps[] = "使用真实商户ID: " . $real_uid . " (而不是测试商户ID: " . $conf['test_pay_uid'] . ")";

		// 创建订单
		$insert_result = $DB->exec("INSERT INTO `pre_order` (`trade_no`,`out_trade_no`,`uid`,`tid`,`addtime`,`name`,`money`,`type`,`channel`,`notify_url`,`return_url`,`domain`,`ip`,`status`) VALUES (:trade_no, :out_trade_no, :uid, 3, NOW(), :name, :money, :type, :channel, :notify_url, :return_url, :domain, :clientip, 0)", [
			':trade_no' => $trade_no,
			':out_trade_no' => $trade_no,
			':uid' => $real_uid,  // 使用真实商户ID
			':name' => $name,
			':money' => $money,
			':type' => $typeid,
			':channel' => $typeid,
			':notify_url' => $return_url,
			':return_url' => $return_url,
			':domain' => $domain,
			':clientip' => $clientip
		]);

		if(!$insert_result) {
			exit('{"code":-1,"msg":"创建订单失败，请返回重试！"}');
		}

		// 尝试直接调用支付插件生成支付宝链接
		$direct_alipay_url = null;
		$use_direct_alipay = false;
		$debug_steps = [];
		$debug_steps[] = "订单创建成功: " . $trade_no;

		try {
			// 🔍 重要：使用真实的登录商户ID，而不是测试商户ID
			// $real_uid 已经在上面定义过了，直接使用
			$debug_steps[] = "使用真实商户ID: " . $real_uid . " (而不是测试商户ID: " . $conf['test_pay_uid'] . ")";

			// 获取商户信息
			$userrow = $DB->getRow("SELECT `uid`,`gid`,`money`,`mode`,`channelinfo`,`ordername` FROM `pre_user` WHERE `uid`=:uid LIMIT 1", [':uid' => $real_uid]);
			if(!$userrow) {
				throw new Exception("商户不存在，uid: " . $real_uid);
			}
			$debug_steps[] = "获取商户信息成功，uid: " . $userrow['uid'] . ", gid: " . $userrow['gid'];

			// 获取组配置
			$groupconfig = getGroupConfig($userrow['gid']);
			$conf_merged = array_merge($conf, $groupconfig);
			$debug_steps[] = "获取组配置成功";

			// 获取支付通道
			$submitData = \lib\Channel::submit2($typeid, $userrow['uid'], $userrow['gid'], $money);
			if(!$submitData) {
				throw new Exception("获取支付通道失败");
			}
			$debug_steps[] = "获取支付通道成功: " . $submitData['plugin'] . " (通道ID: " . $submitData['channel'] . ")";

			// 计算实际支付金额
			if($userrow['mode']==1){ //订单加费模式
				$realmoney = round($money*(100+100-$submitData['rate'])/100,2);
				$getmoney = $money;
			}else{
				$realmoney = $money;
				$getmoney = round($money*$submitData['rate']/100,2);
			}

			// 更新订单信息（使用和submit2.php相同的表名）
			$DB->update('order', [
				'type'=>$submitData['typeid'],
				'channel'=>$submitData['channel'],
				'subchannel'=>$submitData['subchannel'],
				'realmoney'=>$realmoney,
				'getmoney'=>$getmoney
			], ['trade_no'=>$trade_no]);
			$debug_steps[] = "更新订单信息成功";

			// 构建完整的订单对象（完全模拟submit2.php的逻辑）
			$order_full = [
				'trade_no' => $trade_no,
				'out_trade_no' => $trade_no,
				'uid' => $real_uid,  // 使用真实商户ID
				'money' => $money,
				'realmoney' => $realmoney,
				'getmoney' => $getmoney,
				'type' => $submitData['typeid'],
				'channel' => $submitData['channel'],
				'subchannel' => $submitData['subchannel'],
				'typename' => $submitData['typename'],
				'plugin' => $submitData['plugin'],
				'name' => $name,
				'addtime' => date('Y-m-d H:i:s'),
				'status' => 0,
				'notify_url' => $return_url,
				'return_url' => $return_url,
				'domain' => $domain,
				'ip' => $clientip,
				'tid' => 3
			];

			// 添加profits字段（模拟submit2.php第112行）
			$order_full['profits'] = \lib\Payment::updateOrderProfits($order_full, $submitData['plugin']);

			// 设置全局变量
			$GLOBALS['order'] = $order_full;
			$channel_info = \lib\Channel::info($submitData['channel']);
			$GLOBALS['channel'] = $channel_info;
			if(!defined('TRADE_NO')) {
				define('TRADE_NO', $trade_no);
			}
			$debug_steps[] = "设置全局变量成功";
			$debug_steps[] = "通道配置信息: " . json_encode($channel_info);

			// 🎯 关键修改：优先调用插件的alipay方法获取直接支付宝链接
			try {
				$plugin_result = \lib\Plugin::loadClass($submitData['plugin'], 'alipay', $trade_no);
				$debug_steps[] = "✅ 调用支付插件alipay方法成功: " . json_encode($plugin_result);
			} catch (Exception $e) {
				$debug_steps[] = "调用alipay方法失败，回退到submit方法: " . $e->getMessage();
				// 如果alipay方法失败，回退到submit方法
				$plugin_result = \lib\Plugin::loadForSubmit($submitData['plugin'], $trade_no);
				$debug_steps[] = "调用支付插件submit方法: " . json_encode($plugin_result);
			}

			// 检查插件返回结果
			if($plugin_result && isset($plugin_result['type'])){
				if($plugin_result['type'] == 'qrcode' && !empty($plugin_result['url'])){
					// 🎉 成功获取直接支付宝二维码链接！
					$direct_alipay_url = $plugin_result['url'];
					$use_direct_alipay = true;
					$debug_steps[] = "🎉 成功获取真正的支付宝二维码链接: " . $direct_alipay_url;
				} elseif($plugin_result['type'] == 'jump' && !empty($plugin_result['url'])){
					// 需要跳转处理
					$debug_steps[] = "插件返回跳转URL，使用传统方式";
				} else {
					$debug_steps[] = "插件返回未知类型: " . $plugin_result['type'];
				}
			} else {
				$debug_steps[] = "插件未返回有效结果";
			}

		} catch (Exception $e) {
			$debug_steps[] = "异常: " . $e->getMessage();
		}

		// 生成最终的支付URL和二维码
		if($use_direct_alipay && $direct_alipay_url) {
			// 🎯 使用直接支付宝链接
			$payment_url = $direct_alipay_url;
			$qrcode_api = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&format=png&ecc=L&data='.urlencode($direct_alipay_url);
		} else {
			// 降级到传统方式
			$encrypted_merchant = authcode($real_uid, 'ENCODE', SYS_KEY);  // 使用真实商户ID
			$payment_url = $siteurl.'paypage/?merchant='.urlencode($encrypted_merchant).'&order_id='.urlencode($trade_no).'&money='.urlencode($money).'&name='.urlencode($name).'&type='.urlencode($paytype['name']);
			$qrcode_api = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&format=png&ecc=L&data='.urlencode($payment_url);
		}

		// 返回结果
		$result = [
			'code' => 0,
			'msg' => 'success',
			'data' => [
				'trade_no' => $trade_no,
				'qrcode_url' => $qrcode_api,
				'payment_url' => $payment_url,
				'money' => $money,
				'name' => $name,
				'expire_time' => time() + 1800,
				'generated_at' => time(),
				'fast_mode' => true,
				'direct_alipay' => $use_direct_alipay,
				'alipay_url' => $use_direct_alipay ? $direct_alipay_url : null,
				'debug_info' => [
					'found_alipay_link' => $use_direct_alipay,
					'debug_steps' => $debug_steps,
					'submitData' => isset($submitData) ? $submitData : null,
					'plugin_result' => isset($plugin_result) ? $plugin_result : null,
					'test_pay_uid' => $conf['test_pay_uid'],
					'real_uid' => isset($real_uid) ? $real_uid : 'not_set'
				]
			]
		];
		exit(json_encode($result));

	} catch (Exception $e) {
		exit('{"code":-1,"msg":"生成失败：'.$e->getMessage().'"}');
	}
break;
default:
	exit('{"code":-4,"msg":"No Act"}');
break;
}