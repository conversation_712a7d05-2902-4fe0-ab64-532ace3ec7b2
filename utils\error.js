// 错误类型枚举
export const ErrorType = {
  NETWORK_ERROR: 'NETWORK_ERROR',      // 网络错误
  HTTP_ERROR: 'HTTP_ERROR',            // HTTP错误
  BUSINESS_ERROR: 'BUSINESS_ERROR',    // 业务错误
  VALIDATION_ERROR: 'VALIDATION_ERROR', // 验证错误
  SYSTEM_ERROR: 'SYSTEM_ERROR'         // 系统错误
}

// 错误处理类
export class AppError extends Error {
  constructor(type, message, code, data = null) {
    super(message)
    this.type = type
    this.code = code
    this.data = data
    this.name = 'AppError'
  }
}

// 错误处理中心
export class ErrorHandler {
  static handle(error) {
    // 如果是自定义错误
    if (error instanceof AppError) {
      return this.handleAppError(error)
    }
    
    // 如果是网络错误
    if (error.errMsg && error.errMsg.includes('request:fail')) {
      return this.handleNetworkError(error)
    }
    
    // 其他未知错误
    return this.handleUnknownError(error)
  }
  
  static handleAppError(error) {
    const { type, message, code, data } = error
    
    switch (type) {
      case ErrorType.NETWORK_ERROR:
        uni.showToast({
          title: '网络连接失败，请检查网络设置',
          icon: 'none'
        })
        break
        
      case ErrorType.HTTP_ERROR:
        uni.showToast({
          title: `请求错误: ${code}`,
          icon: 'none'
        })
        break
        
      case ErrorType.BUSINESS_ERROR:
        uni.showToast({
          title: message || '操作失败',
          icon: 'none'
        })
        break
        
      case ErrorType.VALIDATION_ERROR:
        uni.showToast({
          title: message || '数据验证失败',
          icon: 'none'
        })
        break
        
      case ErrorType.SYSTEM_ERROR:
        uni.showToast({
          title: '系统错误，请稍后重试',
          icon: 'none'
        })
        break
    }
    
    // 记录错误日志
    this.logError(error)
  }
  
  static handleNetworkError(error) {
    uni.showToast({
      title: '网络连接失败，请检查网络设置',
      icon: 'none'
    })
    this.logError(error)
  }
  
  static handleUnknownError(error) {
    uni.showToast({
      title: '系统错误，请稍后重试',
      icon: 'none'
    })
    this.logError(error)
  }
  
  static logError(error) {
    // 这里可以添加错误上报逻辑
    console.error('Error:', error)
    
    // 示例：上报错误到服务器
    // uni.request({
    //   url: '/api/error/log',
    //   method: 'POST',
    //   data: {
    //     error: error.toString(),
    //     stack: error.stack,
    //     timestamp: Date.now()
    //   }
    // })
  }
}

// 错误处理工具函数
export const handleError = (error) => {
  ErrorHandler.handle(error)
}

// 创建错误
export const createError = (type, message, code, data = null) => {
  return new AppError(type, message, code, data)
} 