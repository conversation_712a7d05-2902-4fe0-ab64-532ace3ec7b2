<?php
/**
 * 真实支付回调诊断脚本
 * 用于检查为什么真实支付没有触发WebSocket通知
 */

require_once './includes/common.php';

echo "<h1>🔍 真实支付回调诊断</h1>";

// 1. 检查订单状态
$trade_no = '2025072717220140'; // 你的真实支付订单号
echo "<h2>📋 订单信息检查</h2>";

$order = $DB->getRow("SELECT * FROM pre_order WHERE trade_no=? LIMIT 1", [$trade_no]);
if ($order) {
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>✅ 订单找到</h3>";
    echo "<p><strong>订单号:</strong> {$order['trade_no']}</p>";
    echo "<p><strong>状态:</strong> {$order['status']} " . getStatusText($order['status']) . "</p>";
    echo "<p><strong>金额:</strong> {$order['realmoney']}元</p>";
    echo "<p><strong>商户ID:</strong> {$order['uid']}</p>";
    echo "<p><strong>支付类型:</strong> {$order['type']}</p>";
    echo "<p><strong>创建时间:</strong> {$order['addtime']}</p>";
    echo "<p><strong>完成时间:</strong> " . ($order['endtime'] ?? '未完成') . "</p>";
    echo "<p><strong>API订单号:</strong> " . ($order['api_trade_no'] ?? '无') . "</p>";
    echo "</div>";
} else {
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>❌ 订单未找到</h3>";
    echo "<p>订单号 {$trade_no} 在数据库中不存在</p>";
    echo "</div>";
    exit;
}

// 2. 检查WebSocket通知函数
echo "<h2>🔧 WebSocket功能检查</h2>";
echo "<div style='background: #f8f8f8; padding: 15px; border-radius: 8px; margin: 10px 0;'>";

$websocketFile = SYSTEM_ROOT . "websocket_notify_workerman.php";
echo "<p><strong>WebSocket文件:</strong> " . ($websocketFile) . "</p>";
echo "<p><strong>文件存在:</strong> " . (file_exists($websocketFile) ? '✅ 是' : '❌ 否') . "</p>";

if (file_exists($websocketFile)) {
    include_once $websocketFile;
}

echo "<p><strong>函数存在:</strong> " . (function_exists('sendWebSocketPaymentNotification') ? '✅ 是' : '❌ 否') . "</p>";
echo "</div>";

// 3. 检查支付日志
echo "<h2>📝 支付日志检查</h2>";
$logFile = SYSTEM_ROOT . 'logs/payment_debug_' . date('Y-m-d') . '.log';
echo "<p><strong>日志文件:</strong> {$logFile}</p>";
echo "<p><strong>文件存在:</strong> " . (file_exists($logFile) ? '✅ 是' : '❌ 否') . "</p>";

if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    $hasOrderLog = strpos($logContent, $trade_no) !== false;
    echo "<p><strong>包含订单日志:</strong> " . ($hasOrderLog ? '✅ 是' : '❌ 否') . "</p>";
    
    if ($hasOrderLog) {
        echo "<h3>📄 相关日志内容</h3>";
        $lines = explode("\n", $logContent);
        $relevantLines = array_filter($lines, function($line) use ($trade_no) {
            return strpos($line, $trade_no) !== false;
        });
        
        echo "<div style='background: #000; color: #00ff00; padding: 15px; border-radius: 8px; font-family: monospace; max-height: 400px; overflow-y: auto;'>";
        foreach ($relevantLines as $line) {
            echo htmlspecialchars($line) . "<br>";
        }
        echo "</div>";
    }
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>⚠️ 没有支付日志</h3>";
    echo "<p>这说明支付回调可能没有被正确处理</p>";
    echo "</div>";
}

// 4. 检查WebSocket通知日志
echo "<h2>🔔 WebSocket通知日志检查</h2>";
$wsLogFile = ROOT . 'logs/websocket_integration.log';
echo "<p><strong>WebSocket日志文件:</strong> {$wsLogFile}</p>";
echo "<p><strong>文件存在:</strong> " . (file_exists($wsLogFile) ? '✅ 是' : '❌ 否') . "</p>";

if (file_exists($wsLogFile)) {
    $wsLogContent = file_get_contents($wsLogFile);
    $hasWsOrderLog = strpos($wsLogContent, $trade_no) !== false;
    echo "<p><strong>包含订单通知:</strong> " . ($hasWsOrderLog ? '✅ 是' : '❌ 否') . "</p>";
    
    if (!$hasWsOrderLog) {
        echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h3>❌ 关键问题发现</h3>";
        echo "<p>订单 {$trade_no} 没有WebSocket通知记录，说明支付回调没有触发WebSocket通知</p>";
        echo "</div>";
    }
}

// 5. 手动测试WebSocket通知
echo "<h2>🧪 手动测试WebSocket通知</h2>";
if (function_exists('sendWebSocketPaymentNotification') && $order) {
    echo "<div style='background: #e6f3ff; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>🔄 正在手动发送WebSocket通知...</h3>";
    
    $testData = [
        'trade_no' => $order['trade_no'],
        'uid' => $order['uid'],
        'money' => $order['realmoney'],
        'type' => $order['type'],
        'typename' => '手动测试',
        'addtime' => date('Y-m-d H:i:s'),
        'api_trade_no' => $order['api_trade_no'] ?? '',
        'buyer' => $order['buyer'] ?? '',
        'status' => 'success'
    ];
    
    try {
        $result = sendWebSocketPaymentNotification($testData);
        if ($result) {
            echo "<p>✅ 手动WebSocket通知发送成功</p>";
            echo "<p>这说明WebSocket功能正常，问题在于支付回调没有调用通知函数</p>";
        } else {
            echo "<p>❌ 手动WebSocket通知发送失败</p>";
            echo "<p>WebSocket服务可能有问题</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ 手动WebSocket通知异常: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
}

// 6. 检查支付插件回调
echo "<h2>🔌 支付插件回调检查</h2>";
echo "<div style='background: #f8f8f8; padding: 15px; border-radius: 8px; margin: 10px 0;'>";

// 检查订单使用的支付插件
if ($order) {
    $channel = $DB->getRow("SELECT * FROM pre_channel WHERE id=? LIMIT 1", [$order['channel']]);
    if ($channel) {
        echo "<p><strong>支付通道:</strong> {$channel['name']}</p>";
        echo "<p><strong>插件类型:</strong> {$channel['plugin']}</p>";
        
        $pluginFile = ROOT . "plugins/{$channel['plugin']}/{$channel['plugin']}_plugin.php";
        echo "<p><strong>插件文件:</strong> {$pluginFile}</p>";
        echo "<p><strong>插件存在:</strong> " . (file_exists($pluginFile) ? '✅ 是' : '❌ 否') . "</p>";
        
        if (file_exists($pluginFile)) {
            $pluginContent = file_get_contents($pluginFile);
            $hasNotifyMethod = strpos($pluginContent, 'function notify') !== false || strpos($pluginContent, 'static public function notify') !== false;
            echo "<p><strong>包含notify方法:</strong> " . ($hasNotifyMethod ? '✅ 是' : '❌ 否') . "</p>";
            
            $hasProcessNotify = strpos($pluginContent, 'processNotify') !== false;
            echo "<p><strong>调用processNotify:</strong> " . ($hasProcessNotify ? '✅ 是' : '❌ 否') . "</p>";
        }
    }
}
echo "</div>";

// 辅助函数
function getStatusText($status) {
    switch ($status) {
        case 0: return "(待支付)";
        case 1: return "(已支付)";
        case 2: return "(已退款)";
        case 3: return "(已关闭)";
        case 4: return "(处理中)";
        default: return "(未知状态)";
    }
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
p { margin: 8px 0; }
code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
</style>
