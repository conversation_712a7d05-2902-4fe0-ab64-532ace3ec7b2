<template>
  <view class="voice-settings">
    <!-- 导航栏 -->
    <view class="navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="nav-content">
        <view class="nav-left" @click="goBack">
          <text class="nav-icon">‹</text>
        </view>
        <view class="nav-title">语音播报设置</view>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 设置内容 -->
    <view class="settings-content">
      <!-- WebSocket状态 -->
      <view class="setting-group">
        <view class="group-title">
          <text class="title-icon">🌐</text>
          <text class="title-text">连接状态</text>
        </view>

        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-label">WebSocket连接</text>
            <text class="setting-desc">{{ websocketStatusText }}</text>
          </view>
          <view class="status-indicator" :class="websocketStatusClass">
            <text class="status-dot"></text>
          </view>
        </view>

        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-label">今日播报</text>
            <text class="setting-desc">{{ todayStatsText }}</text>
          </view>
        </view>
      </view>

      <!-- 基础设置 -->
      <view class="setting-group">
        <view class="group-title">
          <text class="title-icon">🔊</text>
          <text class="title-text">基础设置</text>
        </view>

        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-label">语音播报</text>
            <text class="setting-desc">开启后收到新订单时自动语音提醒</text>
          </view>
          <switch
            :checked="voiceEnabled"
            @change="toggleVoice"
            color="#5145F7"
          />
        </view>
        
        <view class="setting-item" v-if="voiceEnabled">
          <view class="setting-left">
            <text class="setting-label">播报音量</text>
            <text class="setting-desc">当前音量：{{ Math.round(volume * 100) }}%</text>
          </view>
          <view class="setting-right">
            <slider 
              :value="volume * 100" 
              @change="setVolume"
              min="0" 
              max="100"
              show-value
              activeColor="#5145F7"
              backgroundColor="#e5e5e5"
              block-color="#5145F7"
              block-size="20"
            />
          </view>
        </view>
      </view>
      
      <!-- 高级设置 -->
      <view class="setting-group" v-if="voiceEnabled">
        <view class="group-title">
          <text class="title-icon">⚙️</text>
          <text class="title-text">高级设置</text>
        </view>
        
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-label">播报模式</text>
            <text class="setting-desc">{{ currentTemplate.description }}</text>
          </view>
          <picker 
            :value="templateIndex" 
            :range="templateNames"
            @change="setTemplate"
          >
            <view class="picker-text">
              {{ currentTemplate.name }}
              <text class="picker-arrow">›</text>
            </view>
          </picker>
        </view>
        
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-label">大额提醒</text>
            <text class="setting-desc">1000元以上订单特殊提醒</text>
          </view>
          <switch 
            :checked="largeAmountAlert" 
            @change="toggleLargeAlert"
            color="#5145F7"
          />
        </view>
      </view>
      
      <!-- 播报预览 -->
      <view class="setting-group" v-if="voiceEnabled">
        <view class="group-title">
          <text class="title-icon">🎵</text>
          <text class="title-text">播报预览</text>
        </view>
        
        <view class="preview-section">
          <view class="preview-text">
            {{ previewText }}
          </view>
          <button 
            class="test-btn" 
            @click="testVoice"
            :disabled="isTesting"
          >
            <text v-if="!isTesting">🎤 测试播报</text>
            <text v-else>播报中...</text>
          </button>
        </view>
      </view>
      
      <!-- 监听状态 -->
      <view class="setting-group">
        <view class="group-title">
          <text class="title-icon">📊</text>
          <text class="title-text">监听状态</text>
        </view>
        
        <view class="status-section">
          <view class="status-item">
            <text class="status-label">当前状态</text>
            <text class="status-value" :class="{ active: voiceStatus.isMonitoring }">
              {{ voiceStatus.isMonitoring ? '监听中' : '未监听' }}
            </text>
          </view>
          
          <view class="status-item" v-if="voiceStatus.currentPage">
            <text class="status-label">监听页面</text>
            <text class="status-value">{{ getPageName(voiceStatus.currentPage) }}</text>
          </view>
          
          <view class="status-item" v-if="voiceStatus.currentStrategy">
            <text class="status-label">监听频率</text>
            <text class="status-value">{{ voiceStatus.currentStrategy.interval / 1000 }}秒/次</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import voiceManager from '@/utils/voiceManager.js'
import globalWebSocketService from '@/utils/globalWebSocketService.js'

export default {
  data() {
    return {
      statusBarHeight: 20,
      voiceEnabled: true,
      volume: 0.8,
      largeAmountAlert: true,
      templateIndex: 0,
      templates: [],
      isTesting: false,

      // 🔧 直接WebSocket连接状态
      wsConnected: false,
      wsConnecting: false,
      socketTask: null,
      heartbeatTimer: null,

      // 认证信息
      authInfo: {
        merchant_id: '1000',
        user_id: 'user_123',
        token: 'test_token_456'
      },

      // WebSocket状态
      websocketStatus: {
        isConnected: false,
        isStarted: false,
        totalMessages: 0,
        paymentNotifications: 0,
        todayPayments: { count: 0, amount: 0 }
      },
      voiceStatus: {
        isMonitoring: false,
        currentPage: '',
        currentStrategy: null
      }
    }
  },
  
  computed: {
    templateNames() {
      return this.templates.map(t => t.name)
    },

    currentTemplate() {
      return this.templates[this.templateIndex] || this.templates[0] || { name: '标准模式', description: '简洁明了的播报内容' }
    },
    
    previewText() {
      if (!this.currentTemplate) return ''

      // 生成预览文本
      const sampleOrder = {
        money: '88.88',
        typename: '微信支付',
        trade_no: 'TEST123456'
      }

      try {
        // 使用简单的模板替换
        return this.currentTemplate.template.replace(/\{money\}/g, sampleOrder.money)
      } catch (error) {
        return this.currentTemplate.template
      }
    },

    // WebSocket状态文本
    websocketStatusText() {
      if (this.websocketStatus.isConnected) {
        return '已连接，实时监听中'
      } else if (this.websocketStatus.isStarted) {
        return '连接中...'
      } else {
        return '未连接'
      }
    },

    // WebSocket状态样式类
    websocketStatusClass() {
      if (this.websocketStatus.isConnected) {
        return 'connected'
      } else if (this.websocketStatus.isStarted) {
        return 'connecting'
      } else {
        return 'disconnected'
      }
    },

    // 今日统计文本
    todayStatsText() {
      const { count, amount } = this.websocketStatus.todayPayments
      return `${count}笔 / ¥${amount.toFixed(2)}`
    }
  },
  
  onLoad() {
    console.log('🔊 语音设置页面加载')
    this.initSystemInfo()
    this.loadSettings()
    this.loadTemplates()
    this.updateVoiceStatus()
  },
  
  onShow() {
    // 页面显示时更新状态
    this.updateVoiceStatus()

    // 检查WebSocket连接状态，如果断开则重新连接
    if (!globalWebSocketService.isConnected) {
      console.log('🔄 页面显示时检测到WebSocket断开，重新连接...')
      this.connectWebSocket()
    }
  },

  onHide() {
    // 页面隐藏时保持连接，不断开WebSocket
    console.log('📱 语音设置页面隐藏，保持WebSocket连接')
  },

  onUnload() {
    // 🔧 页面卸载时断开直接WebSocket连接
    console.log('🚪 语音设置页面卸载，断开WebSocket连接')
    this.disconnectWebSocket()
  },
  
  methods: {
    // 初始化系统信息
    initSystemInfo() {
      const systemInfo = uni.getSystemInfoSync()
      this.statusBarHeight = systemInfo.statusBarHeight || 20
    },
    
    // 加载设置
    loadSettings() {
      const voiceStatus = voiceManager.getStatus()
      const wsStatus = globalWebSocketService.getStatus()

      this.voiceEnabled = voiceStatus.enabled
      this.volume = voiceStatus.volume
      this.largeAmountAlert = true // 保持兼容

      console.log('🔊 当前语音设置:', voiceStatus)
      console.log('🌐 WebSocket状态:', wsStatus)
    },

    // 加载模板
    loadTemplates() {
      this.templates = [
        { id: 'simple', name: '简洁模式', description: '收款X元', template: '收款{money}元' },
        { id: 'detailed', name: '详细模式', description: '收到XX付款X元', template: '收到{type}付款{money}元' },
        { id: 'custom', name: '自定义模式', description: '恭喜收款X元成功', template: '恭喜收款{money}元成功' }
      ]

      // 设置当前选中的模板
      const currentTemplate = voiceManager.templateMode || 'simple'
      this.templateIndex = this.templates.findIndex(t => t.id === currentTemplate)
      if (this.templateIndex === -1) this.templateIndex = 0
    },

    // 更新语音状态
    updateVoiceStatus() {
      this.loadSettings()
      this.updateWebSocketStatus()
    },

    // 更新WebSocket状态
    updateWebSocketStatus() {
      // 🔧 使用直接连接的状态
      this.websocketStatus.isConnected = this.wsConnected
      this.websocketStatus.isStarted = this.wsConnected || this.wsConnecting

      console.log('🌐 WebSocket状态更新:', this.websocketStatus)
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 切换语音开关
    async toggleVoice(e) {
      this.voiceEnabled = e.detail.value

      // 更新VoiceManager设置
      voiceManager.setEnabled(this.voiceEnabled)

      // 🔧 根据开关状态连接或断开WebSocket
      if (this.voiceEnabled) {
        console.log('🔊 开启语音播报，连接WebSocket')
        this.connectWebSocket()
      } else {
        console.log('🔇 关闭语音播报，断开WebSocket')
        this.disconnectWebSocket()
      }

      this.updateVoiceStatus()

      uni.showToast({
        title: this.voiceEnabled ? '🔊 语音播报已开启，正在连接...' : '🔇 语音播报已关闭',
        icon: 'none',
        duration: 2000
      })
    },
    
    // 设置音量
    setVolume(e) {
      this.volume = e.detail.value / 100
      voiceManager.setVolume(this.volume) // VoiceManager使用0-1范围

      // 实时播报音量变化
      if (this.voiceEnabled) {
        this.debounceTestVolume()
      }
    },

    // 防抖测试音量
    debounceTestVolume() {
      clearTimeout(this.volumeTestTimer)
      this.volumeTestTimer = setTimeout(() => {
        this.testVoice()
      }, 500)
    },

    // 设置模板
    setTemplate(e) {
      this.templateIndex = e.detail.value
      const template = this.templates[this.templateIndex]

      if (template) {
        voiceManager.setTemplate(template.id)

        uni.showToast({
          title: `已切换到${template.name}`,
          icon: 'none',
          duration: 2000
        })
      }
    },
    
    // 切换大额提醒
    toggleLargeAlert(e) {
      this.largeAmountAlert = e.detail.value
      // 全局语音服务暂不支持大额提醒，保持兼容性

      uni.showToast({
        title: this.largeAmountAlert ? '大额提醒已开启' : '大额提醒已关闭',
        icon: 'none',
        duration: 2000
      })
    },

    // 测试语音
    async testVoice() {
      if (this.isTesting) return

      this.isTesting = true

      try {
        await voiceManager.testVoice()

        uni.showToast({
          title: '🎵 测试播报完成',
          icon: 'none',
          duration: 2000
        })
      } catch (error) {
        console.error('测试播报失败:', error)
        uni.showToast({
          title: '播报失败，请检查设备音量',
          icon: 'none',
          duration: 2000
        })
      } finally {
        setTimeout(() => {
          this.isTesting = false
        }, 2000)
      }
    },
    
    // 获取页面名称
    getPageName(pagePath) {
      const pageNames = {
        'pages/index/index': '首页',
        'pages/scan/index': '收款码',
        'pages/dynamic-code/index': '动态收款码',
        'pages/pay/mini-payment': '反扫收银台',
        'pages/bill/index': '账单记录'
      }
      return pageNames[pagePath] || '未知页面'
    },

    // 🔧 直接WebSocket连接方法
    connectWebSocket() {
      if (this.wsConnected || this.wsConnecting) {
        console.log('⚠️ 已经连接或正在连接中')
        return
      }

      this.wsConnecting = true
      const wsUrl = 'ws://ceshi.huisas.com:8080'
      console.log(`🔗 正在连接: ${wsUrl}`)

      this.socketTask = uni.connectSocket({
        url: wsUrl,
        success: () => {
          console.log('🚀 WebSocket连接请求已发送')
        },
        fail: (error) => {
          this.wsConnecting = false
          console.error(`❌ 连接失败:`, error)
          uni.showToast({
            title: '连接失败，请检查网络',
            icon: 'none'
          })
        }
      })

      // 连接成功
      this.socketTask.onOpen(() => {
        this.wsConnected = true
        this.wsConnecting = false
        console.log('✅ WebSocket连接成功！')

        // 启动心跳
        this.startHeartbeat()

        // 发送认证
        this.sendAuth()

        // 更新状态显示
        this.updateWebSocketStatus()

        uni.showToast({
          title: '🔊 语音播报连接成功',
          icon: 'success'
        })
      })

      // 接收消息
      this.socketTask.onMessage((res) => {
        console.log(`📨 收到消息: ${res.data}`)

        try {
          const data = JSON.parse(res.data)
          this.handleMessage(data)
        } catch (error) {
          console.error(`❌ JSON解析失败:`, error)
        }
      })

      // 连接关闭
      this.socketTask.onClose((res) => {
        this.wsConnected = false
        this.wsConnecting = false
        console.log(`🔌 连接关闭: ${res.code || 'unknown'}`)

        // 清理心跳
        if (this.heartbeatTimer) {
          clearInterval(this.heartbeatTimer)
          this.heartbeatTimer = null
        }

        // 更新状态显示
        this.updateWebSocketStatus()
      })

      // 连接错误
      this.socketTask.onError((error) => {
        this.wsConnected = false
        this.wsConnecting = false
        console.error(`❌ 连接错误:`, error)

        // 更新状态显示
        this.updateWebSocketStatus()
      })
    },

    // 断开WebSocket连接
    disconnectWebSocket() {
      if (this.socketTask) {
        this.socketTask.close()
        this.socketTask = null
      }
      this.wsConnected = false
      this.wsConnecting = false

      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer)
        this.heartbeatTimer = null
      }

      console.log('🔌 主动断开连接')
      this.updateWebSocketStatus()
    },

    // 发送认证
    sendAuth() {
      const authMessage = {
        type: 'auth',
        data: this.authInfo
      }
      this.sendMessage(authMessage)
      console.log('🔐 发送认证信息')

      // 认证后立即订阅支付频道
      setTimeout(() => {
        this.subscribePaymentChannel()
      }, 100)
    },

    // 订阅支付频道
    subscribePaymentChannel() {
      const merchantId = this.authInfo.merchant_id || '1000'
      const channel = `merchant_${merchantId}_payment`

      const subscribeMessage = {
        type: 'subscribe',
        data: {
          channel: channel
        }
      }
      this.sendMessage(subscribeMessage)
      console.log(`📡 订阅商户专属频道: ${channel}`)
    },

    // 发送消息
    sendMessage(message) {
      if (!this.wsConnected || !this.socketTask) {
        console.error('❌ 未连接，无法发送消息')
        return
      }

      const jsonData = JSON.stringify(message)
      console.log(`📤 发送消息: ${jsonData}`)

      this.socketTask.send({
        data: jsonData,
        success: () => {
          // console.log(`📤 消息发送成功`)
        },
        fail: (error) => {
          console.error(`❌ 消息发送失败:`, error)
        }
      })
    },

    // 启动心跳
    startHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer)
      }

      this.heartbeatTimer = setInterval(() => {
        if (this.wsConnected && this.socketTask) {
          this.sendMessage({
            type: 'ping',
            data: { timestamp: Date.now() }
          })
        }
      }, 30000) // 30秒心跳
    },

    // 处理收到的消息
    handleMessage(data) {
      console.log(`📨 处理消息类型: ${data.type}`)

      switch (data.type) {
        case 'auth_success':
          console.log('✅ 认证成功')
          break

        case 'auth_failed':
          console.error('❌ 认证失败:', data.message)
          break

        case 'subscribe_success':
          console.log('✅ 订阅成功:', data.channel)
          break

        case 'payment_notification':
          console.log('💰 收到支付通知:', data)
          this.handlePaymentNotification(data)
          break

        case 'pong':
          // 心跳响应，不需要特殊处理
          break

        default:
          console.log('📨 未知消息类型:', data.type)
      }
    },

    // 处理支付通知
    async handlePaymentNotification(data) {
      console.log('💰 处理支付通知:', data)

      try {
        // 播放语音
        await this.playPaymentVoice(data)

        // 更新统计
        this.websocketStatus.paymentNotifications++
        this.websocketStatus.todayPayments.count++

        if (data.data && data.data.money) {
          const amount = parseFloat(data.data.money) || 0
          this.websocketStatus.todayPayments.amount += amount
        }

        this.updateWebSocketStatus()

      } catch (error) {
        console.error('❌ 处理支付通知失败:', error)
      }
    },

    // 播放支付语音
    async playPaymentVoice(notification) {
      try {
        const amount = this.extractPaymentAmount(notification)
        console.log(`🎵 播放语音: 收款${amount}元`)

        // 显示收款提示
        uni.showToast({
          title: `收款 ¥${amount}`,
          icon: 'success',
          duration: 3000
        })

        // 使用VoiceManager播放语音
        if (voiceManager && typeof voiceManager.speak === 'function') {
          await voiceManager.speak(`收到支付${amount}元`)
          console.log(`✅ 语音播放完成: 收款${amount}元`)
        } else {
          console.log('⚠️ VoiceManager不可用')
        }

      } catch (error) {
        console.error('❌ 语音播放失败:', error)
      }
    },

    // 提取支付金额
    extractPaymentAmount(notification) {
      try {
        if (notification.data && notification.data.money) {
          return parseFloat(notification.data.money).toFixed(2)
        }
        return '0.00'
      } catch (error) {
        console.error('❌ 提取支付金额失败:', error)
        return '0.00'
      }
    }
  }
}
</script>

<style scoped>
/* 导航栏 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 100%);
}

.nav-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}

.nav-left, .nav-right {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  font-size: 48rpx;
  color: #fff;
  font-weight: 300;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

/* 设置内容 */
.settings-content {
  padding-top: 108rpx;
  padding-bottom: 40rpx;
}

.setting-group {
  margin: 32rpx 24rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.group-title {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-bottom: 2rpx solid #f0f0f0;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f8f8f8;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-left {
  flex: 1;
}

.setting-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.setting-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.setting-right {
  width: 300rpx;
  margin-left: 24rpx;
}

.picker-text {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #5145F7;
  padding: 16rpx 0;
}

.picker-arrow {
  font-size: 32rpx;
  color: #ccc;
}

/* 预览区域 */
.preview-section {
  padding: 32rpx;
}

.preview-text {
  background: #f8f9ff;
  border: 2rpx solid #e5e7ff;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 26rpx;
  color: #5145F7;
  line-height: 1.5;
  margin-bottom: 24rpx;
  text-align: center;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-btn:disabled {
  background: #ccc;
  color: #999;
}

/* WebSocket状态指示器 */
.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 40rpx;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #ccc;
  animation: none;
}

.status-indicator.connected .status-dot {
  background: #52c41a;
  animation: pulse 2s infinite;
}

.status-indicator.connecting .status-dot {
  background: #faad14;
  animation: spin 1s linear infinite;
}

.status-indicator.disconnected .status-dot {
  background: #ff4d4f;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 状态区域 */
.status-section {
  padding: 32rpx;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f8f8f8;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 26rpx;
  color: #666;
}

.status-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.status-value.active {
  color: #52c41a;
}
</style>
