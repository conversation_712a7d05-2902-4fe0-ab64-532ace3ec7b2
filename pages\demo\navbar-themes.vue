<template>
	<view class="container">
		<!-- 当前主题导航栏 -->
		<custom-navbar 
			:title="currentTheme.name" 
			subtitle="主题演示"
			:show-back="true"
			:show-notification="true"
			:badge-count="5"
			:custom-style="currentThemeStyle"
			@back="goBack"
			@notification="showNotification"
		></custom-navbar>
		
		<!-- 导航栏占位 -->
		<view class="navbar-placeholder"></view>
		
		<!-- 主题选择器 -->
		<view class="content">
			<view class="theme-section">
				<text class="section-title">选择导航栏主题</text>
				<view class="theme-grid">
					<view 
						class="theme-item" 
						v-for="(theme, key) in themes" 
						:key="key"
						:class="{ active: currentThemeKey === key }"
						@click="switchTheme(key)"
					>
						<view class="theme-preview" :style="getPreviewStyle(theme)">
							<text class="theme-preview-text">{{ theme.name }}</text>
						</view>
						<text class="theme-name">{{ theme.name }}</text>
					</view>
				</view>
			</view>
			
			<!-- 功能演示 -->
			<view class="demo-section">
				<text class="section-title">功能演示</text>
				<view class="demo-buttons">
					<button class="demo-btn" @click="toggleNotification">
						{{ showNotificationBtn ? '隐藏' : '显示' }}通知按钮
					</button>
					<button class="demo-btn" @click="changeBadgeCount">
						更改徽章数量 ({{ badgeCount }})
					</button>
					<button class="demo-btn" @click="changeTitle">
						更改标题
					</button>
					<button class="demo-btn" @click="toggleShadow">
						{{ showShadow ? '隐藏' : '显示' }}阴影
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar/custom-navbar.vue'
import { navbarThemes, getThemeStyle } from '@/config/navbar-themes.js'

export default {
	components: {
		CustomNavbar
	},
	
	data() {
		return {
			currentThemeKey: 'default',
			showNotificationBtn: true,
			badgeCount: 5,
			showShadow: true,
			titleIndex: 0,
			titles: ['主题演示', '导航栏测试', '样式展示', 'Navbar Demo'],
			
			themes: {
				default: { name: '默认蓝色', ...navbarThemes.default },
				business: { name: '商务深蓝', ...navbarThemes.business },
				success: { name: '成功绿色', ...navbarThemes.success },
				warning: { name: '警告橙色', ...navbarThemes.warning },
				error: { name: '错误红色', ...navbarThemes.error },
				purple: { name: '紫色主题', ...navbarThemes.purple },
				pink: { name: '粉色主题', ...navbarThemes.pink },
				light: { name: '浅色主题', ...navbarThemes.light },
				dark: { name: '深色主题', ...navbarThemes.dark }
			}
		}
	},
	
	computed: {
		currentTheme() {
			return this.themes[this.currentThemeKey];
		},
		
		currentThemeStyle() {
			const theme = this.currentTheme;
			return {
				background: theme.gradient || theme.backgroundColor,
				color: theme.textColor,
				boxShadow: this.showShadow ? theme.shadow : 'none'
			};
		}
	},
	
	methods: {
		// 切换主题
		switchTheme(themeKey) {
			this.currentThemeKey = themeKey;
			uni.showToast({
				title: `切换到${this.themes[themeKey].name}`,
				icon: 'none'
			});
		},
		
		// 获取预览样式
		getPreviewStyle(theme) {
			return {
				background: theme.gradient || theme.backgroundColor,
				color: theme.textColor
			};
		},
		
		// 返回
		goBack() {
			uni.navigateBack();
		},
		
		// 显示通知
		showNotification() {
			uni.showToast({
				title: `通知徽章: ${this.badgeCount}`,
				icon: 'none'
			});
		},
		
		// 切换通知按钮显示
		toggleNotification() {
			this.showNotificationBtn = !this.showNotificationBtn;
		},
		
		// 更改徽章数量
		changeBadgeCount() {
			this.badgeCount = this.badgeCount >= 99 ? 0 : this.badgeCount + 1;
		},
		
		// 更改标题
		changeTitle() {
			this.titleIndex = (this.titleIndex + 1) % this.titles.length;
		},
		
		// 切换阴影
		toggleShadow() {
			this.showShadow = !this.showShadow;
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.navbar-placeholder {
	height: 88px;
}

.content {
	padding: 20px;
}

.section-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
	margin-bottom: 16px;
	display: block;
}

.theme-section {
	background: white;
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.theme-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 12px;
}

.theme-item {
	text-align: center;
	cursor: pointer;
	transition: transform 0.2s ease;
	
	&.active {
		transform: scale(1.05);
	}
	
	&:active {
		transform: scale(0.95);
	}
}

.theme-preview {
	height: 40px;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.theme-preview-text {
	font-size: 12px;
	font-weight: 500;
}

.theme-name {
	font-size: 12px;
	color: #666;
}

.demo-section {
	background: white;
	border-radius: 12px;
	padding: 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-buttons {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 12px;
}

.demo-btn {
	padding: 12px 16px;
	background: #4A90E2;
	color: white;
	border: none;
	border-radius: 8px;
	font-size: 14px;
	cursor: pointer;
	transition: background 0.2s ease;
	
	&:active {
		background: #357ABD;
	}
}
</style>
