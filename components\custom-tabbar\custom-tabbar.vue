<!-- components/custom-tabbar.vue -->
<template>
  <view class="tabbar-container">
    <!-- 底部导航背景 -->
    <view class="tabbar-background"></view>
    
    <!-- 中间凸起半圆 -->
    <view class="tabbar-center-circle">
      <view class="center-icon-container">
        <image :src="centerIcon" class="center-icon" mode="aspectFit" />
      </view>
    </view>
    
    <!-- 导航项容器 -->
    <view class="tabbar-items">
      <view 
        v-for="(item, index) in list" 
        :key="index"
        class="tabbar-item"
        :class="{ 'is-active': currentPage === item.pagePath, 'is-center': index === 2 }"
        @click="switchTab(item)"
      >
        <image 
          :src="currentPage === item.pagePath ? item.selectedIcon : item.icon" 
          class="tabbar-icon"
          mode="aspectFit"
        />
        <text 
          class="tabbar-text"
          :class="{ 'active-text': currentPage === item.pagePath }"
        >{{ item.text }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentPage: 'pages/index/index',
      centerIcon: '/static/tab/code.png',
      list: [
        { pagePath: '/pages/index/index', text: '首页', icon: '/static/tab/home.png', selectedIcon: '/static/tab/home-active.png' },
        { pagePath: '/pages/bill/index', text: '账单', icon: '/static/tab/bill.png', selectedIcon: '/static/tab/bill-active.png' },
        { pagePath: '/pages/scan/index', text: '一码通', icon: '/static/tab/code.png', selectedIcon: '/static/tab/code-active.png' },
        { pagePath: '/pages/report/index', text: '报表', icon: '/static/tab/report.png', selectedIcon: '/static/tab/report-active.png' },
        { pagePath: '/pages/mine/index', text: '我的', icon: '/static/tab/mine.png', selectedIcon: '/static/tab/mine-active.png' }
      ]
    }
  },
  mounted() {
    // 获取当前页面路径并更新选中状态
    const pages = getCurrentPages();
    if (pages.length) {
      const currentPage = '/' + pages[pages.length - 1].route;
      this.currentPage = currentPage;
      
      // 如果当前页是一码通，更新中心图标
      if (currentPage === '/pages/scan/index') {
        this.centerIcon = '/static/tab/code-active.png';
      }
    }
  },
  methods: {
    switchTab(item) {
      if (this.currentPage === item.pagePath) return;
      
      // 更新中心图标
      if (item.text === '一码通') {
        this.centerIcon = '/static/tab/code-active.png';
      } else {
        this.centerIcon = '/static/tab/code.png';
      }
      
      uni.switchTab({ url: item.pagePath });
      this.currentPage = item.pagePath;
    }
  }
}
</script>

<style>
.tabbar-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  z-index: 999;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.tabbar-background {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  box-shadow: 0 -2rpx 6rpx rgba(0,0,0,0.1);
}

.tabbar-center-circle {
  position: absolute;
  left: 50%;
  bottom: 20rpx;
  transform: translateX(-50%);
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: #333;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  z-index: 3;
  display: flex;
  justify-content: center;
  align-items: center;
}

.center-icon-container {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.center-icon {
  width: 60rpx;
  height: 60rpx;
}

.tabbar-items {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-around;
  height: 100%;
  padding: 0 30rpx;
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 10rpx;
}

.tabbar-item.is-center {
  opacity: 0; /* 中间项隐藏，由凸起圆圈替代 */
}

.tabbar-icon {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 6rpx;
}

.tabbar-text {
  font-size: 24rpx;
  color: #888;
}

.active-text {
  color: #5145F7;
}
</style>