﻿@charset "utf-8";
/* CSS Document */

body, h1, h2, h3, h4, h5, h6, hr, p, blockquote, /* structural elements 结构元素 */ dl, dt, dd, ul, ol, li, /* list elements 列表元素 */ pre, /* text formatting elements 文本格式元素 */ fieldset, lengend, button, input, textarea, /* form elements 表单元素 */ th, td { /* table elements 表格元素 */ margin: 0; padding: 0; }
html,body{ width:100%; height:100%; color:#6e6e6e; font-family:'微软雅黑'}
/* 设置默认字体 */
body, button, input, select, textarea { /* for ie */ /*font: 12px/1 Tahoma, Helvetica, Arial, "宋体", sans-serif;*/
font: 14px/1 Tahoma, Helvetica, Arial, "微软雅黑", sans-serif,; /* 用 ascii 字符表示，使得在任何编码下都无问题 */  }
h1 { font-size: 18px; /* 18px / 12px = 1.5 */  font-family:'微软雅黑'}
h2 { font-size: 24px; font-weight:normal; color:#323232; font-family:'微软雅黑'}
h3 { font-size: 14px; font-weight:normal; font-family:'微软雅黑' }
h4, h5, h6 { font-size: 100%; font-weight:normal; font-family:'微软雅黑' }
p{ font-size:14px; line-height:34px; font-family:'微软雅黑'}/* 段落默认字号和行高 */
address, cite, dfn, em, var { font-style: normal; } /* 将斜体扶正 */
code, kbd, pre, samp, tt { font-family: "Courier New", Courier, monospace; } /* 统一等宽字体 */
small { font-size: 12px; } /* 小于 12px 的中文很难阅读，让 small 正常化 */
/* 重置列表元素 */
ul, ol { list-style: none; font-family:'微软雅黑' }
select{ border-style:none;}
/* 重置文本格式元素 */
a { text-decoration: none; color:#6e6e6e; font-size:14px; outline:none; font-family:'微软雅黑'}
span{ font-size:14px; font-family:'微软雅黑'}
a:hover { text-decoration: none; }
abbr[title], acronym[title] { /* 注：1.ie6 不支持 abbr; 2.这里用了属性选择符，ie6 下无效果 */ border-bottom: 1px dotted; cursor: help; }
q:before, q:after { content: ''; }
/* 重置表单元素 */
input{ border:none;}
legend { color: #000; } /* for ie6 */
fieldset, img { border: none; } /* img 搭车：让链接里的 img 无边框 */
/* 注：optgroup 无法扶正 */
button, input, select, textarea { font-size: 100%; /* 使得表单元素在 ie 下能继承字体大小 */ }
/* 重置表格元素 */
table { border-collapse: collapse; border-spacing: 0; width:100%;}
/* 重置 hr */
hr { border: none; height: 1px; }
/* 让非ie浏览器默认也显示垂直滚动条，防止因滚动条引起的闪烁 */
html { overflow-y: scroll;}
*:focus {outline: none;} 
/* 版心 */
.w100{ width:100%; /*min-width:1080px;*/ clear:both;}
.w1200{ width:1200px; margin:0 auto;}
.w1300{ width:1300px; margin:0 auto;}
.w1000{ width:1000px; margin:0 auto;}
.w1080{ width:1080px; margin:0 auto;}
/*常用样式*/
.fl{ float:left;}
.fr{ float:right;}
.tc{ text-align:center;}
.tr{ text-align:right;}
.tl{ text-align:left;}
.f12{ font-size:12px;}
.f14{ font-size:14px;}
.f15{ font-size:15px;}
.f16{ font-size:16px;}
.f18{ font-size:18px;}
.f20{ font-size:20px; color:#323232;}
.f22{ font-size:22px;}
.f24{ font-size:24px; color:#323232;}
.f36{ font-size:36px; color:#323232;}
.fc{ color:#d2151a;}
.l24{ line-height:24px;}
.f30{ font-size:30px; color:#323232;}
.f32{ font-size:32px; color:#323232;}
.l36{ line-height:36px;}
.t2{ text-indent:2em;}
.mt14{ margin-top:14px;} 
.mt20{ margin-top:20px;}
.t_ul{ text-decoration:underline;}
.lh24{ line-height:24px;}
.lh32{ line-height:32px;}
.blue{ color:#0568a4;}
.blue_ul{ color:#0568a4; font-size:16px; text-decoration:underline;}
.blue_ul02{ color:#fff; font-size:16px; text-decoration:underline;}
.red{ color:#e20000; font-size:14px;}
.ma{ margin:0 auto;}
select {
  /*Chrome和Firefox里面的边框是不一样的，所以复写了一下*/
  border: solid 1px #f8f9fb;

  /*很关键：将默认的select选择框样式清除*/
  appearance:none;
  -moz-appearance:none;
  -webkit-appearance:none;

  /*在选择框的最右侧中间显示小箭头图片*/
  background:url(../images/arrawdown.png) no-repeat scroll 90% center;


  /*为下拉小箭头留出一点位置，避免被文字覆盖*/
  padding-right: 14px;
}/* 重置文本格式元素 */
.mt_userMessage select{ background:url(../images/arrawdown02.png) no-repeat scroll right center;}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/  
::-webkit-scrollbar  
{  
    width: 6px;  
    height: 6px;  
    background-color: #F5F5F5;  
}  
  
/*定义滚动条轨道 内阴影+圆角*/  
::-webkit-scrollbar-track  
{  
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);  
    border-radius: 10px;  
    background-color: #F5F5F5;  
}  
  
/*定义滑块 内阴影+圆角*/  
::-webkit-scrollbar-thumb  
{  
    border-radius: 10px;  
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);  
    background-color: #ccc;  
} 