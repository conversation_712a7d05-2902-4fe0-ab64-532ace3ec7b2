<?php
/**
 * 测试真实支付WebSocket通知修复
 * 模拟真实支付回调，验证WebSocket通知是否正常工作
 */

require_once './includes/common.php';

echo "<h1>🧪 真实支付WebSocket通知修复测试</h1>";

// 测试订单号
$test_trade_no = '2025072717225870150'; // 你的真实支付订单号

echo "<h2>📋 测试步骤</h2>";
echo "<ol>";
echo "<li>查询真实支付订单信息</li>";
echo "<li>模拟支付回调处理</li>";
echo "<li>验证WebSocket通知是否发送</li>";
echo "</ol>";

// 1. 查询订单信息
echo "<h2>1️⃣ 查询订单信息</h2>";
$order = $DB->getRow("SELECT * FROM pre_order WHERE trade_no=? LIMIT 1", [$test_trade_no]);

if (!$order) {
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>❌ 订单未找到</h3>";
    echo "<p>订单号 {$test_trade_no} 在数据库中不存在</p>";
    echo "</div>";
    exit;
}

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h3>✅ 订单信息</h3>";
echo "<p><strong>订单号:</strong> {$order['trade_no']}</p>";
echo "<p><strong>状态:</strong> {$order['status']} " . getStatusText($order['status']) . "</p>";
echo "<p><strong>金额:</strong> {$order['realmoney']}元</p>";
echo "<p><strong>商户ID:</strong> {$order['uid']}</p>";
echo "<p><strong>支付类型:</strong> {$order['type']}</p>";
echo "<p><strong>创建时间:</strong> {$order['addtime']}</p>";
echo "<p><strong>完成时间:</strong> " . ($order['endtime'] ?? '未完成') . "</p>";
echo "</div>";

// 2. 测试WebSocket通知函数
echo "<h2>2️⃣ 测试WebSocket通知函数</h2>";
echo "<div style='background: #f8f8f8; padding: 15px; border-radius: 8px; margin: 10px 0;'>";

$websocketFile = SYSTEM_ROOT . "websocket_notify_workerman.php";
echo "<p><strong>WebSocket文件:</strong> " . $websocketFile . "</p>";
echo "<p><strong>文件存在:</strong> " . (file_exists($websocketFile) ? '✅ 是' : '❌ 否') . "</p>";

if (file_exists($websocketFile)) {
    include_once $websocketFile;
}

echo "<p><strong>函数存在:</strong> " . (function_exists('sendWebSocketPaymentNotification') ? '✅ 是' : '❌ 否') . "</p>";
echo "</div>";

// 3. 模拟支付回调处理
echo "<h2>3️⃣ 模拟支付回调处理</h2>";
echo "<div style='background: #e6f3ff; padding: 15px; border-radius: 8px; margin: 10px 0;'>";

if (function_exists('sendWebSocketPaymentNotification')) {
    echo "<h3>🔄 正在模拟支付回调...</h3>";
    
    try {
        // 模拟调用 \lib\Payment::processOrder 方法
        echo "<p>📞 调用 \\lib\\Payment::processOrder() 方法...</p>";
        
        // 使用真实的API交易号和买家信息
        $api_trade_no = $order['api_trade_no'] ?? '9924704491704223932416';
        $buyer = '2088602002631734';
        
        // 调用修复后的方法
        \lib\Payment::processOrder(true, $order, $api_trade_no, $buyer);
        
        echo "<p>✅ 支付回调处理完成</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ 支付回调处理异常: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p>❌ WebSocket通知函数不存在，无法进行测试</p>";
}

echo "</div>";

// 4. 检查日志记录
echo "<h2>4️⃣ 检查日志记录</h2>";
echo "<div style='background: #f0f8f0; padding: 15px; border-radius: 8px; margin: 10px 0;'>";

// 检查支付调试日志
$logFile = SYSTEM_ROOT . 'logs/payment_debug_' . date('Y-m-d') . '.log';
echo "<p><strong>支付调试日志:</strong> {$logFile}</p>";

if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    $lines = explode("\n", $logContent);
    $recentLines = array_slice($lines, -10); // 获取最后10行
    
    echo "<h4>📄 最近的日志记录</h4>";
    echo "<div style='background: #000; color: #00ff00; padding: 15px; border-radius: 8px; font-family: monospace; max-height: 300px; overflow-y: auto;'>";
    foreach ($recentLines as $line) {
        if (!empty(trim($line))) {
            echo htmlspecialchars($line) . "<br>";
        }
    }
    echo "</div>";
} else {
    echo "<p>⚠️ 支付调试日志文件不存在</p>";
}

// 检查WebSocket通知日志
$wsLogFile = ROOT . 'logs/websocket_integration.log';
echo "<p><strong>WebSocket通知日志:</strong> {$wsLogFile}</p>";

if (file_exists($wsLogFile)) {
    $wsLogContent = file_get_contents($wsLogFile);
    $wsLines = explode("\n", $wsLogContent);
    $recentWsLines = array_slice($wsLines, -5); // 获取最后5行
    
    echo "<h4>🔔 最近的WebSocket通知</h4>";
    echo "<div style='background: #000; color: #00ff00; padding: 15px; border-radius: 8px; font-family: monospace; max-height: 200px; overflow-y: auto;'>";
    foreach ($recentWsLines as $line) {
        if (!empty(trim($line))) {
            echo htmlspecialchars($line) . "<br>";
        }
    }
    echo "</div>";
} else {
    echo "<p>⚠️ WebSocket通知日志文件不存在</p>";
}

echo "</div>";

// 5. 测试结果总结
echo "<h2>5️⃣ 测试结果总结</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h3>🎯 修复要点</h3>";
echo "<ul>";
echo "<li><strong>问题原因:</strong> 真实支付回调时，订单状态更新可能失败，导致WebSocket通知代码不被执行</li>";
echo "<li><strong>修复方案:</strong> 即使订单状态更新失败，也要检查订单是否已经是已支付状态，如果是则继续执行WebSocket通知</li>";
echo "<li><strong>关键改进:</strong> 在 \\lib\\Payment::processOrder() 方法中增加了状态检查和强制执行WebSocket通知的逻辑</li>";
echo "</ul>";

echo "<h3>📝 下一步操作</h3>";
echo "<ol>";
echo "<li>等待新的真实支付测试修复效果</li>";
echo "<li>观察支付调试日志中是否出现WebSocket相关的日志记录</li>";
echo "<li>检查前端是否能收到语音播报</li>";
echo "</ol>";
echo "</div>";

// 辅助函数
function getStatusText($status) {
    switch ($status) {
        case 0: return "(待支付)";
        case 1: return "(已支付)";
        case 2: return "(已退款)";
        case 3: return "(已关闭)";
        case 4: return "(处理中)";
        default: return "(未知状态)";
    }
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
p { margin: 8px 0; }
ol, ul { margin: 10px 0; padding-left: 30px; }
li { margin: 5px 0; }
code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
</style>
