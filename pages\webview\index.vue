<template>
  <view class="container">
    <custom-navbar :title="pageTitle" :show-back="true"></custom-navbar>
    
    <!-- 加载状态 -->
    <view class="loading-container" v-if="isLoading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载支付页面...</text>
      </view>
    </view>
    
    <!-- WebView -->
    <web-view 
      :src="webviewUrl" 
      @message="handleMessage"
      @load="handleLoad"
      @error="handleError"
      v-if="webviewUrl"
    ></web-view>
    
    <!-- 错误状态 -->
    <view class="error-container" v-if="hasError">
      <view class="error-content">
        <text class="error-icon">⚠️</text>
        <text class="error-title">页面加载失败</text>
        <text class="error-message">{{ errorMessage }}</text>
        <button class="retry-button" @tap="retryLoad">重新加载</button>
        <button class="back-button" @tap="goBack">返回</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      webviewUrl: '',
      pageTitle: '支付页面',
      isLoading: true,
      hasError: false,
      errorMessage: '',
      originalUrl: ''
    }
  },
  onLoad(options) {
    console.log('WebView页面参数:', options)
    
    if (options.url) {
      this.originalUrl = decodeURIComponent(options.url)
      this.webviewUrl = this.originalUrl
    }
    
    if (options.title) {
      this.pageTitle = decodeURIComponent(options.title)
    }
    
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: this.pageTitle
    })
    
    // 如果没有URL，显示错误
    if (!this.webviewUrl) {
      this.showError('缺少页面地址参数')
    }
  },
  onShow() {
    // 页面显示时检查是否需要返回
    this.checkPaymentResult()
  },
  methods: {
    // 处理WebView加载完成
    handleLoad(event) {
      console.log('WebView加载完成:', event)
      this.isLoading = false
      this.hasError = false
    },
    
    // 处理WebView加载错误
    handleError(event) {
      console.error('WebView加载错误:', event)
      this.isLoading = false
      this.showError('页面加载失败，请检查网络连接')
    },
    
    // 处理WebView消息
    handleMessage(event) {
      console.log('收到WebView消息:', event)
      
      try {
        const message = event.detail.data[0]
        if (message && message.type) {
          switch (message.type) {
            case 'payment_success':
              this.handlePaymentSuccess(message.data)
              break
            case 'payment_failed':
              this.handlePaymentFailed(message.data)
              break
            case 'close_webview':
              this.goBack()
              break
          }
        }
      } catch (error) {
        console.error('处理WebView消息失败:', error)
      }
    },
    
    // 处理支付成功
    handlePaymentSuccess(data) {
      console.log('支付成功:', data)
      
      uni.showToast({
        title: '支付成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        uni.redirectTo({
          url: `/pages/pay/result?status=success&amount=${data.amount || ''}&tradeNo=${data.tradeNo || ''}`
        })
      }, 1500)
    },
    
    // 处理支付失败
    handlePaymentFailed(data) {
      console.log('支付失败:', data)
      
      uni.showToast({
        title: data.message || '支付失败',
        icon: 'none'
      })
      
      setTimeout(() => {
        uni.redirectTo({
          url: `/pages/pay/result?status=failed&message=${data.message || '支付失败'}`
        })
      }, 1500)
    },
    
    // 显示错误
    showError(message) {
      this.hasError = true
      this.errorMessage = message
      this.isLoading = false
    },
    
    // 重新加载
    retryLoad() {
      this.hasError = false
      this.isLoading = true
      this.webviewUrl = ''
      
      setTimeout(() => {
        this.webviewUrl = this.originalUrl
      }, 100)
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack({
        delta: 1
      })
    },
    
    // 检查支付结果
    checkPaymentResult() {
      // 这里可以添加检查支付结果的逻辑
      // 比如轮询订单状态等
    }
  }
}
</script>

<style>
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 加载状态样式 */
.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #5145F7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 错误状态样式 */
.error-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  padding: 40rpx;
}

.error-content {
  text-align: center;
  max-width: 500rpx;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}

.error-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  display: block;
  line-height: 1.5;
}

.retry-button, .back-button {
  background-color: #5145F7;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  margin: 10rpx;
}

.back-button {
  background-color: #999;
}

.retry-button:active, .back-button:active {
  opacity: 0.8;
}
</style>
