<template>
  <view class="container">
    <custom-navbar title="存储测试" :shadow="true"></custom-navbar>
    
    <view class="test-section">
      <view class="section-title">本地存储检查</view>
      
      <view class="storage-item">
        <text class="label">user_token:</text>
        <text class="value">{{ storageData.user_token || '无' }}</text>
      </view>
      
      <view class="storage-item">
        <text class="label">user_info:</text>
        <text class="value">{{ storageData.user_info ? JSON.stringify(storageData.user_info, null, 2) : '无' }}</text>
      </view>
      
      <view class="storage-item">
        <text class="label">userInfo:</text>
        <text class="value">{{ storageData.userInfo ? JSON.stringify(storageData.userInfo, null, 2) : '无' }}</text>
      </view>
      
      <view class="storage-item">
        <text class="label">user_uid:</text>
        <text class="value">{{ storageData.user_uid || '无' }}</text>
      </view>
      
      <button class="refresh-btn" @tap="refreshStorage">刷新存储数据</button>
      <button class="test-btn" @tap="testGetUserInfo">测试获取用户信息</button>
      
      <view class="api-result" v-if="apiResult">
        <view class="result-title">API响应:</view>
        <text class="result-content">{{ JSON.stringify(apiResult, null, 2) }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { request } from '@/utils/request'

export default {
  data() {
    return {
      storageData: {},
      apiResult: null
    }
  },
  
  onLoad() {
    this.refreshStorage()
  },
  
  methods: {
    refreshStorage() {
      this.storageData = {
        user_token: uni.getStorageSync('user_token'),
        user_info: uni.getStorageSync('user_info'),
        userInfo: uni.getStorageSync('userInfo'),
        user_uid: uni.getStorageSync('user_uid')
      }
      console.log('存储数据:', this.storageData)
    },
    
    async testGetUserInfo() {
      try {
        const response = await request({
          url: '/user/ajax2.php?act=getcount',
          method: 'POST',
          data: {}
        })
        
        this.apiResult = response
        console.log('用户信息API响应:', response)
        
        if (response && response.code === 0) {
          // 保存到本地
          uni.setStorageSync('user_info', response)
          this.refreshStorage()
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.apiResult = { error: error.message }
      }
    }
  }
}
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.storage-item {
  margin-bottom: 20rpx;
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 10rpx;
}

.value {
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
  white-space: pre-wrap;
}

.refresh-btn, .test-btn {
  width: 100%;
  padding: 20rpx;
  margin: 15rpx 0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #fff;
  border: none;
}

.refresh-btn {
  background-color: #007aff;
}

.test-btn {
  background-color: #34c759;
}

.api-result {
  margin-top: 30rpx;
  padding: 20rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
}

.result-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.result-content {
  font-size: 24rpx;
  color: #666;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
