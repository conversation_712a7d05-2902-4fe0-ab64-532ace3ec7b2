<template>
  <view class="staff-login-container">
    <!-- 顶部Logo区域 -->
    <view class="header-section">
      <view class="logo-area">
        <view class="staff-logo-wrapper">
          <text class="staff-logo-icon">👨‍💼</text>
        </view>
        <text class="app-title">员工收款系统</text>
        <text class="app-subtitle">Employee Payment System</text>
      </view>
    </view>

    <!-- 登录表单区域 -->
    <view class="form-section">
      <view class="form-container">
        <view class="form-title">员工登录</view>
        
        <!-- 商户码输入 -->
        <view class="form-item">
          <view class="item-label">
            <text class="label-icon">🏪</text>
            <text class="label-text">商户码</text>
          </view>
          <input
            class="form-input"
            type="number"
            v-model="formData.merchantCode"
            placeholder="请输入商户码（如：99009）"
            maxlength="10"
          />
        </view>

        <!-- 账号输入 -->
        <view class="form-item">
          <view class="item-label">
            <text class="label-icon">👤</text>
            <text class="label-text">员工账号</text>
          </view>
          <input 
            class="form-input" 
            type="text" 
            v-model="formData.account" 
            placeholder="请输入员工账号"
            maxlength="20"
          />
        </view>

        <!-- 密码输入 -->
        <view class="form-item">
          <view class="item-label">
            <text class="label-icon">🔒</text>
            <text class="label-text">登录密码</text>
          </view>
          <view class="password-input-wrapper">
            <input 
              class="form-input password-input" 
              :type="showPassword ? 'text' : 'password'" 
              v-model="formData.password" 
              placeholder="请输入登录密码"
              maxlength="20"
            />
            <text class="password-toggle" @tap="togglePassword">
              {{ showPassword ? '👁️' : '👁️‍🗨️' }}
            </text>
          </view>
        </view>

        <!-- 登录按钮 -->
        <button 
          class="login-btn" 
          :class="{ 'btn-disabled': !canLogin }"
          :disabled="!canLogin"
          @tap="handleLogin"
        >
          <text class="btn-text">{{ isLoading ? '登录中...' : '立即登录' }}</text>
        </button>

        <!-- 帮助信息 -->
        <view class="help-section">
          <text class="help-text">💡 商户码请咨询店长（如：99009）</text>
          <text class="help-text">🔑 忘记密码？请联系店长重置</text>
          <text class="help-text">👋 首次登录？请使用店长提供的账号密码</text>
        </view>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer-section">
      <text class="footer-text">© 2024 员工收款系统</text>
      <text class="footer-text">安全 · 便捷 · 高效</text>
    </view>
  </view>
</template>

<script>
import { request } from '@/utils/request.js'

export default {
  name: 'StaffLogin',
  data() {
    return {
      // 表单数据
      formData: {
        merchantCode: '',
        account: '',
        password: ''
      },
      
      // 界面状态
      showPassword: false,
      isLoading: false
    }
  },
  
  computed: {
    // 是否可以登录
    canLogin() {
      return this.formData.merchantCode.trim() &&
             this.formData.account.trim() &&
             this.formData.password.trim() &&
             !this.isLoading
    }
  },
  
  onLoad() {
    console.log('📱 员工登录页面加载')
    this.initPage()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      // 检查是否已登录
      this.checkLoginStatus()

      // 尝试从缓存读取商户码
      const savedMerchantCode = uni.getStorageSync('remembered_merchant_code')
      if (savedMerchantCode) {
        this.formData.merchantCode = savedMerchantCode
      }
    },
    
    // 检查登录状态
    checkLoginStatus() {
      const staffToken = uni.getStorageSync('staff_token')
      const staffInfo = uni.getStorageSync('staff_info')
      
      if (staffToken && staffInfo) {
        console.log('✅ 员工已登录，跳转到首页')
        uni.reLaunch({
          url: '/pages/staff-home/index'
        })
      }
    },
    

    
    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    // 处理登录
    async handleLogin() {
      if (!this.canLogin) return
      
      try {
        this.isLoading = true
        console.log('🔐 开始员工登录...')
        
        // 调用登录API
        const response = await request({
          url: '/user/staff.php?act=staffLogin',
          method: 'POST',
          data: {
            merchantCode: this.formData.merchantCode.trim(),
            account: this.formData.account.trim(),
            password: this.formData.password.trim()
          }
        })
        
        console.log('📥 登录响应:', response)
        
        if (response && response.code === 0) {
          // 登录成功
          const staffInfo = response.data
          
          // 保存登录信息
          uni.setStorageSync('staff_token', `staff_${staffInfo.id}_${Date.now()}`)
          uni.setStorageSync('staff_info', staffInfo)
          uni.setStorageSync('staff_merchant_code', this.formData.merchantCode.trim())

          // 记住商户码
          uni.setStorageSync('remembered_merchant_code', this.formData.merchantCode.trim())
          
          console.log('✅ 员工登录成功:', staffInfo)
          
          // 显示成功提示
          uni.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 1500
          })
          
          // 跳转到员工首页
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/staff-home/index'
            })
          }, 1500)
          
        } else {
          throw new Error(response?.msg || '登录失败')
        }
        
      } catch (error) {
        console.error('❌ 员工登录失败:', error)
        
        uni.showToast({
          title: error.message || '登录失败',
          icon: 'none',
          duration: 2000
        })
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.staff-login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.header-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx 40rpx;
}

.logo-area {
  text-align: center;
}

.staff-logo-wrapper {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.staff-logo-icon {
  font-size: 60rpx;
}

.app-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.app-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.form-section {
  flex: 2;
  padding: 0 40rpx 40rpx;
}

.form-container {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.form-title {
  font-size: 44rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  margin-bottom: 60rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.item-label {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.label-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.label-text {
  font-size: 32rpx;
  color: #666666;
  font-weight: 500;
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.picker-text {
  font-size: 32rpx;
  color: #333333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999999;
}

.form-input {
  height: 88rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  font-size: 32rpx;
  color: #333333;
}

.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input {
  flex: 1;
  padding-right: 80rpx;
}

.password-toggle {
  position: absolute;
  right: 24rpx;
  font-size: 32rpx;
  color: #999999;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  border: none;
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-disabled {
  background: #cccccc !important;
}

.btn-text {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: bold;
}

.help-section {
  margin-top: 40rpx;
  text-align: center;
}

.help-text {
  display: block;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.footer-section {
  padding: 40rpx;
  text-align: center;
}

.footer-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8rpx;
}
</style>
