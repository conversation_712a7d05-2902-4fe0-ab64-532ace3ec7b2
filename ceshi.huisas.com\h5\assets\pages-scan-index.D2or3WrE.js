import{y as e,a,E as t,u as l,A as s,B as o,d,C as c,f as n,w as i,e as f,j as u,F as m,i as r,o as C,h as _,t as h,D as p,k,l as g,I as y,p as S,z as b}from"./index-B1Q521gi.js";import{r as w,_ as M}from"./uni-app.es.DAfa8VxY.js";import{_ as F}from"./custom-navbar.DuzuSmPc.js";import{_ as N}from"./_plugin-vue_export-helper.BCo6x5W8.js";const B=N({components:{CustomNavbar:F},data:()=>({merchantName:"商家收款助手",paymentCodeName:"默认收款码",selectedStaff:null,tempSelectedStaffId:null,showNameModal:!1,showStaffListModal:!1,showBindCodeModal:!1,tempPaymentCodeName:"",staffList:[{id:1,name:"李店长",role:"店长",color:"purple"},{id:2,name:"王收银",role:"收银员",color:"green"},{id:3,name:"张收银",role:"收银员",color:"blue"}],generatedCodes:[{id:1,name:"主店收款",position:"主店长",date:"2025/04/18创建",color:"blue-light"},{id:2,name:"收银台1",position:"主收银",date:"2025/04/12创建",color:"green-light"},{id:3,name:"外卖专用",position:"配送员",date:"2025/04/10创建",color:"purple-light"}]}),methods:{goBack(){e()},shareCode(){a({title:"分享功能开发中",icon:"none"})},showEditNameModal(){this.tempPaymentCodeName=this.paymentCodeName,this.showNameModal=!0},cancelEditName(){this.showNameModal=!1},confirmEditName(){this.tempPaymentCodeName.trim()&&(this.paymentCodeName=this.tempPaymentCodeName),this.showNameModal=!1},showStaffModal(){var e;this.tempSelectedStaffId=(null==(e=this.selectedStaff)?void 0:e.id)||null,this.showStaffListModal=!0},cancelSelectStaff(){this.showStaffListModal=!1},selectStaff(e){this.tempSelectedStaffId=e.id},confirmSelectStaff(){this.selectedStaff=this.staffList.find((e=>e.id===this.tempSelectedStaffId)),this.showStaffListModal=!1},createNewCode(){a({title:"创建新收款码",icon:"none"})},editCode(e){a({title:"编辑收款码: "+e.name,icon:"none"})},deleteCode(e){t({title:"确认删除",content:'确定要删除收款码 "'+e.name+'" 吗？',success:t=>{t.confirm&&(this.generatedCodes=this.generatedCodes.filter((a=>a.id!==e.id)),a({title:"删除成功",icon:"success"}))}})},showBindEmptyCodeModal(){this.showBindCodeModal=!0,this.showManualInput=!1,this.manualCodeInput=""},cancelBindCode(){this.showBindCodeModal=!1},scanBindCode(){this.showBindCodeModal=!1,l({onlyFromCamera:!0,scanType:["qrCode"],success:e=>{this.handleScannedCode(e.result)},fail:()=>{a({title:"扫码失败",icon:"none"})}})},handleScannedCode(e){t({title:"绑定空码",content:"确认绑定此空码？绑定后将生成新的收款码。",success:e=>{if(e.confirm){const e={id:this.generatedCodes.length+1,name:"新绑定收款码",position:this.selectedStaff?this.selectedStaff.name:"未指定",date:this.formatDate(new Date)+"创建",color:"blue-light"};this.generatedCodes.unshift(e),a({title:"绑定成功",icon:"success"})}}})},enterCodeManually(){this.showManualInput=!0},verifyCode(){this.manualCodeInput.trim()?(s({title:"验证中..."}),setTimeout((()=>{o(),this.showBindCodeModal=!1,this.handleScannedCode(this.manualCodeInput)}),1e3)):a({title:"请输入空码编号",icon:"none"})},formatDate:e=>`${e.getFullYear()}/${(e.getMonth()+1).toString().padStart(2,"0")}/${e.getDate().toString().padStart(2,"0")}`}},[["render",function(e,a,t,l,s,o){const N=w(d("uni-icons"),M),B=r,v=w(d("custom-navbar"),F),I=k,E=g,x=y,L=S;return C(),c(m,null,[n(B,{class:"container"},{default:i((()=>[n(v,{title:"收款码","show-back":!0,shadow:!0,onClickLeft:o.goBack},{right:i((()=>[n(B,{onClick:o.shareCode,style:{padding:"0 16rpx"}},{default:i((()=>[n(N,{type:"share",size:"22",color:"#FFFFFF"})])),_:1},8,["onClick"])])),_:1},8,["onClickLeft"]),n(B,{class:"content"},{default:i((()=>[n(B,{class:"qrcode-card"},{default:i((()=>[n(B,{class:"merchant-name"},{default:i((()=>[_(h(s.merchantName),1)])),_:1}),n(B,{class:"merchant-desc"},{default:i((()=>[_("扫码向我付款")])),_:1}),n(B,{class:"qrcode-container"},{default:i((()=>[n(B,{class:"qrcode"},{default:i((()=>[n(B,{class:"qrcode-placeholder"},{default:i((()=>[n(I,{src:"/h5/static/code/qrcode-placeholder.png",class:"qrcode-image",mode:"aspectFit"})])),_:1})])),_:1})])),_:1})])),_:1}),n(B,{class:"save-image-btn"},{default:i((()=>[n(E,{class:"download-icon"},{default:i((()=>[_("↓")])),_:1}),n(E,null,{default:i((()=>[_("保存图片")])),_:1})])),_:1})])),_:1}),n(B,{class:"settings-card"},{default:i((()=>[n(B,{class:"settings-title"},{default:i((()=>[_("收款码设置")])),_:1}),n(B,{class:"settings-list"},{default:i((()=>[n(B,{class:"setting-item",onClick:o.showEditNameModal},{default:i((()=>[n(B,{class:"setting-left"},{default:i((()=>[n(E,{class:"setting-icon tag-icon"},{default:i((()=>[_("🏷️")])),_:1}),n(E,{class:"setting-label"},{default:i((()=>[_("收款码名称")])),_:1})])),_:1}),n(B,{class:"setting-right"},{default:i((()=>[n(E,{class:"setting-value"},{default:i((()=>[_(h(s.paymentCodeName),1)])),_:1}),n(E,{class:"arrow"},{default:i((()=>[_(">")])),_:1})])),_:1})])),_:1},8,["onClick"]),n(B,{class:"setting-item",onClick:o.showStaffModal},{default:i((()=>[n(B,{class:"setting-left"},{default:i((()=>[n(E,{class:"setting-icon person-icon"},{default:i((()=>[_("👤")])),_:1}),n(E,{class:"setting-label"},{default:i((()=>[_("绑定员工")])),_:1})])),_:1}),n(B,{class:"setting-right"},{default:i((()=>[n(E,{class:"setting-value"},{default:i((()=>{var e;return[_(h((null==(e=s.selectedStaff)?void 0:e.name)||"未绑定"),1)]})),_:1}),n(E,{class:"arrow"},{default:i((()=>[_(">")])),_:1})])),_:1})])),_:1},8,["onClick"])])),_:1}),n(B,{class:"save-button"},{default:i((()=>[n(E,{class:"save-icon"},{default:i((()=>[_("💾")])),_:1}),n(E,null,{default:i((()=>[_("保存")])),_:1})])),_:1})])),_:1}),n(B,{class:"settings-card generated-codes-card"},{default:i((()=>[n(B,{class:"codes-header"},{default:i((()=>[n(E,null,{default:i((()=>[_("已生成收款码")])),_:1}),n(B,{class:"code-actions-container"},{default:i((()=>[n(E,{class:"bind-code-btn",onClick:o.showBindEmptyCodeModal},{default:i((()=>[_("绑定空码")])),_:1},8,["onClick"]),n(E,{class:"create-code-btn",onClick:o.createNewCode},{default:i((()=>[_("+ 创建收款码")])),_:1},8,["onClick"])])),_:1})])),_:1}),n(B,{class:"code-list"},{default:i((()=>[(C(!0),c(m,null,p(s.generatedCodes,((e,a)=>(C(),f(B,{class:b(["code-item","bg-"+e.color]),key:a},{default:i((()=>[n(B,{class:"code-item-left"},{default:i((()=>[n(B,{class:"qr-icon-box"},{default:i((()=>[n(N,{type:"qrcode",size:"20",color:"#5145F7"})])),_:1}),n(B,{class:"code-info"},{default:i((()=>[n(E,{class:"code-name"},{default:i((()=>[_(h(e.name),1)])),_:2},1024),n(E,{class:"code-date"},{default:i((()=>[_(h(e.position)+" | "+h(e.date),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),n(B,{class:"code-actions"},{default:i((()=>[n(B,{class:"action-btn edit-btn",onClick:a=>o.editCode(e)},{default:i((()=>[n(N,{type:"compose",size:"18",color:"#666666"})])),_:2},1032,["onClick"]),n(B,{class:"action-btn delete-btn",onClick:a=>o.deleteCode(e)},{default:i((()=>[n(N,{type:"trash",size:"18",color:"#666666"})])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1032,["class"])))),128)),n(B,{class:"empty-code-entry",onClick:o.showBindEmptyCodeModal},{default:i((()=>[n(B,{class:"empty-code-icon"},{default:i((()=>[n(N,{type:"scan",size:"24",color:"#FFFFFF"})])),_:1}),n(B,{class:"empty-code-text"},{default:i((()=>[n(E,null,{default:i((()=>[_("扫描并绑定空码")])),_:1}),n(E,{class:"empty-code-desc"},{default:i((()=>[_("快速绑定预制收款码")])),_:1})])),_:1}),n(N,{type:"right",size:"16",color:"#CCCCCC"})])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1}),s.showNameModal?(C(),f(B,{key:0,class:"modal"},{default:i((()=>[n(B,{class:"modal-mask",onClick:o.cancelEditName},null,8,["onClick"]),n(B,{class:"modal-content"},{default:i((()=>[n(B,{class:"modal-header"},{default:i((()=>[n(E,null,{default:i((()=>[_("修改收款码名称")])),_:1}),n(E,{class:"close-icon",onClick:o.cancelEditName},{default:i((()=>[_("×")])),_:1},8,["onClick"])])),_:1}),n(B,{class:"modal-body"},{default:i((()=>[n(x,{class:"name-input",modelValue:s.tempPaymentCodeName,"onUpdate:modelValue":a[0]||(a[0]=e=>s.tempPaymentCodeName=e),placeholder:"请输入收款码名称",maxlength:"20"},null,8,["modelValue"])])),_:1}),n(B,{class:"modal-footer"},{default:i((()=>[n(L,{class:"cancel-btn",onClick:o.cancelEditName},{default:i((()=>[_("取消")])),_:1},8,["onClick"]),n(L,{class:"confirm-btn",onClick:o.confirmEditName},{default:i((()=>[_("确定")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})):u("",!0),s.showStaffListModal?(C(),f(B,{key:1,class:"modal"},{default:i((()=>[n(B,{class:"modal-mask",onClick:o.cancelSelectStaff},null,8,["onClick"]),n(B,{class:"modal-content staff-modal"},{default:i((()=>[n(B,{class:"modal-header"},{default:i((()=>[n(E,null,{default:i((()=>[_("选择员工")])),_:1}),n(E,{class:"close-icon",onClick:o.cancelSelectStaff},{default:i((()=>[_("×")])),_:1},8,["onClick"])])),_:1}),n(B,{class:"modal-body staff-list"},{default:i((()=>[(C(!0),c(m,null,p(s.staffList,((e,a)=>(C(),f(B,{class:"staff-item",key:a,onClick:a=>o.selectStaff(e)},{default:i((()=>[n(B,{class:"staff-item-left"},{default:i((()=>[n(B,{class:b(["staff-avatar","bg-"+e.color])},{default:i((()=>[_(h(e.name.charAt(0)),1)])),_:2},1032,["class"]),n(B,{class:"staff-detail"},{default:i((()=>[n(E,{class:"staff-name"},{default:i((()=>[_(h(e.name),1)])),_:2},1024),n(E,{class:"staff-role"},{default:i((()=>[_(h(e.role),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),n(B,{class:b(["staff-checkbox",{checked:s.tempSelectedStaffId===e.id}])},null,8,["class"])])),_:2},1032,["onClick"])))),128))])),_:1}),n(B,{class:"modal-footer"},{default:i((()=>[n(L,{class:"cancel-btn",onClick:o.cancelSelectStaff},{default:i((()=>[_("取消")])),_:1},8,["onClick"]),n(L,{class:"confirm-btn",onClick:o.confirmSelectStaff},{default:i((()=>[_("确定")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})):u("",!0),s.showBindCodeModal?(C(),f(B,{key:2,class:"modal"},{default:i((()=>[n(B,{class:"modal-mask",onClick:o.cancelBindCode},null,8,["onClick"]),n(B,{class:"modal-content bind-code-modal"},{default:i((()=>[n(B,{class:"modal-header"},{default:i((()=>[n(E,null,{default:i((()=>[_("绑定空码")])),_:1}),n(E,{class:"close-icon",onClick:o.cancelBindCode},{default:i((()=>[_("×")])),_:1},8,["onClick"])])),_:1}),n(B,{class:"modal-body"},{default:i((()=>[n(B,{class:"bind-code-options"},{default:i((()=>[n(B,{class:"bind-option",onClick:o.scanBindCode},{default:i((()=>[n(B,{class:"bind-option-icon scan-icon"},{default:i((()=>[n(N,{type:"scan",size:"28",color:"#FFFFFF"})])),_:1}),n(E,{class:"bind-option-text"},{default:i((()=>[_("扫描空码")])),_:1}),n(E,{class:"bind-option-desc"},{default:i((()=>[_("扫描预制码快速绑定")])),_:1})])),_:1},8,["onClick"]),n(B,{class:"bind-option",onClick:o.enterCodeManually},{default:i((()=>[n(B,{class:"bind-option-icon manual-icon"},{default:i((()=>[n(N,{type:"compose",size:"28",color:"#FFFFFF"})])),_:1}),n(E,{class:"bind-option-text"},{default:i((()=>[_("手动输入")])),_:1}),n(E,{class:"bind-option-desc"},{default:i((()=>[_("输入空码编号进行绑定")])),_:1})])),_:1},8,["onClick"])])),_:1}),e.showManualInput?(C(),f(B,{key:0,class:"code-manual-input"},{default:i((()=>[n(x,{class:"code-input",modelValue:e.manualCodeInput,"onUpdate:modelValue":a[1]||(a[1]=a=>e.manualCodeInput=a),placeholder:"请输入空码编号",maxlength:"20"},null,8,["modelValue"]),n(L,{class:"verify-btn",onClick:o.verifyCode},{default:i((()=>[_("验证")])),_:1},8,["onClick"])])),_:1})):u("",!0),n(B,{class:"code-tips"},{default:i((()=>[n(E,{class:"tips-title"},{default:i((()=>[_("空码说明：")])),_:1}),n(E,{class:"tips-content"},{default:i((()=>[_("空码是指未绑定商家信息的收款码，通过绑定空码可快速生成新的收款码，无需等待制作。")])),_:1})])),_:1})])),_:1}),e.showManualInput?u("",!0):(C(),f(B,{key:0,class:"modal-footer"},{default:i((()=>[n(L,{class:"cancel-btn",onClick:o.cancelBindCode},{default:i((()=>[_("取消")])),_:1},8,["onClick"]),n(L,{class:"confirm-btn",onClick:o.scanBindCode},{default:i((()=>[_("开始扫码")])),_:1},8,["onClick"])])),_:1}))])),_:1})])),_:1})):u("",!0)],64)}],["__scopeId","data-v-8efebff4"]]);export{B as default};
