<?php
/**
 * 预制收款码管理 - 最简版本
 */
include("../includes/common.php");
$title='预制收款码管理';
include './head.php';
if($islogin==1){}else exit("<script language='javascript'>window.location.href='./login.php';</script>");

// 关闭所有错误显示
error_reporting(0);
ini_set('display_errors', 0);

$msg = '';

// 检查并创建表
try {
    $DB->exec("CREATE TABLE IF NOT EXISTS `{$dbconfig['dbqz']}_precode` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `code` varchar(50) NOT NULL COMMENT '预制码',
        `qr_url` text COMMENT '完整的二维码URL',
        `uid` int(11) DEFAULT NULL COMMENT '绑定的商户ID',
        `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0未绑定 1已绑定',
        `addtime` datetime DEFAULT NULL COMMENT '生成时间',
        `bindtime` datetime DEFAULT NULL COMMENT '绑定时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `code` (`code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预制收款码';");
} catch(Exception $e) {
    // 忽略错误
}

// 处理生成请求 - 使用GET方式避免JSON问题
if(isset($_GET['generate'])) {
    $num = isset($_GET['num']) ? intval($_GET['num']) : 1;
    if($num < 1 || $num > 100) {
        $msg = '生成数量必须在1-100之间';
    } else {
        $success = 0;
        $codes = array();
        
        try {
            for($i = 0; $i < $num; $i++) {
                $code = generatePreCode();
                if($code) {
                    $qr_url = $siteurl . 'paypage/precode/' . $code;
                    $result = $DB->exec("INSERT INTO `{$dbconfig['dbqz']}_precode` (`code`, `qr_url`, `addtime`) VALUES (?, ?, NOW())", [$code, $qr_url]);
                    if($result) {
                        $success++;
                        $codes[] = $code;
                    }
                }
            }
            
            if($success > 0) {
                $msg = "成功生成 {$success} 个预制码：" . implode(', ', $codes);
            } else {
                $msg = '生成失败，请检查系统配置';
            }
        } catch(Exception $e) {
            $msg = '生成失败：' . $e->getMessage();
        }
    }
}

// 处理删除
if(isset($_GET['delete'])) {
    $id = intval($_GET['delete']);
    try {
        $row = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_precode` WHERE `id`=? LIMIT 1", [$id]);
        if($row && $row['status'] == 0) {
            $result = $DB->exec("DELETE FROM `{$dbconfig['dbqz']}_precode` WHERE `id`=?", [$id]);
            $msg = $result ? '删除成功' : '删除失败';
        } else {
            $msg = '无法删除：预制码不存在或已绑定';
        }
    } catch(Exception $e) {
        $msg = '删除失败：' . $e->getMessage();
    }
}

// 获取数据
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$pagesize = 20;
$offset = ($page - 1) * $pagesize;

try {
    $numrows = $DB->getColumn("SELECT COUNT(*) from `{$dbconfig['dbqz']}_precode`");
    $pages = ceil($numrows / $pagesize);
    $list = $DB->getAll("SELECT a.*,b.username FROM `{$dbconfig['dbqz']}_precode` a LEFT JOIN `{$dbconfig['dbqz']}_user` b ON a.uid=b.uid ORDER BY a.id DESC LIMIT $offset,$pagesize");
} catch(Exception $e) {
    $numrows = 0;
    $pages = 0;
    $list = [];
    $msg = '数据加载失败：' . $e->getMessage();
}
?>

<div class="container" style="padding-top:70px;">
<div class="col-md-12 center-block" style="float: none;">
<div class="panel panel-primary">
<div class="panel-heading"><h3 class="panel-title">预制收款码管理</h3></div>
<div class="panel-body">

<?php if($msg): ?>
<div class="alert alert-info"><?php echo htmlspecialchars($msg); ?></div>
<?php endif; ?>

<!-- 生成表单 -->
<form method="GET" class="form-inline" style="margin-bottom: 20px;">
    <div class="form-group">
        <label>生成数量：</label>
        <input type="number" name="num" min="1" max="100" value="10" class="form-control" style="width:80px;">
    </div>
    <button type="submit" name="generate" value="1" class="btn btn-success">批量生成</button>
    <span class="pull-right">共有 <?php echo $numrows; ?> 条记录</span>
</form>

<div class="table-responsive">
<table class="table table-striped table-bordered">
<thead>
    <tr>
        <th>ID</th>
        <th>预制收款码</th>
        <th>绑定商户</th>
        <th>生成时间</th>
        <th>状态</th>
        <th>操作</th>
    </tr>
</thead>
<tbody>
<?php if($list): ?>
    <?php foreach($list as $row): ?>
    <tr>
        <td><?php echo $row['id']; ?></td>
        <td><strong><?php echo htmlspecialchars($row['code']); ?></strong></td>
        <td><?php echo $row['username'] ? htmlspecialchars($row['username']) : '未绑定'; ?></td>
        <td><?php echo $row['addtime']; ?></td>
        <td>
            <?php if($row['status'] == 1): ?>
                <span class="label label-success">已绑定</span>
            <?php else: ?>
                <span class="label label-default">未绑定</span>
            <?php endif; ?>
        </td>
        <td>
            <button class="btn btn-xs btn-info" onclick="copyToClipboard('<?php echo $row['code']; ?>')">复制</button>
            <a href="<?php echo $row['qr_url']; ?>" target="_blank" class="btn btn-xs btn-success">查看</a>
            <?php if($row['status'] == 0): ?>
                <a href="?delete=<?php echo $row['id']; ?>" class="btn btn-xs btn-danger" onclick="return confirm('确定要删除预制码 <?php echo $row['code']; ?> 吗？')">删除</a>
            <?php endif; ?>
        </td>
    </tr>
    <?php endforeach; ?>
<?php else: ?>
    <tr><td colspan="6" class="text-center">暂无记录</td></tr>
<?php endif; ?>
</tbody>
</table>
</div>

<!-- 简单分页 -->
<?php if($pages > 1): ?>
<nav>
    <ul class="pagination">
        <?php for($i = 1; $i <= $pages; $i++): ?>
            <li <?php echo $i == $page ? 'class="active"' : ''; ?>>
                <a href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
            </li>
        <?php endfor; ?>
    </ul>
</nav>
<?php endif; ?>

</div>
</div>
</div>
</div>

<script>
function copyToClipboard(text) {
    // 创建临时输入框
    var temp = document.createElement('input');
    temp.value = text;
    document.body.appendChild(temp);
    temp.select();
    
    try {
        var successful = document.execCommand('copy');
        if(successful) {
            alert('复制成功：' + text);
        } else {
            alert('复制失败，请手动复制：' + text);
        }
    } catch(err) {
        alert('复制失败，请手动复制：' + text);
    }
    
    document.body.removeChild(temp);
}
</script>

</body>
</html>
