import{G as t,H as e,J as i,K as a,L as o,A as n,B as r,a as s,E as l,M as c,N as h,r as d,O as p,s as u,g,b as f,P as x,Q as m,R as y,n as v,S as b,q as S,c as w,T,U as _,V as A,W as C,o as P,e as k,w as D,f as I,i as L,v as M,j as F,h as O,t as E,X as R,Y as N,Z as B,_ as z,$ as U,d as W,a0 as q,a1 as j,a2 as $,a3 as H,z as G,C as K,D as J,F as X,k as V,l as Y,a4 as Q}from"./index-B1Q521gi.js";import{_ as Z}from"./custom-navbar.DuzuSmPc.js";import{r as tt}from"./uni-app.es.DAfa8VxY.js";import{_ as et}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as it}from"./share.DVl4BbzX.js";const at={pages:[{path:"pages/login/index",style:{navigationBarTitleText:"登录",navigationStyle:"custom"}},{path:"pages/index/index",style:{navigationBarTitleText:"商家收款",navigationStyle:"custom"}},{path:"pages/code/index",style:{navigationBarTitleText:"收款码",navigationStyle:"custom"}},{path:"pages/bill/index",style:{navigationBarTitleText:"交易记录",navigationStyle:"custom"}},{path:"pages/scan/index",style:{navigationBarTitleText:"一码通",navigationStyle:"custom"}},{path:"pages/report/index",style:{navigationBarTitleText:"报表",navigationStyle:"custom"}},{path:"pages/report/diamond",style:{navigationBarTitleText:"多维数据分析",navigationStyle:"custom"}},{path:"pages/mine/index",style:{navigationBarTitleText:"我的",navigationStyle:"custom"}},{path:"pages/wallet/index",style:{navigationBarTitleText:"我的钱包",navigationStyle:"custom"}},{path:"pages/wallet/bank-cards",style:{navigationBarTitleText:"银行卡管理",navigationStyle:"custom"}},{path:"pages/merchant/index",style:{navigationBarTitleText:"商家信息",navigationStyle:"custom"}},{path:"pages/duizhang/index",style:{navigationBarTitleText:"对账单",navigationStyle:"custom"}},{path:"pages/jiesuan/index",style:{navigationBarTitleText:"结算记录",navigationStyle:"custom"}},{path:"pages/xiaoxi/index",style:{navigationBarTitleText:"消息通知",navigationStyle:"custom"}},{path:"pages/yuangong/index",style:{navigationBarTitleText:"员工管理",navigationStyle:"custom"}},{path:"pages/yingxiao/index",style:{navigationBarTitleText:"营销工具",navigationStyle:"custom"}},{path:"pages/bill/date-range",style:{navigationBarTitleText:"日期范围选择",navigationStyle:"custom"}},{path:"pages/pay/mini-payment",style:{navigationBarTitleText:"收银台",navigationStyle:"custom"}},{path:"pages/pay/result",style:{navigationBarTitleText:"支付结果",navigationStyle:"custom"}},{path:"pages_A/user/index",style:{navigationBarTitleText:"会员中心",navigationStyle:"custom"}}],globalStyle:{navigationBarTextStyle:"white",navigationBarTitleText:"商家收款",navigationBarBackgroundColor:"#5145F7",backgroundColor:"#F8F8F8"},easycom:{autoscan:!0,custom:{"^uni-(.*)":"@/uni_modules/uni-$1/components/uni-$1/uni-$1.vue","^custom-(.*)":"@/components/custom-$1.vue"}},tabBar:{custom:!1,color:"#999999",selectedColor:"#5145F7",backgroundColor:"#FFFFFF",position:"bottom",height:"50px",list:[{pagePath:"pages/index/index",text:"首页",iconPath:"static/tab/home.png",selectedIconPath:"static/tab/home-active.png"},{pagePath:"pages/bill/index",text:"账单",iconPath:"static/tab/bill.png",selectedIconPath:"static/tab/bill-active.png"},{pagePath:"pages/scan/index",text:"一码通",iconPath:"static/tab/code.png",selectedIconPath:"static/tab/code-active.png"},{pagePath:"pages/report/index",text:"报表",iconPath:"static/tab/report.png",selectedIconPath:"static/tab/report-active.png"},{pagePath:"pages/mine/index",text:"我的",iconPath:"static/tab/mine.png",selectedIconPath:"static/tab/mine-active.png"}]},uniIdRouter:{}};function ot(t,e,i){return t(i={path:e,exports:{},require:function(t,e){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==e&&i.path)}},i.exports),i.exports}var nt=ot((function(t,e){var i;t.exports=(i=i||function(t,e){var i=Object.create||function(){function t(){}return function(e){var i;return t.prototype=e,i=new t,t.prototype=null,i}}(),a={},o=a.lib={},n=o.Base={extend:function(t){var e=i(this);return t&&e.mixIn(t),e.hasOwnProperty("init")&&this.init!==e.init||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},r=o.WordArray=n.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:4*t.length},toString:function(t){return(t||l).stringify(this)},concat:function(t){var e=this.words,i=t.words,a=this.sigBytes,o=t.sigBytes;if(this.clamp(),a%4)for(var n=0;n<o;n++){var r=i[n>>>2]>>>24-n%4*8&255;e[a+n>>>2]|=r<<24-(a+n)%4*8}else for(n=0;n<o;n+=4)e[a+n>>>2]=i[n>>>2];return this.sigBytes+=o,this},clamp:function(){var e=this.words,i=this.sigBytes;e[i>>>2]&=4294967295<<32-i%4*8,e.length=t.ceil(i/4)},clone:function(){var t=n.clone.call(this);return t.words=this.words.slice(0),t},random:function(e){for(var i,a=[],o=function(e){var i=987654321,a=4294967295;return function(){var o=((i=36969*(65535&i)+(i>>16)&a)<<16)+(e=18e3*(65535&e)+(e>>16)&a)&a;return o/=4294967296,(o+=.5)*(t.random()>.5?1:-1)}},n=0;n<e;n+=4){var s=o(4294967296*(i||t.random()));i=987654071*s(),a.push(4294967296*s()|0)}return new r.init(a,e)}}),s=a.enc={},l=s.Hex={stringify:function(t){for(var e=t.words,i=t.sigBytes,a=[],o=0;o<i;o++){var n=e[o>>>2]>>>24-o%4*8&255;a.push((n>>>4).toString(16)),a.push((15&n).toString(16))}return a.join("")},parse:function(t){for(var e=t.length,i=[],a=0;a<e;a+=2)i[a>>>3]|=parseInt(t.substr(a,2),16)<<24-a%8*4;return new r.init(i,e/2)}},c=s.Latin1={stringify:function(t){for(var e=t.words,i=t.sigBytes,a=[],o=0;o<i;o++){var n=e[o>>>2]>>>24-o%4*8&255;a.push(String.fromCharCode(n))}return a.join("")},parse:function(t){for(var e=t.length,i=[],a=0;a<e;a++)i[a>>>2]|=(255&t.charCodeAt(a))<<24-a%4*8;return new r.init(i,e)}},h=s.Utf8={stringify:function(t){try{return decodeURIComponent(escape(c.stringify(t)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(t){return c.parse(unescape(encodeURIComponent(t)))}},d=o.BufferedBlockAlgorithm=n.extend({reset:function(){this._data=new r.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=h.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var i=this._data,a=i.words,o=i.sigBytes,n=this.blockSize,s=o/(4*n),l=(s=e?t.ceil(s):t.max((0|s)-this._minBufferSize,0))*n,c=t.min(4*l,o);if(l){for(var h=0;h<l;h+=n)this._doProcessBlock(a,h);var d=a.splice(0,l);i.sigBytes-=c}return new r.init(d,c)},clone:function(){var t=n.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0});o.Hasher=d.extend({cfg:n.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,i){return new t.init(i).finalize(e)}},_createHmacHelper:function(t){return function(e,i){return new p.HMAC.init(t,i).finalize(e)}}});var p=a.algo={};return a}(Math),i)})),rt=nt,st=(ot((function(t,e){var i;t.exports=(i=rt,function(t){var e=i,a=e.lib,o=a.WordArray,n=a.Hasher,r=e.algo,s=[];!function(){for(var e=0;e<64;e++)s[e]=4294967296*t.abs(t.sin(e+1))|0}();var l=r.MD5=n.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,e){for(var i=0;i<16;i++){var a=e+i,o=t[a];t[a]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var n=this._hash.words,r=t[e+0],l=t[e+1],u=t[e+2],g=t[e+3],f=t[e+4],x=t[e+5],m=t[e+6],y=t[e+7],v=t[e+8],b=t[e+9],S=t[e+10],w=t[e+11],T=t[e+12],_=t[e+13],A=t[e+14],C=t[e+15],P=n[0],k=n[1],D=n[2],I=n[3];P=c(P,k,D,I,r,7,s[0]),I=c(I,P,k,D,l,12,s[1]),D=c(D,I,P,k,u,17,s[2]),k=c(k,D,I,P,g,22,s[3]),P=c(P,k,D,I,f,7,s[4]),I=c(I,P,k,D,x,12,s[5]),D=c(D,I,P,k,m,17,s[6]),k=c(k,D,I,P,y,22,s[7]),P=c(P,k,D,I,v,7,s[8]),I=c(I,P,k,D,b,12,s[9]),D=c(D,I,P,k,S,17,s[10]),k=c(k,D,I,P,w,22,s[11]),P=c(P,k,D,I,T,7,s[12]),I=c(I,P,k,D,_,12,s[13]),D=c(D,I,P,k,A,17,s[14]),P=h(P,k=c(k,D,I,P,C,22,s[15]),D,I,l,5,s[16]),I=h(I,P,k,D,m,9,s[17]),D=h(D,I,P,k,w,14,s[18]),k=h(k,D,I,P,r,20,s[19]),P=h(P,k,D,I,x,5,s[20]),I=h(I,P,k,D,S,9,s[21]),D=h(D,I,P,k,C,14,s[22]),k=h(k,D,I,P,f,20,s[23]),P=h(P,k,D,I,b,5,s[24]),I=h(I,P,k,D,A,9,s[25]),D=h(D,I,P,k,g,14,s[26]),k=h(k,D,I,P,v,20,s[27]),P=h(P,k,D,I,_,5,s[28]),I=h(I,P,k,D,u,9,s[29]),D=h(D,I,P,k,y,14,s[30]),P=d(P,k=h(k,D,I,P,T,20,s[31]),D,I,x,4,s[32]),I=d(I,P,k,D,v,11,s[33]),D=d(D,I,P,k,w,16,s[34]),k=d(k,D,I,P,A,23,s[35]),P=d(P,k,D,I,l,4,s[36]),I=d(I,P,k,D,f,11,s[37]),D=d(D,I,P,k,y,16,s[38]),k=d(k,D,I,P,S,23,s[39]),P=d(P,k,D,I,_,4,s[40]),I=d(I,P,k,D,r,11,s[41]),D=d(D,I,P,k,g,16,s[42]),k=d(k,D,I,P,m,23,s[43]),P=d(P,k,D,I,b,4,s[44]),I=d(I,P,k,D,T,11,s[45]),D=d(D,I,P,k,C,16,s[46]),P=p(P,k=d(k,D,I,P,u,23,s[47]),D,I,r,6,s[48]),I=p(I,P,k,D,y,10,s[49]),D=p(D,I,P,k,A,15,s[50]),k=p(k,D,I,P,x,21,s[51]),P=p(P,k,D,I,T,6,s[52]),I=p(I,P,k,D,g,10,s[53]),D=p(D,I,P,k,S,15,s[54]),k=p(k,D,I,P,l,21,s[55]),P=p(P,k,D,I,v,6,s[56]),I=p(I,P,k,D,C,10,s[57]),D=p(D,I,P,k,m,15,s[58]),k=p(k,D,I,P,_,21,s[59]),P=p(P,k,D,I,f,6,s[60]),I=p(I,P,k,D,w,10,s[61]),D=p(D,I,P,k,u,15,s[62]),k=p(k,D,I,P,b,21,s[63]),n[0]=n[0]+P|0,n[1]=n[1]+k|0,n[2]=n[2]+D|0,n[3]=n[3]+I|0},_doFinalize:function(){var e=this._data,i=e.words,a=8*this._nDataBytes,o=8*e.sigBytes;i[o>>>5]|=128<<24-o%32;var n=t.floor(a/4294967296),r=a;i[15+(o+64>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),i[14+(o+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),e.sigBytes=4*(i.length+1),this._process();for(var s=this._hash,l=s.words,c=0;c<4;c++){var h=l[c];l[c]=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8)}return s},clone:function(){var t=n.clone.call(this);return t._hash=this._hash.clone(),t}});function c(t,e,i,a,o,n,r){var s=t+(e&i|~e&a)+o+r;return(s<<n|s>>>32-n)+e}function h(t,e,i,a,o,n,r){var s=t+(e&a|i&~a)+o+r;return(s<<n|s>>>32-n)+e}function d(t,e,i,a,o,n,r){var s=t+(e^i^a)+o+r;return(s<<n|s>>>32-n)+e}function p(t,e,i,a,o,n,r){var s=t+(i^(e|~a))+o+r;return(s<<n|s>>>32-n)+e}e.MD5=n._createHelper(l),e.HmacMD5=n._createHmacHelper(l)}(Math),i.MD5)})),ot((function(t,e){var i,a,o;t.exports=(a=(i=rt).lib.Base,o=i.enc.Utf8,void(i.algo.HMAC=a.extend({init:function(t,e){t=this._hasher=new t.init,"string"==typeof e&&(e=o.parse(e));var i=t.blockSize,a=4*i;e.sigBytes>a&&(e=t.finalize(e)),e.clamp();for(var n=this._oKey=e.clone(),r=this._iKey=e.clone(),s=n.words,l=r.words,c=0;c<i;c++)s[c]^=1549556828,l[c]^=909522486;n.sigBytes=r.sigBytes=a,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher,i=e.finalize(t);return e.reset(),e.finalize(this._oKey.clone().concat(i))}})))})),ot((function(t,e){t.exports=rt.HmacMD5}))),lt=ot((function(t,e){t.exports=rt.enc.Utf8})),ct=ot((function(t,e){var i,a,o;t.exports=(o=(a=i=rt).lib.WordArray,a.enc.Base64={stringify:function(t){var e=t.words,i=t.sigBytes,a=this._map;t.clamp();for(var o=[],n=0;n<i;n+=3)for(var r=(e[n>>>2]>>>24-n%4*8&255)<<16|(e[n+1>>>2]>>>24-(n+1)%4*8&255)<<8|e[n+2>>>2]>>>24-(n+2)%4*8&255,s=0;s<4&&n+.75*s<i;s++)o.push(a.charAt(r>>>6*(3-s)&63));var l=a.charAt(64);if(l)for(;o.length%4;)o.push(l);return o.join("")},parse:function(t){var e=t.length,i=this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var n=0;n<i.length;n++)a[i.charCodeAt(n)]=n}var r=i.charAt(64);if(r){var s=t.indexOf(r);-1!==s&&(e=s)}return function(t,e,i){for(var a=[],n=0,r=0;r<e;r++)if(r%4){var s=i[t.charCodeAt(r-1)]<<r%4*2,l=i[t.charCodeAt(r)]>>>6-r%4*2;a[n>>>2]|=(s|l)<<24-n%4*8,n++}return o.create(a,n)}(t,e,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},i.enc.Base64)}));const ht="FUNCTION",dt="pending",pt="rejected";function ut(t){return Object.prototype.toString.call(t).slice(8,-1).toLowerCase()}function gt(t){return"object"===ut(t)}function ft(t){return"function"==typeof t}function xt(t){return function(){try{return t.apply(t,arguments)}catch(e){console.error(e)}}}const mt="REJECTED",yt="NOT_PENDING";class vt{constructor({createPromise:t,retryRule:e=mt}={}){this.createPromise=t,this.status=null,this.promise=null,this.retryRule=e}get needRetry(){if(!this.status)return!0;switch(this.retryRule){case mt:return this.status===pt;case yt:return this.status!==dt}}exec(){return this.needRetry?(this.status=dt,this.promise=this.createPromise().then((t=>(this.status="fulfilled",Promise.resolve(t))),(t=>(this.status=pt,Promise.reject(t)))),this.promise):this.promise}}function bt(t){return t&&"string"==typeof t?JSON.parse(t):t}const St=bt([]);bt("");const wt=bt("[]")||[];let Tt="";try{Tt="__UNI__41DB369"}catch(pe){}let _t,At={};function Ct(t,e={}){var i,a;return i=At,a=t,Object.prototype.hasOwnProperty.call(i,a)||(At[t]=e),At[t]}const Pt=["invoke","success","fail","complete"],kt=Ct("_globalUniCloudInterceptor");function Dt(t,e){kt[t]||(kt[t]={}),gt(e)&&Object.keys(e).forEach((i=>{Pt.indexOf(i)>-1&&function(t,e,i){let a=kt[t][e];a||(a=kt[t][e]=[]),-1===a.indexOf(i)&&ft(i)&&a.push(i)}(t,i,e[i])}))}function It(t,e){kt[t]||(kt[t]={}),gt(e)?Object.keys(e).forEach((i=>{Pt.indexOf(i)>-1&&function(t,e,i){const a=kt[t][e];if(!a)return;const o=a.indexOf(i);o>-1&&a.splice(o,1)}(t,i,e[i])})):delete kt[t]}function Lt(t,e){return t&&0!==t.length?t.reduce(((t,i)=>t.then((()=>i(e)))),Promise.resolve()):Promise.resolve()}function Mt(t,e){return kt[t]&&kt[t][e]||[]}function Ft(t){Dt("callObject",t)}const Ot=Ct("_globalUniCloudListener"),Et="response",Rt="needLogin",Nt="refreshToken",Bt="clientdb",zt="cloudfunction",Ut="cloudobject";function Wt(t){return Ot[t]||(Ot[t]=[]),Ot[t]}function qt(t,e){const i=Wt(t);i.includes(e)||i.push(e)}function jt(t,e){const i=Wt(t),a=i.indexOf(e);-1!==a&&i.splice(a,1)}function $t(t,e){const i=Wt(t);for(let a=0;a<i.length;a++)(0,i[a])(e)}let Ht,Gt=!1;function Kt(){return Ht||(Ht=new Promise((t=>{Gt&&t(),function e(){if("function"==typeof i){const e=i();e&&e[0]&&(Gt=!0,t())}Gt||setTimeout((()=>{e()}),30)}()})),Ht)}function Jt(t){const e={};for(const i in t){const a=t[i];ft(a)&&(e[i]=xt(a))}return e}class Xt extends Error{constructor(t){super(t.message),this.errMsg=t.message||t.errMsg||"unknown system error",this.code=this.errCode=t.code||t.errCode||"SYSTEM_ERROR",this.errSubject=this.subject=t.subject||t.errSubject,this.cause=t.cause,this.requestId=t.requestId}toJson(t=0){if(!(t>=10))return t++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(t):this.cause}}}var Vt={request:t=>d(t),uploadFile:t=>p(t),setStorageSync:(t,e)=>u(t,e),getStorageSync:t=>g(t),removeStorageSync:t=>f(t),clearStorageSync:()=>x(),connectSocket:t=>m(t)};function Yt(t){return t&&Yt(t.__v_raw)||t}function Qt(){return{token:Vt.getStorageSync("uni_id_token")||Vt.getStorageSync("uniIdToken"),tokenExpired:Vt.getStorageSync("uni_id_token_expired")}}function Zt({token:t,tokenExpired:e}={}){t&&Vt.setStorageSync("uni_id_token",t),e&&Vt.setStorageSync("uni_id_token_expired",e)}let te,ee;function ie(){return te||(te=y()),te}function ae(){let t,e;try{if(C){if(C.toString().indexOf("not yet implemented")>-1)return;const{scene:i,channel:a}=C();t=a,e=i}}catch(i){}return{channel:t,scene:e}}let oe={};function ne(){const t=A&&A()||"en";if(ee)return{...oe,...ee,locale:t,LOCALE:t};const e=ie(),{deviceId:i,osName:a,uniPlatform:o,appId:n}=e,r=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(const s in e)Object.hasOwnProperty.call(e,s)&&-1===r.indexOf(s)&&delete e[s];return ee={PLATFORM:o,OS:a,APPID:n,DEVICEID:i,...ae(),...e},{...oe,...ee,locale:t,LOCALE:t}}var re=function(t,e){let i="";return Object.keys(t).sort().forEach((function(e){t[e]&&(i=i+"&"+e+"="+t[e])})),i=i.slice(1),st(i,e).toString()},se=function(t,e){return new Promise(((i,a)=>{e(Object.assign(t,{complete(t){t||(t={});const e=t.data&&t.data.header&&t.data.header["x-serverless-request-id"]||t.header&&t.header["request-id"];if(!t.statusCode||t.statusCode>=400){const i=t.data&&t.data.error&&t.data.error.code||"SYS_ERR",o=t.data&&t.data.error&&t.data.error.message||t.errMsg||"request:fail";return a(new Xt({code:i,message:o,requestId:e}))}const o=t.data;if(o.error)return a(new Xt({code:o.error.code,message:o.error.message,requestId:e}));o.result=o.data,o.requestId=e,delete o.data,i(o)}}))}))},le=function(t){return ct.stringify(lt.parse(t))},ce={init(t){const e=new class{constructor(t){["spaceId","clientSecret"].forEach((e=>{if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error(`${e} required`)})),this.config=Object.assign({},{endpoint:0===t.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},t),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=Vt,this._getAccessTokenPromiseHub=new vt({createPromise:()=>this.requestAuth(this.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((t=>{if(!t.result||!t.result.accessToken)throw new Xt({code:"AUTH_FAILED",message:"获取accessToken失败"});this.setAccessToken(t.result.accessToken)})),retryRule:yt})}get hasAccessToken(){return!!this.accessToken}setAccessToken(t){this.accessToken=t}requestWrapped(t){return se(t,this.adapter.request)}requestAuth(t){return this.requestWrapped(t)}request(t,e){return Promise.resolve().then((()=>this.hasAccessToken?e?this.requestWrapped(t):this.requestWrapped(t).catch((e=>new Promise(((t,i)=>{!e||"GATEWAY_INVALID_TOKEN"!==e.code&&"InvalidParameter.InvalidToken"!==e.code?i(e):t()})).then((()=>this.getAccessToken())).then((()=>{const e=this.rebuildRequest(t);return this.request(e,!0)})))):this.getAccessToken().then((()=>{const e=this.rebuildRequest(t);return this.request(e,!0)}))))}rebuildRequest(t){const e=Object.assign({},t);return e.data.token=this.accessToken,e.header["x-basement-token"]=this.accessToken,e.header["x-serverless-sign"]=re(e.data,this.config.clientSecret),e}setupRequest(t,e){const i=Object.assign({},t,{spaceId:this.config.spaceId,timestamp:Date.now()}),a={"Content-Type":"application/json"};return"auth"!==e&&(i.token=this.accessToken,a["x-basement-token"]=this.accessToken),a["x-serverless-sign"]=re(i,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:i,dataType:"json",header:a}}getAccessToken(){return this._getAccessTokenPromiseHub.exec()}async authorize(){await this.getAccessToken()}callFunction(t){const e={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:t.name,functionArgs:t.data||{}})};return this.request({...this.setupRequest(e),timeout:t.timeout})}getOSSUploadOptionsFromPath(t){const e={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(t)};return this.request(this.setupRequest(e))}uploadFileToOSS({url:t,formData:e,name:i,filePath:a,fileType:o,onUploadProgress:n}){return new Promise(((r,s)=>{const l=this.adapter.uploadFile({url:t,formData:e,name:i,filePath:a,fileType:o,header:{"X-OSS-server-side-encrpytion":"AES256"},success(t){t&&t.statusCode<400?r(t):s(new Xt({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(t){s(new Xt({code:t.code||"UPLOAD_FAILED",message:t.message||t.errMsg||"文件上传失败"}))}});"function"==typeof n&&l&&"function"==typeof l.onProgressUpdate&&l.onProgressUpdate((t=>{n({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}reportOSSUpload(t){const e={method:"serverless.file.resource.report",params:JSON.stringify(t)};return this.request(this.setupRequest(e))}async uploadFile({filePath:t,cloudPath:e,fileType:i="image",cloudPathAsRealPath:a=!1,onUploadProgress:o,config:n}){if("string"!==ut(e))throw new Xt({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(e=e.trim()))throw new Xt({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(e))throw new Xt({code:"INVALID_PARAM",message:"cloudPath不合法"});const r=n&&n.envType||this.config.envType;if(a&&("/"!==e[0]&&(e="/"+e),e.indexOf("\\")>-1))throw new Xt({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});const s=(await this.getOSSUploadOptionsFromPath({env:r,filename:a?e.split("/").pop():e,fileId:a?e:void 0})).result,l="https://"+s.cdnDomain+"/"+s.ossPath,{securityToken:c,accessKeyId:h,signature:d,host:p,ossPath:u,id:g,policy:f,ossCallbackUrl:x}=s,m={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:h,Signature:d,host:p,id:g,key:u,policy:f,success_action_status:200};if(c&&(m["x-oss-security-token"]=c),x){const t=JSON.stringify({callbackUrl:x,callbackBody:JSON.stringify({fileId:g,spaceId:this.config.spaceId}),callbackBodyType:"application/json"});m.callback=le(t)}const y={url:"https://"+s.host,formData:m,fileName:"file",name:"file",filePath:t,fileType:i};if(await this.uploadFileToOSS(Object.assign({},y,{onUploadProgress:o})),x)return{success:!0,filePath:t,fileID:l};if((await this.reportOSSUpload({id:g})).success)return{success:!0,filePath:t,fileID:l};throw new Xt({code:"UPLOAD_FAILED",message:"文件上传失败"})}getTempFileURL({fileList:t}={}){return new Promise(((e,i)=>{Array.isArray(t)&&0!==t.length||i(new Xt({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),e({fileList:t.map((t=>({fileID:t,tempFileURL:t})))})}))}async getFileInfo({fileList:t}={}){if(!Array.isArray(t)||0===t.length)throw new Xt({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const e={method:"serverless.file.resource.info",params:JSON.stringify({id:t.map((t=>t.split("?")[0])).join(",")})};return{fileList:(await this.request(this.setupRequest(e))).result}}}(t),i={signInAnonymously:function(){return e.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return e.auth=function(){return i},e.customAuth=e.auth,e}};const he="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";var de,pe;(pe=de||(de={})).local="local",pe.none="none",pe.session="session";var ue=function(){},ge=ot((function(t,e){var i;t.exports=(i=rt,function(t){var e=i,a=e.lib,o=a.WordArray,n=a.Hasher,r=e.algo,s=[],l=[];!function(){function e(e){for(var i=t.sqrt(e),a=2;a<=i;a++)if(!(e%a))return!1;return!0}function i(t){return 4294967296*(t-(0|t))|0}for(var a=2,o=0;o<64;)e(a)&&(o<8&&(s[o]=i(t.pow(a,.5))),l[o]=i(t.pow(a,1/3)),o++),a++}();var c=[],h=r.SHA256=n.extend({_doReset:function(){this._hash=new o.init(s.slice(0))},_doProcessBlock:function(t,e){for(var i=this._hash.words,a=i[0],o=i[1],n=i[2],r=i[3],s=i[4],h=i[5],d=i[6],p=i[7],u=0;u<64;u++){if(u<16)c[u]=0|t[e+u];else{var g=c[u-15],f=(g<<25|g>>>7)^(g<<14|g>>>18)^g>>>3,x=c[u-2],m=(x<<15|x>>>17)^(x<<13|x>>>19)^x>>>10;c[u]=f+c[u-7]+m+c[u-16]}var y=a&o^a&n^o&n,v=(a<<30|a>>>2)^(a<<19|a>>>13)^(a<<10|a>>>22),b=p+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&h^~s&d)+l[u]+c[u];p=d,d=h,h=s,s=r+b|0,r=n,n=o,o=a,a=b+(v+y)|0}i[0]=i[0]+a|0,i[1]=i[1]+o|0,i[2]=i[2]+n|0,i[3]=i[3]+r|0,i[4]=i[4]+s|0,i[5]=i[5]+h|0,i[6]=i[6]+d|0,i[7]=i[7]+p|0},_doFinalize:function(){var e=this._data,i=e.words,a=8*this._nDataBytes,o=8*e.sigBytes;return i[o>>>5]|=128<<24-o%32,i[14+(o+64>>>9<<4)]=t.floor(a/4294967296),i[15+(o+64>>>9<<4)]=a,e.sigBytes=4*i.length,this._process(),this._hash},clone:function(){var t=n.clone.call(this);return t._hash=this._hash.clone(),t}});e.SHA256=n._createHelper(h),e.HmacSHA256=n._createHmacHelper(h)}(Math),i.SHA256)})),fe=ge,xe=ot((function(t,e){t.exports=rt.HmacSHA256}));const me=()=>{let t;if(!Promise){t=()=>{},t.promise={};const e=()=>{throw new Xt({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(t.promise,"then",{get:e}),Object.defineProperty(t.promise,"catch",{get:e}),t}const e=new Promise(((e,i)=>{t=(t,a)=>t?i(t):e(a)}));return t.promise=e,t};function ye(t){return void 0===t}function ve(t){return"[object Null]"===Object.prototype.toString.call(t)}function be(t=""){return t.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function Se(t=32){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",i=e.length;let a="";for(let o=0;o<t;o++)a+=e.charAt(Math.floor(Math.random()*i));return a}var we;!function(t){t.WEB="web",t.WX_MP="wx_mp"}(we||(we={}));const Te={adapter:null,runtime:void 0},_e=["anonymousUuidKey"];class Ae extends ue{constructor(){super(),Te.adapter.root.tcbObject||(Te.adapter.root.tcbObject={})}setItem(t,e){Te.adapter.root.tcbObject[t]=e}getItem(t){return Te.adapter.root.tcbObject[t]}removeItem(t){delete Te.adapter.root.tcbObject[t]}clear(){delete Te.adapter.root.tcbObject}}function Ce(t,e){switch(t){case"local":return e.localStorage||new Ae;case"none":return new Ae;default:return e.sessionStorage||new Ae}}class Pe{constructor(t){if(!this._storage){this._persistence=Te.adapter.primaryStorage||t.persistence,this._storage=Ce(this._persistence,Te.adapter);const e=`access_token_${t.env}`,i=`access_token_expire_${t.env}`,a=`refresh_token_${t.env}`,o=`anonymous_uuid_${t.env}`,n=`login_type_${t.env}`,r="device_id",s=`token_type_${t.env}`,l=`user_info_${t.env}`;this.keys={accessTokenKey:e,accessTokenExpireKey:i,refreshTokenKey:a,anonymousUuidKey:o,loginTypeKey:n,userInfoKey:l,deviceIdKey:r,tokenTypeKey:s}}}updatePersistence(t){if(t===this._persistence)return;const e="local"===this._persistence;this._persistence=t;const i=Ce(t,Te.adapter);for(const a in this.keys){const t=this.keys[a];if(e&&_e.includes(a))continue;const o=this._storage.getItem(t);ye(o)||ve(o)||(i.setItem(t,o),this._storage.removeItem(t))}this._storage=i}setStore(t,e,i){if(!this._storage)return;const a={version:i||"localCachev1",content:e},o=JSON.stringify(a);try{this._storage.setItem(t,o)}catch(n){throw n}}getStore(t,e){try{if(!this._storage)return}catch(a){return""}e=e||"localCachev1";const i=this._storage.getItem(t);return i&&i.indexOf(e)>=0?JSON.parse(i).content:""}removeStore(t){this._storage.removeItem(t)}}const ke={},De={};function Ie(t){return ke[t]}class Le{constructor(t,e){this.data=e||null,this.name=t}}class Me extends Le{constructor(t,e){super("error",{error:t,data:e}),this.error=t}}const Fe=new class{constructor(){this._listeners={}}on(t,e){return i=t,a=e,(o=this._listeners)[i]=o[i]||[],o[i].push(a),this;var i,a,o}off(t,e){return function(t,e,i){if(i&&i[t]){const a=i[t].indexOf(e);-1!==a&&i[t].splice(a,1)}}(t,e,this._listeners),this}fire(t,e){if(t instanceof Me)return console.error(t.error),this;const i="string"==typeof t?new Le(t,e||{}):t,a=i.name;if(this._listens(a)){i.target=this;const t=this._listeners[a]?[...this._listeners[a]]:[];for(const e of t)e.call(this,i)}return this}_listens(t){return this._listeners[t]&&this._listeners[t].length>0}};function Oe(t,e){Fe.on(t,e)}function Ee(t,e={}){Fe.fire(t,e)}function Re(t,e){Fe.off(t,e)}const Ne="loginStateChanged",Be="loginStateExpire",ze="loginTypeChanged",Ue="anonymousConverted",We="refreshAccessToken";var qe;!function(t){t.ANONYMOUS="ANONYMOUS",t.WECHAT="WECHAT",t.WECHAT_PUBLIC="WECHAT-PUBLIC",t.WECHAT_OPEN="WECHAT-OPEN",t.CUSTOM="CUSTOM",t.EMAIL="EMAIL",t.USERNAME="USERNAME",t.NULL="NULL"}(qe||(qe={}));class je{constructor(){this._fnPromiseMap=new Map}async run(t,e){let i=this._fnPromiseMap.get(t);return i||(i=new Promise((async(i,a)=>{try{await this._runIdlePromise();const a=e();i(await a)}catch(o){a(o)}finally{this._fnPromiseMap.delete(t)}})),this._fnPromiseMap.set(t,i)),i}_runIdlePromise(){return Promise.resolve()}}class $e{constructor(t){this._singlePromise=new je,this._cache=Ie(t.env),this._baseURL=`https://${t.env}.ap-shanghai.tcb-api.tencentcloudapi.com`,this._reqClass=new Te.adapter.reqClass({timeout:t.timeout,timeoutMsg:`请求在${t.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]})}_getDeviceId(){if(this._deviceID)return this._deviceID;const{deviceIdKey:t}=this._cache.keys;let e=this._cache.getStore(t);return"string"==typeof e&&e.length>=16&&e.length<=48||(e=Se(),this._cache.setStore(t,e)),this._deviceID=e,e}async _request(t,e,i={}){const a={"x-request-id":Se(),"x-device-id":this._getDeviceId()};if(i.withAccessToken){const{tokenTypeKey:t}=this._cache.keys,e=await this.getAccessToken(),i=this._cache.getStore(t);a.authorization=`${i} ${e}`}return this._reqClass["get"===i.method?"get":"post"]({url:`${this._baseURL}${t}`,data:e,headers:a})}async _fetchAccessToken(){const{loginTypeKey:t,accessTokenKey:e,accessTokenExpireKey:i,tokenTypeKey:a}=this._cache.keys,o=this._cache.getStore(t);if(o&&o!==qe.ANONYMOUS)throw new Xt({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});const n=await this._singlePromise.run("fetchAccessToken",(async()=>(await this._request("/auth/v1/signin/anonymously",{},{method:"post"})).data)),{access_token:r,expires_in:s,token_type:l}=n;return this._cache.setStore(a,l),this._cache.setStore(e,r),this._cache.setStore(i,Date.now()+1e3*s),r}isAccessTokenExpired(t,e){let i=!0;return t&&e&&(i=e<Date.now()),i}async getAccessToken(){const{accessTokenKey:t,accessTokenExpireKey:e}=this._cache.keys,i=this._cache.getStore(t),a=this._cache.getStore(e);return this.isAccessTokenExpired(i,a)?this._fetchAccessToken():i}async refreshAccessToken(){const{accessTokenKey:t,accessTokenExpireKey:e,loginTypeKey:i}=this._cache.keys;return this._cache.removeStore(t),this._cache.removeStore(e),this._cache.setStore(i,qe.ANONYMOUS),this.getAccessToken()}async getUserInfo(){return this._singlePromise.run("getUserInfo",(async()=>(await this._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"})).data))}}const He=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],Ge={"X-SDK-Version":"1.3.5"};function Ke(t,e,i){const a=t[e];t[e]=function(e){const o={},n={};i.forEach((i=>{const{data:a,headers:r}=i.call(t,e);Object.assign(o,a),Object.assign(n,r)}));const r=e.data;return r&&(()=>{var t;if(t=r,"[object FormData]"!==Object.prototype.toString.call(t))e.data={...r,...o};else for(const e in o)r.append(e,o[e])})(),e.headers={...e.headers||{},...n},a.call(t,e)}}function Je(){const t=Math.random().toString(16).slice(2);return{data:{seqId:t},headers:{...Ge,"x-seqid":t}}}class Xe{constructor(t={}){var e;this.config=t,this._reqClass=new Te.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:`请求在${this.config.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]}),this._cache=Ie(this.config.env),this._localCache=(e=this.config.env,De[e]),this.oauth=new $e(this.config),Ke(this._reqClass,"post",[Je]),Ke(this._reqClass,"upload",[Je]),Ke(this._reqClass,"download",[Je])}async post(t){return await this._reqClass.post(t)}async upload(t){return await this._reqClass.upload(t)}async download(t){return await this._reqClass.download(t)}async refreshAccessToken(){let t,e;this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken());try{t=await this._refreshAccessTokenPromise}catch(i){e=i}if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,e)throw e;return t}async _refreshAccessToken(){const{accessTokenKey:t,accessTokenExpireKey:e,refreshTokenKey:i,loginTypeKey:a,anonymousUuidKey:o}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(e);let n=this._cache.getStore(i);if(!n)throw new Xt({message:"未登录CloudBase"});const r={refresh_token:n},s=await this.request("auth.fetchAccessTokenWithRefreshToken",r);if(s.data.code){const{code:t}=s.data;if("SIGN_PARAM_INVALID"===t||"REFRESH_TOKEN_EXPIRED"===t||"INVALID_REFRESH_TOKEN"===t){if(this._cache.getStore(a)===qe.ANONYMOUS&&"INVALID_REFRESH_TOKEN"===t){const t=this._cache.getStore(o),e=this._cache.getStore(i),a=await this.send("auth.signInAnonymously",{anonymous_uuid:t,refresh_token:e});return this.setRefreshToken(a.refresh_token),this._refreshAccessToken()}Ee(Be),this._cache.removeStore(i)}throw new Xt({code:s.data.code,message:`刷新access token失败：${s.data.code}`})}if(s.data.access_token)return Ee(We),this._cache.setStore(t,s.data.access_token),this._cache.setStore(e,s.data.access_token_expire+Date.now()),{accessToken:s.data.access_token,accessTokenExpire:s.data.access_token_expire};s.data.refresh_token&&(this._cache.removeStore(i),this._cache.setStore(i,s.data.refresh_token),this._refreshAccessToken())}async getAccessToken(){const{accessTokenKey:t,accessTokenExpireKey:e,refreshTokenKey:i}=this._cache.keys;if(!this._cache.getStore(i))throw new Xt({message:"refresh token不存在，登录状态异常"});let a=this._cache.getStore(t),o=this._cache.getStore(e),n=!0;return this._shouldRefreshAccessTokenHook&&!(await this._shouldRefreshAccessTokenHook(a,o))&&(n=!1),(!a||!o||o<Date.now())&&n?this.refreshAccessToken():{accessToken:a,accessTokenExpire:o}}async request(t,e,i){const a=`x-tcb-trace_${this.config.env}`;let o="application/x-www-form-urlencoded";const n={action:t,env:this.config.env,dataVersion:"2019-08-16",...e};let r;if(-1===He.indexOf(t)&&(this._cache.keys,n.access_token=await this.oauth.getAccessToken()),"storage.uploadFile"===t){r=new FormData;for(let t in r)r.hasOwnProperty(t)&&void 0!==r[t]&&r.append(t,n[t]);o="multipart/form-data"}else{o="application/json",r={};for(let t in n)void 0!==n[t]&&(r[t]=n[t])}let s={headers:{"content-type":o}};i&&i.timeout&&(s.timeout=i.timeout),i&&i.onUploadProgress&&(s.onUploadProgress=i.onUploadProgress);const l=this._localCache.getStore(a);l&&(s.headers["X-TCB-Trace"]=l);const{parse:c,inQuery:h,search:d}=e;let p={env:this.config.env};c&&(p.parse=!0),h&&(p={...h,...p});let u=function(t,e,i={}){const a=/\?/.test(e);let o="";for(let n in i)""===o?!a&&(e+="?"):o+="&",o+=`${n}=${encodeURIComponent(i[n])}`;return/^http(s)?\:\/\//.test(e+=o)?e:`${t}${e}`}(he,"//tcb-api.tencentcloudapi.com/web",p);d&&(u+=d);const g=await this.post({url:u,data:r,...s}),f=g.header&&g.header["x-tcb-trace"];if(f&&this._localCache.setStore(a,f),200!==Number(g.status)&&200!==Number(g.statusCode)||!g.data)throw new Xt({code:"NETWORK_ERROR",message:"network request error"});return g}async send(t,e={},i={}){const a=await this.request(t,e,{...i,onUploadProgress:e.onUploadProgress});if(("ACCESS_TOKEN_DISABLED"===a.data.code||"ACCESS_TOKEN_EXPIRED"===a.data.code)&&-1===He.indexOf(t)){await this.oauth.refreshAccessToken();const a=await this.request(t,e,{...i,onUploadProgress:e.onUploadProgress});if(a.data.code)throw new Xt({code:a.data.code,message:be(a.data.message)});return a.data}if(a.data.code)throw new Xt({code:a.data.code,message:be(a.data.message)});return a.data}setRefreshToken(t){const{accessTokenKey:e,accessTokenExpireKey:i,refreshTokenKey:a}=this._cache.keys;this._cache.removeStore(e),this._cache.removeStore(i),this._cache.setStore(a,t)}}const Ve={};function Ye(t){return Ve[t]}class Qe{constructor(t){this.config=t,this._cache=Ie(t.env),this._request=Ye(t.env)}setRefreshToken(t){const{accessTokenKey:e,accessTokenExpireKey:i,refreshTokenKey:a}=this._cache.keys;this._cache.removeStore(e),this._cache.removeStore(i),this._cache.setStore(a,t)}setAccessToken(t,e){const{accessTokenKey:i,accessTokenExpireKey:a}=this._cache.keys;this._cache.setStore(i,t),this._cache.setStore(a,e)}async refreshUserInfo(){const{data:t}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(t),t}setLocalUserInfo(t){const{userInfoKey:e}=this._cache.keys;this._cache.setStore(e,t)}}class Ze{constructor(t){if(!t)throw new Xt({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=t,this._cache=Ie(this._envId),this._request=Ye(this._envId),this.setUserInfo()}linkWithTicket(t){if("string"!=typeof t)throw new Xt({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:t})}linkWithRedirect(t){t.signInWithRedirect()}updatePassword(t,e){return this._request.send("auth.updatePassword",{oldPassword:e,newPassword:t})}updateEmail(t){return this._request.send("auth.updateEmail",{newEmail:t})}updateUsername(t){if("string"!=typeof t)throw new Xt({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:t})}async getLinkedUidList(){const{data:t}=await this._request.send("auth.getLinkedUidList",{});let e=!1;const{users:i}=t;return i.forEach((t=>{t.wxOpenId&&t.wxPublicId&&(e=!0)})),{users:i,hasPrimaryUid:e}}setPrimaryUid(t){return this._request.send("auth.setPrimaryUid",{uid:t})}unlink(t){return this._request.send("auth.unlink",{platform:t})}async update(t){const{nickName:e,gender:i,avatarUrl:a,province:o,country:n,city:r}=t,{data:s}=await this._request.send("auth.updateUserInfo",{nickName:e,gender:i,avatarUrl:a,province:o,country:n,city:r});this.setLocalUserInfo(s)}async refresh(){const t=await this._request.oauth.getUserInfo();return this.setLocalUserInfo(t),t}setUserInfo(){const{userInfoKey:t}=this._cache.keys,e=this._cache.getStore(t);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((t=>{this[t]=e[t]})),this.location={country:e.country,province:e.province,city:e.city}}setLocalUserInfo(t){const{userInfoKey:e}=this._cache.keys;this._cache.setStore(e,t),this.setUserInfo()}}class ti{constructor(t){if(!t)throw new Xt({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=Ie(t);const{refreshTokenKey:e,accessTokenKey:i,accessTokenExpireKey:a}=this._cache.keys,o=this._cache.getStore(e),n=this._cache.getStore(i),r=this._cache.getStore(a);this.credential={refreshToken:o,accessToken:n,accessTokenExpire:r},this.user=new Ze(t)}get isAnonymousAuth(){return this.loginType===qe.ANONYMOUS}get isCustomAuth(){return this.loginType===qe.CUSTOM}get isWeixinAuth(){return this.loginType===qe.WECHAT||this.loginType===qe.WECHAT_OPEN||this.loginType===qe.WECHAT_PUBLIC}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}class ei extends Qe{async signIn(){this._cache.updatePersistence("local"),await this._request.oauth.getAccessToken(),Ee(Ne),Ee(ze,{env:this.config.env,loginType:qe.ANONYMOUS,persistence:"local"});const t=new ti(this.config.env);return await t.user.refresh(),t}async linkAndRetrieveDataWithTicket(t){const{anonymousUuidKey:e,refreshTokenKey:i}=this._cache.keys,a=this._cache.getStore(e),o=this._cache.getStore(i),n=await this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:a,refresh_token:o,ticket:t});if(n.refresh_token)return this._clearAnonymousUUID(),this.setRefreshToken(n.refresh_token),await this._request.refreshAccessToken(),Ee(Ue,{env:this.config.env}),Ee(ze,{loginType:qe.CUSTOM,persistence:"local"}),{credential:{refreshToken:n.refresh_token}};throw new Xt({message:"匿名转化失败"})}_setAnonymousUUID(t){const{anonymousUuidKey:e,loginTypeKey:i}=this._cache.keys;this._cache.removeStore(e),this._cache.setStore(e,t),this._cache.setStore(i,qe.ANONYMOUS)}_clearAnonymousUUID(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}class ii extends Qe{async signIn(t){if("string"!=typeof t)throw new Xt({code:"PARAM_ERROR",message:"ticket must be a string"});const{refreshTokenKey:e}=this._cache.keys,i=await this._request.send("auth.signInWithTicket",{ticket:t,refresh_token:this._cache.getStore(e)||""});if(i.refresh_token)return this.setRefreshToken(i.refresh_token),await this._request.refreshAccessToken(),Ee(Ne),Ee(ze,{env:this.config.env,loginType:qe.CUSTOM,persistence:this.config.persistence}),await this.refreshUserInfo(),new ti(this.config.env);throw new Xt({message:"自定义登录失败"})}}class ai extends Qe{async signIn(t,e){if("string"!=typeof t)throw new Xt({code:"PARAM_ERROR",message:"email must be a string"});const{refreshTokenKey:i}=this._cache.keys,a=await this._request.send("auth.signIn",{loginType:"EMAIL",email:t,password:e,refresh_token:this._cache.getStore(i)||""}),{refresh_token:o,access_token:n,access_token_expire:r}=a;if(o)return this.setRefreshToken(o),n&&r?this.setAccessToken(n,r):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Ee(Ne),Ee(ze,{env:this.config.env,loginType:qe.EMAIL,persistence:this.config.persistence}),new ti(this.config.env);throw a.code?new Xt({code:a.code,message:`邮箱登录失败: ${a.message}`}):new Xt({message:"邮箱登录失败"})}async activate(t){return this._request.send("auth.activateEndUserMail",{token:t})}async resetPasswordWithToken(t,e){return this._request.send("auth.resetPasswordWithToken",{token:t,newPassword:e})}}class oi extends Qe{async signIn(t,e){if("string"!=typeof t)throw new Xt({code:"PARAM_ERROR",message:"username must be a string"});"string"!=typeof e&&(e="",console.warn("password is empty"));const{refreshTokenKey:i}=this._cache.keys,a=await this._request.send("auth.signIn",{loginType:qe.USERNAME,username:t,password:e,refresh_token:this._cache.getStore(i)||""}),{refresh_token:o,access_token_expire:n,access_token:r}=a;if(o)return this.setRefreshToken(o),r&&n?this.setAccessToken(r,n):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Ee(Ne),Ee(ze,{env:this.config.env,loginType:qe.USERNAME,persistence:this.config.persistence}),new ti(this.config.env);throw a.code?new Xt({code:a.code,message:`用户名密码登录失败: ${a.message}`}):new Xt({message:"用户名密码登录失败"})}}class ni{constructor(t){this.config=t,this._cache=Ie(t.env),this._request=Ye(t.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),Oe(ze,this._onLoginTypeChanged)}get currentUser(){const t=this.hasLoginState();return t&&t.user||null}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}anonymousAuthProvider(){return new ei(this.config)}customAuthProvider(){return new ii(this.config)}emailAuthProvider(){return new ai(this.config)}usernameAuthProvider(){return new oi(this.config)}async signInAnonymously(){return new ei(this.config).signIn()}async signInWithEmailAndPassword(t,e){return new ai(this.config).signIn(t,e)}signInWithUsernameAndPassword(t,e){return new oi(this.config).signIn(t,e)}async linkAndRetrieveDataWithTicket(t){return this._anonymousAuthProvider||(this._anonymousAuthProvider=new ei(this.config)),Oe(Ue,this._onAnonymousConverted),await this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(t)}async signOut(){if(this.loginType===qe.ANONYMOUS)throw new Xt({message:"匿名用户不支持登出操作"});const{refreshTokenKey:t,accessTokenKey:e,accessTokenExpireKey:i}=this._cache.keys,a=this._cache.getStore(t);if(!a)return;const o=await this._request.send("auth.logout",{refresh_token:a});return this._cache.removeStore(t),this._cache.removeStore(e),this._cache.removeStore(i),Ee(Ne),Ee(ze,{env:this.config.env,loginType:qe.NULL,persistence:this.config.persistence}),o}async signUpWithEmailAndPassword(t,e){return this._request.send("auth.signUpWithEmailAndPassword",{email:t,password:e})}async sendPasswordResetEmail(t){return this._request.send("auth.sendPasswordResetEmail",{email:t})}onLoginStateChanged(t){Oe(Ne,(()=>{const e=this.hasLoginState();t.call(this,e)}));const e=this.hasLoginState();t.call(this,e)}onLoginStateExpired(t){Oe(Be,t.bind(this))}onAccessTokenRefreshed(t){Oe(We,t.bind(this))}onAnonymousConverted(t){Oe(Ue,t.bind(this))}onLoginTypeChanged(t){Oe(ze,(()=>{const e=this.hasLoginState();t.call(this,e)}))}async getAccessToken(){return{accessToken:(await this._request.getAccessToken()).accessToken,env:this.config.env}}hasLoginState(){const{accessTokenKey:t,accessTokenExpireKey:e}=this._cache.keys,i=this._cache.getStore(t),a=this._cache.getStore(e);return this._request.oauth.isAccessTokenExpired(i,a)?null:new ti(this.config.env)}async isUsernameRegistered(t){if("string"!=typeof t)throw new Xt({code:"PARAM_ERROR",message:"username must be a string"});const{data:e}=await this._request.send("auth.isUsernameRegistered",{username:t});return e&&e.isRegistered}getLoginState(){return Promise.resolve(this.hasLoginState())}async signInWithTicket(t){return new ii(this.config).signIn(t)}shouldRefreshAccessToken(t){this._request._shouldRefreshAccessTokenHook=t.bind(this)}getUserInfo(){return this._request.send("auth.getUserInfo",{}).then((t=>t.code?t:{...t.data,requestId:t.seqId}))}getAuthHeader(){const{refreshTokenKey:t,accessTokenKey:e}=this._cache.keys,i=this._cache.getStore(t);return{"x-cloudbase-credentials":this._cache.getStore(e)+"/@@/"+i}}_onAnonymousConverted(t){const{env:e}=t.data;e===this.config.env&&this._cache.updatePersistence(this.config.persistence)}_onLoginTypeChanged(t){const{loginType:e,persistence:i,env:a}=t.data;a===this.config.env&&(this._cache.updatePersistence(i),this._cache.setStore(this._cache.keys.loginTypeKey,e))}}const ri=function(t,e){e=e||me();const i=Ye(this.config.env),{cloudPath:a,filePath:o,onUploadProgress:n,fileType:r="image"}=t;return i.send("storage.getUploadMetadata",{path:a}).then((t=>{const{data:{url:s,authorization:l,token:c,fileId:h,cosFileId:d},requestId:p}=t,u={key:a,signature:l,"x-cos-meta-fileid":d,success_action_status:"201","x-cos-security-token":c};i.upload({url:s,data:u,file:o,name:a,fileType:r,onUploadProgress:n}).then((t=>{201===t.statusCode?e(null,{fileID:h,requestId:p}):e(new Xt({code:"STORAGE_REQUEST_FAIL",message:`STORAGE_REQUEST_FAIL: ${t.data}`}))})).catch((t=>{e(t)}))})).catch((t=>{e(t)})),e.promise},si=function(t,e){e=e||me();const i=Ye(this.config.env),{cloudPath:a}=t;return i.send("storage.getUploadMetadata",{path:a}).then((t=>{e(null,t)})).catch((t=>{e(t)})),e.promise},li=function({fileList:t},e){if(e=e||me(),!t||!Array.isArray(t))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};for(let a of t)if(!a||"string"!=typeof a)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"};const i={fileid_list:t};return Ye(this.config.env).send("storage.batchDeleteFile",i).then((t=>{t.code?e(null,t):e(null,{fileList:t.data.delete_list,requestId:t.requestId})})).catch((t=>{e(t)})),e.promise},ci=function({fileList:t},e){e=e||me(),t&&Array.isArray(t)||e(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});let i=[];for(let o of t)"object"==typeof o?(o.hasOwnProperty("fileID")&&o.hasOwnProperty("maxAge")||e(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),i.push({fileid:o.fileID,max_age:o.maxAge})):"string"==typeof o?i.push({fileid:o}):e(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"});const a={file_list:i};return Ye(this.config.env).send("storage.batchGetDownloadUrl",a).then((t=>{t.code?e(null,t):e(null,{fileList:t.data.download_list,requestId:t.requestId})})).catch((t=>{e(t)})),e.promise},hi=async function({fileID:t},e){const i=(await ci.call(this,{fileList:[{fileID:t,maxAge:600}]})).fileList[0];if("SUCCESS"!==i.code)return e?e(i):new Promise((t=>{t(i)}));const a=Ye(this.config.env);let o=i.download_url;if(o=encodeURI(o),!e)return a.download({url:o});e(await a.download({url:o}))},di=function({name:t,data:e,query:i,parse:a,search:o,timeout:n},r){const s=r||me();let l;try{l=e?JSON.stringify(e):""}catch(h){return Promise.reject(h)}if(!t)return Promise.reject(new Xt({code:"PARAM_ERROR",message:"函数名不能为空"}));const c={inQuery:i,parse:a,search:o,function_name:t,request_data:l};return Ye(this.config.env).send("functions.invokeFunction",c,{timeout:n}).then((t=>{if(t.code)s(null,t);else{let i=t.data.response_data;if(a)s(null,{result:i,requestId:t.requestId});else try{i=JSON.parse(t.data.response_data),s(null,{result:i,requestId:t.requestId})}catch(e){s(new Xt({message:"response data must be json"}))}}return s.promise})).catch((t=>{s(t)})),s.promise},pi={timeout:15e3,persistence:"session"},ui={};class gi{constructor(t){this.config=t||this.config,this.authObj=void 0}init(t){switch(Te.adapter||(this.requestClient=new Te.adapter.reqClass({timeout:t.timeout||5e3,timeoutMsg:`请求在${(t.timeout||5e3)/1e3}s内未完成，已中断`})),this.config={...pi,...t},!0){case this.config.timeout>6e5:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new gi(this.config)}auth({persistence:t}={}){if(this.authObj)return this.authObj;const e=t||Te.adapter.primaryStorage||pi.persistence;var i;return e!==this.config.persistence&&(this.config.persistence=e),function(t){const{env:e}=t;ke[e]=new Pe(t),De[e]=new Pe({...t,persistence:"local"})}(this.config),i=this.config,Ve[i.env]=new Xe(i),this.authObj=new ni(this.config),this.authObj}on(t,e){return Oe.apply(this,[t,e])}off(t,e){return Re.apply(this,[t,e])}callFunction(t,e){return di.apply(this,[t,e])}deleteFile(t,e){return li.apply(this,[t,e])}getTempFileURL(t,e){return ci.apply(this,[t,e])}downloadFile(t,e){return hi.apply(this,[t,e])}uploadFile(t,e){return ri.apply(this,[t,e])}getUploadMetadata(t,e){return si.apply(this,[t,e])}registerExtension(t){ui[t.name]=t}async invokeExtension(t,e){const i=ui[t];if(!i)throw new Xt({message:`扩展${t} 必须先注册`});return await i.invoke(e,this)}useAdapters(t){const{adapter:e,runtime:i}=function(t){const e=(i=t,"[object Array]"===Object.prototype.toString.call(i)?t:[t]);var i;for(const a of e){const{isMatch:t,genAdapter:e,runtime:i}=a;if(t())return{adapter:e(),runtime:i}}}(t)||{};e&&(Te.adapter=e),i&&(Te.runtime=i)}}var fi=new gi;function xi(t,e,i){void 0===i&&(i={});var a=/\?/.test(e),o="";for(var n in i)""===o?!a&&(e+="?"):o+="&",o+=n+"="+encodeURIComponent(i[n]);return/^http(s)?:\/\//.test(e+=o)?e:""+t+e}class mi{get(t){const{url:e,data:i,headers:a,timeout:o}=t;return new Promise(((t,n)=>{Vt.request({url:xi("https:",e),data:i,method:"GET",header:a,timeout:o,success(e){t(e)},fail(t){n(t)}})}))}post(t){const{url:e,data:i,headers:a,timeout:o}=t;return new Promise(((t,n)=>{Vt.request({url:xi("https:",e),data:i,method:"POST",header:a,timeout:o,success(e){t(e)},fail(t){n(t)}})}))}upload(t){return new Promise(((e,i)=>{const{url:a,file:o,data:n,headers:r,fileType:s}=t,l=Vt.uploadFile({url:xi("https:",a),name:"file",formData:Object.assign({},n),filePath:o,fileType:s,header:r,success(t){const i={statusCode:t.statusCode,data:t.data||{}};200===t.statusCode&&n.success_action_status&&(i.statusCode=parseInt(n.success_action_status,10)),e(i)},fail(t){i(new Error(t.errMsg||"uploadFile:fail"))}});"function"==typeof t.onUploadProgress&&l&&"function"==typeof l.onProgressUpdate&&l.onProgressUpdate((e=>{t.onUploadProgress({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}}const yi={setItem(t,e){Vt.setStorageSync(t,e)},getItem:t=>Vt.getStorageSync(t),removeItem(t){Vt.removeStorageSync(t)},clear(){Vt.clearStorageSync()}};var vi={genAdapter:function(){return{root:{},reqClass:mi,localStorage:yi,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};fi.useAdapters(vi);const bi=fi,Si=bi.init;bi.init=function(t){t.env=t.spaceId;const e=Si.call(this,t);e.config.provider="tencent",e.config.spaceId=t.spaceId;const i=e.auth;return e.auth=function(t){const e=i.call(this,t);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((t=>{var i;e[t]=(i=e[t],function(t){t=t||{};const{success:e,fail:a,complete:o}=Jt(t);if(!(e||a||o))return i.call(this,t);i.call(this,t).then((t=>{e&&e(t),o&&o(t)}),(t=>{a&&a(t),o&&o(t)}))}).bind(e)})),e},e.customAuth=e.auth,e};var wi=bi;async function Ti(t,e){const i=`http://${t}:${e}/system/ping`;try{const t=await(a={url:i,timeout:500},new Promise(((t,e)=>{Vt.request({...a,success(e){t(e)},fail(t){e(t)}})})));return!(!t.data||0!==t.data.code)}catch(o){return!1}var a}const _i={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"};var Ai={init(t){const e=new class{constructor(t){if(["spaceId","clientSecret"].forEach((e=>{if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error(`${e} required`)})),!t.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},t),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=Vt}async request(t,e=!0){return t=this.setupRequest(t),Promise.resolve().then((()=>se(t,this.adapter.request)))}requestLocal(t){return new Promise(((e,i)=>{this.adapter.request(Object.assign(t,{complete(t){if(t||(t={}),!t.statusCode||t.statusCode>=400){const e=t.data&&t.data.code||"SYS_ERR",a=t.data&&t.data.message||"request:fail";return i(new Xt({code:e,message:a}))}e({success:!0,result:t.data})}}))}))}setupRequest(t){const e=Object.assign({},t,{spaceId:this.config.spaceId,timestamp:Date.now()}),i={"Content-Type":"application/json"};i["x-serverless-sign"]=re(e,this.config.clientSecret);const a=ne();i["x-client-info"]=encodeURIComponent(JSON.stringify(a));const{token:o}=Qt();return i["x-client-token"]=o,{url:this.config.requestUrl,method:"POST",data:e,dataType:"json",header:JSON.parse(JSON.stringify(i))}}async setupLocalRequest(t){const e=ne(),{token:i}=Qt(),a=Object.assign({},t,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:e,token:i}),{address:o,servePort:n}=this.__dev__&&this.__dev__.debugInfo||{},{address:r}=await async function(t,e){let i;for(let a=0;a<t.length;a++){const o=t[a];if(await Ti(o,e)){i=o;break}}return{address:i,port:e}}(o,n);return{url:`http://${r}:${n}/${_i[t.method]}`,method:"POST",data:a,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))}}callFunction(t){const e={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:t.name,functionArgs:t.data||{}})};return this.request(e,!1)}getUploadFileOptions(t){const e={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(t)};return this.request(e)}reportUploadFile(t){const e={method:"serverless.file.resource.report",params:JSON.stringify(t)};return this.request(e)}uploadFile({filePath:t,cloudPath:e,fileType:i="image",onUploadProgress:a}){if(!e)throw new Xt({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});let o;return this.getUploadFileOptions({cloudPath:e}).then((e=>{const{url:n,formData:r,name:s}=e.result;return o=e.result.fileUrl,new Promise(((e,o)=>{const l=this.adapter.uploadFile({url:n,formData:r,name:s,filePath:t,fileType:i,success(t){t&&t.statusCode<400?e(t):o(new Xt({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(t){o(new Xt({code:t.code||"UPLOAD_FAILED",message:t.message||t.errMsg||"文件上传失败"}))}});"function"==typeof a&&l&&"function"==typeof l.onProgressUpdate&&l.onProgressUpdate((t=>{a({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))})).then((()=>this.reportUploadFile({cloudPath:e}))).then((e=>new Promise(((i,a)=>{e.success?i({success:!0,filePath:t,fileID:o}):a(new Xt({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))))}deleteFile({fileList:t}){const e={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:t})};return this.request(e).then((t=>{if(t.success)return t.result;throw new Xt({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}getTempFileURL({fileList:t,maxAge:e}={}){if(!Array.isArray(t)||0===t.length)throw new Xt({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const i={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:t,maxAge:e})};return this.request(i).then((t=>{if(t.success)return{fileList:t.result.fileList.map((t=>({fileID:t.fileID,tempFileURL:t.tempFileURL})))};throw new Xt({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}(t),i={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return e.auth=function(){return i},e.customAuth=e.auth,e}},Ci=ot((function(t,e){t.exports=rt.enc.Hex}));function Pi(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)}))}function ki(t="",e={}){const{data:i,functionName:a,method:o,headers:n,signHeaderKeys:r=[],config:s}=e,l=String(Date.now()),c=Pi(),h=Object.assign({},n,{"x-from-app-id":s.spaceAppId,"x-from-env-id":s.spaceId,"x-to-env-id":s.spaceId,"x-from-instance-id":l,"x-from-function-name":a,"x-client-timestamp":l,"x-alipay-source":"client","x-request-id":c,"x-alipay-callid":c,"x-trace-id":c}),d=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(r),[p="",u=""]=t.split("?")||[],g=function(t){const e=t.signedHeaders.join(";"),i=t.signedHeaders.map((e=>`${e.toLowerCase()}:${t.headers[e]}\n`)).join(""),a=fe(t.body).toString(Ci),o=`${t.method.toUpperCase()}\n${t.path}\n${t.query}\n${i}\n${e}\n${a}\n`,n=fe(o).toString(Ci),r=`HMAC-SHA256\n${t.timestamp}\n${n}\n`,s=xe(r,t.secretKey).toString(Ci);return`HMAC-SHA256 Credential=${t.secretId}, SignedHeaders=${e}, Signature=${s}`}({path:p,query:u,method:o,headers:h,timestamp:l,body:JSON.stringify(i),secretId:s.accessKey,secretKey:s.secretKey,signedHeaders:d.sort()});return{url:`${s.endpoint}${t}`,headers:Object.assign({},h,{Authorization:g})}}function Di({url:t,data:e,method:i="POST",headers:a={},timeout:o}){return new Promise(((n,r)=>{Vt.request({url:t,method:i,data:"object"==typeof e?JSON.stringify(e):e,header:a,dataType:"json",timeout:o,complete:(t={})=>{const e=a["x-trace-id"]||"";if(!t.statusCode||t.statusCode>=400){const{message:i,errMsg:a,trace_id:o}=t.data||{};return r(new Xt({code:"SYS_ERR",message:i||a||"request:fail",requestId:o||e}))}n({status:t.statusCode,data:t.data,headers:t.header,requestId:e})}})}))}function Ii(t,e){const{path:i,data:a,method:o="GET"}=t,{url:n,headers:r}=ki(i,{functionName:"",data:a,method:o,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:e});return Di({url:n,data:a,method:o,headers:r}).then((t=>{const e=t.data||{};if(!e.success)throw new Xt({code:t.errCode,message:t.errMsg,requestId:t.requestId});return e.data||{}})).catch((t=>{throw new Xt({code:t.errCode,message:t.errMsg,requestId:t.requestId})}))}function Li(t=""){const e=t.trim().replace(/^cloud:\/\//,""),i=e.indexOf("/");if(i<=0)throw new Xt({code:"INVALID_PARAM",message:"fileID不合法"});const a=e.substring(0,i),o=e.substring(i+1);return a!==this.config.spaceId&&console.warn("file ".concat(t," does not belong to env ").concat(this.config.spaceId)),o}function Mi(t=""){return"cloud://".concat(this.config.spaceId,"/").concat(t.replace(/^\/+/,""))}class Fi{constructor(t){this.config=t}signedURL(t,e={}){const i=`/ws/function/${t}`,a=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),o=Object.assign({},e,{accessKeyId:this.config.accessKey,signatureNonce:Pi(),timestamp:""+Date.now()}),n=[i,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(t){return o[t]?"".concat(t,"=").concat(o[t]):null})).filter(Boolean).join("&"),`host:${a}`].join("\n"),r=["HMAC-SHA256",fe(n).toString(Ci)].join("\n"),s=xe(r,this.config.secretKey).toString(Ci),l=Object.keys(o).map((t=>`${t}=${encodeURIComponent(o[t])}`)).join("&");return`${this.config.wsEndpoint}${i}?${l}&signature=${s}`}}var Oi={init:t=>{t.provider="alipay";const e=new class{constructor(t){if(["spaceId","spaceAppId","accessKey","secretKey"].forEach((e=>{if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error(`${e} required`)})),t.endpoint){if("string"!=typeof t.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(t.endpoint))throw new Error("endpoint must start with https://");t.endpoint=t.endpoint.replace(/\/$/,"")}this.config=Object.assign({},t,{endpoint:t.endpoint||`https://${t.spaceId}.api-hz.cloudbasefunction.cn`,wsEndpoint:t.wsEndpoint||`wss://${t.spaceId}.api-hz.cloudbasefunction.cn`}),this._websocket=new Fi(this.config)}callFunction(t){return function(t,e){const{name:i,data:a,async:o=!1,timeout:n}=t,r="POST",s={"x-to-function-name":i};o&&(s["x-function-invoke-type"]="async");const{url:l,headers:c}=ki("/functions/invokeFunction",{functionName:i,data:a,method:r,headers:s,signHeaderKeys:["x-to-function-name"],config:e});return Di({url:l,data:a,method:r,headers:c,timeout:n}).then((t=>{let e=0;if(o){const i=t.data||{};e="200"===i.errCode?0:i.errCode,t.data=i.data||{},t.errMsg=i.errMsg}if(0!==e)throw new Xt({code:e,message:t.errMsg,requestId:t.requestId});return{errCode:e,success:0===e,requestId:t.requestId,result:t.data}})).catch((t=>{throw new Xt({code:t.errCode,message:t.errMsg,requestId:t.requestId})}))}(t,this.config)}uploadFileToOSS({url:t,filePath:e,fileType:i,formData:a,onUploadProgress:o}){return new Promise(((n,r)=>{const s=Vt.uploadFile({url:t,filePath:e,fileType:i,formData:a,name:"file",success(t){t&&t.statusCode<400?n(t):r(new Xt({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(t){r(new Xt({code:t.code||"UPLOAD_FAILED",message:t.message||t.errMsg||"文件上传失败"}))}});"function"==typeof o&&s&&"function"==typeof s.onProgressUpdate&&s.onProgressUpdate((t=>{o({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}async uploadFile({filePath:t,cloudPath:e="",fileType:i="image",onUploadProgress:a}){if("string"!==ut(e))throw new Xt({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(e=e.trim()))throw new Xt({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(e))throw new Xt({code:"INVALID_PARAM",message:"cloudPath不合法"});const o=await Ii({path:"/".concat(e.replace(/^\//,""),"?post_url")},this.config),{file_id:n,upload_url:r,form_data:s}=o,l=s&&s.reduce(((t,e)=>(t[e.key]=e.value,t)),{});return this.uploadFileToOSS({url:r,filePath:t,fileType:i,formData:l,onUploadProgress:a}).then((()=>({fileID:n})))}async getTempFileURL({fileList:t}){return new Promise(((e,i)=>{(!t||t.length<0)&&e({code:"INVALID_PARAM",message:"fileList不能为空数组"}),t.length>50&&e({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});const a=[];for(const n of t){let t;"string"!==ut(n)&&e({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{t=Li.call(this,n)}catch(o){console.warn(o.errCode,o.errMsg),t=n}a.push({file_id:t,expire:600})}Ii({path:"/?download_url",data:{file_list:a},method:"POST"},this.config).then((t=>{const{file_list:i=[]}=t;e({fileList:i.map((t=>({fileID:Mi.call(this,t.file_id),tempFileURL:t.download_url})))})})).catch((t=>i(t)))}))}async connectWebSocket(t){const{name:e,query:i}=t;return Vt.connectSocket({url:this._websocket.signedURL(e,i),complete:()=>{}})}}(t);return e.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},e}};function Ei({data:t}){let e;e=ne();const i=JSON.parse(JSON.stringify(t||{}));if(Object.assign(i,{clientInfo:e}),!i.uniIdToken){const{token:t}=Qt();t&&(i.uniIdToken=t)}return i}const Ri=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}];var Ni=/[\\^$.*+?()[\]{}|]/g,Bi=RegExp(Ni.source);function zi(t,e,i){return t.replace(new RegExp((a=e)&&Bi.test(a)?a.replace(Ni,"\\$&"):a,"g"),i);var a}const Ui=2e4,Wi={code:20101,message:"Invalid client"};function qi(t){const{errSubject:e,subject:i,errCode:a,errMsg:o,code:n,message:r,cause:s}=t||{};return new Xt({subject:e||i||"uni-secure-network",code:a||n||Ui,message:o||r,cause:s})}let ji;function $i({secretType:t}={}){return"request"===t||"response"===t||"both"===t}function Hi({functionName:t,result:e,logPvd:i}){}function Gi(t){const e=t.callFunction,i=function(i){const a=i.name;i.data=Ei.call(t,{data:i.data});const o={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],n=$i(i)||false;return e.call(this,i).then((t=>(t.errCode=0,!n&&Hi.call(this,{functionName:a,result:t,logPvd:o}),Promise.resolve(t))),(t=>(!n&&Hi.call(this,{functionName:a,result:t,logPvd:o}),t&&t.message&&(t.message=function({message:t="",extraInfo:e={},formatter:i=[]}={}){for(let a=0;a<i.length;a++){const{rule:o,content:n,mode:r}=i[a],s=t.match(o);if(!s)continue;let l=n;for(let t=1;t<s.length;t++)l=zi(l,`{$${t}}`,s[t]);for(const t in e)l=zi(l,`{${t}}`,e[t]);return"replace"===r?l:t+l}return t}({message:`[${i.name}]: ${t.message}`,formatter:Ri,extraInfo:{functionName:a}})),Promise.reject(t))))};t.callFunction=function(e){const{provider:a,spaceId:o}=t.config,n=e.name;let r,s;return e.data=e.data||{},r=i,r=r.bind(t),s=$i(e)?new ji({secretType:e.secretType,uniCloudIns:t}).wrapEncryptDataCallFunction(i.bind(t))(e):function({provider:t,spaceId:e,functionName:i}={}){const{appId:a,uniPlatform:o,osName:n}=ie();let r=o;"app"===o&&(r=n);const s=function({provider:t,spaceId:e}={}){const i=St;if(!i)return{};t=function(t){return"tencent"===t?"tcb":t}(t);const a=i.find((i=>i.provider===t&&i.spaceId===e));return a&&a.config}({provider:t,spaceId:e});if(!s||!s.accessControl||!s.accessControl.enable)return!1;const l=s.accessControl.function||{},c=Object.keys(l);if(0===c.length)return!0;const h=function(t,e){let i,a,o;for(let n=0;n<t.length;n++){const r=t[n];r!==e?"*"!==r?r.split(",").map((t=>t.trim())).indexOf(e)>-1&&(a=r):o=r:i=r}return i||a||o}(c,i);if(!h)return!1;if((l[h]||[]).find(((t={})=>t.appId===a&&(t.platform||"").toLowerCase()===r.toLowerCase())))return!0;throw console.error(`此应用[appId: ${a}, platform: ${r}]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client`),qi(Wi)}({provider:a,spaceId:o,functionName:n})?new ji({secretType:e.secretType,uniCloudIns:t}).wrapVerifyClientCallFunction(i.bind(t))(e):r(e),Object.defineProperty(s,"result",{get:()=>(console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{})}),s.then((t=>("undefined"!=typeof UTSJSONObject&&"undefined"!=typeof UTS&&(t.result=UTS.JSON.parse(JSON.stringify(t.result))),t)))}}ji=class{constructor(){throw qi({message:"Platform web is not supported by secure network"})}};const Ki=Symbol("CLIENT_DB_INTERNAL");function Ji(t,e){return t.then="DoNotReturnProxyWithAFunctionNamedThen",t._internalType=Ki,t.inspect=null,t.__v_raw=void 0,new Proxy(t,{get(t,i,a){if("_uniClient"===i)return null;if("symbol"==typeof i)return t[i];if(i in t||"string"!=typeof i){const e=t[i];return"function"==typeof e?e.bind(t):e}return e.get(t,i,a)}})}function Xi(t){return{on:(e,i)=>{t[e]=t[e]||[],t[e].indexOf(i)>-1||t[e].push(i)},off:(e,i)=>{t[e]=t[e]||[];const a=t[e].indexOf(i);-1!==a&&t[e].splice(a,1)}}}const Vi=["db.Geo","db.command","command.aggregate"];function Yi(t,e){return Vi.indexOf(`${t}.${e}`)>-1}function Qi(t){switch(ut(t=Yt(t))){case"array":return t.map((t=>Qi(t)));case"object":return t._internalType===Ki||Object.keys(t).forEach((e=>{t[e]=Qi(t[e])})),t;case"regexp":return{$regexp:{source:t.source,flags:t.flags}};case"date":return{$date:t.toISOString()};default:return t}}function Zi(t){return t&&t.content&&t.content.$method}class ta{constructor(t,e,i){this.content=t,this.prevStage=e||null,this.udb=null,this._database=i}toJSON(){let t=this;const e=[t.content];for(;t.prevStage;)t=t.prevStage,e.push(t.content);return{$db:e.reverse().map((t=>({$method:t.$method,$param:Qi(t.$param)})))}}toString(){return JSON.stringify(this.toJSON())}getAction(){const t=this.toJSON().$db.find((t=>"action"===t.$method));return t&&t.$param&&t.$param[0]}getCommand(){return{$db:this.toJSON().$db.filter((t=>"action"!==t.$method))}}get isAggregate(){let t=this;for(;t;){const e=Zi(t),i=Zi(t.prevStage);if("aggregate"===e&&"collection"===i||"pipeline"===e)return!0;t=t.prevStage}return!1}get isCommand(){let t=this;for(;t;){if("command"===Zi(t))return!0;t=t.prevStage}return!1}get isAggregateCommand(){let t=this;for(;t;){const e=Zi(t),i=Zi(t.prevStage);if("aggregate"===e&&"command"===i)return!0;t=t.prevStage}return!1}getNextStageFn(t){const e=this;return function(){return ea({$method:t,$param:Qi(Array.from(arguments))},e,e._database)}}get count(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}get remove(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}get(){return this._send("get",Array.from(arguments))}get add(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}update(){return this._send("update",Array.from(arguments))}end(){return this._send("end",Array.from(arguments))}get set(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}_send(t,e){const i=this.getAction(),a=this.getCommand();return a.$db.push({$method:t,$param:Qi(e)}),this._database._callCloudFunction({action:i,command:a})}}function ea(t,e,i){return Ji(new ta(t,e,i),{get(t,e){let a="db";return t&&t.content&&(a=t.content.$method),Yi(a,e)?ea({$method:e},t,i):function(){return ea({$method:e,$param:Qi(Array.from(arguments))},t,i)}}})}function ia({path:t,method:e}){return class{constructor(){this.param=Array.from(arguments)}toJSON(){return{$newDb:[...t.map((t=>({$method:t}))),{$method:e,$param:this.param}]}}toString(){return JSON.stringify(this.toJSON())}}}function aa(t,e={}){return Ji(new t(e),{get:(t,e)=>Yi("db",e)?ea({$method:e},null,t):function(){return ea({$method:e,$param:Qi(Array.from(arguments))},null,t)}})}class oa extends class{constructor({uniClient:t={},isJQL:e=!1}={}){this._uniClient=t,this._authCallBacks={},this._dbCallBacks={},t._isDefault&&(this._dbCallBacks=Ct("_globalUniCloudDatabaseCallback")),e||(this.auth=Xi(this._authCallBacks)),this._isJQL=e,Object.assign(this,Xi(this._dbCallBacks)),this.env=Ji({},{get:(t,e)=>({$env:e})}),this.Geo=Ji({},{get:(t,e)=>ia({path:["Geo"],method:e})}),this.serverDate=ia({path:[],method:"serverDate"}),this.RegExp=ia({path:[],method:"RegExp"})}getCloudEnv(t){if("string"!=typeof t||!t.trim())throw new Error("getCloudEnv参数错误");return{$env:t.replace("$cloudEnv_","")}}_callback(t,e){const i=this._dbCallBacks;i[t]&&i[t].forEach((t=>{t(...e)}))}_callbackAuth(t,e){const i=this._authCallBacks;i[t]&&i[t].forEach((t=>{t(...e)}))}multiSend(){const t=Array.from(arguments),e=t.map((t=>{const e=t.getAction(),i=t.getCommand();if("getTemp"!==i.$db[i.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:e,command:i}}));return this._callCloudFunction({multiCommand:e,queryList:t})}}{_parseResult(t){return this._isJQL?t.result:t}_callCloudFunction({action:t,command:e,multiCommand:i,queryList:a}){function o(t,e){if(i&&a)for(let i=0;i<a.length;i++){const o=a[i];o.udb&&"function"==typeof o.udb.setResult&&(e?o.udb.setResult(e):o.udb.setResult(t.result.dataList[i]))}}const n=this,r=this._isJQL?"databaseForJQL":"database";function s(t){return n._callback("error",[t]),Lt(Mt(r,"fail"),t).then((()=>Lt(Mt(r,"complete"),t))).then((()=>(o(null,t),$t(Et,{type:Bt,content:t}),Promise.reject(t))))}const l=Lt(Mt(r,"invoke")),c=this._uniClient;return l.then((()=>c.callFunction({name:"DCloud-clientDB",type:"CLIENT_DB",data:{action:t,command:e,multiCommand:i}}))).then((t=>{const{code:e,message:i,token:a,tokenExpired:l,systemInfo:c=[]}=t.result;if(c)for(let o=0;o<c.length;o++){const{level:t,message:e,detail:i}=c[o],a=console[t]||console.log;let n="[System Info]"+e;i&&(n=`${n}\n详细信息：${i}`),a(n)}if(e)return s(new Xt({code:e,message:i,requestId:t.requestId}));t.result.errCode=t.result.errCode||t.result.code,t.result.errMsg=t.result.errMsg||t.result.message,a&&l&&(Zt({token:a,tokenExpired:l}),this._callbackAuth("refreshToken",[{token:a,tokenExpired:l}]),this._callback("refreshToken",[{token:a,tokenExpired:l}]),$t(Nt,{token:a,tokenExpired:l}));const h=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}];for(let o=0;o<h.length;o++){const{prop:e,tips:i}=h[o];if(e in t.result){const a=t.result[e];Object.defineProperty(t.result,e,{get:()=>(console.warn(i),a)})}}return d=t,Lt(Mt(r,"success"),d).then((()=>Lt(Mt(r,"complete"),d))).then((()=>{o(d,null);const t=n._parseResult(d);return $t(Et,{type:Bt,content:t}),Promise.resolve(t)}));var d}),(t=>(/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(t.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),s(new Xt({code:t.code||"SYSTEM_ERROR",message:t.message,requestId:t.requestId})))))}}const na="token无效，跳转登录页面",ra="token过期，跳转登录页面",sa={TOKEN_INVALID_TOKEN_EXPIRED:ra,TOKEN_INVALID_INVALID_CLIENTID:na,TOKEN_INVALID:na,TOKEN_INVALID_WRONG_TOKEN:na,TOKEN_INVALID_ANONYMOUS_USER:na},la={"uni-id-token-expired":ra,"uni-id-check-token-failed":na,"uni-id-token-not-exist":na,"uni-id-check-device-feature-failed":na};function ca(t,e){let i="";return i=t?`${t}/${e}`:e,i.replace(/^\//,"")}function ha(t=[],e=""){const i=[],a=[];return t.forEach((t=>{!0===t.needLogin?i.push(ca(e,t.path)):!1===t.needLogin&&a.push(ca(e,t.path))})),{needLoginPage:i,notNeedLoginPage:a}}function da(t){return t.split("?")[0].replace(/^\//,"")}function pa(){return function(t){let e=t&&t.$page&&t.$page.fullPath||"";return e?("/"!==e.charAt(0)&&(e="/"+e),e):e}(function(){const t=i();return t[t.length-1]}())}function ua(){return da(pa())}function ga(t="",e={}){if(!t)return!1;if(!(e&&e.list&&e.list.length))return!1;const i=e.list,a=da(t);return i.some((t=>t.pagePath===a))}const fa=!!at.uniIdRouter,{loginPage:xa,routerNeedLogin:ma,resToLogin:ya,needLoginPage:va,notNeedLoginPage:ba,loginPageInTabBar:Sa}=function({pages:t=[],subPackages:e=[],uniIdRouter:i={},tabBar:a={}}=at){const{loginPage:o,needLogin:n=[],resToLogin:r=!0}=i,{needLoginPage:s,notNeedLoginPage:l}=ha(t),{needLoginPage:c,notNeedLoginPage:h}=function(t=[]){const e=[],i=[];return t.forEach((t=>{const{root:a,pages:o=[]}=t,{needLoginPage:n,notNeedLoginPage:r}=ha(o,a);e.push(...n),i.push(...r)})),{needLoginPage:e,notNeedLoginPage:i}}(e);return{loginPage:o,routerNeedLogin:n,resToLogin:r,needLoginPage:[...s,...c],notNeedLoginPage:[...l,...h],loginPageInTabBar:ga(o,a)}}();if(va.indexOf(xa)>-1)throw new Error(`Login page [${xa}] should not be "needLogin", please check your pages.json`);function wa(t){const e=ua();if("/"===t.charAt(0))return t;const[i,a]=t.split("?"),o=i.replace(/^\//,"").split("/"),n=e.split("/");n.pop();for(let r=0;r<o.length;r++){const t=o[r];".."===t?n.pop():"."!==t&&n.push(t)}return""===n[0]&&n.shift(),"/"+n.join("/")+(a?"?"+a:"")}function Ta({redirect:t}){const e=da(t),i=da(xa);return ua()!==i&&e!==i}function _a({api:t,redirect:e}={}){if(!e||!Ta({redirect:e}))return;const i=(o=e,"/"!==(a=xa).charAt(0)&&(a="/"+a),o?a.indexOf("?")>-1?a+`&uniIdRedirectUrl=${encodeURIComponent(o)}`:a+`?uniIdRedirectUrl=${encodeURIComponent(o)}`:a);var a,o;Sa?"navigateTo"!==t&&"redirectTo"!==t||(t="switchTab"):"switchTab"===t&&(t="navigateTo");const n={navigateTo:v,redirectTo:b,switchTab:S,reLaunch:w};setTimeout((()=>{n[t]({url:i})}),0)}function Aa({url:t}={}){const e={abortLoginPageJump:!1,autoToLoginPage:!1},i=function(){const{token:t,tokenExpired:e}=Qt();let i;if(t){if(e<Date.now()){const t="uni-id-token-expired";i={errCode:t,errMsg:la[t]}}}else{const t="uni-id-check-token-failed";i={errCode:t,errMsg:la[t]}}return i}();if(function(t){const e=da(wa(t));return!(ba.indexOf(e)>-1)&&(va.indexOf(e)>-1||ma.some((e=>{return i=t,new RegExp(e).test(i);var i})))}(t)&&i){if(i.uniIdRedirectUrl=t,Wt(Rt).length>0)return setTimeout((()=>{$t(Rt,i)}),0),e.abortLoginPageJump=!0,e;e.autoToLoginPage=!0}return e}function Ca(){!function(){const t=pa(),{abortLoginPageJump:e,autoToLoginPage:i}=Aa({url:t});e||i&&_a({api:"redirectTo",redirect:t})}();const t=["navigateTo","redirectTo","reLaunch","switchTab"];for(let e=0;e<t.length;e++){const i=t[e];a(i,{invoke(t){const{abortLoginPageJump:e,autoToLoginPage:a}=Aa({url:t.url});return e?t:a?(_a({api:i,redirect:wa(t.url)}),!1):t}})}}function Pa(){this.onResponse((t=>{const{type:e,content:i}=t;let a=!1;switch(e){case"cloudobject":a=function(t){if("object"!=typeof t)return!1;const{errCode:e}=t||{};return e in la}(i);break;case"clientdb":a=function(t){if("object"!=typeof t)return!1;const{errCode:e}=t||{};return e in sa}(i)}a&&function(t={}){const e=Wt(Rt);Kt().then((()=>{const i=pa();if(i&&Ta({redirect:i}))return e.length>0?$t(Rt,Object.assign({uniIdRedirectUrl:i},t)):void(xa&&_a({api:"navigateTo",redirect:i}))}))}(i)}))}function ka(t){var e;(e=t).onResponse=function(t){qt(Et,t)},e.offResponse=function(t){jt(Et,t)},function(t){t.onNeedLogin=function(t){qt(Rt,t)},t.offNeedLogin=function(t){jt(Rt,t)},fa&&(Ct("_globalUniCloudStatus").needLoginInit||(Ct("_globalUniCloudStatus").needLoginInit=!0,Kt().then((()=>{Ca.call(t)})),ya&&Pa.call(t)))}(t),function(t){t.onRefreshToken=function(t){qt(Nt,t)},t.offRefreshToken=function(t){jt(Nt,t)}}(t)}let Da;const Ia="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",La=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function Ma(){const t=Qt().token||"",e=t.split(".");if(!t||3!==e.length)return{uid:null,role:[],permission:[],tokenExpired:0};let i;try{i=JSON.parse((a=e[1],decodeURIComponent(Da(a).split("").map((function(t){return"%"+("00"+t.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(o){throw new Error("获取当前用户信息出错，详细错误信息为："+o.message)}var a;return i.tokenExpired=1e3*i.exp,delete i.exp,delete i.iat,i}Da="function"!=typeof atob?function(t){if(t=String(t).replace(/[\t\n\f\r ]+/g,""),!La.test(t))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var e;t+="==".slice(2-(3&t.length));for(var i,a,o="",n=0;n<t.length;)e=Ia.indexOf(t.charAt(n++))<<18|Ia.indexOf(t.charAt(n++))<<12|(i=Ia.indexOf(t.charAt(n++)))<<6|(a=Ia.indexOf(t.charAt(n++))),o+=64===i?String.fromCharCode(e>>16&255):64===a?String.fromCharCode(e>>16&255,e>>8&255):String.fromCharCode(e>>16&255,e>>8&255,255&e);return o}:atob;var Fa=function(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}(ot((function(i,a){Object.defineProperty(a,"__esModule",{value:!0});const n="chooseAndUploadFile:ok",r="chooseAndUploadFile:fail";function s(t,e){return t.tempFiles.forEach(((t,i)=>{t.name||(t.name=t.path.substring(t.path.lastIndexOf("/")+1)),e&&(t.fileType=e),t.cloudPath=Date.now()+"_"+i+t.name.substring(t.name.lastIndexOf("."))})),t.tempFilePaths||(t.tempFilePaths=t.tempFiles.map((t=>t.path))),t}function l(t,e,{onChooseFile:i,onUploadProgress:a}){return e.then((t=>{if(i){const e=i(t);if(void 0!==e)return Promise.resolve(e).then((e=>void 0===e?t:e))}return t})).then((e=>!1===e?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(t,e,i=5,a){(e=Object.assign({},e)).errMsg=n;const o=e.tempFiles,r=o.length;let s=0;return new Promise((n=>{for(;s<i;)l();function l(){const i=s++;if(i>=r)return void(!o.find((t=>!t.url&&!t.errMsg))&&n(e));const c=o[i];t.uploadFile({provider:c.provider,filePath:c.path,cloudPath:c.cloudPath,fileType:c.fileType,cloudPathAsRealPath:c.cloudPathAsRealPath,onUploadProgress(t){t.index=i,t.tempFile=c,t.tempFilePath=c.path,a&&a(t)}}).then((t=>{c.url=t.fileID,i<r&&l()})).catch((t=>{c.errMsg=t.errMsg||t.message,i<r&&l()}))}}))}(t,e,5,a)))}a.initChooseAndUploadFile=function(i){return function(a={type:"all"}){return"image"===a.type?l(i,function(e){const{count:i,sizeType:a,sourceType:o=["album","camera"],extension:n}=e;return new Promise(((e,l)=>{t({count:i,sizeType:a,sourceType:o,extension:n,success(t){e(s(t,"image"))},fail(t){l({errMsg:t.errMsg.replace("chooseImage:fail",r)})}})}))}(a),a):"video"===a.type?l(i,function(t){const{camera:i,compressed:a,maxDuration:o,sourceType:n=["album","camera"],extension:l}=t;return new Promise(((t,c)=>{e({camera:i,compressed:a,maxDuration:o,sourceType:n,extension:l,success(e){const{tempFilePath:i,duration:a,size:o,height:n,width:r}=e;t(s({errMsg:"chooseVideo:ok",tempFilePaths:[i],tempFiles:[{name:e.tempFile&&e.tempFile.name||"",path:i,size:o,type:e.tempFile&&e.tempFile.type||"",width:r,height:n,duration:a,fileType:"video",cloudPath:""}]},"video"))},fail(t){c({errMsg:t.errMsg.replace("chooseVideo:fail",r)})}})}))}(a),a):l(i,function(t){const{count:e,extension:i}=t;return new Promise(((t,a)=>{let n=o;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(n=wx.chooseMessageFile),"function"!=typeof n)return a({errMsg:r+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});n({type:"all",count:e,extension:i,success(e){t(s(e))},fail(t){a({errMsg:t.errMsg.replace("chooseFile:fail",r)})}})}))}(a),a)}}})));function Oa(t){return{props:{localdata:{type:Array,default:()=>[]},options:{type:[Object,Array],default:()=>({})},spaceInfo:{type:Object,default:()=>({})},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:()=>({mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}),created(){this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((()=>{var t=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((e=>{t.push(this[e])})),t}),((t,e)=>{if("manual"===this.loadtime)return;let i=!1;const a=[];for(let o=2;o<t.length;o++)t[o]!==e[o]&&(a.push(t[o]),i=!0);t[0]!==e[0]&&(this.mixinDatacomPage.current=this.pageCurrent),this.mixinDatacomPage.size=this.pageSize,this.onMixinDatacomPropsChange(i,a)}))},methods:{onMixinDatacomPropsChange(t,e){},mixinDatacomEasyGet({getone:t=!1,success:e,fail:i}={}){this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((i=>{this.mixinDatacomLoading=!1;const{data:a,count:o}=i.result;this.getcount&&(this.mixinDatacomPage.count=o),this.mixinDatacomHasMore=a.length<this.pageSize;const n=t?a.length?a[0]:void 0:a;this.mixinDatacomResData=n,e&&e(n)})).catch((t=>{this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=t,this.mixinDatacomError=t,i&&i(t)})))},mixinDatacomGet(e={}){let i;e=e||{},i="undefined"!=typeof __uniX&&__uniX?t.databaseForJQL(this.spaceInfo):t.database(this.spaceInfo);const a=e.action||this.action;a&&(i=i.action(a));const o=e.collection||this.collection;i=Array.isArray(o)?i.collection(...o):i.collection(o);const n=e.where||this.where;n&&Object.keys(n).length&&(i=i.where(n));const r=e.field||this.field;r&&(i=i.field(r));const s=e.foreignKey||this.foreignKey;s&&(i=i.foreignKey(s));const l=e.groupby||this.groupby;l&&(i=i.groupBy(l));const c=e.groupField||this.groupField;c&&(i=i.groupField(c)),!0===(void 0!==e.distinct?e.distinct:this.distinct)&&(i=i.distinct());const h=e.orderby||this.orderby;h&&(i=i.orderBy(h));const d=void 0!==e.pageCurrent?e.pageCurrent:this.mixinDatacomPage.current,p=void 0!==e.pageSize?e.pageSize:this.mixinDatacomPage.size,u=void 0!==e.getcount?e.getcount:this.getcount,g=void 0!==e.gettree?e.gettree:this.gettree,f=void 0!==e.gettreepath?e.gettreepath:this.gettreepath,x={getCount:u},m={limitLevel:void 0!==e.limitlevel?e.limitlevel:this.limitlevel,startWith:void 0!==e.startwith?e.startwith:this.startwith};return g&&(x.getTree=m),f&&(x.getTreePath=m),i=i.skip(p*(d-1)).limit(p).get(x),i}}}}function Ea(t){return Ct("_globalUniCloudSecureNetworkCache__{spaceId}".replace("{spaceId}",t.config.spaceId))}async function Ra({openid:t,callLoginByWeixin:e=!1}={}){throw Ea(this),new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `web`")}async function Na(t){const e=Ea(this);return e.initPromise||(e.initPromise=Ra.call(this,t).then((t=>t)).catch((t=>{throw delete e.initPromise,t}))),e.initPromise}function Ba(t){oe=t}function za(t){const e={getSystemInfo:T,getPushClientId:_};return function(i){return new Promise(((a,o)=>{e[t]({...i,success(t){a(t)},fail(t){o(t)}})}))}}class Ua extends class{constructor(){this._callback={}}addListener(t,e){this._callback[t]||(this._callback[t]=[]),this._callback[t].push(e)}on(t,e){return this.addListener(t,e)}removeListener(t,e){if(!e)throw new Error('The "listener" argument must be of type function. Received undefined');const i=this._callback[t];if(!i)return;const a=function(t,e){for(let i=t.length-1;i>=0;i--)if(t[i]===e)return i;return-1}(i,e);i.splice(a,1)}off(t,e){return this.removeListener(t,e)}removeAllListener(t){delete this._callback[t]}emit(t,...e){const i=this._callback[t];if(i)for(let a=0;a<i.length;a++)i[a](...e)}}{constructor(){super(),this._uniPushMessageCallback=this._receivePushMessage.bind(this),this._currentMessageId=-1,this._payloadQueue=[]}init(){return Promise.all([za("getSystemInfo")(),za("getPushClientId")()]).then((([{appId:t}={},{cid:e}={}]=[])=>{if(!t)throw new Error("Invalid appId, please check the manifest.json file");if(!e)throw new Error("Invalid push client id");this._appId=t,this._pushClientId=e,this._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),this.emit("open"),this._initMessageListener()}),(t=>{throw this.emit("error",t),this.close(),t}))}async open(){return this.init()}_isUniCloudSSE(t){if("receive"!==t.type)return!1;const e=t&&t.data&&t.data.payload;return!(!e||"UNI_CLOUD_SSE"!==e.channel||e.seqId!==this._seqId)}_receivePushMessage(t){if(!this._isUniCloudSSE(t))return;const e=t&&t.data&&t.data.payload,{action:i,messageId:a,message:o}=e;this._payloadQueue.push({action:i,messageId:a,message:o}),this._consumMessage()}_consumMessage(){for(;;){const t=this._payloadQueue.find((t=>t.messageId===this._currentMessageId+1));if(!t)break;this._currentMessageId++,this._parseMessagePayload(t)}}_parseMessagePayload(t){const{action:e,messageId:i,message:a}=t;"end"===e?this._end({messageId:i,message:a}):"message"===e&&this._appendMessage({messageId:i,message:a})}_appendMessage({messageId:t,message:e}={}){this.emit("message",e)}_end({messageId:t,message:e}={}){this.emit("end",e),this.close()}_initMessageListener(){c(this._uniPushMessageCallback)}_destroy(){h(this._uniPushMessageCallback)}toJSON(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}close(){this._destroy(),this.emit("close")}}const Wa={tcb:wi,tencent:wi,aliyun:ce,private:Ai,dcloud:Ai,alipay:Oi};let qa=new class{init(t){let e={};const i=Wa[t.provider];if(!i)throw new Error("未提供正确的provider参数");var a;return e=i.init(t),function(t){t._initPromiseHub||(t._initPromiseHub=new vt({createPromise:function(){let e=Promise.resolve();e=new Promise((t=>{setTimeout((()=>{t()}),1)}));const i=t.auth();return e.then((()=>i.getLoginState())).then((t=>t?Promise.resolve():i.signInAnonymously()))}}))}(e),Gi(e),function(t){const e=t.uploadFile;t.uploadFile=function(t){return e.call(this,t)}}(e),(a=e).database=function(t){if(t&&Object.keys(t).length>0)return a.init(t).database();if(this._database)return this._database;const e=aa(oa,{uniClient:a});return this._database=e,e},a.databaseForJQL=function(t){if(t&&Object.keys(t).length>0)return a.init(t).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;const e=aa(oa,{uniClient:a,isJQL:!0});return this._databaseForJQL=e,e},function(t){t.getCurrentUserInfo=Ma,t.chooseAndUploadFile=Fa.initChooseAndUploadFile(t),Object.assign(t,{get mixinDatacom(){return Oa(t)}}),t.SSEChannel=Ua,t.initSecureNetworkByWeixin=function(t){return function({openid:e,callLoginByWeixin:i=!1}={}){return Na.call(t,{openid:e,callLoginByWeixin:i})}}(t),t.setCustomClientInfo=Ba,t.importObject=function(e){return function(i,a={}){a=function(t,e={}){return t.customUI=e.customUI||t.customUI,t.parseSystemError=e.parseSystemError||t.parseSystemError,Object.assign(t.loadingOptions,e.loadingOptions),Object.assign(t.errorOptions,e.errorOptions),"object"==typeof e.secretMethods&&(t.secretMethods=e.secretMethods),t}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},a);const{customUI:o,loadingOptions:c,errorOptions:h,parseSystemError:d}=a,p=!o;return new Proxy({},{get(o,u){switch(u){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function({fn:t,interceptorName:e,getCallbackArgs:i}={}){return async function(...a){const o=i?i({params:a}):{};let n,r;try{return await Lt(Mt(e,"invoke"),{...o}),n=await t(...a),await Lt(Mt(e,"success"),{...o,result:n}),n}catch(s){throw r=s,await Lt(Mt(e,"fail"),{...o,error:r}),r}finally{await Lt(Mt(e,"complete"),r?{...o,error:r}:{...o,result:n})}}}({fn:async function o(...g){let f;p&&n({title:c.title,mask:c.mask});const x={name:i,type:"OBJECT",data:{method:u,params:g}};"object"==typeof a.secretMethods&&function(t,e){const i=e.data.method,a=t.secretMethods||{},o=a[i]||a["*"];o&&(e.secretType=o)}(a,x);let m=!1;try{f=await e.callFunction(x)}catch(t){m=!0,f={result:new Xt(t)}}const{errSubject:y,errCode:v,errMsg:b,newToken:S}=f.result||{};if(p&&r(),S&&S.token&&S.tokenExpired&&(Zt(S),$t(Nt,{...S})),v){let t=b;if(m&&d&&(t=(await d({objectName:i,methodName:u,params:g,errSubject:y,errCode:v,errMsg:b})).errMsg||b),p)if("toast"===h.type)s({title:t,icon:"none"});else{if("modal"!==h.type)throw new Error(`Invalid errorOptions.type: ${h.type}`);{const{confirm:e}=await async function({title:t,content:e,showCancel:i,cancelText:a,confirmText:o}={}){return new Promise(((n,r)=>{l({title:t,content:e,showCancel:i,cancelText:a,confirmText:o,success(t){n(t)},fail(){n({confirm:!1,cancel:!0})}})}))}({title:"提示",content:t,showCancel:h.retry,cancelText:"取消",confirmText:h.retry?"重试":"确定"});if(h.retry&&e)return o(...g)}}const e=new Xt({subject:y,code:v,message:b,requestId:f.requestId});throw e.detail=f.result,$t(Et,{type:Ut,content:e}),e}return $t(Et,{type:Ut,content:f.result}),f.result},interceptorName:"callObject",getCallbackArgs:function({params:t}={}){return{objectName:i,methodName:u,params:t}}})}})}}(t)}(e),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((t=>{if(!e[t])return;const i=e[t];e[t]=function(){return i.apply(e,Array.from(arguments))},e[t]=function(t,e){return function(i){let a=!1;if("callFunction"===e){const t=i&&i.type||ht;a=t!==ht}const o="callFunction"===e&&!a,n=this._initPromiseHub.exec();i=i||{};const{success:r,fail:s,complete:l}=Jt(i),c=n.then((()=>a?Promise.resolve():Lt(Mt(e,"invoke"),i))).then((()=>t.call(this,i))).then((t=>a?Promise.resolve(t):Lt(Mt(e,"success"),t).then((()=>Lt(Mt(e,"complete"),t))).then((()=>(o&&$t(Et,{type:zt,content:t}),Promise.resolve(t))))),(t=>a?Promise.reject(t):Lt(Mt(e,"fail"),t).then((()=>Lt(Mt(e,"complete"),t))).then((()=>($t(Et,{type:zt,content:t}),Promise.reject(t))))));if(!(r||s||l))return c;c.then((t=>{r&&r(t),l&&l(t),o&&$t(Et,{type:zt,content:t})}),(t=>{s&&s(t),l&&l(t),o&&$t(Et,{type:zt,content:t})}))}}(e[t],t).bind(e)})),e.init=this.init,e}};(()=>{const t=wt;let e={};if(t&&1===t.length)e=t[0],qa=qa.init(e),qa._isDefault=!0;else{const e=["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"];let i;i=t&&t.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",e.forEach((t=>{qa[t]=function(){return console.error(i),Promise.reject(new Xt({code:"SYS_ERR",message:i}))}}))}Object.assign(qa,{get mixinDatacom(){return Oa(qa)}}),ka(qa),qa.addInterceptor=Dt,qa.removeInterceptor=It,qa.interceptObject=Ft;{const t=_t||(_t=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function t(){return this}return void 0!==t()?t():new Function("return this")()}(),_t);t.uniCloud=qa,t.UniCloudError=Xt}})();var ja=qa;const $a=et({components:{Loading1:et({name:"loading1",data:()=>({})},[["render",function(t,e,i,a,o,n){const r=L;return P(),k(r,{class:"container loading1"},{default:D((()=>[I(r,{class:"shape shape1"}),I(r,{class:"shape shape2"}),I(r,{class:"shape shape3"}),I(r,{class:"shape shape4"})])),_:1})}],["__scopeId","data-v-a16ea79e"]]),Loading2:et({name:"loading2",data:()=>({})},[["render",function(t,e,i,a,o,n){const r=L;return P(),k(r,{class:"container loading2"},{default:D((()=>[I(r,{class:"shape shape1"}),I(r,{class:"shape shape2"}),I(r,{class:"shape shape3"}),I(r,{class:"shape shape4"})])),_:1})}],["__scopeId","data-v-51d40d8b"]]),Loading3:et({name:"loading3",data:()=>({})},[["render",function(t,e,i,a,o,n){const r=L;return P(),k(r,{class:"container loading3"},{default:D((()=>[I(r,{class:"shape shape1"}),I(r,{class:"shape shape2"}),I(r,{class:"shape shape3"}),I(r,{class:"shape shape4"})])),_:1})}],["__scopeId","data-v-47408335"]]),Loading4:et({name:"loading5",data:()=>({})},[["render",function(t,e,i,a,o,n){const r=L;return P(),k(r,{class:"container loading5"},{default:D((()=>[I(r,{class:"shape shape1"}),I(r,{class:"shape shape2"}),I(r,{class:"shape shape3"}),I(r,{class:"shape shape4"})])),_:1})}],["__scopeId","data-v-49a6258a"]]),Loading5:et({name:"loading6",data:()=>({})},[["render",function(t,e,i,a,o,n){const r=L;return P(),k(r,{class:"container loading6"},{default:D((()=>[I(r,{class:"shape shape1"}),I(r,{class:"shape shape2"}),I(r,{class:"shape shape3"}),I(r,{class:"shape shape4"})])),_:1})}],["__scopeId","data-v-08a06125"]])},name:"qiun-loading",props:{loadingType:{type:Number,default:2}},data:()=>({})},[["render",function(t,e,i,a,o,n){const r=M("Loading1"),s=M("Loading2"),l=M("Loading3"),c=M("Loading4"),h=M("Loading5"),d=L;return P(),k(d,null,{default:D((()=>[1==i.loadingType?(P(),k(r,{key:0})):F("",!0),2==i.loadingType?(P(),k(s,{key:1})):F("",!0),3==i.loadingType?(P(),k(l,{key:2})):F("",!0),4==i.loadingType?(P(),k(c,{key:3})):F("",!0),5==i.loadingType?(P(),k(h,{key:4})):F("",!0)])),_:1})}]]);const Ha=et({name:"qiun-error",props:{errorMessage:{type:String,default:null}},data:()=>({})},[["render",function(t,e,i,a,o,n){const r=L;return P(),k(r,{class:"chartsview"},{default:D((()=>[I(r,{class:"charts-error"}),I(r,{class:"charts-font"},{default:D((()=>[O(E(null==i.errorMessage?"请点击重试":i.errorMessage),1)])),_:1})])),_:1})}],["__scopeId","data-v-af32de60"]]);var Ga={version:"v2.5.0-20230101",yAxisWidth:15,xAxisHeight:22,padding:[10,10,10,10],rotate:!1,fontSize:13,fontColor:"#666666",dataPointShape:["circle","circle","circle","circle"],color:["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],linearColor:["#0EE2F8","#2BDCA8","#FA7D8D","#EB88E2","#2AE3A0","#0EE2F8","#EB88E2","#6773E3","#F78A85"],pieChartLinePadding:15,pieChartTextPadding:5,titleFontSize:20,subtitleFontSize:15,radarLabelTextMargin:13},Ka=function(t,...e){if(null==t)throw new TypeError("[uCharts] Cannot convert undefined or null to object");if(!e||e.length<=0)return t;function i(t,e){for(let a in e)t[a]=t[a]&&"[object Object]"===t[a].toString()?i(t[a],e[a]):t[a]=e[a];return t}return e.forEach((e=>{t=i(t,e)})),t},Ja={toFixed:function(t,e){return e=e||2,this.isFloat(t)&&(t=t.toFixed(e)),t},isFloat:function(t){return t%1!=0},approximatelyEqual:function(t,e){return Math.abs(t-e)<1e-10},isSameSign:function(t,e){return Math.abs(t)===t&&Math.abs(e)===e||Math.abs(t)!==t&&Math.abs(e)!==e},isSameXCoordinateArea:function(t,e){return this.isSameSign(t.x,e.x)},isCollision:function(t,e){return t.end={},t.end.x=t.start.x+t.width,t.end.y=t.start.y-t.height,e.end={},e.end.x=e.start.x+e.width,e.end.y=e.start.y-e.height,!(e.start.x>t.end.x||e.end.x<t.start.x||e.end.y>t.start.y||e.start.y<t.end.y)}};function Xa(t,e){var i=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(function(t,e,i,a){return e+e+i+i+a+a})),a=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(i);return"rgba("+parseInt(a[1],16)+","+parseInt(a[2],16)+","+parseInt(a[3],16)+","+e+")"}function Va(t,e,i){if(isNaN(t))throw new Error("[uCharts] series数据需为Number格式");i=i||10,e=e||"upper";for(var a=1;i<1;)i*=10,a*=10;for(t="upper"===e?Math.ceil(t*a):Math.floor(t*a);t%i!=0;)if("upper"===e){if(t==t+1)break;t++}else t--;return t/a}function Ya(t,e,i,a,o){var n=o.width-o.area[1]-o.area[3],r=i.eachSpacing*(o.chartData.xAxisData.xAxisPoints.length-1);"mount"==o.type&&o.extra&&o.extra.mount&&o.extra.mount.widthRatio&&o.extra.mount.widthRatio>1&&(o.extra.mount.widthRatio>2&&(o.extra.mount.widthRatio=2),r+=(o.extra.mount.widthRatio-1)*i.eachSpacing);var s=e;return e>=0?(s=0,t.uevent.trigger("scrollLeft"),t.scrollOption.position="left",o.xAxis.scrollPosition="left"):Math.abs(e)>=r-n?(s=n-r,t.uevent.trigger("scrollRight"),t.scrollOption.position="right",o.xAxis.scrollPosition="right"):(t.scrollOption.position=e,o.xAxis.scrollPosition=e),s}function Qa(t,e,i){function a(t){for(;t<0;)t+=2*Math.PI;for(;t>2*Math.PI;)t-=2*Math.PI;return t}return t=a(t),(e=a(e))>(i=a(i))&&(i+=2*Math.PI,t<e&&(t+=2*Math.PI)),t>=e&&t<=i}function Za(t,e){function i(t,e){return!(!t[e-1]||!t[e+1])&&(t[e].y>=Math.max(t[e-1].y,t[e+1].y)||t[e].y<=Math.min(t[e-1].y,t[e+1].y))}function a(t,e){return!(!t[e-1]||!t[e+1])&&(t[e].x>=Math.max(t[e-1].x,t[e+1].x)||t[e].x<=Math.min(t[e-1].x,t[e+1].x))}var o=.2,n=.2,r=null,s=null,l=null,c=null;if(e<1?(r=t[0].x+(t[1].x-t[0].x)*o,s=t[0].y+(t[1].y-t[0].y)*o):(r=t[e].x+(t[e+1].x-t[e-1].x)*o,s=t[e].y+(t[e+1].y-t[e-1].y)*o),e>t.length-3){var h=t.length-1;l=t[h].x-(t[h].x-t[h-1].x)*n,c=t[h].y-(t[h].y-t[h-1].y)*n}else l=t[e+1].x-(t[e+2].x-t[e].x)*n,c=t[e+1].y-(t[e+2].y-t[e].y)*n;return i(t,e+1)&&(c=t[e+1].y),i(t,e)&&(s=t[e].y),a(t,e+1)&&(l=t[e+1].x),a(t,e)&&(r=t[e].x),(s>=Math.max(t[e].y,t[e+1].y)||s<=Math.min(t[e].y,t[e+1].y))&&(s=t[e].y),(c>=Math.max(t[e].y,t[e+1].y)||c<=Math.min(t[e].y,t[e+1].y))&&(c=t[e+1].y),(r>=Math.max(t[e].x,t[e+1].x)||r<=Math.min(t[e].x,t[e+1].x))&&(r=t[e].x),(l>=Math.max(t[e].x,t[e+1].x)||l<=Math.min(t[e].x,t[e+1].x))&&(l=t[e+1].x),{ctrA:{x:r,y:s},ctrB:{x:l,y:c}}}function to(t,e,i){return{x:i.x+t,y:i.y-e}}function eo(t,e){if(e)for(;Ja.isCollision(t,e);)t.start.x>0?t.start.y--:t.start.x<0||t.start.y>0?t.start.y++:t.start.y--;return t}function io(t,e,i){for(var a=0,o=0;o<t.length;o++){let n=t[o];if(n.color||(n.color=i.color[a],a=(a+1)%i.color.length),n.linearIndex||(n.linearIndex=o),n.index||(n.index=0),n.type||(n.type=e.type),void 0===n.show&&(n.show=!0),n.type||(n.type=e.type),n.pointShape||(n.pointShape="circle"),!n.legendShape)switch(n.type){case"line":n.legendShape="line";break;case"column":case"bar":n.legendShape="rect";break;case"area":case"mount":n.legendShape="triangle";break;default:n.legendShape="circle"}}return t}function ao(t,e,i,a){var o=e||[];if("custom"==t&&0==o.length&&(o=a.linearColor),"custom"==t&&o.length<i.length){let t=i.length-o.length;for(var n=0;n<t;n++)o.push(a.linearColor[(n+1)%a.linearColor.length])}return o}function oo(t,e,i){var a=0;if(t=String(t),!1!==i&&void 0!==i&&i.setFontSize&&i.measureText)return i.setFontSize(e),i.measureText(t).width;t=t.split("");for(let o=0;o<t.length;o++){let e=t[o];/[a-zA-Z]/.test(e)?a+=7:/[0-9]/.test(e)?a+=5.5:/\./.test(e)?a+=2.7:/-/.test(e)?a+=3.25:/:/.test(e)?a+=2.5:/[\u4e00-\u9fa5]/.test(e)?a+=10:/\(|\)/.test(e)?a+=3.73:/\s/.test(e)?a+=2.5:/%/.test(e)?a+=8:a+=10}return a*e/10}function no(t){return t.reduce((function(t,e){return(t.data?t.data:t).concat(e.data)}),[])}function ro(t,e){for(var i=new Array(e),a=0;a<i.length;a++)i[a]=0;for(var o=0;o<t.length;o++)for(a=0;a<i.length;a++)i[a]+=t[o].data[a];return t.reduce((function(t,e){return(t.data?t.data:t).concat(e.data).concat(i)}),[])}function so(t,e,i){let a,o;return t.clientX?e.rotate?(o=e.height-t.clientX*e.pix,a=(t.pageY-i.currentTarget.offsetTop-e.height/e.pix/2*(e.pix-1))*e.pix):(a=t.clientX*e.pix,o=(t.pageY-i.currentTarget.offsetTop-e.height/e.pix/2*(e.pix-1))*e.pix):e.rotate?(o=e.height-t.x*e.pix,a=t.y*e.pix):(a=t.x*e.pix,o=t.y*e.pix),{x:a,y:o}}function lo(t,e,i){var a=[],o=[],n=e.constructor.toString().indexOf("Array")>-1;if(n){let e=fo(t);for(var r=0;r<i.length;r++)o.push(e[i[r]])}else o=t;for(let s=0;s<o.length;s++){let t=o[s],i=-1;if(i=n?e[s]:e,null!==t.data[i]&&void 0!==t.data[i]&&t.show){let e={};e.color=t.color,e.type=t.type,e.style=t.style,e.pointShape=t.pointShape,e.disableLegend=t.disableLegend,e.legendShape=t.legendShape,e.name=t.name,e.show=t.show,e.data=t.formatter?t.formatter(t.data[i]):t.data[i],a.push(e)}}return a}function co(t,e,i){var a=t.map((function(t){return oo(t,e,i)}));return Math.max.apply(null,a)}function ho(t){for(var e=2*Math.PI/t,i=[],a=0;a<t;a++)i.push(e*a);return i.map((function(t){return-1*t+Math.PI/2}))}function po(t,e,i,a,o){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},r=e.chartData.calPoints?e.chartData.calPoints:[];let s={};if(a.length>0){let t=[];for(let e=0;e<a.length;e++)t.push(r[a[e]]);s=t[0][i[0]]}else for(let h=0;h<r.length;h++)if(r[h][i]){s=r[h][i];break}var l=t.map((function(t){let a=null;return e.categories&&e.categories.length>0&&(a=o[i]),{text:n.formatter?n.formatter(t,a,i,e):t.name+": "+t.data,color:t.color,legendShape:"auto"==e.extra.tooltip.legendShape?t.legendShape:e.extra.tooltip.legendShape}})),c={x:Math.round(s.x),y:Math.round(s.y)};return{textList:l,offset:c}}function uo(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},n=e.chartData.xAxisPoints[i]+e.chartData.eachSpacing/2,r=t.map((function(t){return{text:o.formatter?o.formatter(t,a[i],i,e):t.name+": "+t.data,color:t.color,disableLegend:!!t.disableLegend,legendShape:"auto"==e.extra.tooltip.legendShape?t.legendShape:e.extra.tooltip.legendShape}}));r=r.filter((function(t){if(!0!==t.disableLegend)return t}));var s={x:Math.round(n),y:0};return{textList:r,offset:s}}function go(t,e,i,a,o,n){var r=i.chartData.calPoints;let s=n.color.upFill,l=n.color.downFill,c=[s,s,l,s];var h=[];e.map((function(e){0==a?e.data[1]-e.data[0]<0?c[1]=l:c[1]=s:(e.data[0]<t[a-1][1]&&(c[0]=l),e.data[1]<e.data[0]&&(c[1]=l),e.data[2]>t[a-1][1]&&(c[2]=s),e.data[3]<t[a-1][1]&&(c[3]=l));let o={text:"开盘："+e.data[0],color:c[0],legendShape:"auto"==i.extra.tooltip.legendShape?e.legendShape:i.extra.tooltip.legendShape},n={text:"收盘："+e.data[1],color:c[1],legendShape:"auto"==i.extra.tooltip.legendShape?e.legendShape:i.extra.tooltip.legendShape},r={text:"最低："+e.data[2],color:c[2],legendShape:"auto"==i.extra.tooltip.legendShape?e.legendShape:i.extra.tooltip.legendShape},d={text:"最高："+e.data[3],color:c[3],legendShape:"auto"==i.extra.tooltip.legendShape?e.legendShape:i.extra.tooltip.legendShape};h.push(o,n,r,d)}));var d=[],p={x:0,y:0};for(let u=0;u<r.length;u++){let t=r[u];void 0!==t[a]&&null!==t[a]&&d.push(t[a])}return p.x=Math.round(d[0][0].x),{textList:h,offset:p}}function fo(t){let e=[];for(let i=0;i<t.length;i++)1==t[i].show&&e.push(t[i]);return e}function xo(t,e,i){return t.x<=e.width-e.area[1]+10&&t.x>=e.area[3]-10&&t.y>=e.area[0]&&t.y<=e.height-e.area[2]}function mo(t,e,i){return Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2)<=Math.pow(i,2)}function yo(t,e){var i=[],a=[];return t.forEach((function(t,o){e.connectNulls?null!==t&&a.push(t):null!==t?a.push(t):(a.length&&i.push(a),a=[])})),a.length&&i.push(a),i}function vo(t,e,i,a,o){var n={angle:0,xAxisHeight:e.xAxis.lineHeight*e.pix+e.xAxis.marginTop*e.pix},r=e.xAxis.fontSize*e.pix,s=t.map((function(t,i){var a=e.xAxis.formatter?e.xAxis.formatter(t,i,e):t;return oo(String(a),r,o)})),l=Math.max.apply(this,s);if(1==e.xAxis.rotateLabel){n.angle=e.xAxis.rotateAngle*Math.PI/180;let t=e.xAxis.marginTop*e.pix*2+Math.abs(l*Math.sin(n.angle));t=t<r+e.xAxis.marginTop*e.pix*2?t+e.xAxis.marginTop*e.pix*2:t,n.xAxisHeight=t}return e.enableScroll&&e.xAxis.scrollShow&&(n.xAxisHeight+=6*e.pix),e.xAxis.disabled&&(n.xAxisHeight=0),n}function bo(t,e,i,a){var o=Ka({},{type:""},e.extra.bar),n={angle:0,xAxisHeight:e.xAxis.lineHeight*e.pix+e.xAxis.marginTop*e.pix};n.ranges=function(t,e,i,a){var o,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1;o="stack"==a?ro(t,e.categories.length):no(t);var r=[];(o=o.filter((function(t){return"object"==typeof t&&null!==t?t.constructor.toString().indexOf("Array")>-1?null!==t:null!==t.value:null!==t}))).map((function(t){"object"==typeof t?t.constructor.toString().indexOf("Array")>-1?"candle"==e.type?t.map((function(t){r.push(t)})):r.push(t[0]):r.push(t.value):r.push(t)}));var s=0,l=0;if(r.length>0&&(s=Math.min.apply(this,r),l=Math.max.apply(this,r)),n>-1?("number"==typeof e.xAxis.data[n].min&&(s=Math.min(e.xAxis.data[n].min,s)),"number"==typeof e.xAxis.data[n].max&&(l=Math.max(e.xAxis.data[n].max,l))):("number"==typeof e.xAxis.min&&(s=Math.min(e.xAxis.min,s)),"number"==typeof e.xAxis.max&&(l=Math.max(e.xAxis.max,l))),s===l){l+=l||10}for(var c=s,h=[],d=(l-c)/e.xAxis.splitNumber,p=0;p<=e.xAxis.splitNumber;p++)h.push(c+d*p);return h}(t,e,i,o.type),n.rangesFormat=n.ranges.map((function(t){return t=Ja.toFixed(t,2)}));var r=n.ranges.map((function(t){return t=Ja.toFixed(t,2)}));return(n=Object.assign(n,Oo(r,e))).eachSpacing,r.map((function(t){return oo(t,e.xAxis.fontSize*e.pix,a)})),!0===e.xAxis.disabled&&(n.xAxisHeight=0),n}function So(t,e,i,a,o){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,r=o.extra.radar||{};r.max=r.max||0;var s=Math.max(r.max,Math.max.apply(null,no(a))),l=[];for(let c=0;c<a.length;c++){let o=a[c],r={};r.color=o.color,r.legendShape=o.legendShape,r.pointShape=o.pointShape,r.data=[],o.data.forEach((function(a,o){let l={};l.angle=t[o],l.proportion=a/s,l.value=a,l.position=to(i*l.proportion*n*Math.cos(l.angle),i*l.proportion*n*Math.sin(l.angle),e),r.data.push(l)})),l.push(r)}return l}function wo(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,a=0,o=0;for(let n=0;n<t.length;n++){let e=t[n];e.data=null===e.data?0:e.data,a+=e.data}for(let n=0;n<t.length;n++){let o=t[n];o.data=null===o.data?0:o.data,o._proportion_=0===a?1/t.length*i:o.data/a*i,o._radius_=e}for(let n=0;n<t.length;n++){let e=t[n];e._start_=o,o+=2*e._proportion_*Math.PI}return t}function To(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;for(let n=0;n<t.length;n++)"funnel"==i.type?t[n].radius=t[n].data/t[0].data*e*o:t[n].radius=a*(t.length-n)/(a*t.length)*e*o,t[n]._proportion_=t[n].data/t[0].data;return t}function _o(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=0,r=0,s=[];for(let d=0;d<t.length;d++){let e=t[d];e.data=null===e.data?0:e.data,n+=e.data,s.push(e.data)}var l=Math.min.apply(null,s),c=Math.max.apply(null,s),h=a-i;for(let d=0;d<t.length;d++){let r=t[d];r.data=null===r.data?0:r.data,0===n?(r._proportion_=1/t.length*o,r._rose_proportion_=1/t.length*o):(r._proportion_=r.data/n*o,r._rose_proportion_="area"==e?1/t.length*o:r.data/n*o),r._radius_=i+h*((r.data-l)/(c-l))||a}for(let d=0;d<t.length;d++){let e=t[d];e._start_=r,r+=2*e._rose_proportion_*Math.PI}return t}function Ao(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==i&&(i=.999999);for(let a=0;a<t.length;a++){let o,n=t[a];n.data=null===n.data?0:n.data,o="circle"==e.type?2:"ccw"==e.direction?e.startAngle<e.endAngle?2+e.startAngle-e.endAngle:e.startAngle-e.endAngle:e.endAngle<e.startAngle?2+e.endAngle-e.startAngle:e.startAngle-e.endAngle,n._proportion_=o*n.data*i+e.startAngle,"ccw"==e.direction&&(n._proportion_=e.startAngle-o*n.data*i),n._proportion_>=2&&(n._proportion_=n._proportion_%2)}return t}function Co(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==i&&(i=.999999);for(let a=0;a<t.length;a++){let o,n=t[a];n.data=null===n.data?0:n.data,o="circle"==e.type?2:e.endAngle<e.startAngle?2+e.endAngle-e.startAngle:e.startAngle-e.endAngle,n._proportion_=o*n.data*i+e.startAngle,n._proportion_>=2&&(n._proportion_=n._proportion_%2)}return t}function Po(t,e,i){let a;a=i<e?2+i-e:e-i;let o=e;for(let n=0;n<t.length;n++)t[n].value=null===t[n].value?0:t[n].value,t[n]._startAngle_=o,t[n]._endAngle_=a*t[n].value+e,t[n]._endAngle_>=2&&(t[n]._endAngle_=t[n]._endAngle_%2),o=t[n]._endAngle_;return t}function ko(t,e,i){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;for(let o=0;o<t.length;o++){let n,r=t[o];if(r.data=null===r.data?0:r.data,"auto"==i.pointer.color){for(let t=0;t<e.length;t++)if(r.data<=e[t].value){r.color=e[t].color;break}}else r.color=i.pointer.color;n=i.endAngle<i.startAngle?2+i.endAngle-i.startAngle:i.startAngle-i.endAngle,r._endAngle_=n*r.data+i.startAngle,r._oldAngle_=i.oldAngle,i.oldAngle<i.endAngle&&(r._oldAngle_+=2),r.data>=i.oldData?r._proportion_=(r._endAngle_-r._oldAngle_)*a+i.oldAngle:r._proportion_=r._oldAngle_-(r._oldAngle_-r._endAngle_)*a,r._proportion_>=2&&(r._proportion_=r._proportion_%2)}return t}function Do(t,e,i,a,o,n){return t.map((function(t){if(null===t)return null;var o=0,r=0;return"mix"==n.type?(o=n.extra.mix.column.seriesGap*n.pix||0,r=n.extra.mix.column.categoryGap*n.pix||0):(o=n.extra.column.seriesGap*n.pix||0,r=n.extra.column.categoryGap*n.pix||0),o=Math.min(o,e/i),r=Math.min(r,e/i),t.width=Math.ceil((e-2*r-o*(i-1))/i),n.extra.mix&&n.extra.mix.column.width&&+n.extra.mix.column.width>0&&(t.width=Math.min(t.width,+n.extra.mix.column.width*n.pix)),n.extra.column&&n.extra.column.width&&+n.extra.column.width>0&&(t.width=Math.min(t.width,+n.extra.column.width*n.pix)),t.width<=0&&(t.width=1),t.x+=(a+.5-i/2)*(t.width+o),t}))}function Io(t,e,i,a,o,n){return t.map((function(t){if(null===t)return null;var o=0,r=0;return o=n.extra.bar.seriesGap*n.pix||0,r=n.extra.bar.categoryGap*n.pix||0,o=Math.min(o,e/i),r=Math.min(r,e/i),t.width=Math.ceil((e-2*r-o*(i-1))/i),n.extra.bar&&n.extra.bar.width&&+n.extra.bar.width>0&&(t.width=Math.min(t.width,+n.extra.bar.width*n.pix)),t.width<=0&&(t.width=1),t.y+=(a+.5-i/2)*(t.width+o),t}))}function Lo(t,e,i,a,o,n,r){var s=n.extra.column.categoryGap*n.pix||0;return t.map((function(t){return null===t?null:(t.width=e-2*s,n.extra.column&&n.extra.column.width&&+n.extra.column.width>0&&(t.width=Math.min(t.width,+n.extra.column.width*n.pix)),a>0&&(t.width-=r),t)}))}function Mo(t,e,i,a,o,n,r){var s=n.extra.column.categoryGap*n.pix||0;return t.map((function(t,i){return null===t?null:(t.width=Math.ceil(e-2*s),n.extra.column&&n.extra.column.width&&+n.extra.column.width>0&&(t.width=Math.min(t.width,+n.extra.column.width*n.pix)),t.width<=0&&(t.width=1),t)}))}function Fo(t,e,i,a,o,n,r){var s=n.extra.bar.categoryGap*n.pix||0;return t.map((function(t,i){return null===t?null:(t.width=Math.ceil(e-2*s),n.extra.bar&&n.extra.bar.width&&+n.extra.bar.width>0&&(t.width=Math.min(t.width,+n.extra.bar.width*n.pix)),t.width<=0&&(t.width=1),t)}))}function Oo(t,e,i){var a=e.width-e.area[1]-e.area[3],o=e.enableScroll?Math.min(e.xAxis.itemCount,t.length):t.length;("line"==e.type||"area"==e.type||"scatter"==e.type||"bubble"==e.type||"bar"==e.type)&&o>1&&"justify"==e.xAxis.boundaryGap&&(o-=1);var n=0;"mount"==e.type&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),o+=n=e.extra.mount.widthRatio-1);var r=a/o,s=[],l=e.area[3],c=e.width-e.area[1];return t.forEach((function(t,e){s.push(l+n/2*r+e*r)})),"justify"!==e.xAxis.boundaryGap&&(!0===e.enableScroll?s.push(l+n*r+t.length*r):s.push(c)),{xAxisPoints:s,startX:l,endX:c,eachSpacing:r}}function Eo(t,e,i,a,o,n,r){var s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,l=[],c=n.height-n.area[0]-n.area[2];return t.forEach((function(t,r){if(null===t)l.push(null);else{var h=[];t.forEach((function(t,l){var d={};d.x=a[r]+Math.round(o/2);var p=t.value||t,u=c*(p-e)/(i-e);u*=s,d.y=n.height-Math.round(u)-n.area[2],h.push(d)})),l.push(h)}})),l}function Ro(t,e,i,a,o,n,r){var s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,l="center";"line"!=n.type&&"area"!=n.type&&"scatter"!=n.type&&"bubble"!=n.type||(l=n.xAxis.boundaryGap);var c=[],h=n.height-n.area[0]-n.area[2],d=n.width-n.area[1]-n.area[3];return t.forEach((function(t,r){if(null===t)c.push(null);else{var p={};p.color=t.color,p.x=a[r];var u=t;if("object"==typeof t&&null!==t)if(t.constructor.toString().indexOf("Array")>-1){let e,i,a;e=[].concat(n.chartData.xAxisData.ranges),i=e.shift(),a=e.pop(),u=t[1],p.x=n.area[3]+d*(t[0]-i)/(a-i),"bubble"==n.type&&(p.r=t[2],p.t=t[3])}else u=t.value;"center"==l&&(p.x+=o/2);var g=h*(u-e)/(i-e);g*=s,p.y=n.height-g-n.area[2],c.push(p)}})),c}function No(t,e,i,a,o,n,r,s,l){l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1;var c=n.xAxis.boundaryGap,h=[],d=n.height-n.area[0]-n.area[2],p=n.width-n.area[1]-n.area[3];return t.forEach((function(t,r){if(null===t)h.push(null);else{var u={};if(u.color=t.color,"vertical"==s.animation){u.x=a[r];var g=t;if("object"==typeof t&&null!==t)if(t.constructor.toString().indexOf("Array")>-1){let e,i,a;e=[].concat(n.chartData.xAxisData.ranges),i=e.shift(),a=e.pop(),g=t[1],u.x=n.area[3]+p*(t[0]-i)/(a-i)}else g=t.value;"center"==c&&(u.x+=o/2);var f=d*(g-e)/(i-e);f*=l,u.y=n.height-f-n.area[2],h.push(u)}else{u.x=a[0]+o*r*l;g=t;"center"==c&&(u.x+=o/2);f=d*(g-e)/(i-e);u.y=n.height-f-n.area[2],h.push(u)}}})),h}function Bo(t,e,i,a,o,n,r,s,l){l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1;var c=[],h=n.height-n.area[0]-n.area[2],d=n.width-n.area[1]-n.area[3];return t.forEach((function(t,r){if(null===t)c.push(null);else{var s={};s.color=t.color,s.x=a[r];var p=t;if("object"==typeof t&&null!==t)if(t.constructor.toString().indexOf("Array")>-1){let e,i,a;e=[].concat(n.chartData.xAxisData.ranges),i=e.shift(),a=e.pop(),p=t[1],s.x=n.area[3]+d*(t[0]-i)/(a-i)}else p=t.value;s.x+=o/2;var u=h*(p*l-e)/(i-e);s.y=n.height-u-n.area[2],c.push(s)}})),c}function zo(t,e,i,a,o,n,r,s){var l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1,c=[],h=n.height-n.area[0]-n.area[2];n.width,n.area[1],n.area[3];var d=o*r.widthRatio;return t.forEach((function(t,r){if(null===t)c.push(null);else{var s={};s.color=t.color,s.x=a[r],s.x+=o/2;var p=t.data,u=h*(p*l-e)/(i-e);s.y=n.height-u-n.area[2],s.value=p,s.width=d,c.push(s)}})),c}function Uo(t,e,i,a,o,n,r){var s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,l=[];n.height,n.area[0],n.area[2];var c=n.width-n.area[1]-n.area[3];return t.forEach((function(t,o){if(null===t)l.push(null);else{var r={};r.color=t.color,r.y=a[o];var h=t;"object"==typeof t&&null!==t&&(h=t.value);var d=c*(h-e)/(i-e);d*=s,r.height=d,r.value=h,r.x=d+n.area[3],l.push(r)}})),l}function Wo(t,e,i,a,o,n,r,s,l){var c=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,h=[],d=n.height-n.area[0]-n.area[2];return t.forEach((function(t,r){if(null===t)h.push(null);else{var p={};if(p.color=t.color,p.x=a[r]+Math.round(o/2),s>0){var u=0;for(let t=0;t<=s;t++)u+=l[t].data[r];var g=d*(u-e)/(i-e),f=d*(u-t-e)/(i-e)}else{u=t;"object"==typeof t&&null!==t&&(u=t.value);g=d*(u-e)/(i-e),f=0}var x=f;g*=c,x*=c,p.y=n.height-Math.round(g)-n.area[2],p.y0=n.height-Math.round(x)-n.area[2],h.push(p)}})),h}function qo(t,e,i,a,o,n,r,s,l){var c=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,h=[],d=n.width-n.area[1]-n.area[3];return t.forEach((function(t,o){if(null===t)h.push(null);else{var r={};if(r.color=t.color,r.y=a[o],s>0){var p=0;for(let t=0;t<=s;t++)p+=l[t].data[o];var u=d*(p-e)/(i-e),g=d*(p-t-e)/(i-e)}else{p=t;"object"==typeof t&&null!==t&&(p=t.value);u=d*(p-e)/(i-e),g=0}var f=g;u*=c,f*=c,r.height=u-f,r.x=n.area[3]+u,r.x0=n.area[3]+f,h.push(r)}})),h}function jo(t,e,i,a,o){var n;n="stack"==a?ro(t,e.categories.length):no(t);var r=[];(n=n.filter((function(t){return"object"==typeof t&&null!==t?t.constructor.toString().indexOf("Array")>-1?null!==t:null!==t.value:null!==t}))).map((function(t){"object"==typeof t?t.constructor.toString().indexOf("Array")>-1?"candle"==e.type?t.map((function(t){r.push(t)})):r.push(t[1]):r.push(t.value):r.push(t)}));var s=o.min||0,l=o.max||0;r.length>0&&(s=Math.min.apply(this,r),l=Math.max.apply(this,r)),s===l&&(0==l?l=10:s=0);for(var c=function(t,e){var i=0,a=e-t;return{minRange:Va(t,"lower",i=a>=1e4?1e3:a>=1e3?100:a>=100?10:a>=10?5:a>=1?1:a>=.1?.1:a>=.01?.01:a>=.001?.001:a>=1e-4?1e-4:a>=1e-5?1e-5:1e-6),maxRange:Va(e,"upper",i)}}(s,l),h=void 0===o.min||null===o.min?c.minRange:o.min,d=((void 0===o.max||null===o.max?c.maxRange:o.max)-h)/e.yAxis.splitNumber,p=[],u=0;u<=e.yAxis.splitNumber;u++)p.push(h+d*u);return p.reverse()}function $o(t,e,i,a){var o=Ka({},{type:""},e.extra.column),n=e.yAxis.data.length,r=new Array(n);if(n>0){for(let e=0;e<n;e++){r[e]=[];for(let i=0;i<t.length;i++)t[i].index==e&&r[e].push(t[i])}var s=new Array(n),l=new Array(n),c=new Array(n);for(let t=0;t<n;t++){let n=e.yAxis.data[t];1==e.yAxis.disabled&&(n.disabled=!0),"categories"===n.type?(n.formatter||(n.formatter=(t,e,i)=>t+(n.unit||"")),n.categories=n.categories||e.categories,s[t]=n.categories):(n.formatter||(n.formatter=(t,e,i)=>Ja.toFixed(t,n.tofix||0)+(n.unit||"")),s[t]=jo(r[t],e,0,o.type,n));let h=n.fontSize*e.pix||i.fontSize;c[t]={position:n.position?n.position:"left",width:0},l[t]=s[t].map((function(i,o){return i=n.formatter(i,o,e),c[t].width=Math.max(c[t].width,oo(i,h,a)+5),i}));let d=n.calibration?4*e.pix:0;c[t].width+=d+3*e.pix,!0===n.disabled&&(c[t].width=0)}}else{s=new Array(1),l=new Array(1),c=new Array(1);"bar"===e.type?(s[0]=e.categories,e.yAxis.formatter||(e.yAxis.formatter=(t,e,i)=>t+(i.yAxis.unit||""))):(e.yAxis.formatter||(e.yAxis.formatter=(t,e,i)=>t.toFixed(i.yAxis.tofix)+(i.yAxis.unit||"")),s[0]=jo(t,e,0,o.type,{})),c[0]={position:"left",width:0};var h=e.yAxis.fontSize*e.pix||i.fontSize;l[0]=s[0].map((function(t,i){return t=e.yAxis.formatter(t,i,e),c[0].width=Math.max(c[0].width,oo(t,h,a)+5),t})),c[0].width+=3*e.pix,!0===e.yAxis.disabled?(c[0]={position:"left",width:0},e.yAxis.data[0]={disabled:!0}):(e.yAxis.data[0]={disabled:!1,position:"left",max:e.yAxis.max,min:e.yAxis.min,formatter:e.yAxis.formatter},"bar"===e.type&&(e.yAxis.data[0].categories=e.categories,e.yAxis.data[0].type="categories"))}return{rangesFormat:l,ranges:s,yAxisWidth:c}}function Ho(t,e){!0!==e.rotateLock?(t.translate(e.height,0),t.rotate(90*Math.PI/180)):!0!==e._rotate_&&(t.translate(e.height,0),t.rotate(90*Math.PI/180),e._rotate_=!0)}function Go(t,e,i,a,o){if(a.beginPath(),"hollow"==o.dataPointShapeType?(a.setStrokeStyle(e),a.setFillStyle(o.background),a.setLineWidth(2*o.pix)):(a.setStrokeStyle("#ffffff"),a.setFillStyle(e),a.setLineWidth(1*o.pix)),"diamond"===i)t.forEach((function(t,e){null!==t&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y),a.lineTo(t.x,t.y****),a.lineTo(t.x****,t.y),a.lineTo(t.x,t.y-4.5))}));else if("circle"===i)t.forEach((function(t,e){null!==t&&(a.moveTo(t.x*****o.pix,t.y),a.arc(t.x,t.y,3*o.pix,0,2*Math.PI,!1))}));else if("square"===i)t.forEach((function(t,e){null!==t&&(a.moveTo(t.x-3.5,t.y-3.5),a.rect(t.x-3.5,t.y-3.5,7,7))}));else if("triangle"===i)t.forEach((function(t,e){null!==t&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y****),a.lineTo(t.x****,t.y****),a.lineTo(t.x,t.y-4.5))}));else if("none"===i)return;a.closePath(),a.fill(),a.stroke()}function Ko(t,e,i,a,o,n,r){if(o.tooltip&&!(o.tooltip.group.length>0&&0==o.tooltip.group.includes(r))){var s="number"==typeof o.tooltip.index?o.tooltip.index:o.tooltip.index[o.tooltip.group.indexOf(r)];if(a.beginPath(),"hollow"==n.activeType?(a.setStrokeStyle(e),a.setFillStyle(o.background),a.setLineWidth(2*o.pix)):(a.setStrokeStyle("#ffffff"),a.setFillStyle(e),a.setLineWidth(1*o.pix)),"diamond"===i)t.forEach((function(t,e){null!==t&&s==e&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y),a.lineTo(t.x,t.y****),a.lineTo(t.x****,t.y),a.lineTo(t.x,t.y-4.5))}));else if("circle"===i)t.forEach((function(t,e){null!==t&&s==e&&(a.moveTo(t.x*****o.pix,t.y),a.arc(t.x,t.y,3*o.pix,0,2*Math.PI,!1))}));else if("square"===i)t.forEach((function(t,e){null!==t&&s==e&&(a.moveTo(t.x-3.5,t.y-3.5),a.rect(t.x-3.5,t.y-3.5,7,7))}));else if("triangle"===i)t.forEach((function(t,e){null!==t&&s==e&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y****),a.lineTo(t.x****,t.y****),a.lineTo(t.x,t.y-4.5))}));else if("none"===i)return;a.closePath(),a.fill(),a.stroke()}}function Jo(t,e,i,a){var o=t.title.fontSize||e.titleFontSize,n=t.subtitle.fontSize||e.subtitleFontSize,r=t.title.name||"",s=t.subtitle.name||"",l=t.title.color||t.fontColor,c=t.subtitle.color||t.fontColor,h=r?o:0,d=s?n:0;if(s){var p=oo(s,n*t.pix,i),u=a.x-p/2+(t.subtitle.offsetX||0)*t.pix,g=a.y+n*t.pix/2+(t.subtitle.offsetY||0)*t.pix;r&&(g+=(h*t.pix+5)/2),i.beginPath(),i.setFontSize(n*t.pix),i.setFillStyle(c),i.fillText(s,u,g),i.closePath(),i.stroke()}if(r){var f=oo(r,o*t.pix,i),x=a.x-f/2+(t.title.offsetX||0),m=a.y+o*t.pix/2+(t.title.offsetY||0)*t.pix;s&&(m-=(d*t.pix+5)/2),i.beginPath(),i.setFontSize(o*t.pix),i.setFillStyle(l),i.fillText(r,x,m),i.closePath(),i.stroke()}}function Xo(t,e,i,a,o){var n=e.data,r=e.textOffset?e.textOffset:0;t.forEach((function(t,s){if(null!==t){a.beginPath();var l=e.textSize?e.textSize*o.pix:i.fontSize;a.setFontSize(l),a.setFillStyle(e.textColor||o.fontColor);var c=n[s];"object"==typeof n[s]&&null!==n[s]&&(c=n[s].constructor.toString().indexOf("Array")>-1?n[s][1]:n[s].value);var h=e.formatter?e.formatter(c,s,e,o):c;a.setTextAlign("center"),a.fillText(String(h),t.x,t.y-4+r*o.pix),a.closePath(),a.stroke(),a.setTextAlign("left")}}))}function Vo(t,e,i,a,o){var n=e.data,r=e.textOffset?e.textOffset:0,s=o.extra.column.labelPosition;t.forEach((function(t,l){if(null!==t){a.beginPath();var c=e.textSize?e.textSize*o.pix:i.fontSize;a.setFontSize(c),a.setFillStyle(e.textColor||o.fontColor);var h=n[l];"object"==typeof n[l]&&null!==n[l]&&(h=n[l].constructor.toString().indexOf("Array")>-1?n[l][1]:n[l].value);var d=e.formatter?e.formatter(h,l,e,o):h;a.setTextAlign("center");var p=t.y-4*o.pix+r*o.pix;t.y>e.zeroPoints&&(p=t.y+r*o.pix+c),"insideTop"==s&&(p=t.y+c+r*o.pix,t.y>e.zeroPoints&&(p=t.y-r*o.pix-4*o.pix)),"center"==s&&(p=t.y+r*o.pix+(o.height-o.area[2]-t.y+c)/2,e.zeroPoints<o.height-o.area[2]&&(p=t.y+r*o.pix+(e.zeroPoints-t.y+c)/2),t.y>e.zeroPoints&&(p=t.y-r*o.pix-(t.y-e.zeroPoints-c)/2),"stack"==o.extra.column.type&&(p=t.y+r*o.pix+(t.y0-t.y+c)/2)),"bottom"==s&&(p=o.height-o.area[2]+r*o.pix-4*o.pix,e.zeroPoints<o.height-o.area[2]&&(p=e.zeroPoints+r*o.pix-4*o.pix),t.y>e.zeroPoints&&(p=e.zeroPoints-r*o.pix+c+2*o.pix),"stack"==o.extra.column.type&&(p=t.y0+r*o.pix-4*o.pix)),a.fillText(String(d),t.x,p),a.closePath(),a.stroke(),a.setTextAlign("left")}}))}function Yo(t,e,i,a,o,n){e.data;var r=e.textOffset?e.textOffset:0;o.extra.mount.labelPosition,t.forEach((function(t,s){if(null!==t){a.beginPath();var l=e[s].textSize?e[s].textSize*o.pix:i.fontSize;a.setFontSize(l),a.setFillStyle(e[s].textColor||o.fontColor);var c=t.value,h=e[s].formatter?e[s].formatter(c,s,e,o):c;a.setTextAlign("center");var d=t.y-4*o.pix+r*o.pix;t.y>n&&(d=t.y+r*o.pix+l),a.fillText(String(h),t.x,d),a.closePath(),a.stroke(),a.setTextAlign("left")}}))}function Qo(t,e,i,a,o){var n=e.data;e.textOffset&&e.textOffset,t.forEach((function(t,r){if(null!==t){a.beginPath();var s=e.textSize?e.textSize*o.pix:i.fontSize;a.setFontSize(s),a.setFillStyle(e.textColor||o.fontColor);var l=n[r];"object"==typeof n[r]&&null!==n[r]&&(l=n[r].value);var c=e.formatter?e.formatter(l,r,e,o):l;a.setTextAlign("left"),a.fillText(String(c),t.x+4*o.pix,t.y+s/2-3),a.closePath(),a.stroke()}}))}function Zo(t,e,i,a,o,n){let r;e=(e-=t.width/2+t.labelOffset*a.pix)<10?10:e,r=t.endAngle<t.startAngle?2+t.endAngle-t.startAngle:t.startAngle-t.endAngle;let s=r/t.splitLine.splitNumber,l=(t.endNumber-t.startNumber)/t.splitLine.splitNumber,c=t.startAngle,h=t.startNumber;for(let f=0;f<t.splitLine.splitNumber+1;f++){var d={x:e*Math.cos(c*Math.PI),y:e*Math.sin(c*Math.PI)},p=t.formatter?t.formatter(h,f,a):h;d.x+=i.x-oo(p,o.fontSize,n)/2,d.y+=i.y;var u=d.x,g=d.y;n.beginPath(),n.setFontSize(o.fontSize),n.setFillStyle(t.labelColor||a.fontColor),n.fillText(p,u,g+o.fontSize/2),n.closePath(),n.stroke(),c+=s,c>=2&&(c%=2),h+=l}}function tn(t,e,i,a,o,n){var r=a.extra.radar||{};t.forEach((function(t,s){if(!0===r.labelPointShow&&""!==a.categories[s]){var l={x:e*Math.cos(t),y:e*Math.sin(t)},c=to(l.x,l.y,i);n.setFillStyle(r.labelPointColor),n.beginPath(),n.arc(c.x,c.y,r.labelPointRadius*a.pix,0,2*Math.PI,!1),n.closePath(),n.fill()}if(!0===r.labelShow){var h={x:(e+o.radarLabelTextMargin*a.pix)*Math.cos(t),y:(e+o.radarLabelTextMargin*a.pix)*Math.sin(t)},d=to(h.x,h.y,i),p=d.x,u=d.y;Ja.approximatelyEqual(h.x,0)?p-=oo(a.categories[s]||"",o.fontSize,n)/2:h.x<0&&(p-=oo(a.categories[s]||"",o.fontSize,n)),n.beginPath(),n.setFontSize(o.fontSize),n.setFillStyle(r.labelColor||a.fontColor),n.fillText(a.categories[s]||"",p,u+o.fontSize/2),n.closePath(),n.stroke()}}))}function en(t,e,i,a,o,n){var r=i.pieChartLinePadding,s=[],l=null,c=t.map((function(i,a){var o=i.formatter?i.formatter(i,a,t,e):Ja.toFixed(100*i._proportion_.toFixed(4))+"%";o=i.labelText?i.labelText:o;var n=2*Math.PI-(i._start_+2*Math.PI*i._proportion_/2);return i._rose_proportion_&&(n=2*Math.PI-(i._start_+2*Math.PI*i._rose_proportion_/2)),{arc:n,text:o,color:i.color,radius:i._radius_,textColor:i.textColor,textSize:i.textSize,labelShow:i.labelShow}}));for(let h=0;h<c.length;h++){let t=c[h],o=Math.cos(t.arc)*(t.radius+r),n=Math.sin(t.arc)*(t.radius+r),d=Math.cos(t.arc)*t.radius,p=Math.sin(t.arc)*t.radius,u=o>=0?o+i.pieChartTextPadding:o-i.pieChartTextPadding,g=n,f=oo(t.text,t.textSize*e.pix||i.fontSize,a),x=g;l&&Ja.isSameXCoordinateArea(l.start,{x:u})&&(x=u>0?Math.min(g,l.start.y):o<0||g>0?Math.max(g,l.start.y):Math.min(g,l.start.y)),u<0&&(u-=f),l=eo({lineStart:{x:d,y:p},lineEnd:{x:o,y:n},start:{x:u,y:x},width:f,height:i.fontSize,text:t.text,color:t.color,textColor:t.textColor,textSize:t.textSize},l),s.push(l)}for(let h=0;h<s.length;h++){if(!1===c[h].labelShow)continue;let t=s[h],o=to(t.lineStart.x,t.lineStart.y,n),r=to(t.lineEnd.x,t.lineEnd.y,n),l=to(t.start.x,t.start.y,n);a.setLineWidth(1*e.pix),a.setFontSize(t.textSize*e.pix||i.fontSize),a.beginPath(),a.setStrokeStyle(t.color),a.setFillStyle(t.color),a.moveTo(o.x,o.y);let d=t.start.x<0?l.x+t.width:l.x,p=t.start.x<0?l.x-5:l.x+5;a.quadraticCurveTo(r.x,r.y,d,l.y),a.moveTo(o.x,o.y),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(l.x+t.width,l.y),a.arc(d,l.y,2*e.pix,0,2*Math.PI),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(t.textSize*e.pix||i.fontSize),a.setFillStyle(t.textColor||e.fontColor),a.fillText(t.text,p,l.y+3),a.closePath(),a.stroke(),a.closePath()}}function an(t,e,i){let a=Ka({},{type:"solid",dashLength:4,data:[]},t.extra.markLine),o=t.area[3],n=t.width-t.area[1],r=function(t,e){let i,a,o=e.height-e.area[0]-e.area[2];for(let n=0;n<t.length;n++){t[n].yAxisIndex=t[n].yAxisIndex?t[n].yAxisIndex:0;let r=[].concat(e.chartData.yAxisData.ranges[t[n].yAxisIndex]);i=r.pop(),a=r.shift();let s=o*(t[n].value-i)/(a-i);t[n].y=e.height-Math.round(s)-e.area[2]}return t}(a.data,t);for(let s=0;s<r.length;s++){let e=Ka({},{lineColor:"#DE4A42",showLabel:!1,labelFontSize:13,labelPadding:6,labelFontColor:"#666666",labelBgColor:"#DFE8FF",labelBgOpacity:.8,labelAlign:"left",labelOffsetX:0,labelOffsetY:0},r[s]);if("dash"==a.type&&i.setLineDash([a.dashLength,a.dashLength]),i.setStrokeStyle(e.lineColor),i.setLineWidth(1*t.pix),i.beginPath(),i.moveTo(o,e.y),i.lineTo(n,e.y),i.stroke(),i.setLineDash([]),e.showLabel){let a=e.labelFontSize*t.pix,o=e.labelText?e.labelText:e.value;i.setFontSize(a);let n=oo(o,a,i)+e.labelPadding*t.pix*2,r="left"==e.labelAlign?t.area[3]-n:t.width-t.area[1];r+=e.labelOffsetX;let s=e.y-.5*a-e.labelPadding*t.pix;s+=e.labelOffsetY;let l=r+e.labelPadding*t.pix;e.y,i.setFillStyle(Xa(e.labelBgColor,e.labelBgOpacity)),i.setStrokeStyle(e.labelBgColor),i.setLineWidth(1*t.pix),i.beginPath(),i.rect(r,s,n,a+2*e.labelPadding*t.pix),i.closePath(),i.stroke(),i.fill(),i.setFontSize(a),i.setTextAlign("left"),i.setFillStyle(e.labelFontColor),i.fillText(String(o),l,s+a+e.labelPadding*t.pix/2),i.stroke(),i.setTextAlign("left")}}}function on(t,e,i,a,o){var n=Ka({},{gridType:"solid",dashLength:4},t.extra.tooltip),r=t.area[3],s=t.width-t.area[1];if("dash"==n.gridType&&i.setLineDash([n.dashLength,n.dashLength]),i.setStrokeStyle(n.gridColor||"#cccccc"),i.setLineWidth(1*t.pix),i.beginPath(),i.moveTo(r,t.tooltip.offset.y),i.lineTo(s,t.tooltip.offset.y),i.stroke(),i.setLineDash([]),n.yAxisLabel){let a=n.boxPadding*t.pix,o=function(t,e,i,a,o){let n=[].concat(i.chartData.yAxisData.ranges),r=i.height-i.area[0]-i.area[2],s=i.area[0],l=[];for(let c=0;c<n.length;c++){let e=Math.max.apply(this,n[c]),a=e-(e-Math.min.apply(this,n[c]))*(t-s)/r;a=i.yAxis.data&&i.yAxis.data[c].formatter?i.yAxis.data[c].formatter(a,c,i):a.toFixed(0),l.push(String(a))}return l}(t.tooltip.offset.y,t.series,t),r=t.chartData.yAxisData.yAxisWidth,s=t.area[3],l=t.width-t.area[1];for(let c=0;c<o.length;c++){i.setFontSize(n.fontSize*t.pix);let h,d,p,u=oo(o[c],n.fontSize*t.pix,i);"left"==r[c].position?(h=s-(u+2*a)-2*t.pix,d=Math.max(h,h+u+2*a)):(h=l+2*t.pix,d=Math.max(h+r[c].width,h+u+2*a)),p=d-h;let g=h+(p-u)/2,f=t.tooltip.offset.y;i.beginPath(),i.setFillStyle(Xa(n.labelBgColor||e.toolTipBackground,n.labelBgOpacity||e.toolTipOpacity)),i.setStrokeStyle(n.labelBgColor||e.toolTipBackground),i.setLineWidth(1*t.pix),i.rect(h,f-.5*e.fontSize-a,p,e.fontSize+2*a),i.closePath(),i.stroke(),i.fill(),i.beginPath(),i.setFontSize(e.fontSize),i.setFillStyle(n.labelFontColor||t.fontColor),i.fillText(o[c],g,f+.5*e.fontSize),i.closePath(),i.stroke(),"left"==r[c].position?s-=r[c].width+t.yAxis.padding*t.pix:l+=r[c].width+t.yAxis.padding*t.pix}}}function nn(t,e,i,a,o){var n=Ka({},{activeBgColor:"#000000",activeBgOpacity:.08,activeWidth:o},e.extra.column);n.activeWidth=n.activeWidth>o?o:n.activeWidth;var r=e.area[0],s=e.height-e.area[2];a.beginPath(),a.setFillStyle(Xa(n.activeBgColor,n.activeBgOpacity)),a.rect(t-n.activeWidth/2,r,n.activeWidth,s-r),a.closePath(),a.fill(),a.setFillStyle("#FFFFFF")}function rn(t,e,i,a,o){var n=Ka({},{activeBgColor:"#000000",activeBgOpacity:.08},e.extra.bar),r=e.area[3],s=e.width-e.area[1];a.beginPath(),a.setFillStyle(Xa(n.activeBgColor,n.activeBgOpacity)),a.rect(r,t-o/2,s-r,o),a.closePath(),a.fill(),a.setFillStyle("#FFFFFF")}function sn(t,e,i,a,o,n,r){var s=Ka({},{showBox:!0,showArrow:!0,showCategory:!1,bgColor:"#000000",bgOpacity:.7,borderColor:"#000000",borderWidth:0,borderRadius:0,borderOpacity:.7,boxPadding:3,fontColor:"#FFFFFF",fontSize:13,lineHeight:20,legendShow:!0,legendShape:"auto",splitLine:!0},i.extra.tooltip);1==s.showCategory&&i.categories&&t.unshift({text:i.categories[i.tooltip.index],color:null});var l=s.fontSize*i.pix,c=s.lineHeight*i.pix,h=s.boxPadding*i.pix,d=l,p=5*i.pix;0==s.legendShow&&(d=0,p=0);var u=s.showArrow?8*i.pix:0,g=!1;"line"!=i.type&&"mount"!=i.type&&"area"!=i.type&&"candle"!=i.type&&"mix"!=i.type||1==s.splitLine&&function(t,e,i,a){var o=e.extra.tooltip||{};o.gridType=null==o.gridType?"solid":o.gridType,o.dashLength=null==o.dashLength?4:o.dashLength;var n=e.area[0],r=e.height-e.area[2];if("dash"==o.gridType&&a.setLineDash([o.dashLength,o.dashLength]),a.setStrokeStyle(o.gridColor||"#cccccc"),a.setLineWidth(1*e.pix),a.beginPath(),a.moveTo(t,n),a.lineTo(t,r),a.stroke(),a.setLineDash([]),o.xAxisLabel){let n=e.categories[e.tooltip.index];a.setFontSize(i.fontSize);let s=oo(n,i.fontSize,a),l=t-.5*s,c=r+2*e.pix;a.beginPath(),a.setFillStyle(Xa(o.labelBgColor||i.toolTipBackground,o.labelBgOpacity||i.toolTipOpacity)),a.setStrokeStyle(o.labelBgColor||i.toolTipBackground),a.setLineWidth(1*e.pix),a.rect(l-o.boxPadding*e.pix,c,s+2*o.boxPadding*e.pix,i.fontSize+2*o.boxPadding*e.pix),a.closePath(),a.stroke(),a.fill(),a.beginPath(),a.setFontSize(i.fontSize),a.setFillStyle(o.labelFontColor||e.fontColor),a.fillText(String(n),l,c+o.boxPadding*e.pix+i.fontSize),a.closePath(),a.stroke()}}(i.tooltip.offset.x,i,a,o),(e=Ka({x:0,y:0},e)).y-=8*i.pix;var f=t.map((function(t){return oo(t.text,l,o)})),x=d+p+4*h+Math.max.apply(null,f),m=2*h+t.length*c;if(0!=s.showBox){e.x-Math.abs(i._scrollDistance_||0)+u+x>i.width&&(g=!0),m+e.y>i.height&&(e.y=i.height-m),o.beginPath(),o.setFillStyle(Xa(s.bgColor,s.bgOpacity)),o.setLineWidth(s.borderWidth*i.pix),o.setStrokeStyle(Xa(s.borderColor,s.borderOpacity));var y=s.borderRadius;g?(x+u>i.width&&(e.x=i.width+Math.abs(i._scrollDistance_||0)+u+(x-i.width)),x>e.x&&(e.x=i.width+Math.abs(i._scrollDistance_||0)+u+(x-i.width)),s.showArrow&&(o.moveTo(e.x,e.y+10*i.pix),o.lineTo(e.x-u,e.y+10*i.pix+5*i.pix)),o.arc(e.x-u-y,e.y+m-y,y,0,Math.PI/2,!1),o.arc(e.x-u-Math.round(x)+y,e.y+m-y,y,Math.PI/2,Math.PI,!1),o.arc(e.x-u-Math.round(x)+y,e.y+y,y,-Math.PI,-Math.PI/2,!1),o.arc(e.x-u-y,e.y+y,y,-Math.PI/2,0,!1),s.showArrow&&(o.lineTo(e.x-u,e.y+10*i.pix-5*i.pix),o.lineTo(e.x,e.y+10*i.pix))):(s.showArrow&&(o.moveTo(e.x,e.y+10*i.pix),o.lineTo(e.x+u,e.y+10*i.pix-5*i.pix)),o.arc(e.x+u+y,e.y+y,y,-Math.PI,-Math.PI/2,!1),o.arc(e.x+u+Math.round(x)-y,e.y+y,y,-Math.PI/2,0,!1),o.arc(e.x+u+Math.round(x)-y,e.y+m-y,y,0,Math.PI/2,!1),o.arc(e.x+u+y,e.y+m-y,y,Math.PI/2,Math.PI,!1),s.showArrow&&(o.lineTo(e.x+u,e.y+10*i.pix+5*i.pix),o.lineTo(e.x,e.y+10*i.pix))),o.closePath(),o.fill(),s.borderWidth>0&&o.stroke(),s.legendShow&&t.forEach((function(t,a){if(null!==t.color){o.beginPath(),o.setFillStyle(t.color);var n=e.x+u+2*h,r=e.y+(c-l)/2+c*a+h+1;switch(g&&(n=e.x-x-u+2*h),t.legendShape){case"line":o.moveTo(n,r+.5*d-2*i.pix),o.fillRect(n,r+.5*d-2*i.pix,d,4*i.pix);break;case"triangle":o.moveTo(n+7.5*i.pix,r+.5*d-5*i.pix),o.lineTo(n*****i.pix,r+.5*d+5*i.pix),o.lineTo(n+12.5*i.pix,r+.5*d+5*i.pix),o.lineTo(n+7.5*i.pix,r+.5*d-5*i.pix);break;case"diamond":o.moveTo(n+7.5*i.pix,r+.5*d-5*i.pix),o.lineTo(n*****i.pix,r+.5*d),o.lineTo(n+7.5*i.pix,r+.5*d+5*i.pix),o.lineTo(n+12.5*i.pix,r+.5*d),o.lineTo(n+7.5*i.pix,r+.5*d-5*i.pix);break;case"circle":o.moveTo(n+7.5*i.pix,r+.5*d),o.arc(n+7.5*i.pix,r+.5*d,5*i.pix,0,2*Math.PI);break;case"rect":default:o.moveTo(n,r+.5*d-5*i.pix),o.fillRect(n,r+.5*d-5*i.pix,15*i.pix,10*i.pix);break;case"square":o.moveTo(n+2*i.pix,r+.5*d-5*i.pix),o.fillRect(n+2*i.pix,r+.5*d-5*i.pix,10*i.pix,10*i.pix)}o.closePath(),o.fill()}})),t.forEach((function(t,i){var a=e.x+u+2*h+d+p;g&&(a=e.x-x-u+2*h+d+p);var n=e.y+c*i+(c-l)/2-1+h+l;o.beginPath(),o.setFontSize(l),o.setTextBaseline("normal"),o.setFillStyle(s.fontColor),o.fillText(t.text,a,n),o.closePath(),o.stroke()}))}}function ln(t,e,i,a,o,n){(t.extra.tooltip||{}).horizentalLine&&t.tooltip&&1===a&&("line"==t.type||"area"==t.type||"column"==t.type||"mount"==t.type||"candle"==t.type||"mix"==t.type)&&on(t,e,i),i.save(),t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&i.translate(t._scrollDistance_,0),t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===a&&sn(t.tooltip.textList,t.tooltip.offset,t,e,i),i.restore()}function cn(t,e,i,a){let o=e.chartData.xAxisData,n=o.xAxisPoints,r=o.startX,s=o.endX,l=o.eachSpacing;var c="center";"bar"!=e.type&&"line"!=e.type&&"area"!=e.type&&"scatter"!=e.type&&"bubble"!=e.type||(c=e.xAxis.boundaryGap);var h=e.height-e.area[2],d=e.area[0];if(e.enableScroll&&e.xAxis.scrollShow){var p=e.height-e.area[2]+i.xAxisHeight,u=s-r,g=l*(n.length-1);"mount"==e.type&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),g+=(e.extra.mount.widthRatio-1)*l);var f=u*u/g,x=0;e._scrollDistance_&&(x=-e._scrollDistance_*u/g),a.beginPath(),a.setLineCap("round"),a.setLineWidth(6*e.pix),a.setStrokeStyle(e.xAxis.scrollBackgroundColor||"#EFEBEF"),a.moveTo(r,p),a.lineTo(s,p),a.stroke(),a.closePath(),a.beginPath(),a.setLineCap("round"),a.setLineWidth(6*e.pix),a.setStrokeStyle(e.xAxis.scrollColor||"#A6A6A6"),a.moveTo(r+x,p),a.lineTo(r+x+f,p),a.stroke(),a.closePath(),a.setLineCap("butt")}if(a.save(),e._scrollDistance_&&0!==e._scrollDistance_&&a.translate(e._scrollDistance_,0),!0===e.xAxis.calibration&&(a.setStrokeStyle(e.xAxis.gridColor||"#cccccc"),a.setLineCap("butt"),a.setLineWidth(1*e.pix),n.forEach((function(t,i){i>0&&(a.beginPath(),a.moveTo(t-l/2,h),a.lineTo(t-l/2,h+3*e.pix),a.closePath(),a.stroke())}))),!0!==e.xAxis.disableGrid&&(a.setStrokeStyle(e.xAxis.gridColor||"#cccccc"),a.setLineCap("butt"),a.setLineWidth(1*e.pix),"dash"==e.xAxis.gridType&&a.setLineDash([e.xAxis.dashLength*e.pix,e.xAxis.dashLength*e.pix]),e.xAxis.gridEval=e.xAxis.gridEval||1,n.forEach((function(t,i){i%e.xAxis.gridEval==0&&(a.beginPath(),a.moveTo(t,h),a.lineTo(t,d),a.stroke())})),a.setLineDash([])),!0!==e.xAxis.disabled){let o=t.length;e.xAxis.labelCount&&(o=e.xAxis.itemCount?Math.ceil(t.length/e.xAxis.itemCount*e.xAxis.labelCount):e.xAxis.labelCount,o-=1);let r=Math.ceil(t.length/o),s=[],d=t.length;for(let e=0;e<d;e++)e%r!=0?s.push(""):s.push(t[e]);s[d-1]=t[d-1];var m=e.xAxis.fontSize*e.pix||i.fontSize;0===i._xAxisTextAngle_?s.forEach((function(t,i){var o=e.xAxis.formatter?e.xAxis.formatter(t,i,e):t,r=-oo(String(o),m,a)/2;"center"==c&&(r+=l/2),e.xAxis.scrollShow&&e.pix;var s=e._scrollDistance_||0,d="center"==c?n[i]+l/2:n[i];d-Math.abs(s)>=e.area[3]-1&&d-Math.abs(s)<=e.width-e.area[1]+1&&(a.beginPath(),a.setFontSize(m),a.setFillStyle(e.xAxis.fontColor||e.fontColor),a.fillText(String(o),n[i]+r,h+e.xAxis.marginTop*e.pix+(e.xAxis.lineHeight-e.xAxis.fontSize)*e.pix/2+e.xAxis.fontSize*e.pix),a.closePath(),a.stroke())})):s.forEach((function(t,o){var r=e.xAxis.formatter?e.xAxis.formatter(t):t,s=e._scrollDistance_||0,d="center"==c?n[o]+l/2:n[o];if(d-Math.abs(s)>=e.area[3]-1&&d-Math.abs(s)<=e.width-e.area[1]+1){a.save(),a.beginPath(),a.setFontSize(m),a.setFillStyle(e.xAxis.fontColor||e.fontColor);var p=oo(String(r),m,a),u=n[o];"center"==c&&(u=n[o]+l/2),e.xAxis.scrollShow&&e.pix;var g=h+e.xAxis.marginTop*e.pix+m-m*Math.abs(Math.sin(i._xAxisTextAngle_));e.xAxis.rotateAngle<0?(u-=m/2,p=0):(u+=m/2,p=-p),a.translate(u,g),a.rotate(-1*i._xAxisTextAngle_),a.fillText(String(r),p,0),a.closePath(),a.stroke(),a.restore()}}))}a.restore(),e.xAxis.title&&(a.beginPath(),a.setFontSize(e.xAxis.titleFontSize*e.pix),a.setFillStyle(e.xAxis.titleFontColor),a.fillText(String(e.xAxis.title),e.width-e.area[1]+e.xAxis.titleOffsetX*e.pix,e.height-e.area[2]+e.xAxis.marginTop*e.pix+(e.xAxis.lineHeight-e.xAxis.titleFontSize)*e.pix/2+(e.xAxis.titleFontSize+e.xAxis.titleOffsetY)*e.pix),a.closePath(),a.stroke()),e.xAxis.axisLine&&(a.beginPath(),a.setStrokeStyle(e.xAxis.axisLineColor),a.setLineWidth(1*e.pix),a.moveTo(r,e.height-e.area[2]),a.lineTo(s,e.height-e.area[2]),a.stroke())}function hn(t,e,i,a){if(!0===e.yAxis.disableGrid)return;let o=(e.height-e.area[0]-e.area[2])/e.yAxis.splitNumber,n=e.area[3],r=e.chartData.xAxisData.xAxisPoints,s=e.chartData.xAxisData.eachSpacing,l=s*(r.length-1);"mount"==e.type&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),l+=(e.extra.mount.widthRatio-1)*s);let c=n+l,h=[],d=1;!1===e.xAxis.axisLine&&(d=0);for(let p=d;p<e.yAxis.splitNumber+1;p++)h.push(e.height-e.area[2]-o*p);a.save(),e._scrollDistance_&&0!==e._scrollDistance_&&a.translate(e._scrollDistance_,0),"dash"==e.yAxis.gridType&&a.setLineDash([e.yAxis.dashLength*e.pix,e.yAxis.dashLength*e.pix]),a.setStrokeStyle(e.yAxis.gridColor),a.setLineWidth(1*e.pix),h.forEach((function(t,e){a.beginPath(),a.moveTo(n,t),a.lineTo(c,t),a.stroke()})),a.setLineDash([]),a.restore()}function dn(t,e,i,a){if(!0===e.yAxis.disabled)return;var o=e.height-e.area[0]-e.area[2],n=o/e.yAxis.splitNumber,r=e.area[3],s=e.width-e.area[1],l=e.height-e.area[2];a.beginPath(),a.setFillStyle(e.background),1==e.enableScroll&&e.xAxis.scrollPosition&&"left"!==e.xAxis.scrollPosition&&a.fillRect(0,0,r,l+2*e.pix),1==e.enableScroll&&e.xAxis.scrollPosition&&"right"!==e.xAxis.scrollPosition&&a.fillRect(s,0,e.width,l+2*e.pix),a.closePath(),a.stroke();let c=e.area[3],h=e.width-e.area[1],d=e.area[3]+(e.width-e.area[1]-e.area[3])/2;if(e.yAxis.data)for(let u=0;u<e.yAxis.data.length;u++){let t=e.yAxis.data[u];var p=[];if("categories"===t.type)for(let i=0;i<=t.categories.length;i++)p.push(e.area[0]+o/t.categories.length/2+o/t.categories.length*i);else for(let i=0;i<=e.yAxis.splitNumber;i++)p.push(e.area[0]+n*i);if(!0!==t.disabled){let o=e.chartData.yAxisData.rangesFormat[u],n=t.fontSize?t.fontSize*e.pix:i.fontSize,r=e.chartData.yAxisData.yAxisWidth[u],s=t.textAlign||"right";if(o.forEach((function(i,o){var l=p[o];a.beginPath(),a.setFontSize(n),a.setLineWidth(1*e.pix),a.setStrokeStyle(t.axisLineColor||"#cccccc"),a.setFillStyle(t.fontColor||e.fontColor);let u=0,g=4*e.pix;if("left"==r.position){switch(1==t.calibration&&(a.moveTo(c,l),a.lineTo(c-3*e.pix,l),g+=3*e.pix),s){case"left":a.setTextAlign("left"),u=c-r.width;break;case"right":a.setTextAlign("right"),u=c-g;break;default:a.setTextAlign("center"),u=c-r.width/2}a.fillText(String(i),u,l+n/2-3*e.pix)}else if("right"==r.position){switch(1==t.calibration&&(a.moveTo(h,l),a.lineTo(h+3*e.pix,l),g+=3*e.pix),s){case"left":a.setTextAlign("left"),u=h+g;break;case"right":a.setTextAlign("right"),u=h+r.width;break;default:a.setTextAlign("center"),u=h+r.width/2}a.fillText(String(i),u,l+n/2-3*e.pix)}else if("center"==r.position){switch(1==t.calibration&&(a.moveTo(d,l),a.lineTo(d-3*e.pix,l),g+=3*e.pix),s){case"left":a.setTextAlign("left"),u=d-r.width;break;case"right":a.setTextAlign("right"),u=d-g;break;default:a.setTextAlign("center"),u=d-r.width/2}a.fillText(String(i),u,l+n/2-3*e.pix)}a.closePath(),a.stroke(),a.setTextAlign("left")})),!1!==t.axisLine&&(a.beginPath(),a.setStrokeStyle(t.axisLineColor||"#cccccc"),a.setLineWidth(1*e.pix),"left"==r.position?(a.moveTo(c,e.height-e.area[2]),a.lineTo(c,e.area[0])):"right"==r.position?(a.moveTo(h,e.height-e.area[2]),a.lineTo(h,e.area[0])):"center"==r.position&&(a.moveTo(d,e.height-e.area[2]),a.lineTo(d,e.area[0])),a.stroke()),e.yAxis.showTitle){let o=t.titleFontSize*e.pix||i.fontSize,n=t.title;a.beginPath(),a.setFontSize(o),a.setFillStyle(t.titleFontColor||e.fontColor),"left"==r.position?a.fillText(n,c-oo(n,o,a)/2+(t.titleOffsetX||0),e.area[0]-(10-(t.titleOffsetY||0))*e.pix):"right"==r.position?a.fillText(n,h-oo(n,o,a)/2+(t.titleOffsetX||0),e.area[0]-(10-(t.titleOffsetY||0))*e.pix):"center"==r.position&&a.fillText(n,d-oo(n,o,a)/2+(t.titleOffsetX||0),e.area[0]-(10-(t.titleOffsetY||0))*e.pix),a.closePath(),a.stroke()}"left"==r.position?c-=r.width+e.yAxis.padding*e.pix:h+=r.width+e.yAxis.padding*e.pix}}}function pn(t,e,i,a,o){if(!1===e.legend.show)return;let n=o.legendData,r=n.points,s=n.area,l=e.legend.padding*e.pix,c=e.legend.fontSize*e.pix,h=15*e.pix,d=5*e.pix,p=e.legend.itemGap*e.pix,u=Math.max(e.legend.lineHeight*e.pix,c);a.beginPath(),a.setLineWidth(e.legend.borderWidth*e.pix),a.setStrokeStyle(e.legend.borderColor),a.setFillStyle(e.legend.backgroundColor),a.moveTo(s.start.x,s.start.y),a.rect(s.start.x,s.start.y,s.width,s.height),a.closePath(),a.fill(),a.stroke(),r.forEach((function(t,o){let r=0,g=0;r=n.widthArr[o],g=n.heightArr[o];let f=0,x=0;if("top"==e.legend.position||"bottom"==e.legend.position){switch(e.legend.float){case"left":f=s.start.x+l;break;case"right":f=s.start.x+s.width-r;break;default:f=s.start.x+(s.width-r)/2}x=s.start.y+l+o*u}else r=0==o?0:n.widthArr[o-1],f=s.start.x+l+r,x=s.start.y+l+(s.height-g)/2;a.setFontSize(i.fontSize);for(let i=0;i<t.length;i++){let o=t[i];switch(o.area=[0,0,0,0],o.area[0]=f,o.area[1]=x,o.area[3]=x+u,a.beginPath(),a.setLineWidth(1*e.pix),a.setStrokeStyle(o.show?o.color:e.legend.hiddenColor),a.setFillStyle(o.show?o.color:e.legend.hiddenColor),o.legendShape){case"line":a.moveTo(f,x+.5*u-2*e.pix),a.fillRect(f,x+.5*u-2*e.pix,15*e.pix,4*e.pix);break;case"triangle":a.moveTo(f+7.5*e.pix,x+.5*u-5*e.pix),a.lineTo(f*****e.pix,x+.5*u+5*e.pix),a.lineTo(f+12.5*e.pix,x+.5*u+5*e.pix),a.lineTo(f+7.5*e.pix,x+.5*u-5*e.pix);break;case"diamond":a.moveTo(f+7.5*e.pix,x+.5*u-5*e.pix),a.lineTo(f*****e.pix,x+.5*u),a.lineTo(f+7.5*e.pix,x+.5*u+5*e.pix),a.lineTo(f+12.5*e.pix,x+.5*u),a.lineTo(f+7.5*e.pix,x+.5*u-5*e.pix);break;case"circle":a.moveTo(f+7.5*e.pix,x+.5*u),a.arc(f+7.5*e.pix,x+.5*u,5*e.pix,0,2*Math.PI);break;case"rect":default:a.moveTo(f,x+.5*u-5*e.pix),a.fillRect(f,x+.5*u-5*e.pix,15*e.pix,10*e.pix);break;case"square":a.moveTo(f+5*e.pix,x+.5*u-5*e.pix),a.fillRect(f+5*e.pix,x+.5*u-5*e.pix,10*e.pix,10*e.pix);case"none":}a.closePath(),a.fill(),a.stroke(),f+=h+d;let n=.5*u+.5*c-2;const r=o.legendText?o.legendText:o.name;a.beginPath(),a.setFontSize(c),a.setFillStyle(o.show?e.legend.fontColor:e.legend.hiddenColor),a.fillText(r,f,x+n),a.closePath(),a.stroke(),"top"==e.legend.position||"bottom"==e.legend.position?(f+=oo(r,c,a)+p,o.area[2]=f):(o.area[2]=f+oo(r,c,a)+p,f-=h+d,x+=u)}}))}function un(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=Ka({},{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,ringWidth:30,customRadius:0,border:!1,borderWidth:2,borderColor:"#FFFFFF",centerColor:"#FFFFFF",linearType:"none",customColor:[]},"pie"==e.type?e.extra.pie:e.extra.ring),r={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2};0==i.pieChartLinePadding&&(i.pieChartLinePadding=n.activeRadius*e.pix);var s=Math.min((e.width-e.area[1]-e.area[3])/2-i.pieChartLinePadding-i.pieChartTextPadding-i._pieTextMaxLength_,(e.height-e.area[0]-e.area[2])/2-i.pieChartLinePadding-i.pieChartTextPadding);s=s<10?10:s,n.customRadius>0&&(s=n.customRadius*e.pix),t=wo(t,s,o);var l=n.activeRadius*e.pix;if(n.customColor=ao(n.linearType,n.customColor,t,i),(t=t.map((function(t){return t._start_+=n.offsetAngle*Math.PI/180,t}))).forEach((function(t,i){e.tooltip&&e.tooltip.index==i&&(a.beginPath(),a.setFillStyle(Xa(t.color,n.activeOpacity||.5)),a.moveTo(r.x,r.y),a.arc(r.x,r.y,t._radius_+l,t._start_,t._start_+2*t._proportion_*Math.PI),a.closePath(),a.fill()),a.beginPath(),a.setLineWidth(n.borderWidth*e.pix),a.lineJoin="round",a.setStrokeStyle(n.borderColor);var o,s=t.color;"custom"==n.linearType&&((o=a.createCircularGradient?a.createCircularGradient(r.x,r.y,t._radius_):a.createRadialGradient(r.x,r.y,0,r.x,r.y,t._radius_)).addColorStop(0,Xa(n.customColor[t.linearIndex],1)),o.addColorStop(1,Xa(t.color,1)),s=o);a.setFillStyle(s),a.moveTo(r.x,r.y),a.arc(r.x,r.y,t._radius_,t._start_,t._start_+2*t._proportion_*Math.PI),a.closePath(),a.fill(),1==n.border&&a.stroke()})),"ring"===e.type){var c=.6*s;"number"==typeof n.ringWidth&&n.ringWidth>0&&(c=Math.max(0,s-n.ringWidth*e.pix)),a.beginPath(),a.setFillStyle(n.centerColor),a.moveTo(r.x,r.y),a.arc(r.x,r.y,c,0,2*Math.PI),a.closePath(),a.fill()}return!1!==e.dataLabel&&1===o&&en(t,e,i,a,0,r),1===o&&"ring"===e.type&&Jo(e,i,a,r),{center:r,radius:s,series:t}}function gn(t,e){var i=Array(2),a=20037508.34*t/180,o=Math.log(Math.tan((90+e)*Math.PI/360))/(Math.PI/180);return o=20037508.34*o/180,i[0]=a,i[1]=o,i}function fn(t,e,i,a,o,n){return{x:(e-i.xMin)*a+o,y:(i.yMax-t)*a+n}}function xn(t,e,i){if(e[1]==i[1])return!1;if(e[1]>t[1]&&i[1]>t[1])return!1;if(e[1]<t[1]&&i[1]<t[1])return!1;if(e[1]==t[1]&&i[1]>t[1])return!1;if(i[1]==t[1]&&e[1]>t[1])return!1;if(e[0]<t[0]&&i[1]<t[1])return!1;return!(i[0]-(i[0]-e[0])*(i[1]-t[1])/(i[1]-e[1])<t[0])}function mn(t,e,i){let a=0;for(let o=0;o<e.length;o++){let n=e[o][0];1==e.length&&(n=e[o][0]);for(let e=0;e<n.length-1;e++){let o=n[e],r=n[e+1];i&&(o=gn(n[e][0],n[e][1]),r=gn(n[e+1][0],n[e+1][1])),xn(t,o,r)&&(a+=1)}}return a%2==1}function yn(t,e,i){i=0==i?1:i;for(var a=[],o=0;o<i;o++)a[o]=Math.random();return Math.floor(a.reduce((function(t,e){return t+e}))/i*(e-t))+t}function vn(t,e,i,a){var o=!1;for(let n=0;n<e.length;n++)if(e[n].area){if(!(t[3]<e[n].area[1]||t[0]>e[n].area[2]||t[1]>e[n].area[3]||t[2]<e[n].area[0])){o=!0;break}if(t[0]<0||t[1]<0||t[2]>i||t[3]>a){o=!0;break}o=!1}return o}function bn(t,e,i){let a=t.series;switch(e){case"normal":for(let o=0;o<a.length;o++){let e,n,r,s=a[o].name,l=a[o].textSize*t.pix,c=oo(s,l,i),h=0;for(;;){if(h++,e=yn(-t.width/2,t.width/2,5)-c/2,n=yn(-t.height/2,t.height/2,5)+l/2,r=[e-5+t.width/2,n-5-l+t.height/2,e+c+5+t.width/2,n+5+t.height/2],!vn(r,a,t.width,t.height))break;if(1e3==h){r=[-100,-100,-100,-100];break}}a[o].area=r}break;case"vertical":let e=function(){return Math.random()>.7};for(let o=0;o<a.length;o++){let n,r,s,l,c=a[o].name,h=a[o].textSize*t.pix,d=oo(c,h,i),p=e(),u=0;for(;;){let e;if(u++,p?(n=yn(-t.width/2,t.width/2,5)-d/2,r=yn(-t.height/2,t.height/2,5)+h/2,s=[r-5-d+t.width/2,-n-5+t.height/2,r+5+t.width/2,-n+h+5+t.height/2],l=[t.width-(t.width/2-t.height/2)-(-n+h+5+t.height/2)-5,t.height/2-t.width/2+(r-5-d+t.width/2)-5,t.width-(t.width/2-t.height/2)-(-n+h+5+t.height/2)+h,t.height/2-t.width/2+(r-5-d+t.width/2)+d+5],e=vn(l,a,t.height,t.width)):(n=yn(-t.width/2,t.width/2,5)-d/2,r=yn(-t.height/2,t.height/2,5)+h/2,s=[n-5+t.width/2,r-5-h+t.height/2,n+d+5+t.width/2,r+5+t.height/2],e=vn(s,a,t.width,t.height)),!e)break;if(1e3==u){s=[-1e3,-1e3,-1e3,-1e3];break}}p?(a[o].area=l,a[o].areav=s):a[o].area=s,a[o].rotate=p}}return a}function Sn(t,e,i,a,o,n,r){for(let s=0;s<t.length;s++){let l,c,h,d,p=t[s];if(!1===p.labelShow)continue;let u=p.formatter?p.formatter(p,s,t,e):Ja.toFixed(100*p._proportion_)+"%";u=p.labelText?p.labelText:u,"right"==o&&(l=s==t.length-1?(p.funnelArea[2]+r.x)/2:(p.funnelArea[2]+t[s+1].funnelArea[2])/2,c=l+2*n,h=p.funnelArea[1]+a/2,d=p.textSize*e.pix||e.fontSize*e.pix,i.setLineWidth(1*e.pix),i.setStrokeStyle(p.color),i.setFillStyle(p.color),i.beginPath(),i.moveTo(l,h),i.lineTo(c,h),i.stroke(),i.closePath(),i.beginPath(),i.moveTo(c,h),i.arc(c,h,2*e.pix,0,2*Math.PI),i.closePath(),i.fill(),i.beginPath(),i.setFontSize(d),i.setFillStyle(p.textColor||e.fontColor),i.fillText(u,c+5,h+d/2-2),i.closePath(),i.stroke(),i.closePath()),"left"==o&&(l=s==t.length-1?(p.funnelArea[0]+r.x)/2:(p.funnelArea[0]+t[s+1].funnelArea[0])/2,c=l-2*n,h=p.funnelArea[1]+a/2,d=p.textSize*e.pix||e.fontSize*e.pix,i.setLineWidth(1*e.pix),i.setStrokeStyle(p.color),i.setFillStyle(p.color),i.beginPath(),i.moveTo(l,h),i.lineTo(c,h),i.stroke(),i.closePath(),i.beginPath(),i.moveTo(c,h),i.arc(c,h,2,0,2*Math.PI),i.closePath(),i.fill(),i.beginPath(),i.setFontSize(d),i.setFillStyle(p.textColor||e.fontColor),i.fillText(u,c-5-oo(u,d,i),h+d/2-2),i.closePath(),i.stroke(),i.closePath())}}function wn(t,e,i,a,o,n,r){for(let s=0;s<t.length;s++){let o,n,l=t[s];l.centerText&&(o=l.funnelArea[1]+a/2,n=l.centerTextSize*e.pix||e.fontSize*e.pix,i.beginPath(),i.setFontSize(n),i.setFillStyle(l.centerTextColor||"#FFFFFF"),i.fillText(l.centerText,r.x-oo(l.centerText,n,i)/2,o+n/2-2),i.closePath(),i.stroke(),i.closePath())}}function Tn(t,e){e.save(),e.translate(0,.5),e.restore(),e.draw()}var _n={easeIn:function(t){return Math.pow(t,3)},easeOut:function(t){return Math.pow(t-1,3)+1},easeInOut:function(t){return(t/=.5)<1?.5*Math.pow(t,3):.5*(Math.pow(t-2,3)+2)},linear:function(t){return t}};function An(t){this.isStop=!1,t.duration=void 0===t.duration?1e3:t.duration,t.timing=t.timing||"easeInOut";var e="undefined"!=typeof setTimeout?function(t,e){setTimeout((function(){t(+new Date)}),e)}:"undefined"!=typeof requestAnimationFrame?requestAnimationFrame:function(t){t(null)},i=null,a=function(o){if(null===o||!0===this.isStop)return t.onProcess&&t.onProcess(1),void(t.onAnimationFinish&&t.onAnimationFinish());if(null===i&&(i=o),o-i<t.duration){var n=(o-i)/t.duration;n=(0,_n[t.timing])(n),t.onProcess&&t.onProcess(n),e(a,17)}else t.onProcess&&t.onProcess(1),t.onAnimationFinish&&t.onAnimationFinish()};a=a.bind(this),e(a,17)}function Cn(t,e,i,a){var o=this,n=e.series;"pie"!==t&&"ring"!==t&&"mount"!==t&&"rose"!==t&&"funnel"!==t||(n=function(t,e,i){let a=[];if(t.length>0&&t[0].data.constructor.toString().indexOf("Array")>-1){e._pieSeries_=t;let i=t[0].data;for(var o=0;o<i.length;o++)i[o].formatter=t[0].formatter,i[o].data=i[o].value,a.push(i[o]);e.series=a}else a=t;return a}(n,e));var r=e.categories;if("mount"===t){r=[];for(let t=0;t<n.length;t++)!1!==n[t].show&&r.push(n[t].name);e.categories=r}n=io(n,e,i);var s=e.animation?e.duration:0;o.animationInstance&&o.animationInstance.stop();var l=null;if("candle"==t){let t=Ka({},e.extra.candle.average);t.show?(l=io(l=function(t,e,i,a){let o=[];for(let n=0;n<t.length;n++){let r={data:[],name:e[n],color:i[n]};for(let e=0,i=a.length;e<i;e++){if(e<t[n]){r.data.push(null);continue}let i=0;for(let o=0;o<t[n];o++)i+=a[e-o][1];r.data.push(+(i/t[n]).toFixed(3))}o.push(r)}return o}(t.day,t.name,t.color,n[0].data),e,i),e.seriesMA=l):l=e.seriesMA?e.seriesMA=io(e.seriesMA,e,i):n}else l=n;e._series_=n=fo(n),e.area=new Array(4);for(let g=0;g<4;g++)e.area[g]=e.padding[g]*e.pix;var c=function(t,e,i,a,o){let n={area:{start:{x:0,y:0},end:{x:0,y:0},width:0,height:0,wholeWidth:0,wholeHeight:0},points:[],widthArr:[],heightArr:[]};if(!1===e.legend.show)return a.legendData=n,n;let r=e.legend.padding*e.pix,s=e.legend.margin*e.pix,l=e.legend.fontSize?e.legend.fontSize*e.pix:i.fontSize,c=15*e.pix,h=5*e.pix,d=Math.max(e.legend.lineHeight*e.pix,l);if("top"==e.legend.position||"bottom"==e.legend.position){let i=[],a=0,p=[],u=[];for(let n=0;n<t.length;n++){let r=t[n],s=c+h+oo((r.legendText?r.legendText:r.name)||"undefined",l,o)+e.legend.itemGap*e.pix;a+s>e.width-e.area[1]-e.area[3]?(i.push(u),p.push(a-e.legend.itemGap*e.pix),a=s,u=[r]):(a+=s,u.push(r))}if(u.length){i.push(u),p.push(a-e.legend.itemGap*e.pix),n.widthArr=p;let t=Math.max.apply(null,p);switch(e.legend.float){case"left":n.area.start.x=e.area[3],n.area.end.x=e.area[3]+t+2*r;break;case"right":n.area.start.x=e.width-e.area[1]-t-2*r,n.area.end.x=e.width-e.area[1];break;default:n.area.start.x=(e.width-t)/2-r,n.area.end.x=(e.width+t)/2+r}n.area.width=t+2*r,n.area.wholeWidth=t+2*r,n.area.height=i.length*d+2*r,n.area.wholeHeight=i.length*d+2*r+2*s,n.points=i}}else{let i=t.length,a=e.height-e.area[0]-e.area[2]-2*s-2*r,p=Math.min(Math.floor(a/d),i);switch(n.area.height=p*d+2*r,n.area.wholeHeight=p*d+2*r,e.legend.float){case"top":n.area.start.y=e.area[0]+s,n.area.end.y=e.area[0]+s+n.area.height;break;case"bottom":n.area.start.y=e.height-e.area[2]-s-n.area.height,n.area.end.y=e.height-e.area[2]-s;break;default:n.area.start.y=(e.height-n.area.height)/2,n.area.end.y=(e.height+n.area.height)/2}let u=i%p==0?i/p:Math.floor(i/p+1),g=[];for(let e=0;e<u;e++){let i=t.slice(e*p,e*p+p);g.push(i)}if(n.points=g,g.length){for(let i=0;i<g.length;i++){let t=g[i],a=0;for(let i=0;i<t.length;i++){let n=c+h+oo(t[i].name||"undefined",l,o)+e.legend.itemGap*e.pix;n>a&&(a=n)}n.widthArr.push(a),n.heightArr.push(t.length*d+2*r)}let t=0;for(let e=0;e<n.widthArr.length;e++)t+=n.widthArr[e];n.area.width=t-e.legend.itemGap*e.pix+2*r,n.area.wholeWidth=n.area.width+r}}switch(e.legend.position){case"top":n.area.start.y=e.area[0]+s,n.area.end.y=e.area[0]+s+n.area.height;break;case"bottom":n.area.start.y=e.height-e.area[2]-n.area.height-s,n.area.end.y=e.height-e.area[2]-s;break;case"left":n.area.start.x=e.area[3],n.area.end.x=e.area[3]+n.area.width;break;case"right":n.area.start.x=e.width-e.area[1]-n.area.width,n.area.end.x=e.width-e.area[1]}return a.legendData=n,n}(l,e,i,e.chartData,a),h=c.area.wholeHeight,d=c.area.wholeWidth;switch(e.legend.position){case"top":e.area[0]+=h;break;case"bottom":e.area[2]+=h;break;case"left":e.area[3]+=d;break;case"right":e.area[1]+=d}let p={},u=0;if("line"===e.type||"column"===e.type||"mount"===e.type||"area"===e.type||"mix"===e.type||"candle"===e.type||"scatter"===e.type||"bubble"===e.type||"bar"===e.type){if(p=$o(n,e,i,a),u=p.yAxisWidth,e.yAxis.showTitle){let t=0;for(let a=0;a<e.yAxis.data.length;a++)t=Math.max(t,e.yAxis.data[a].titleFontSize?e.yAxis.data[a].titleFontSize*e.pix:i.fontSize);e.area[0]+=t}let t=0,o=0;for(let i=0;i<u.length;i++)"left"==u[i].position?(e.area[3]+=o>0?u[i].width+e.yAxis.padding*e.pix:u[i].width,o+=1):"right"==u[i].position&&(e.area[1]+=t>0?u[i].width+e.yAxis.padding*e.pix:u[i].width,t+=1)}else i.yAxisWidth=u;if(e.chartData.yAxisData=p,e.categories&&e.categories.length&&"radar"!==e.type&&"gauge"!==e.type&&"bar"!==e.type){e.chartData.xAxisData=Oo(e.categories,e);let t=vo(e.categories,e,0,e.chartData.xAxisData.eachSpacing,a),o=t.xAxisHeight,n=t.angle;i.xAxisHeight=o,i._xAxisTextAngle_=n,e.area[2]+=o,e.chartData.categoriesData=t}else if("line"===e.type||"area"===e.type||"scatter"===e.type||"bubble"===e.type||"bar"===e.type){e.chartData.xAxisData=bo(n,e,i,a);let t=vo(r=e.chartData.xAxisData.rangesFormat,e,0,e.chartData.xAxisData.eachSpacing,a),o=t.xAxisHeight,s=t.angle;i.xAxisHeight=o,i._xAxisTextAngle_=s,e.area[2]+=o,e.chartData.categoriesData=t}else e.chartData.xAxisData={xAxisPoints:[]};if(e.enableScroll&&"right"==e.xAxis.scrollAlign&&void 0===e._scrollDistance_){let t=0,i=e.chartData.xAxisData.xAxisPoints,a=e.chartData.xAxisData.startX;t=e.chartData.xAxisData.endX-a-e.chartData.xAxisData.eachSpacing*(i.length-1),o.scrollOption.currentOffset=t,o.scrollOption.startTouchX=t,o.scrollOption.distance=0,o.scrollOption.lastMoveTime=0,e._scrollDistance_=t}switch("pie"!==t&&"ring"!==t&&"rose"!==t||(i._pieTextMaxLength_=!1===e.dataLabel?0:function(t,e,i,a){t=wo(t);let o=0;for(let n=0;n<t.length;n++){let r=t[n],s=r.formatter?r.formatter(+r._proportion_.toFixed(2)):Ja.toFixed(100*r._proportion_)+"%";o=Math.max(o,oo(s,r.textSize*a.pix||e.fontSize,i))}return o}(l,i,a,e)),t){case"word":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),function(t,e,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=Ka({},{type:"normal",autoColors:!0},e.extra.word);e.chartData.wordCloudData||(e.chartData.wordCloudData=bn(e,n.type,a)),a.beginPath(),a.setFillStyle(e.background),a.rect(0,0,e.width,e.height),a.fill(),a.save();let r=e.chartData.wordCloudData;a.translate(e.width/2,e.height/2);for(let s=0;s<r.length;s++){a.save(),r[s].rotate&&a.rotate(90*Math.PI/180);let t=r[s].name,i=r[s].textSize*e.pix,n=oo(t,i,a);a.beginPath(),a.setStrokeStyle(r[s].color),a.setFillStyle(r[s].color),a.setFontSize(i),r[s].rotate?r[s].areav[0]>0&&(e.tooltip&&e.tooltip.index==s?a.strokeText(t,(r[s].areav[0]+5-e.width/2)*o-n*(1-o)/2,(r[s].areav[1]+5+i-e.height/2)*o):a.fillText(t,(r[s].areav[0]+5-e.width/2)*o-n*(1-o)/2,(r[s].areav[1]+5+i-e.height/2)*o)):r[s].area[0]>0&&(e.tooltip&&e.tooltip.index==s?a.strokeText(t,(r[s].area[0]+5-e.width/2)*o-n*(1-o)/2,(r[s].area[1]+5+i-e.height/2)*o):a.fillText(t,(r[s].area[0]+5-e.width/2)*o-n*(1-o)/2,(r[s].area[1]+5+i-e.height/2)*o)),a.stroke(),a.restore()}a.restore()}(n,e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"map":a.clearRect(0,0,e.width,e.height),function(t,e,i,a){var o,n,r=Ka({},{border:!0,mercator:!1,borderWidth:1,active:!0,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#f04864",activeFillColor:"#facc14",activeFillOpacity:1},e.extra.map),s=t,l=function(t){for(var e,i={xMin:180,xMax:0,yMin:90,yMax:0},a=0;a<t.length;a++)for(var o=t[a].geometry.coordinates,n=0;n<o.length;n++){1==(e=o[n]).length&&(e=e[0]);for(var r=0;r<e.length;r++){var s={x:e[r][0],y:e[r][1]};i.xMin=i.xMin<s.x?i.xMin:s.x,i.xMax=i.xMax>s.x?i.xMax:s.x,i.yMin=i.yMin<s.y?i.yMin:s.y,i.yMax=i.yMax>s.y?i.yMax:s.y}}return i}(s);if(r.mercator){var c=gn(l.xMax,l.yMax),h=gn(l.xMin,l.yMin);l.xMax=c[0],l.yMax=c[1],l.xMin=h[0],l.yMin=h[1]}for(var d=e.width/Math.abs(l.xMax-l.xMin),p=e.height/Math.abs(l.yMax-l.yMin),u=d<p?d:p,g=e.width/2-Math.abs(l.xMax-l.xMin)/2*u,f=e.height/2-Math.abs(l.yMax-l.yMin)/2*u,x=0;x<s.length;x++){a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.setStrokeStyle(r.borderColor),a.setFillStyle(Xa(t[x].color,t[x].fillOpacity||r.fillOpacity)),1==r.active&&e.tooltip&&e.tooltip.index==x&&(a.setStrokeStyle(r.activeBorderColor),a.setFillStyle(Xa(r.activeFillColor,r.activeFillOpacity)));for(var m=s[x].geometry.coordinates,y=0;y<m.length;y++){1==(o=m[y]).length&&(o=o[0]);for(var v=0;v<o.length;v++){var b=Array(2);n=fn((b=r.mercator?gn(o[v][0],o[v][1]):o[v])[1],b[0],l,u,g,f),0===v?(a.beginPath(),a.moveTo(n.x,n.y)):a.lineTo(n.x,n.y)}a.fill(),1==r.border&&a.stroke()}}if(1==e.dataLabel)for(x=0;x<s.length;x++){var S=s[x].properties.centroid;if(S){r.mercator&&(S=gn(s[x].properties.centroid[0],s[x].properties.centroid[1])),n=fn(S[1],S[0],l,u,g,f);let t=s[x].textSize*e.pix||i.fontSize,o=s[x].textColor||e.fontColor;r.active&&r.activeTextColor&&e.tooltip&&e.tooltip.index==x&&(o=r.activeTextColor);let c=s[x].properties.name;a.beginPath(),a.setFontSize(t),a.setFillStyle(o),a.fillText(c,n.x-oo(c,t,a)/2,n.y+t/2),a.closePath(),a.stroke()}}e.chartData.mapData={bounds:l,scale:u,xoffset:g,yoffset:f,mercator:r.mercator},ln(e,i,a,1),a.draw()}(n,e,i,a),setTimeout((()=>{this.uevent.trigger("renderComplete")}),50);break;case"funnel":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),e.chartData.funnelData=function(t,e,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=Ka({},{type:"funnel",activeWidth:10,activeOpacity:.3,border:!1,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,minSize:0,labelAlign:"right",linearType:"none",customColor:[]},e.extra.funnel),r=(e.height-e.area[0]-e.area[2])/t.length,s={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.height-e.area[2]},l=n.activeWidth*e.pix,c=Math.min((e.width-e.area[1]-e.area[3])/2-l,(e.height-e.area[0]-e.area[2])/2-l),h=To(t,c,n,r,o);if(a.save(),a.translate(s.x,s.y),n.customColor=ao(n.linearType,n.customColor,t,i),"pyramid"==n.type)for(let u=0;u<h.length;u++){if(u==h.length-1){e.tooltip&&e.tooltip.index==u&&(a.beginPath(),a.setFillStyle(Xa(h[u].color,n.activeOpacity)),a.moveTo(-l,-r),a.lineTo(-h[u].radius-l,0),a.lineTo(h[u].radius+l,0),a.lineTo(l,-r),a.lineTo(-l,-r),a.closePath(),a.fill()),h[u].funnelArea=[s.x-h[u].radius,s.y-r*(u+1),s.x+h[u].radius,s.y-r*u],a.beginPath(),a.setLineWidth(n.borderWidth*e.pix),a.setStrokeStyle(n.borderColor);var d=Xa(h[u].color,n.fillOpacity);"custom"==n.linearType&&((p=a.createLinearGradient(h[u].radius,-r,-h[u].radius,-r)).addColorStop(0,Xa(h[u].color,n.fillOpacity)),p.addColorStop(.5,Xa(n.customColor[h[u].linearIndex],n.fillOpacity)),p.addColorStop(1,Xa(h[u].color,n.fillOpacity)),d=p),a.setFillStyle(d),a.moveTo(0,-r),a.lineTo(-h[u].radius,0),a.lineTo(h[u].radius,0),a.lineTo(0,-r),a.closePath(),a.fill(),1==n.border&&a.stroke()}else e.tooltip&&e.tooltip.index==u&&(a.beginPath(),a.setFillStyle(Xa(h[u].color,n.activeOpacity)),a.moveTo(0,0),a.lineTo(-h[u].radius-l,0),a.lineTo(-h[u+1].radius-l,-r),a.lineTo(h[u+1].radius+l,-r),a.lineTo(h[u].radius+l,0),a.lineTo(0,0),a.closePath(),a.fill()),h[u].funnelArea=[s.x-h[u].radius,s.y-r*(u+1),s.x+h[u].radius,s.y-r*u],a.beginPath(),a.setLineWidth(n.borderWidth*e.pix),a.setStrokeStyle(n.borderColor),d=Xa(h[u].color,n.fillOpacity),"custom"==n.linearType&&((p=a.createLinearGradient(h[u].radius,-r,-h[u].radius,-r)).addColorStop(0,Xa(h[u].color,n.fillOpacity)),p.addColorStop(.5,Xa(n.customColor[h[u].linearIndex],n.fillOpacity)),p.addColorStop(1,Xa(h[u].color,n.fillOpacity)),d=p),a.setFillStyle(d),a.moveTo(0,0),a.lineTo(-h[u].radius,0),a.lineTo(-h[u+1].radius,-r),a.lineTo(h[u+1].radius,-r),a.lineTo(h[u].radius,0),a.lineTo(0,0),a.closePath(),a.fill(),1==n.border&&a.stroke();a.translate(0,-r)}else{a.translate(0,-(h.length-1)*r);for(let t=0;t<h.length;t++){var p;if(t==h.length-1)e.tooltip&&e.tooltip.index==t&&(a.beginPath(),a.setFillStyle(Xa(h[t].color,n.activeOpacity)),a.moveTo(-l-n.minSize/2,0),a.lineTo(-h[t].radius-l,-r),a.lineTo(h[t].radius+l,-r),a.lineTo(l+n.minSize/2,0),a.lineTo(-l-n.minSize/2,0),a.closePath(),a.fill()),h[t].funnelArea=[s.x-h[t].radius,s.y-r,s.x+h[t].radius,s.y],a.beginPath(),a.setLineWidth(n.borderWidth*e.pix),a.setStrokeStyle(n.borderColor),d=Xa(h[t].color,n.fillOpacity),"custom"==n.linearType&&((p=a.createLinearGradient(h[t].radius,-r,-h[t].radius,-r)).addColorStop(0,Xa(h[t].color,n.fillOpacity)),p.addColorStop(.5,Xa(n.customColor[h[t].linearIndex],n.fillOpacity)),p.addColorStop(1,Xa(h[t].color,n.fillOpacity)),d=p),a.setFillStyle(d),a.moveTo(0,0),a.lineTo(-n.minSize/2,0),a.lineTo(-h[t].radius,-r),a.lineTo(h[t].radius,-r),a.lineTo(n.minSize/2,0),a.lineTo(0,0),a.closePath(),a.fill(),1==n.border&&a.stroke();else e.tooltip&&e.tooltip.index==t&&(a.beginPath(),a.setFillStyle(Xa(h[t].color,n.activeOpacity)),a.moveTo(0,0),a.lineTo(-h[t+1].radius-l,0),a.lineTo(-h[t].radius-l,-r),a.lineTo(h[t].radius+l,-r),a.lineTo(h[t+1].radius+l,0),a.lineTo(0,0),a.closePath(),a.fill()),h[t].funnelArea=[s.x-h[t].radius,s.y-r*(h.length-t),s.x+h[t].radius,s.y-r*(h.length-t-1)],a.beginPath(),a.setLineWidth(n.borderWidth*e.pix),a.setStrokeStyle(n.borderColor),d=Xa(h[t].color,n.fillOpacity),"custom"==n.linearType&&((p=a.createLinearGradient(h[t].radius,-r,-h[t].radius,-r)).addColorStop(0,Xa(h[t].color,n.fillOpacity)),p.addColorStop(.5,Xa(n.customColor[h[t].linearIndex],n.fillOpacity)),p.addColorStop(1,Xa(h[t].color,n.fillOpacity)),d=p),a.setFillStyle(d),a.moveTo(0,0),a.lineTo(-h[t+1].radius,0),a.lineTo(-h[t].radius,-r),a.lineTo(h[t].radius,-r),a.lineTo(h[t+1].radius,0),a.lineTo(0,0),a.closePath(),a.fill(),1==n.border&&a.stroke();a.translate(0,r)}}return a.restore(),!1!==e.dataLabel&&1===o&&Sn(h,e,a,r,n.labelAlign,l,s),1===o&&wn(h,e,a,r,n.labelAlign,0,s),{center:s,radius:c,series:h}}(n,e,i,a,t),pn(e.series,e,i,a,e.chartData),ln(e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"line":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),hn(0,e,0,a),cn(r,e,i,a);var o=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=Ka({},{type:"straight",width:2,activeType:"none",linearType:"none",onShadow:!1,animation:"vertical"},e.extra.line);n.width*=e.pix;let r=e.chartData.xAxisData,s=r.xAxisPoints,l=r.eachSpacing;var c=[];a.save();let h=0,d=e.width+l;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),h=-e._scrollDistance_-2*l+e.area[3],d=h+(e.xAxis.itemCount+4)*l),t.forEach((function(t,r){let p,u,g;a.beginPath(),a.setStrokeStyle(t.color),a.moveTo(-1e4,-1e4),a.lineTo(-10001,-10001),a.stroke(),p=[].concat(e.chartData.yAxisData.ranges[t.index]),u=p.pop(),g=p.shift();var f=No(t.data,u,g,s,l,e,i,n,o);c.push(f);var x=yo(f,t);if("dash"==t.lineType){let i=t.dashLength?t.dashLength:8;i*=e.pix,a.setLineDash([i,i])}a.beginPath();var m=t.color;if("none"!==n.linearType&&t.linearColor&&t.linearColor.length>0){for(var y=a.createLinearGradient(e.chartData.xAxisData.startX,e.height/2,e.chartData.xAxisData.endX,e.height/2),v=0;v<t.linearColor.length;v++)y.addColorStop(t.linearColor[v][0],Xa(t.linearColor[v][1],1));m=y}a.setStrokeStyle(m),1==n.onShadow&&t.setShadow&&t.setShadow.length>0?a.setShadow(t.setShadow[0],t.setShadow[1],t.setShadow[2],t.setShadow[3]):a.setShadow(0,0,0,"rgba(0,0,0,0)"),a.setLineWidth(n.width),x.forEach((function(t,e){if(1===t.length)a.moveTo(t[0].x,t[0].y);else{a.moveTo(t[0].x,t[0].y);let e=0;if("curve"===n.type)for(let o=0;o<t.length;o++){let n=t[o];if(0==e&&n.x>h&&(a.moveTo(n.x,n.y),e=1),o>0&&n.x>h&&n.x<d){var i=Za(t,o-1);a.bezierCurveTo(i.ctrA.x,i.ctrA.y,i.ctrB.x,i.ctrB.y,n.x,n.y)}}if("straight"===n.type)for(let i=0;i<t.length;i++){let o=t[i];0==e&&o.x>h&&(a.moveTo(o.x,o.y),e=1),i>0&&o.x>h&&o.x<d&&a.lineTo(o.x,o.y)}if("step"===n.type)for(let i=0;i<t.length;i++){let o=t[i];0==e&&o.x>h&&(a.moveTo(o.x,o.y),e=1),i>0&&o.x>h&&o.x<d&&(a.lineTo(o.x,t[i-1].y),a.lineTo(o.x,o.y))}a.moveTo(t[0].x,t[0].y)}})),a.stroke(),a.setLineDash([]),!1!==e.dataPointShape&&Go(f,t.color,t.pointShape,a,e),Ko(f,t.color,t.pointShape,a,e,n)})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,n){let r,c,h;r=[].concat(e.chartData.yAxisData.ranges[t.index]),c=r.pop(),h=r.shift(),Xo(Ro(t.data,c,h,s,l,e,i,o),t,i,a,e)})),a.restore(),{xAxisPoints:s,calPoints:c,eachSpacing:l}}(n,e,i,a,t),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=s,e.chartData.calPoints=l,e.chartData.eachSpacing=c,dn(0,e,i,a),!1!==e.enableMarkLine&&1===t&&an(e,0,a),pn(e.series,e,i,a,e.chartData),ln(e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"scatter":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),hn(0,e,0,a),cn(r,e,i,a);var o=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;Ka({},{type:"circle"},e.extra.scatter);let n=e.chartData.xAxisData,r=n.xAxisPoints,s=n.eachSpacing;var l=[];a.save();let c=0;return e.width,e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),c=-e._scrollDistance_-2*s+e.area[3],e.xAxis.itemCount),t.forEach((function(t,n){let l,c,h;l=[].concat(e.chartData.yAxisData.ranges[t.index]),c=l.pop(),h=l.shift();var d=Ro(t.data,c,h,r,s,e,i,o);a.beginPath(),a.setStrokeStyle(t.color),a.setFillStyle(t.color),a.setLineWidth(1*e.pix);var p=t.pointShape;if("diamond"===p)d.forEach((function(t,e){null!==t&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y),a.lineTo(t.x,t.y****),a.lineTo(t.x****,t.y),a.lineTo(t.x,t.y-4.5))}));else if("circle"===p)d.forEach((function(t,i){null!==t&&(a.moveTo(t.x*****e.pix,t.y),a.arc(t.x,t.y,3*e.pix,0,2*Math.PI,!1))}));else if("square"===p)d.forEach((function(t,e){null!==t&&(a.moveTo(t.x-3.5,t.y-3.5),a.rect(t.x-3.5,t.y-3.5,7,7))}));else if("triangle"===p)d.forEach((function(t,e){null!==t&&(a.moveTo(t.x,t.y-4.5),a.lineTo(t.x-4.5,t.y****),a.lineTo(t.x****,t.y****),a.lineTo(t.x,t.y-4.5))}));else if("triangle"===p)return;a.closePath(),a.fill(),a.stroke()})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,n){let l,c,h;l=[].concat(e.chartData.yAxisData.ranges[t.index]),c=l.pop(),h=l.shift(),Xo(Ro(t.data,c,h,r,s,e,i,o),t,i,a,e)})),a.restore(),{xAxisPoints:r,calPoints:l,eachSpacing:s}}(n,e,i,a,t),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=s,e.chartData.calPoints=l,e.chartData.eachSpacing=c,dn(0,e,i,a),!1!==e.enableMarkLine&&1===t&&an(e,0,a),pn(e.series,e,i,a,e.chartData),ln(e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"bubble":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),hn(0,e,0,a),cn(r,e,i,a);var o=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=Ka({},{opacity:1,border:2},e.extra.bubble);let r=e.chartData.xAxisData,s=r.xAxisPoints,l=r.eachSpacing;var c=[];a.save();let h=0;return e.width,e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),h=-e._scrollDistance_-2*l+e.area[3],e.xAxis.itemCount),t.forEach((function(t,r){let c,h,d;c=[].concat(e.chartData.yAxisData.ranges[t.index]),h=c.pop(),d=c.shift();var p=Ro(t.data,h,d,s,l,e,i,o);a.beginPath(),a.setStrokeStyle(t.color),a.setLineWidth(n.border*e.pix),a.setFillStyle(Xa(t.color,n.opacity)),p.forEach((function(t,i){a.moveTo(t.x+t.r,t.y),a.arc(t.x,t.y,t.r*e.pix,0,2*Math.PI,!1)})),a.closePath(),a.fill(),a.stroke(),!1!==e.dataLabel&&1===o&&p.forEach((function(o,n){a.beginPath();var r=t.textSize*e.pix||i.fontSize;a.setFontSize(r),a.setFillStyle(t.textColor||"#FFFFFF"),a.setTextAlign("center"),a.fillText(String(o.t),o.x,o.y+r/2),a.closePath(),a.stroke(),a.setTextAlign("left")}))})),a.restore(),{xAxisPoints:s,calPoints:c,eachSpacing:l}}(n,e,i,a,t),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=s,e.chartData.calPoints=l,e.chartData.eachSpacing=c,dn(0,e,i,a),!1!==e.enableMarkLine&&1===t&&an(e,0,a),pn(e.series,e,i,a,e.chartData),ln(e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"mix":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),hn(0,e,0,a),cn(r,e,i,a);var o=function(t,e,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.chartData.xAxisData,r=n.xAxisPoints,s=n.eachSpacing,l=Ka({},{width:s/2,barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.mix.column),c=Ka({},{opacity:.2,gradient:!1},e.extra.mix.area),h=Ka({},{width:2},e.extra.mix.line),d=e.height-e.area[2],p=[];var u=0,g=0;t.forEach((function(t,e){"column"==t.type&&(g+=1)})),a.save();let f=-2,x=r.length+2,m=0,y=e.width+s;e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),f=Math.floor(-e._scrollDistance_/s)-2,x=f+e.xAxis.itemCount+4,m=-e._scrollDistance_-2*s+e.area[3],y=m+(e.xAxis.itemCount+4)*s),l.customColor=ao(l.linearType,l.customColor,t,i),t.forEach((function(t,n){let v,b,S;v=[].concat(e.chartData.yAxisData.ranges[t.index]),b=v.pop(),S=v.shift();var w=Ro(t.data,b,S,r,s,e,i,o);if(p.push(w),"column"==t.type){w=Do(w,s,g,u,0,e);for(let i=0;i<w.length;i++){let o=w[i];if(null!==o&&i>f&&i<x){var T=o.x-o.width/2;e.height,o.y,e.area[2],a.beginPath();var _=o.color||t.color,A=o.color||t.color;if("none"!==l.linearType){var C=a.createLinearGradient(T,o.y,T,e.height-e.area[2]);"opacity"==l.linearType?(C.addColorStop(0,Xa(_,l.linearOpacity)),C.addColorStop(1,Xa(_,1))):(C.addColorStop(0,Xa(l.customColor[t.linearIndex],l.linearOpacity)),C.addColorStop(l.colorStop,Xa(l.customColor[t.linearIndex],l.linearOpacity)),C.addColorStop(1,Xa(_,1))),_=C}if(l.barBorderRadius&&4===l.barBorderRadius.length||l.barBorderCircle){const t=T,i=o.y,n=o.width,r=e.height-e.area[2]-o.y;l.barBorderCircle&&(l.barBorderRadius=[n/2,n/2,0,0]);let[s,c,h,d]=l.barBorderRadius,p=Math.min(n/2,r/2);s=s>p?p:s,c=c>p?p:c,h=h>p?p:h,d=d>p?p:d,s=s<0?0:s,c=c<0?0:c,h=h<0?0:h,d=d<0?0:d,a.arc(t+s,i+s,s,-Math.PI,-Math.PI/2),a.arc(t+n-c,i+c,c,-Math.PI/2,0),a.arc(t+n-h,i+r-h,h,0,Math.PI/2),a.arc(t+d,i+r-d,d,Math.PI/2,Math.PI)}else a.moveTo(T,o.y),a.lineTo(T+o.width,o.y),a.lineTo(T+o.width,e.height-e.area[2]),a.lineTo(T,e.height-e.area[2]),a.lineTo(T,o.y),a.setLineWidth(1),a.setStrokeStyle(A);a.setFillStyle(_),a.closePath(),a.fill()}}u+=1}if("area"==t.type){let i=yo(w,t);for(let o=0;o<i.length;o++){let n=i[o];if(a.beginPath(),a.setStrokeStyle(t.color),a.setStrokeStyle(Xa(t.color,c.opacity)),c.gradient){let i=a.createLinearGradient(0,e.area[0],0,e.height-e.area[2]);i.addColorStop("0",Xa(t.color,c.opacity)),i.addColorStop("1.0",Xa("#FFFFFF",.1)),a.setFillStyle(i)}else a.setFillStyle(Xa(t.color,c.opacity));if(a.setLineWidth(2*e.pix),n.length>1){var P=n[0];let e=n[n.length-1];a.moveTo(P.x,P.y);let i=0;if("curve"===t.style)for(let t=0;t<n.length;t++){let e=n[t];if(0==i&&e.x>m&&(a.moveTo(e.x,e.y),i=1),t>0&&e.x>m&&e.x<y){var k=Za(n,t-1);a.bezierCurveTo(k.ctrA.x,k.ctrA.y,k.ctrB.x,k.ctrB.y,e.x,e.y)}}else for(let t=0;t<n.length;t++){let e=n[t];0==i&&e.x>m&&(a.moveTo(e.x,e.y),i=1),t>0&&e.x>m&&e.x<y&&a.lineTo(e.x,e.y)}a.lineTo(e.x,d),a.lineTo(P.x,d),a.lineTo(P.x,P.y)}else{let t=n[0];a.moveTo(t.x-s/2,t.y)}a.closePath(),a.fill()}}"line"==t.type&&yo(w,t).forEach((function(i,o){if("dash"==t.lineType){let i=t.dashLength?t.dashLength:8;i*=e.pix,a.setLineDash([i,i])}if(a.beginPath(),a.setStrokeStyle(t.color),a.setLineWidth(h.width*e.pix),1===i.length)a.moveTo(i[0].x,i[0].y);else{a.moveTo(i[0].x,i[0].y);let e=0;if("curve"==t.style)for(let t=0;t<i.length;t++){let o=i[t];if(0==e&&o.x>m&&(a.moveTo(o.x,o.y),e=1),t>0&&o.x>m&&o.x<y){var n=Za(i,t-1);a.bezierCurveTo(n.ctrA.x,n.ctrA.y,n.ctrB.x,n.ctrB.y,o.x,o.y)}}else for(let t=0;t<i.length;t++){let o=i[t];0==e&&o.x>m&&(a.moveTo(o.x,o.y),e=1),t>0&&o.x>m&&o.x<y&&a.lineTo(o.x,o.y)}a.moveTo(i[0].x,i[0].y)}a.stroke(),a.setLineDash([])})),"point"==t.type&&(t.addPoint=!0),1==t.addPoint&&"column"!==t.type&&Go(w,t.color,t.pointShape,a,e)})),!1!==e.dataLabel&&1===o&&(u=0,t.forEach((function(t,n){let l,c,h;l=[].concat(e.chartData.yAxisData.ranges[t.index]),c=l.pop(),h=l.shift();var d=Ro(t.data,c,h,r,s,e,i,o);"column"!==t.type?Xo(d,t,i,a,e):(Xo(d=Do(d,s,g,u,0,e),t,i,a,e),u+=1)})));return a.restore(),{xAxisPoints:r,calPoints:p,eachSpacing:s}}(n,e,i,a,t),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=s,e.chartData.calPoints=l,e.chartData.eachSpacing=c,dn(0,e,i,a),!1!==e.enableMarkLine&&1===t&&an(e,0,a),pn(e.series,e,i,a,e.chartData),ln(e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"column":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),hn(0,e,0,a),cn(r,e,i,a);var o=function(t,e,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.chartData.xAxisData,r=n.xAxisPoints,s=n.eachSpacing,l=Ka({},{type:"group",width:s/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0,labelPosition:"outside"},e.extra.column),c=[];a.save();let h=-2,d=r.length+2;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),h=Math.floor(-e._scrollDistance_/s)-2,d=h+e.xAxis.itemCount+4),e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===o&&nn(e.tooltip.offset.x,e,0,a,s),l.customColor=ao(l.linearType,l.customColor,t,i),t.forEach((function(n,p){let u,g,f;u=[].concat(e.chartData.yAxisData.ranges[n.index]),g=u.pop(),f=u.shift();let x=(e.height-e.area[0]-e.area[2])*(0-g)/(f-g),m=e.height-Math.round(x)-e.area[2];n.zeroPoints=m;var y=n.data;switch(l.type){case"group":var v=Bo(y,g,f,r,s,e,i,m,o),b=Wo(y,g,f,r,s,e,i,p,t,o);c.push(b),v=Do(v,s,t.length,p,0,e);for(let t=0;t<v.length;t++){let i=v[t];if(null!==i&&t>h&&t<d){var S=i.x-i.width/2,w=e.height-i.y-e.area[2];a.beginPath();var T=i.color||n.color,_=i.color||n.color;if("none"!==l.linearType){var A=a.createLinearGradient(S,i.y,S,m);"opacity"==l.linearType?(A.addColorStop(0,Xa(T,l.linearOpacity)),A.addColorStop(1,Xa(T,1))):(A.addColorStop(0,Xa(l.customColor[n.linearIndex],l.linearOpacity)),A.addColorStop(l.colorStop,Xa(l.customColor[n.linearIndex],l.linearOpacity)),A.addColorStop(1,Xa(T,1))),T=A}if(l.barBorderRadius&&4===l.barBorderRadius.length||!0===l.barBorderCircle){const t=S,e=i.y>m?m:i.y,o=i.width,n=Math.abs(m-i.y);l.barBorderCircle&&(l.barBorderRadius=[o/2,o/2,0,0]),i.y>m&&(l.barBorderRadius=[0,0,o/2,o/2]);let[r,s,c,h]=l.barBorderRadius,d=Math.min(o/2,n/2);r=r>d?d:r,s=s>d?d:s,c=c>d?d:c,h=h>d?d:h,r=r<0?0:r,s=s<0?0:s,c=c<0?0:c,h=h<0?0:h,a.arc(t+r,e+r,r,-Math.PI,-Math.PI/2),a.arc(t+o-s,e+s,s,-Math.PI/2,0),a.arc(t+o-c,e+n-c,c,0,Math.PI/2),a.arc(t+h,e+n-h,h,Math.PI/2,Math.PI)}else a.moveTo(S,i.y),a.lineTo(S+i.width,i.y),a.lineTo(S+i.width,m),a.lineTo(S,m),a.lineTo(S,i.y),a.setLineWidth(1),a.setStrokeStyle(_);a.setFillStyle(T),a.closePath(),a.fill()}}break;case"stack":v=Wo(y,g,f,r,s,e,i,p,t,o),c.push(v),v=Mo(v,s,t.length,0,0,e);for(let t=0;t<v.length;t++){let i=v[t];if(null!==i&&t>h&&t<d){a.beginPath(),T=i.color||n.color,S=i.x-i.width/2+1,w=e.height-i.y-e.area[2];var C=e.height-i.y0-e.area[2];p>0&&(w-=C),a.setFillStyle(T),a.moveTo(S,i.y),a.fillRect(S,i.y,i.width,w),a.closePath(),a.fill()}}break;case"meter":v=Ro(y,g,f,r,s,e,i,o),c.push(v),v=Lo(v,s,t.length,p,0,e,l.meterBorder);for(let t=0;t<v.length;t++){let i=v[t];if(null!==i&&t>h&&t<d){if(a.beginPath(),0==p&&l.meterBorder>0&&(a.setStrokeStyle(n.color),a.setLineWidth(l.meterBorder*e.pix)),0==p?a.setFillStyle(l.meterFillColor):a.setFillStyle(i.color||n.color),S=i.x-i.width/2,w=e.height-i.y-e.area[2],l.barBorderRadius&&4===l.barBorderRadius.length||!0===l.barBorderCircle){const t=S,e=i.y,o=i.width,n=m-i.y;l.barBorderCircle&&(l.barBorderRadius=[o/2,o/2,0,0]);let[r,s,c,h]=l.barBorderRadius,d=Math.min(o/2,n/2);r=r>d?d:r,s=s>d?d:s,c=c>d?d:c,h=h>d?d:h,r=r<0?0:r,s=s<0?0:s,c=c<0?0:c,h=h<0?0:h,a.arc(t+r,e+r,r,-Math.PI,-Math.PI/2),a.arc(t+o-s,e+s,s,-Math.PI/2,0),a.arc(t+o-c,e+n-c,c,0,Math.PI/2),a.arc(t+h,e+n-h,h,Math.PI/2,Math.PI),a.fill()}else a.moveTo(S,i.y),a.lineTo(S+i.width,i.y),a.lineTo(S+i.width,m),a.lineTo(S,m),a.lineTo(S,i.y),a.fill();0==p&&l.meterBorder>0&&(a.closePath(),a.stroke())}}}})),!1!==e.dataLabel&&1===o&&t.forEach((function(n,c){let h,d,p;h=[].concat(e.chartData.yAxisData.ranges[n.index]),d=h.pop(),p=h.shift();var u=n.data;switch(l.type){case"group":Vo(Do(Bo(u,d,p,r,s,e,i,o),s,t.length,c,0,e),n,i,a,e);break;case"stack":Vo(Wo(u,d,p,r,s,e,i,c,t,o),n,i,a,e);break;case"meter":Vo(Ro(u,d,p,r,s,e,i,o),n,i,a,e)}})),a.restore(),{xAxisPoints:r,calPoints:c,eachSpacing:s}}(n,e,i,a,t),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=s,e.chartData.calPoints=l,e.chartData.eachSpacing=c,dn(0,e,i,a),!1!==e.enableMarkLine&&1===t&&an(e,0,a),pn(e.series,e,i,a,e.chartData),ln(e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"mount":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),hn(0,e,0,a),cn(r,e,i,a);var o=function(t,e,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=e.chartData.xAxisData,r=n.xAxisPoints,s=n.eachSpacing,l=Ka({},{type:"mount",widthRatio:1,borderWidth:1,barBorderCircle:!1,barBorderRadius:[],linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.mount);l.widthRatio=l.widthRatio<=0?0:l.widthRatio,l.widthRatio=l.widthRatio>=2?2:l.widthRatio,a.save();let c,h,d,p=-2,u=r.length+2;e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),p=Math.floor(-e._scrollDistance_/s)-2,u=p+e.xAxis.itemCount+4),l.customColor=ao(l.linearType,l.customColor,t,i),c=[].concat(e.chartData.yAxisData.ranges[0]),h=c.pop(),d=c.shift();let g=(e.height-e.area[0]-e.area[2])*(0-h)/(d-h),f=e.height-Math.round(g)-e.area[2];var x=zo(t,h,d,r,s,e,l,f,o);switch(l.type){case"bar":for(let i=0;i<x.length;i++){let o=x[i];if(null!==o&&i>p&&i<u){var m=o.x-s*l.widthRatio/2,y=e.height-o.y-e.area[2];a.beginPath();var v=o.color||t[i].color,b=o.color||t[i].color;if("none"!==l.linearType){var S=a.createLinearGradient(m,o.y,m,f);"opacity"==l.linearType?(S.addColorStop(0,Xa(v,l.linearOpacity)),S.addColorStop(1,Xa(v,1))):(S.addColorStop(0,Xa(l.customColor[t[i].linearIndex],l.linearOpacity)),S.addColorStop(l.colorStop,Xa(l.customColor[t[i].linearIndex],l.linearOpacity)),S.addColorStop(1,Xa(v,1))),v=S}if(l.barBorderRadius&&4===l.barBorderRadius.length||!0===l.barBorderCircle){const t=m,e=o.y>f?f:o.y,i=o.width,n=Math.abs(f-o.y);l.barBorderCircle&&(l.barBorderRadius=[i/2,i/2,0,0]),o.y>f&&(l.barBorderRadius=[0,0,i/2,i/2]);let[r,s,c,h]=l.barBorderRadius,d=Math.min(i/2,n/2);r=r>d?d:r,s=s>d?d:s,c=c>d?d:c,h=h>d?d:h,r=r<0?0:r,s=s<0?0:s,c=c<0?0:c,h=h<0?0:h,a.arc(t+r,e+r,r,-Math.PI,-Math.PI/2),a.arc(t+i-s,e+s,s,-Math.PI/2,0),a.arc(t+i-c,e+n-c,c,0,Math.PI/2),a.arc(t+h,e+n-h,h,Math.PI/2,Math.PI)}else a.moveTo(m,o.y),a.lineTo(m+o.width,o.y),a.lineTo(m+o.width,f),a.lineTo(m,f),a.lineTo(m,o.y);a.setStrokeStyle(b),a.setFillStyle(v),l.borderWidth>0&&(a.setLineWidth(l.borderWidth*e.pix),a.closePath(),a.stroke()),a.fill()}}break;case"triangle":for(let i=0;i<x.length;i++){let o=x[i];null!==o&&i>p&&i<u&&(m=o.x-s*l.widthRatio/2,y=e.height-o.y-e.area[2],a.beginPath(),v=o.color||t[i].color,b=o.color||t[i].color,"none"!==l.linearType&&(S=a.createLinearGradient(m,o.y,m,f),"opacity"==l.linearType?(S.addColorStop(0,Xa(v,l.linearOpacity)),S.addColorStop(1,Xa(v,1))):(S.addColorStop(0,Xa(l.customColor[t[i].linearIndex],l.linearOpacity)),S.addColorStop(l.colorStop,Xa(l.customColor[t[i].linearIndex],l.linearOpacity)),S.addColorStop(1,Xa(v,1))),v=S),a.moveTo(m,f),a.lineTo(o.x,o.y),a.lineTo(m+o.width,f),a.setStrokeStyle(b),a.setFillStyle(v),l.borderWidth>0&&(a.setLineWidth(l.borderWidth*e.pix),a.stroke()),a.fill())}break;case"mount":for(let i=0;i<x.length;i++){let o=x[i];null!==o&&i>p&&i<u&&(m=o.x-s*l.widthRatio/2,y=e.height-o.y-e.area[2],a.beginPath(),v=o.color||t[i].color,b=o.color||t[i].color,"none"!==l.linearType&&(S=a.createLinearGradient(m,o.y,m,f),"opacity"==l.linearType?(S.addColorStop(0,Xa(v,l.linearOpacity)),S.addColorStop(1,Xa(v,1))):(S.addColorStop(0,Xa(l.customColor[t[i].linearIndex],l.linearOpacity)),S.addColorStop(l.colorStop,Xa(l.customColor[t[i].linearIndex],l.linearOpacity)),S.addColorStop(1,Xa(v,1))),v=S),a.moveTo(m,f),a.bezierCurveTo(o.x-o.width/4,f,o.x-o.width/4,o.y,o.x,o.y),a.bezierCurveTo(o.x+o.width/4,o.y,o.x+o.width/4,f,m+o.width,f),a.setStrokeStyle(b),a.setFillStyle(v),l.borderWidth>0&&(a.setLineWidth(l.borderWidth*e.pix),a.stroke()),a.fill())}break;case"sharp":for(let i=0;i<x.length;i++){let o=x[i];null!==o&&i>p&&i<u&&(m=o.x-s*l.widthRatio/2,y=e.height-o.y-e.area[2],a.beginPath(),v=o.color||t[i].color,b=o.color||t[i].color,"none"!==l.linearType&&(S=a.createLinearGradient(m,o.y,m,f),"opacity"==l.linearType?(S.addColorStop(0,Xa(v,l.linearOpacity)),S.addColorStop(1,Xa(v,1))):(S.addColorStop(0,Xa(l.customColor[t[i].linearIndex],l.linearOpacity)),S.addColorStop(l.colorStop,Xa(l.customColor[t[i].linearIndex],l.linearOpacity)),S.addColorStop(1,Xa(v,1))),v=S),a.moveTo(m,f),a.quadraticCurveTo(o.x-0,f-y/4,o.x,o.y),a.quadraticCurveTo(o.x+0,f-y/4,m+o.width,f),a.setStrokeStyle(b),a.setFillStyle(v),l.borderWidth>0&&(a.setLineWidth(l.borderWidth*e.pix),a.stroke()),a.fill())}}if(!1!==e.dataLabel&&1===o){let n,c,h;n=[].concat(e.chartData.yAxisData.ranges[0]),c=n.pop(),h=n.shift(),Yo(x=zo(t,c,h,r,s,e,l,f,o),t,i,a,e,f)}return a.restore(),{xAxisPoints:r,calPoints:x,eachSpacing:s}}(n,e,i,a,t),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=s,e.chartData.calPoints=l,e.chartData.eachSpacing=c,dn(0,e,i,a),!1!==e.enableMarkLine&&1===t&&an(e,0,a),pn(e.series,e,i,a,e.chartData),ln(e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"bar":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),cn(r,e,i,a);var o=function(t,e,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=[],r=(e.height-e.area[0]-e.area[2])/e.categories.length;for(let d=0;d<e.categories.length;d++)n.push(e.area[0]+r/2+r*d);let s=Ka({},{type:"group",width:r/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.bar),l=[];a.save();let c=-2,h=n.length+2;return e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===o&&rn(e.tooltip.offset.y,e,0,a,r),s.customColor=ao(s.linearType,s.customColor,t,i),t.forEach((function(d,p){let u,g,f;u=[].concat(e.chartData.xAxisData.ranges),f=u.pop(),g=u.shift();var x=d.data;switch(s.type){case"group":var m=Uo(x,g,f,n,r,e,i,o),y=qo(x,g,f,n,r,e,i,p,t,o);l.push(y),m=Io(m,r,t.length,p,0,e);for(let t=0;t<m.length;t++){let i=m[t];if(null!==i&&t>c&&t<h){var v=e.area[3],b=i.y-i.width/2;i.height,a.beginPath();var S=i.color||d.color,w=i.color||d.color;if("none"!==s.linearType){var T=a.createLinearGradient(v,i.y,i.x,i.y);"opacity"==s.linearType?(T.addColorStop(0,Xa(S,s.linearOpacity)),T.addColorStop(1,Xa(S,1))):(T.addColorStop(0,Xa(s.customColor[d.linearIndex],s.linearOpacity)),T.addColorStop(s.colorStop,Xa(s.customColor[d.linearIndex],s.linearOpacity)),T.addColorStop(1,Xa(S,1))),S=T}if(s.barBorderRadius&&4===s.barBorderRadius.length||!0===s.barBorderCircle){const t=v,e=i.width,o=i.y-i.width/2,n=i.height;s.barBorderCircle&&(s.barBorderRadius=[e/2,e/2,0,0]);let[r,l,c,h]=s.barBorderRadius,d=Math.min(e/2,n/2);r=r>d?d:r,l=l>d?d:l,c=c>d?d:c,h=h>d?d:h,r=r<0?0:r,l=l<0?0:l,c=c<0?0:c,h=h<0?0:h,a.arc(t+h,o+h,h,-Math.PI,-Math.PI/2),a.arc(i.x-r,o+r,r,-Math.PI/2,0),a.arc(i.x-l,o+e-l,l,0,Math.PI/2),a.arc(t+c,o+e-c,c,Math.PI/2,Math.PI)}else a.moveTo(v,b),a.lineTo(i.x,b),a.lineTo(i.x,b+i.width),a.lineTo(v,b+i.width),a.lineTo(v,b),a.setLineWidth(1),a.setStrokeStyle(w);a.setFillStyle(S),a.closePath(),a.fill()}}break;case"stack":m=qo(x,g,f,n,r,e,i,p,t,o),l.push(m),m=Fo(m,r,t.length,0,0,e);for(let t=0;t<m.length;t++){let e=m[t];null!==e&&t>c&&t<h&&(a.beginPath(),S=e.color||d.color,v=e.x0,a.setFillStyle(S),a.moveTo(v,e.y-e.width/2),a.fillRect(v,e.y-e.width/2,e.height,e.width),a.closePath(),a.fill())}}})),!1!==e.dataLabel&&1===o&&t.forEach((function(l,c){let h,d,p;h=[].concat(e.chartData.xAxisData.ranges),p=h.pop(),d=h.shift();var u=l.data;switch(s.type){case"group":Qo(Io(Uo(u,d,p,n,r,e,i,o),r,t.length,c,0,e),l,i,a,e);break;case"stack":Qo(qo(u,d,p,n,r,e,i,c,t,o),l,i,a,e)}})),{yAxisPoints:n,calPoints:l,eachSpacing:r}}(n,e,i,a,t),s=o.yAxisPoints,l=o.calPoints,c=o.eachSpacing;e.chartData.yAxisPoints=s,e.chartData.xAxisPoints=e.chartData.xAxisData.xAxisPoints,e.chartData.calPoints=l,e.chartData.eachSpacing=c,dn(0,e,i,a),!1!==e.enableMarkLine&&1===t&&an(e,0,a),pn(e.series,e,i,a,e.chartData),ln(e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"area":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),hn(0,e,0,a),cn(r,e,i,a);var o=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=Ka({},{type:"straight",opacity:.2,addLine:!1,width:2,gradient:!1,activeType:"none"},e.extra.area);let r=e.chartData.xAxisData,s=r.xAxisPoints,l=r.eachSpacing,c=e.height-e.area[2],h=[];a.save();let d=0,p=e.width+l;return e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&(a.translate(e._scrollDistance_,0),d=-e._scrollDistance_-2*l+e.area[3],p=d+(e.xAxis.itemCount+4)*l),t.forEach((function(t,r){let u,g,f;u=[].concat(e.chartData.yAxisData.ranges[t.index]),g=u.pop(),f=u.shift();let x=Ro(t.data,g,f,s,l,e,i,o);h.push(x);let m=yo(x,t);for(let i=0;i<m.length;i++){let o=m[i];if(a.beginPath(),a.setStrokeStyle(Xa(t.color,n.opacity)),n.gradient){let i=a.createLinearGradient(0,e.area[0],0,e.height-e.area[2]);i.addColorStop("0",Xa(t.color,n.opacity)),i.addColorStop("1.0",Xa("#FFFFFF",.1)),a.setFillStyle(i)}else a.setFillStyle(Xa(t.color,n.opacity));if(a.setLineWidth(n.width*e.pix),o.length>1){let t=o[0],e=o[o.length-1];a.moveTo(t.x,t.y);let i=0;if("curve"===n.type)for(let n=0;n<o.length;n++){let t=o[n];if(0==i&&t.x>d&&(a.moveTo(t.x,t.y),i=1),n>0&&t.x>d&&t.x<p){let e=Za(o,n-1);a.bezierCurveTo(e.ctrA.x,e.ctrA.y,e.ctrB.x,e.ctrB.y,t.x,t.y)}}if("straight"===n.type)for(let n=0;n<o.length;n++){let t=o[n];0==i&&t.x>d&&(a.moveTo(t.x,t.y),i=1),n>0&&t.x>d&&t.x<p&&a.lineTo(t.x,t.y)}if("step"===n.type)for(let n=0;n<o.length;n++){let t=o[n];0==i&&t.x>d&&(a.moveTo(t.x,t.y),i=1),n>0&&t.x>d&&t.x<p&&(a.lineTo(t.x,o[n-1].y),a.lineTo(t.x,t.y))}a.lineTo(e.x,c),a.lineTo(t.x,c),a.lineTo(t.x,t.y)}else{let t=o[0];a.moveTo(t.x-l/2,t.y)}if(a.closePath(),a.fill(),n.addLine){if("dash"==t.lineType){let i=t.dashLength?t.dashLength:8;i*=e.pix,a.setLineDash([i,i])}if(a.beginPath(),a.setStrokeStyle(t.color),a.setLineWidth(n.width*e.pix),1===o.length)a.moveTo(o[0].x,o[0].y);else{a.moveTo(o[0].x,o[0].y);let t=0;if("curve"===n.type)for(let e=0;e<o.length;e++){let i=o[e];if(0==t&&i.x>d&&(a.moveTo(i.x,i.y),t=1),e>0&&i.x>d&&i.x<p){let t=Za(o,e-1);a.bezierCurveTo(t.ctrA.x,t.ctrA.y,t.ctrB.x,t.ctrB.y,i.x,i.y)}}if("straight"===n.type)for(let e=0;e<o.length;e++){let i=o[e];0==t&&i.x>d&&(a.moveTo(i.x,i.y),t=1),e>0&&i.x>d&&i.x<p&&a.lineTo(i.x,i.y)}if("step"===n.type)for(let e=0;e<o.length;e++){let i=o[e];0==t&&i.x>d&&(a.moveTo(i.x,i.y),t=1),e>0&&i.x>d&&i.x<p&&(a.lineTo(i.x,o[e-1].y),a.lineTo(i.x,i.y))}a.moveTo(o[0].x,o[0].y)}a.stroke(),a.setLineDash([])}}!1!==e.dataPointShape&&Go(x,t.color,t.pointShape,a,e),Ko(x,t.color,t.pointShape,a,e,n,r)})),!1!==e.dataLabel&&1===o&&t.forEach((function(t,n){let r,c,h;r=[].concat(e.chartData.yAxisData.ranges[t.index]),c=r.pop(),h=r.shift(),Xo(Ro(t.data,c,h,s,l,e,i,o),t,i,a,e)})),a.restore(),{xAxisPoints:s,calPoints:h,eachSpacing:l}}(n,e,i,a,t),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;e.chartData.xAxisPoints=s,e.chartData.calPoints=l,e.chartData.eachSpacing=c,dn(0,e,i,a),!1!==e.enableMarkLine&&1===t&&an(e,0,a),pn(e.series,e,i,a,e.chartData),ln(e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"ring":case"pie":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),e.chartData.pieData=un(n,e,i,a,t),pn(e.series,e,i,a,e.chartData),ln(e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"rose":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),e.chartData.pieData=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=Ka({},{type:"area",activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF",linearType:"none",customColor:[]},e.extra.rose);0==i.pieChartLinePadding&&(i.pieChartLinePadding=n.activeRadius*e.pix);var r={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2},s=Math.min((e.width-e.area[1]-e.area[3])/2-i.pieChartLinePadding-i.pieChartTextPadding-i._pieTextMaxLength_,(e.height-e.area[0]-e.area[2])/2-i.pieChartLinePadding-i.pieChartTextPadding);s=s<10?10:s;var l=n.minRadius||.5*s;s<l&&(s=l+10),t=_o(t,n.type,l,s,o);var c=n.activeRadius*e.pix;return n.customColor=ao(n.linearType,n.customColor,t,i),(t=t.map((function(t){return t._start_+=(n.offsetAngle||0)*Math.PI/180,t}))).forEach((function(t,i){e.tooltip&&e.tooltip.index==i&&(a.beginPath(),a.setFillStyle(Xa(t.color,n.activeOpacity||.5)),a.moveTo(r.x,r.y),a.arc(r.x,r.y,c+t._radius_,t._start_,t._start_+2*t._rose_proportion_*Math.PI),a.closePath(),a.fill()),a.beginPath(),a.setLineWidth(n.borderWidth*e.pix),a.lineJoin="round",a.setStrokeStyle(n.borderColor);var o,s=t.color;"custom"==n.linearType&&((o=a.createCircularGradient?a.createCircularGradient(r.x,r.y,t._radius_):a.createRadialGradient(r.x,r.y,0,r.x,r.y,t._radius_)).addColorStop(0,Xa(n.customColor[t.linearIndex],1)),o.addColorStop(1,Xa(t.color,1)),s=o),a.setFillStyle(s),a.moveTo(r.x,r.y),a.arc(r.x,r.y,t._radius_,t._start_,t._start_+2*t._rose_proportion_*Math.PI),a.closePath(),a.fill(),1==n.border&&a.stroke()})),!1!==e.dataLabel&&1===o&&en(t,e,i,a,0,r),{center:r,radius:s,series:t}}(n,e,i,a,t),pn(e.series,e,i,a,e.chartData),ln(e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"radar":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),e.chartData.radarData=function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=Ka({},{gridColor:"#cccccc",gridType:"radar",gridEval:1,axisLabel:!1,axisLabelTofix:0,labelShow:!0,labelColor:"#666666",labelPointShow:!1,labelPointRadius:3,labelPointColor:"#cccccc",opacity:.2,gridCount:3,border:!1,borderWidth:2,linearType:"none",customColor:[]},e.extra.radar),r=ho(e.categories.length),s={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2},l=(e.width-e.area[1]-e.area[3])/2,c=(e.height-e.area[0]-e.area[2])/2,h=Math.min(l-(co(e.categories,i.fontSize,a)+i.radarLabelTextMargin),c-i.radarLabelTextMargin);h=(h-=i.radarLabelTextMargin*e.pix)<10?10:h,h=n.radius?n.radius:h,a.beginPath(),a.setLineWidth(1*e.pix),a.setStrokeStyle(n.gridColor),r.forEach((function(t,e){var i=to(h*Math.cos(t),h*Math.sin(t),s);a.moveTo(s.x,s.y),e%n.gridEval==0&&a.lineTo(i.x,i.y)})),a.stroke(),a.closePath();for(var d=function(t){var i={};if(a.beginPath(),a.setLineWidth(1*e.pix),a.setStrokeStyle(n.gridColor),"radar"==n.gridType)r.forEach((function(e,o){var r=to(h/n.gridCount*t*Math.cos(e),h/n.gridCount*t*Math.sin(e),s);0===o?(i=r,a.moveTo(r.x,r.y)):a.lineTo(r.x,r.y)})),a.lineTo(i.x,i.y);else{var o=to(h/n.gridCount*t*Math.cos(1.5),h/n.gridCount*t*Math.sin(1.5),s);a.arc(s.x,s.y,s.y-o.y,0,2*Math.PI,!1)}a.stroke(),a.closePath()},p=1;p<=n.gridCount;p++)d(p);n.customColor=ao(n.linearType,n.customColor,t,i);var u=So(r,s,h,t,e,o);if(u.forEach((function(i,o){a.beginPath(),a.setLineWidth(n.borderWidth*e.pix),a.setStrokeStyle(i.color);var r,l=Xa(i.color,n.opacity);"custom"==n.linearType&&((r=a.createCircularGradient?a.createCircularGradient(s.x,s.y,h):a.createRadialGradient(s.x,s.y,0,s.x,s.y,h)).addColorStop(0,Xa(n.customColor[t[o].linearIndex],n.opacity)),r.addColorStop(1,Xa(i.color,n.opacity)),l=r),a.setFillStyle(l),i.data.forEach((function(t,e){0===e?a.moveTo(t.position.x,t.position.y):a.lineTo(t.position.x,t.position.y)})),a.closePath(),a.fill(),!0===n.border&&a.stroke(),a.closePath(),!1!==e.dataPointShape&&Go(i.data.map((function(t){return t.position})),i.color,i.pointShape,a,e)})),!0===n.axisLabel){const i=Math.max(n.max,Math.max.apply(null,no(t))),o=h/n.gridCount,r=e.fontSize*e.pix;for(a.setFontSize(r),a.setFillStyle(e.fontColor),a.setTextAlign("left"),p=0;p<n.gridCount+1;p++){let t=p*i/n.gridCount;t=t.toFixed(n.axisLabelTofix),a.fillText(String(t),s.x+3*e.pix,s.y-p*o+r/2)}}return tn(r,h,s,e,i,a),!1!==e.dataLabel&&1===o&&(u.forEach((function(t,o){a.beginPath();var n=t.textSize*e.pix||i.fontSize;a.setFontSize(n),a.setFillStyle(t.textColor||e.fontColor),t.data.forEach((function(t,e){Math.abs(t.position.x-s.x)<2?t.position.y<s.y?(a.setTextAlign("center"),a.fillText(t.value,t.position.x,t.position.y-4)):(a.setTextAlign("center"),a.fillText(t.value,t.position.x,t.position.y+n+2)):t.position.x<s.x?(a.setTextAlign("right"),a.fillText(t.value,t.position.x-4,t.position.y+n/2-2)):(a.setTextAlign("left"),a.fillText(t.value,t.position.x+4,t.position.y+n/2-2))})),a.closePath(),a.stroke()})),a.setTextAlign("left")),{center:s,radius:h,angleList:r}}(n,e,i,a,t),pn(e.series,e,i,a,e.chartData),ln(e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"arcbar":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),e.chartData.arcbarData=function(t,e,i,a){var o,n,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,s=Ka({},{startAngle:.75,endAngle:.25,type:"default",direction:"cw",lineCap:"round",width:12,gap:2,linearType:"none",customColor:[]},e.extra.arcbar);t=Ao(t,s,r),o=s.centerX||s.centerY?{x:s.centerX?s.centerX:e.width/2,y:s.centerY?s.centerY:e.height/2}:{x:e.width/2,y:e.height/2},s.radius?n=s.radius:(n=Math.min(o.x,o.y),n-=5*e.pix,n-=s.width/2),n=n<10?10:n,s.customColor=ao(s.linearType,s.customColor,t,i);for(let h=0;h<t.length;h++){let i=t[h];a.setLineWidth(s.width*e.pix),a.setStrokeStyle(s.backgroundColor||"#E9E9E9"),a.setLineCap(s.lineCap),a.beginPath(),"default"==s.type?a.arc(o.x,o.y,n-(s.width*e.pix+s.gap*e.pix)*h,s.startAngle*Math.PI,s.endAngle*Math.PI,"ccw"==s.direction):a.arc(o.x,o.y,n-(s.width*e.pix+s.gap*e.pix)*h,0,2*Math.PI,"ccw"==s.direction),a.stroke();var l=i.color;if("custom"==s.linearType){var c=a.createLinearGradient(o.x-n,o.y,o.x+n,o.y);c.addColorStop(1,Xa(s.customColor[i.linearIndex],1)),c.addColorStop(0,Xa(i.color,1)),l=c}a.setLineWidth(s.width*e.pix),a.setStrokeStyle(l),a.setLineCap(s.lineCap),a.beginPath(),a.arc(o.x,o.y,n-(s.width*e.pix+s.gap*e.pix)*h,s.startAngle*Math.PI,i._proportion_*Math.PI,"ccw"==s.direction),a.stroke()}return Jo(e,i,a,o),{center:o,radius:n,series:t}}(n,e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"gauge":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),e.chartData.gaugeData=function(t,e,i,a,o){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,r=Ka({},{type:"default",startAngle:.75,endAngle:.25,width:15,labelOffset:13,splitLine:{fixRadius:0,splitNumber:10,width:15,color:"#FFFFFF",childNumber:5,childWidth:5},pointer:{width:15,color:"auto"}},i.extra.gauge);null==r.oldAngle&&(r.oldAngle=r.startAngle),null==r.oldData&&(r.oldData=0),t=Po(t,r.startAngle,r.endAngle);var s={x:i.width/2,y:i.height/2},l=Math.min(s.x,s.y);l-=5*i.pix;var c=(l=(l-=r.width/2)<10?10:l)-r.width,h=0;if("progress"==r.type){var d=l-3*r.width;o.beginPath();let t=o.createLinearGradient(s.x,s.y-d,s.x,s.y+d);t.addColorStop("0",Xa(e[0].color,.3)),t.addColorStop("1.0",Xa("#FFFFFF",.1)),o.setFillStyle(t),o.arc(s.x,s.y,d,0,2*Math.PI,!1),o.fill(),o.setLineWidth(r.width),o.setStrokeStyle(Xa(e[0].color,.3)),o.setLineCap("round"),o.beginPath(),o.arc(s.x,s.y,c,r.startAngle*Math.PI,r.endAngle*Math.PI,!1),o.stroke(),h=r.endAngle<r.startAngle?2+r.endAngle-r.startAngle:r.startAngle-r.endAngle,r.splitLine.splitNumber;let a=h/r.splitLine.splitNumber/r.splitLine.childNumber,p=-l-.5*r.width-r.splitLine.fixRadius,u=-l-r.width-r.splitLine.fixRadius+r.splitLine.width;o.save(),o.translate(s.x,s.y),o.rotate((r.startAngle-1)*Math.PI);let g=r.splitLine.splitNumber*r.splitLine.childNumber+1,f=e[0].data*n;for(let n=0;n<g;n++)o.beginPath(),f>n/g?o.setStrokeStyle(Xa(e[0].color,1)):o.setStrokeStyle(Xa(e[0].color,.3)),o.setLineWidth(3*i.pix),o.moveTo(p,0),o.lineTo(u,0),o.stroke(),o.rotate(a*Math.PI);o.restore(),e=Co(e,r,n),o.setLineWidth(r.width),o.setStrokeStyle(e[0].color),o.setLineCap("round"),o.beginPath(),o.arc(s.x,s.y,c,r.startAngle*Math.PI,e[0]._proportion_*Math.PI,!1),o.stroke();let x=l-2.5*r.width;o.save(),o.translate(s.x,s.y),o.rotate((e[0]._proportion_-1)*Math.PI),o.beginPath(),o.setLineWidth(r.width/3);let m=o.createLinearGradient(0,.6*-x,0,.6*x);m.addColorStop("0",Xa("#FFFFFF",0)),m.addColorStop("0.5",Xa(e[0].color,1)),m.addColorStop("1.0",Xa("#FFFFFF",0)),o.setStrokeStyle(m),o.arc(0,0,x,.85*Math.PI,1.15*Math.PI,!1),o.stroke(),o.beginPath(),o.setLineWidth(1),o.setStrokeStyle(e[0].color),o.setFillStyle(e[0].color),o.moveTo(-x-r.width/3/2,-4),o.lineTo(-x-r.width/3/2-4,0),o.lineTo(-x-r.width/3/2,4),o.lineTo(-x-r.width/3/2,-4),o.stroke(),o.fill(),o.restore()}else{o.setLineWidth(r.width),o.setLineCap("butt");for(let e=0;e<t.length;e++){let i=t[e];o.beginPath(),o.setStrokeStyle(i.color),o.arc(s.x,s.y,l,i._startAngle_*Math.PI,i._endAngle_*Math.PI,!1),o.stroke()}o.save();let d=(h=r.endAngle<r.startAngle?2+r.endAngle-r.startAngle:r.startAngle-r.endAngle)/r.splitLine.splitNumber,p=h/r.splitLine.splitNumber/r.splitLine.childNumber,u=-l-.5*r.width-r.splitLine.fixRadius,g=-l-.5*r.width-r.splitLine.fixRadius+r.splitLine.width,f=-l-.5*r.width-r.splitLine.fixRadius+r.splitLine.childWidth;o.translate(s.x,s.y),o.rotate((r.startAngle-1)*Math.PI);for(let t=0;t<r.splitLine.splitNumber+1;t++)o.beginPath(),o.setStrokeStyle(r.splitLine.color),o.setLineWidth(2*i.pix),o.moveTo(u,0),o.lineTo(g,0),o.stroke(),o.rotate(d*Math.PI);o.restore(),o.save(),o.translate(s.x,s.y),o.rotate((r.startAngle-1)*Math.PI);for(let t=0;t<r.splitLine.splitNumber*r.splitLine.childNumber+1;t++)o.beginPath(),o.setStrokeStyle(r.splitLine.color),o.setLineWidth(1*i.pix),o.moveTo(u,0),o.lineTo(f,0),o.stroke(),o.rotate(p*Math.PI);o.restore(),e=ko(e,t,r,n);for(let t=0;t<e.length;t++){let i=e[t];o.save(),o.translate(s.x,s.y),o.rotate((i._proportion_-1)*Math.PI),o.beginPath(),o.setFillStyle(i.color),o.moveTo(r.pointer.width,0),o.lineTo(0,-r.pointer.width/2),o.lineTo(-c,0),o.lineTo(0,r.pointer.width/2),o.lineTo(r.pointer.width,0),o.closePath(),o.fill(),o.beginPath(),o.setFillStyle("#FFFFFF"),o.arc(0,0,r.pointer.width/6,0,2*Math.PI,!1),o.fill(),o.restore()}!1!==i.dataLabel&&Zo(r,l,s,i,a,o)}return Jo(i,a,o,s),1===n&&"gauge"===i.type&&(i.extra.gauge.oldAngle=e[0]._proportion_,i.extra.gauge.oldData=e[0].data),{center:s,radius:l,innerRadius:c,categories:t,totalAngle:h}}(r,n,e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"candle":this.animationInstance=new An({timing:e.timing,duration:s,onProcess:function(t){a.clearRect(0,0,e.width,e.height),e.rotate&&Ho(a,e),hn(0,e,0,a),cn(r,e,i,a);var o=function(t,e,i,a,o){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,r=Ka({},{color:{},average:{}},i.extra.candle);r.color=Ka({},{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},r.color),r.average=Ka({},{show:!1,name:[],day:[],color:a.color},r.average),i.extra.candle=r;let s=i.chartData.xAxisData,l=s.xAxisPoints,c=s.eachSpacing,h=[];o.save();let d=-2,p=l.length+2,u=0,g=i.width+c;return i._scrollDistance_&&0!==i._scrollDistance_&&!0===i.enableScroll&&(o.translate(i._scrollDistance_,0),d=Math.floor(-i._scrollDistance_/c)-2,p=d+i.xAxis.itemCount+4,u=-i._scrollDistance_-2*c+i.area[3],g=u+(i.xAxis.itemCount+4)*c),(r.average.show||e)&&e.forEach((function(t,e){let r,s,h;r=[].concat(i.chartData.yAxisData.ranges[t.index]),s=r.pop(),h=r.shift();var d=yo(Ro(t.data,s,h,l,c,i,a,n),t);for(let i=0;i<d.length;i++){let e=d[i];if(o.beginPath(),o.setStrokeStyle(t.color),o.setLineWidth(1),1===e.length)o.moveTo(e[0].x,e[0].y),o.arc(e[0].x,e[0].y,1,0,2*Math.PI);else{o.moveTo(e[0].x,e[0].y);let t=0;for(let i=0;i<e.length;i++){let a=e[i];if(0==t&&a.x>u&&(o.moveTo(a.x,a.y),t=1),i>0&&a.x>u&&a.x<g){var p=Za(e,i-1);o.bezierCurveTo(p.ctrA.x,p.ctrA.y,p.ctrB.x,p.ctrB.y,a.x,a.y)}}o.moveTo(e[0].x,e[0].y)}o.closePath(),o.stroke()}})),t.forEach((function(t,e){let s,u,g;s=[].concat(i.chartData.yAxisData.ranges[t.index]),u=s.pop(),g=s.shift();var f=t.data,x=Eo(f,u,g,l,c,i,a,n);h.push(x);var m=yo(x,t);for(let a=0;a<m[0].length;a++)if(a>d&&a<p){let t=m[0][a];o.beginPath(),f[a][1]-f[a][0]>0?(o.setStrokeStyle(r.color.upLine),o.setFillStyle(r.color.upFill),o.setLineWidth(1*i.pix),o.moveTo(t[3].x,t[3].y),o.lineTo(t[1].x,t[1].y),o.lineTo(t[1].x-c/4,t[1].y),o.lineTo(t[0].x-c/4,t[0].y),o.lineTo(t[0].x,t[0].y),o.lineTo(t[2].x,t[2].y),o.lineTo(t[0].x,t[0].y),o.lineTo(t[0].x+c/4,t[0].y),o.lineTo(t[1].x+c/4,t[1].y),o.lineTo(t[1].x,t[1].y),o.moveTo(t[3].x,t[3].y)):(o.setStrokeStyle(r.color.downLine),o.setFillStyle(r.color.downFill),o.setLineWidth(1*i.pix),o.moveTo(t[3].x,t[3].y),o.lineTo(t[0].x,t[0].y),o.lineTo(t[0].x-c/4,t[0].y),o.lineTo(t[1].x-c/4,t[1].y),o.lineTo(t[1].x,t[1].y),o.lineTo(t[2].x,t[2].y),o.lineTo(t[1].x,t[1].y),o.lineTo(t[1].x+c/4,t[1].y),o.lineTo(t[0].x+c/4,t[0].y),o.lineTo(t[0].x,t[0].y),o.moveTo(t[3].x,t[3].y)),o.closePath(),o.fill(),o.stroke()}})),o.restore(),{xAxisPoints:l,calPoints:h,eachSpacing:c}}(n,l,e,i,a,t),s=o.xAxisPoints,c=o.calPoints,h=o.eachSpacing;e.chartData.xAxisPoints=s,e.chartData.calPoints=c,e.chartData.eachSpacing=h,dn(0,e,i,a),!1!==e.enableMarkLine&&1===t&&an(e,0,a),pn(l?0:e.series,e,i,a,e.chartData),ln(e,i,a,t),Tn(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}})}}function Pn(){this.events={}}An.prototype.stop=function(){this.isStop=!0},Pn.prototype.addEventListener=function(t,e){this.events[t]=this.events[t]||[],this.events[t].push(e)},Pn.prototype.delEventListener=function(t){this.events[t]=[]},Pn.prototype.trigger=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];var a=e[0],o=e.slice(1);this.events[a]&&this.events[a].forEach((function(t){try{t.apply(null,o)}catch(pe){}}))};var kn=function(t){t.pix=t.pixelRatio?t.pixelRatio:1,t.fontSize=t.fontSize?t.fontSize:13,t.fontColor=t.fontColor?t.fontColor:Ga.fontColor,""!=t.background&&"none"!=t.background||(t.background="#FFFFFF"),t.title=Ka({},t.title),t.subtitle=Ka({},t.subtitle),t.duration=t.duration?t.duration:1e3,t.yAxis=Ka({},{data:[],showTitle:!1,disabled:!1,disableGrid:!1,gridSet:"number",splitNumber:5,gridType:"solid",dashLength:4*t.pix,gridColor:"#cccccc",padding:10,fontColor:"#666666"},t.yAxis),t.xAxis=Ka({},{rotateLabel:!1,rotateAngle:45,disabled:!1,disableGrid:!1,splitNumber:5,calibration:!1,fontColor:"#666666",fontSize:13,lineHeight:20,marginTop:0,gridType:"solid",dashLength:4,scrollAlign:"left",boundaryGap:"center",axisLine:!0,axisLineColor:"#cccccc",titleFontSize:13,titleOffsetY:0,titleOffsetX:0,titleFontColor:"#666666"},t.xAxis),t.xAxis.scrollPosition=t.xAxis.scrollAlign,t.legend=Ka({},{show:!0,position:"bottom",float:"center",backgroundColor:"rgba(0,0,0,0)",borderColor:"rgba(0,0,0,0)",borderWidth:0,padding:5,margin:5,itemGap:10,fontSize:t.fontSize,lineHeight:t.fontSize,fontColor:t.fontColor,formatter:{},hiddenColor:"#CECECE"},t.legend),t.extra=Ka({tooltip:{legendShape:"auto"}},t.extra),t.rotate=!!t.rotate,t.animation=!!t.animation,t.rotate=!!t.rotate,t.canvas2d=!!t.canvas2d;let e=Ka({},Ga);if(e.color=t.color?t.color:e.color,"pie"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.pie.labelWidth*t.pix||e.pieChartLinePadding*t.pix),"ring"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.ring.labelWidth*t.pix||e.pieChartLinePadding*t.pix),"rose"==t.type&&(e.pieChartLinePadding=!1===t.dataLabel?0:t.extra.rose.labelWidth*t.pix||e.pieChartLinePadding*t.pix),e.pieChartTextPadding=!1===t.dataLabel?0:e.pieChartTextPadding*t.pix,e.rotate=t.rotate,t.rotate){let e=t.width,i=t.height;t.width=i,t.height=e}if(t.padding=t.padding?t.padding:e.padding,e.yAxisWidth=Ga.yAxisWidth*t.pix,e.fontSize=t.fontSize*t.pix,e.titleFontSize=Ga.titleFontSize*t.pix,e.subtitleFontSize=Ga.subtitleFontSize*t.pix,!t.context)throw new Error("[uCharts] 未获取到context！注意：v2.0版本后，需要自行获取canvas的绘图上下文并传入opts.context！");this.context=t.context,this.context.setTextAlign||(this.context.setStrokeStyle=function(t){return this.strokeStyle=t},this.context.setLineWidth=function(t){return this.lineWidth=t},this.context.setLineCap=function(t){return this.lineCap=t},this.context.setFontSize=function(t){return this.font=t+"px sans-serif"},this.context.setFillStyle=function(t){return this.fillStyle=t},this.context.setTextAlign=function(t){return this.textAlign=t},this.context.setTextBaseline=function(t){return this.textBaseline=t},this.context.setShadow=function(t,e,i,a){this.shadowColor=a,this.shadowOffsetX=t,this.shadowOffsetY=e,this.shadowBlur=i},this.context.draw=function(){}),this.context.setLineDash||(this.context.setLineDash=function(t){}),t.chartData={},this.uevent=new Pn,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0},this.opts=t,this.config=e,Cn.call(this,t.type,t,e,this.context)};kn.prototype.updateData=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.opts=Ka({},this.opts,t),this.opts.updateData=!0;let e=t.scrollPosition||"current";switch(e){case"current":this.opts._scrollDistance_=this.scrollOption.currentOffset;break;case"left":this.opts._scrollDistance_=0,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0};break;case"right":let t=$o(this.opts.series,this.opts,this.config,this.context).yAxisWidth;this.config.yAxisWidth=t;let e=0,i=Oo(this.opts.categories,this.opts,this.config),a=i.xAxisPoints,o=i.startX;e=i.endX-o-i.eachSpacing*(a.length-1),this.scrollOption={currentOffset:e,startTouchX:e,distance:0,lastMoveTime:0},this.opts._scrollDistance_=e}Cn.call(this,this.opts.type,this.opts,this.config,this.context)},kn.prototype.zoom=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.opts.xAxis.itemCount;if(!0!==this.opts.enableScroll)return void console.log("[uCharts] 请启用滚动条后使用");let e=Math.round(Math.abs(this.scrollOption.currentOffset)/this.opts.chartData.eachSpacing)+Math.round(this.opts.xAxis.itemCount/2);this.opts.animation=!1,this.opts.xAxis.itemCount=t.itemCount;let i=$o(this.opts.series,this.opts,this.config,this.context),a=i.yAxisWidth;this.config.yAxisWidth=a;let o=0,n=Oo(this.opts.categories,this.opts,this.config),r=n.xAxisPoints,s=n.startX,l=n.endX,c=n.eachSpacing,h=c*e,d=l-s,p=d-c*(r.length-1);o=d/2-h,o>0&&(o=0),o<p&&(o=p),this.scrollOption={currentOffset:o,startTouchX:0,distance:0,lastMoveTime:0},Ya(this,o,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=o,Cn.call(this,this.opts.type,this.opts,this.config,this.context)},kn.prototype.dobuleZoom=function(t){if(!0!==this.opts.enableScroll)return void console.log("[uCharts] 请启用滚动条后使用");const e=t.changedTouches;if(e.length<2)return;for(var i=0;i<e.length;i++)e[i].x=e[i].x?e[i].x:e[i].clientX,e[i].y=e[i].y?e[i].y:e[i].clientY;const a=[so(e[0],this.opts,t),so(e[1],this.opts,t)],o=Math.abs(a[0].x-a[1].x);if(!this.scrollOption.moveCount){let t={changedTouches:[{x:e[0].x,y:this.opts.area[0]/this.opts.pix+2}]},i={changedTouches:[{x:e[1].x,y:this.opts.area[0]/this.opts.pix+2}]};this.opts.rotate&&(t={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:e[0].y}]},i={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:e[1].y}]});const a=this.getCurrentDataIndex(t).index,o=this.getCurrentDataIndex(i).index,n=Math.abs(a-o);return this.scrollOption.moveCount=n,this.scrollOption.moveCurrent1=Math.min(a,o),void(this.scrollOption.moveCurrent2=Math.max(a,o))}let n=o/this.scrollOption.moveCount,r=(this.opts.width-this.opts.area[1]-this.opts.area[3])/n;r=r<=2?2:r,r=r>=this.opts.categories.length?this.opts.categories.length:r,this.opts.animation=!1,this.opts.xAxis.itemCount=r;let s=0,l=Oo(this.opts.categories,this.opts,this.config),c=l.xAxisPoints,h=l.startX,d=l.endX,p=l.eachSpacing,u=p*this.scrollOption.moveCurrent1,g=d-h-p*(c.length-1);s=-u+Math.min(a[0].x,a[1].x)-this.opts.area[3]-p,s>0&&(s=0),s<g&&(s=g),this.scrollOption.currentOffset=s,this.scrollOption.startTouchX=0,this.scrollOption.distance=0,Ya(this,s,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=s,Cn.call(this,this.opts.type,this.opts,this.config,this.context)},kn.prototype.stopAnimation=function(){this.animationInstance&&this.animationInstance.stop()},kn.prototype.addEventListener=function(t,e){this.uevent.addEventListener(t,e)},kn.prototype.delEventListener=function(t){this.uevent.delEventListener(t)},kn.prototype.getCurrentDataIndex=function(t){var e=null;if(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0]){let i=so(e,this.opts,t);return"pie"===this.opts.type||"ring"===this.opts.type?function(t,e,i){var a=-1,o=wo(e.series);if(e&&e.center&&mo(t,e.center,e.radius)){var n=Math.atan2(e.center.y-t.y,t.x-e.center.x);n=-n,i.extra.pie&&i.extra.pie.offsetAngle&&(n-=i.extra.pie.offsetAngle*Math.PI/180),i.extra.ring&&i.extra.ring.offsetAngle&&(n-=i.extra.ring.offsetAngle*Math.PI/180);for(var r=0,s=o.length;r<s;r++)if(Qa(n,o[r]._start_,o[r]._start_+2*o[r]._proportion_*Math.PI)){a=r;break}}return a}({x:i.x,y:i.y},this.opts.chartData.pieData,this.opts):"rose"===this.opts.type?function(t,e,i){var a=-1,o=_o(i._series_,i.extra.rose.type,e.radius,e.radius);if(e&&e.center&&mo(t,e.center,e.radius)){var n=Math.atan2(e.center.y-t.y,t.x-e.center.x);n=-n,i.extra.rose&&i.extra.rose.offsetAngle&&(n-=i.extra.rose.offsetAngle*Math.PI/180);for(var r=0,s=o.length;r<s;r++)if(Qa(n,o[r]._start_,o[r]._start_+2*o[r]._rose_proportion_*Math.PI)){a=r;break}}return a}({x:i.x,y:i.y},this.opts.chartData.pieData,this.opts):"radar"===this.opts.type?function(t,e,i){var a=2*Math.PI/i,o=-1;if(mo(t,e.center,e.radius)){var n=function(t){return t<0&&(t+=2*Math.PI),t>2*Math.PI&&(t-=2*Math.PI),t},r=Math.atan2(e.center.y-t.y,t.x-e.center.x);(r*=-1)<0&&(r+=2*Math.PI),e.angleList.map((function(t){return n(-1*t)})).forEach((function(t,e){var i=n(t-a/2),s=n(t+a/2);s<i&&(s+=2*Math.PI),(r>=i&&r<=s||r+2*Math.PI>=i&&r+2*Math.PI<=s)&&(o=e)}))}return o}({x:i.x,y:i.y},this.opts.chartData.radarData,this.opts.categories.length):"funnel"===this.opts.type?function(t,e){for(var i=-1,a=0,o=e.series.length;a<o;a++){var n=e.series[a];if(t.x>n.funnelArea[0]&&t.x<n.funnelArea[2]&&t.y>n.funnelArea[1]&&t.y<n.funnelArea[3]){i=a;break}}return i}({x:i.x,y:i.y},this.opts.chartData.funnelData):"map"===this.opts.type?function(t,e){for(var i,a,o,n,r,s,l=-1,c=e.chartData.mapData,h=e.series,d=(i=t.y,a=t.x,o=c.bounds,n=c.scale,r=c.xoffset,s=c.yoffset,{x:(a-r)/n+o.xMin,y:o.yMax-(i-s)/n}),p=[d.x,d.y],u=0,g=h.length;u<g;u++)if(mn(p,h[u].geometry.coordinates,e.chartData.mapData.mercator)){l=u;break}return l}({x:i.x,y:i.y},this.opts):"word"===this.opts.type?function(t,e){for(var i=-1,a=0,o=e.length;a<o;a++){var n=e[a];if(t.x>n.area[0]&&t.x<n.area[2]&&t.y>n.area[1]&&t.y<n.area[3]){i=a;break}}return i}({x:i.x,y:i.y},this.opts.chartData.wordCloudData):"bar"===this.opts.type?function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,n={index:-1,group:[]},r=i.chartData.eachSpacing/2;let s=i.chartData.yAxisPoints;return e&&e.length>0&&xo(t,i)&&s.forEach((function(e,i){t.y+o+r>e&&(n.index=i)})),n}({x:i.x,y:i.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset)):function(t,e,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,n={index:-1,group:[]},r=i.chartData.eachSpacing/2;let s=[];if(e&&e.length>0){if(i.categories){for(let t=1;t<i.chartData.xAxisPoints.length;t++)s.push(i.chartData.xAxisPoints[t]-r);"line"!=i.type&&"area"!=i.type||"justify"!=i.xAxis.boundaryGap||(s=i.chartData.xAxisPoints)}else r=0;if(xo(t,i))if(i.categories)s.forEach((function(e,i){t.x+o+r>e&&(n.index=i)}));else{let i=Array(e.length);for(let n=0;n<e.length;n++){i[n]=Array(e[n].length);for(let a=0;a<e[n].length;a++)i[n][a]=Math.abs(e[n][a].x-t.x)}let a=Array(i.length),o=Array(i.length);for(let t=0;t<i.length;t++)a[t]=Math.min.apply(null,i[t]),o[t]=i[t].indexOf(a[t]);let r=Math.min.apply(null,a);n.index=[];for(let t=0;t<a.length;t++)a[t]==r&&(n.group.push(t),n.index.push(o[t]))}}return n}({x:i.x,y:i.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset))}return-1},kn.prototype.getLegendDataIndex=function(t){var e=null;if(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0]){let i=so(e,this.opts,t);return function(t,e,i){let a=-1;if(function(t,e){return t.x>e.start.x&&t.x<e.end.x&&t.y>e.start.y&&t.y<e.end.y}(t,e.area)){let i=e.points,o=-1;for(let e=0,n=i.length;e<n;e++){let n=i[e];for(let e=0;e<n.length;e++){o+=1;let i=n[e].area;if(i&&t.x>i[0]-0&&t.x<i[2]+0&&t.y>i[1]-0&&t.y<i[3]+0){a=o;break}}}return a}return a}({x:i.x,y:i.y},this.opts.chartData.legendData)}return-1},kn.prototype.touchLegend=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=null;if(i=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0]){so(i,this.opts,t);var a=this.getLegendDataIndex(t);a>=0&&("candle"==this.opts.type?this.opts.seriesMA[a].show=!this.opts.seriesMA[a].show:this.opts.series[a].show=!this.opts.series[a].show,this.opts.animation=!!e.animation,this.opts._scrollDistance_=this.scrollOption.currentOffset,Cn.call(this,this.opts.type,this.opts,this.config,this.context))}},kn.prototype.showToolTip=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=null;(i=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0])||console.log("[uCharts] 未获取到event坐标信息");var a=so(i,this.opts,t),o=this.scrollOption.currentOffset,n=Ka({},this.opts,{_scrollDistance_:o,animation:!1});if("line"===this.opts.type||"area"===this.opts.type||"column"===this.opts.type||"scatter"===this.opts.type||"bubble"===this.opts.type){var r=this.getCurrentDataIndex(t);if((p=null==e.index?r.index:e.index)>-1||p.length>0)if(0!==(l=lo(this.opts.series,p,r.group)).length){var s=(d=po(l,this.opts,p,r.group,this.opts.categories,e)).textList;(c=d.offset).y=a.y,n.tooltip={textList:void 0!==e.textList?e.textList:s,offset:void 0!==e.offset?e.offset:c,option:e,index:p,group:r.group}}Cn.call(this,n.type,n,this.config,this.context)}if("mount"===this.opts.type){if((p=null==e.index?this.getCurrentDataIndex(t).index:e.index)>-1){n=Ka({},this.opts,{animation:!1});var l=Ka({},n._series_[p]),c=(s=[{text:e.formatter?e.formatter(l,void 0,p,n):l.name+": "+l.data,color:l.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?l.legendShape:this.opts.extra.tooltip.legendShape}],{x:n.chartData.calPoints[p].x,y:a.y});n.tooltip={textList:e.textList?e.textList:s,offset:void 0!==e.offset?e.offset:c,option:e,index:p}}Cn.call(this,n.type,n,this.config,this.context)}if("bar"===this.opts.type){r=this.getCurrentDataIndex(t);if((p=null==e.index?r.index:e.index)>-1||p.length>0)if(0!==(l=lo(this.opts.series,p,r.group)).length){s=(d=po(l,this.opts,p,r.group,this.opts.categories,e)).textList;(c=d.offset).x=a.x,n.tooltip={textList:void 0!==e.textList?e.textList:s,offset:void 0!==e.offset?e.offset:c,option:e,index:p}}Cn.call(this,n.type,n,this.config,this.context)}if("mix"===this.opts.type){r=this.getCurrentDataIndex(t);if((p=null==e.index?r.index:e.index)>-1){o=this.scrollOption.currentOffset,n=Ka({},this.opts,{_scrollDistance_:o,animation:!1});if(0!==(l=lo(this.opts.series,p)).length){var h=uo(l,this.opts,p,this.opts.categories,e);s=h.textList;(c=h.offset).y=a.y,n.tooltip={textList:e.textList?e.textList:s,offset:void 0!==e.offset?e.offset:c,option:e,index:p}}}Cn.call(this,n.type,n,this.config,this.context)}if("candle"===this.opts.type){r=this.getCurrentDataIndex(t);if((p=null==e.index?r.index:e.index)>-1){o=this.scrollOption.currentOffset,n=Ka({},this.opts,{_scrollDistance_:o,animation:!1});if(0!==(l=lo(this.opts.series,p)).length){var d;s=(d=go(this.opts.series[0].data,l,this.opts,p,this.opts.categories,this.opts.extra.candle)).textList;(c=d.offset).y=a.y,n.tooltip={textList:e.textList?e.textList:s,offset:void 0!==e.offset?e.offset:c,option:e,index:p}}}Cn.call(this,n.type,n,this.config,this.context)}if("pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type||"funnel"===this.opts.type){if((p=null==e.index?this.getCurrentDataIndex(t):e.index)>-1){n=Ka({},this.opts,{animation:!1}),l=Ka({},n._series_[p]),s=[{text:e.formatter?e.formatter(l,void 0,p,n):l.name+": "+l.data,color:l.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?l.legendShape:this.opts.extra.tooltip.legendShape}],c={x:a.x,y:a.y};n.tooltip={textList:e.textList?e.textList:s,offset:void 0!==e.offset?e.offset:c,option:e,index:p}}Cn.call(this,n.type,n,this.config,this.context)}if("map"===this.opts.type){if((p=null==e.index?this.getCurrentDataIndex(t):e.index)>-1){n=Ka({},this.opts,{animation:!1});(l=Ka({},this.opts.series[p])).name=l.properties.name;s=[{text:e.formatter?e.formatter(l,void 0,p,this.opts):l.name,color:l.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?l.legendShape:this.opts.extra.tooltip.legendShape}],c={x:a.x,y:a.y};n.tooltip={textList:e.textList?e.textList:s,offset:void 0!==e.offset?e.offset:c,option:e,index:p}}n.updateData=!1,Cn.call(this,n.type,n,this.config,this.context)}if("word"===this.opts.type){if((p=null==e.index?this.getCurrentDataIndex(t):e.index)>-1){n=Ka({},this.opts,{animation:!1}),l=Ka({},this.opts.series[p]),s=[{text:e.formatter?e.formatter(l,void 0,p,this.opts):l.name,color:l.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?l.legendShape:this.opts.extra.tooltip.legendShape}],c={x:a.x,y:a.y};n.tooltip={textList:e.textList?e.textList:s,offset:void 0!==e.offset?e.offset:c,option:e,index:p}}n.updateData=!1,Cn.call(this,n.type,n,this.config,this.context)}if("radar"===this.opts.type){var p;if((p=null==e.index?this.getCurrentDataIndex(t):e.index)>-1){n=Ka({},this.opts,{animation:!1});if(0!==(l=lo(this.opts.series,p)).length){s=l.map((t=>({text:e.formatter?e.formatter(t,this.opts.categories[p],p,this.opts):t.name+": "+t.data,color:t.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?t.legendShape:this.opts.extra.tooltip.legendShape}))),c={x:a.x,y:a.y};n.tooltip={textList:e.textList?e.textList:s,offset:void 0!==e.offset?e.offset:c,option:e,index:p}}}Cn.call(this,n.type,n,this.config,this.context)}},kn.prototype.translate=function(t){this.scrollOption={currentOffset:t,startTouchX:t,distance:0,lastMoveTime:0};let e=Ka({},this.opts,{_scrollDistance_:t,animation:!1});Cn.call(this,this.opts.type,e,this.config,this.context)},kn.prototype.scrollStart=function(t){var e=null,i=so(e=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0],this.opts,t);e&&!0===this.opts.enableScroll&&(this.scrollOption.startTouchX=i.x)},kn.prototype.scroll=function(t){0===this.scrollOption.lastMoveTime&&(this.scrollOption.lastMoveTime=Date.now());let e=this.opts.touchMoveLimit||60,i=Date.now();if(!(i-this.scrollOption.lastMoveTime<Math.floor(1e3/e))&&0!=this.scrollOption.startTouchX){this.scrollOption.lastMoveTime=i;var a=null;if((a=t.changedTouches?t.changedTouches[0]:t.mp.changedTouches[0])&&!0===this.opts.enableScroll){var o;o=so(a,this.opts,t).x-this.scrollOption.startTouchX;var n=this.scrollOption.currentOffset,r=Ya(this,n+o,this.opts.chartData,this.config,this.opts);this.scrollOption.distance=o=r-n;var s=Ka({},this.opts,{_scrollDistance_:n+o,animation:!1});return this.opts=s,Cn.call(this,s.type,s,this.config,this.context),n+o}}},kn.prototype.scrollEnd=function(t){if(!0===this.opts.enableScroll){var e=this.scrollOption,i=e.currentOffset,a=e.distance;this.scrollOption.currentOffset=i+a,this.scrollOption.distance=0,this.scrollOption.moveCount=0}};const Dn=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],In={type:["pie","ring","rose","word","funnel","map","arcbar","line","column","mount","bar","area","radar","gauge","candle","mix","tline","tarea","scatter","bubble","demotype"],range:["饼状图","圆环图","玫瑰图","词云图","漏斗图","地图","圆弧进度条","折线图","柱状图","山峰图","条状图","区域图","雷达图","仪表盘","K线图","混合图","时间轴折线","时间轴区域","散点图","气泡图","自定义类型"],categories:["line","column","mount","bar","area","radar","gauge","candle","mix","demotype"],instance:{},option:{},formatter:{yAxisDemo1:function(t,e,i){return t+"元"},yAxisDemo2:function(t,e,i){return t.toFixed(2)},xAxisDemo1:function(t,e,i){return t+"年"},xAxisDemo2:function(t,e,i){return((t,e)=>{var i=new Date;i.setTime(1e3*t);var a=i.getFullYear(),o=i.getMonth()+1;o=o<10?"0"+o:o;var n=i.getDate();n=n<10?"0"+n:n;var r=i.getHours();r=r<10?"0"+r:r;var s=i.getMinutes(),l=i.getSeconds();return s=s<10?"0"+s:s,l=l<10?"0"+l:l,"full"==e?a+"-"+o+"-"+n+" "+r+":"+s+":"+l:"y-m-d"==e?a+"-"+o+"-"+n:"h:m"==e?r+":"+s:"h:m:s"==e?r+":"+s+":"+l:[a,o,n,r,s,l]})(t,"h:m")},seriesDemo1:function(t,e,i,a){return t+"元"},tooltipDemo1:function(t,e,i,a){return 0==i?"随便用"+t.data+"年":"其他我没改"+t.data+"天"},pieDemo:function(t,e,i,a){if(void 0!==e)return i[e].name+"："+i[e].data+"元"}},demotype:{type:"line",color:Dn,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"curve",width:2}}},pie:{type:"pie",color:Dn,padding:[5,5,5,5],extra:{pie:{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},ring:{type:"ring",color:Dn,padding:[5,5,5,5],rotate:!1,dataLabel:!0,legend:{show:!0,position:"right",lineHeight:25},title:{name:"收益率",fontSize:15,color:"#666666"},subtitle:{name:"70%",fontSize:25,color:"#7cb5ec"},extra:{ring:{ringWidth:30,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},rose:{type:"rose",color:Dn,padding:[5,5,5,5],legend:{show:!0,position:"left",lineHeight:25},extra:{rose:{type:"area",minRadius:50,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF"}}},word:{type:"word",color:Dn,extra:{word:{type:"normal",autoColors:!1}}},funnel:{type:"funnel",color:Dn,padding:[15,15,0,15],extra:{funnel:{activeOpacity:.3,activeWidth:10,border:!0,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,labelAlign:"right"}}},map:{type:"map",color:Dn,padding:[0,0,0,0],dataLabel:!0,extra:{map:{border:!0,borderWidth:1,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#F04864",activeFillColor:"#FACC14",activeFillOpacity:1}}},arcbar:{type:"arcbar",color:Dn,title:{name:"百分比",fontSize:25,color:"#00FF00"},subtitle:{name:"默认标题",fontSize:15,color:"#666666"},extra:{arcbar:{type:"default",width:12,backgroundColor:"#E9E9E9",startAngle:.75,endAngle:.25,gap:2}}},line:{type:"line",color:Dn,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"straight",width:2,activeType:"hollow"}}},tline:{type:"line",color:Dn,padding:[15,10,0,15],xAxis:{disableGrid:!1,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{line:{type:"curve",width:2,activeType:"hollow"}}},tarea:{type:"area",color:Dn,padding:[15,10,0,15],xAxis:{disableGrid:!0,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{area:{type:"curve",opacity:.2,addLine:!0,width:2,gradient:!0,activeType:"hollow"}}},column:{type:"column",color:Dn,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{column:{type:"group",width:30,activeBgColor:"#000000",activeBgOpacity:.08}}},mount:{type:"mount",color:Dn,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{mount:{type:"mount",widthRatio:1.5}}},bar:{type:"bar",color:Dn,padding:[15,30,0,5],xAxis:{boundaryGap:"justify",disableGrid:!1,min:0,axisLine:!1},yAxis:{},legend:{},extra:{bar:{type:"group",width:30,meterBorde:1,meterFillColor:"#FFFFFF",activeBgColor:"#000000",activeBgOpacity:.08}}},area:{type:"area",color:Dn,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{area:{type:"straight",opacity:.2,addLine:!0,width:2,gradient:!1,activeType:"hollow"}}},radar:{type:"radar",color:Dn,padding:[5,5,5,5],dataLabel:!1,legend:{show:!0,position:"right",lineHeight:25},extra:{radar:{gridType:"radar",gridColor:"#CCCCCC",gridCount:3,opacity:.2,max:200,labelShow:!0}}},gauge:{type:"gauge",color:Dn,title:{name:"66Km/H",fontSize:25,color:"#2fc25b",offsetY:50},subtitle:{name:"实时速度",fontSize:15,color:"#1890ff",offsetY:-50},extra:{gauge:{type:"default",width:30,labelColor:"#666666",startAngle:.75,endAngle:.25,startNumber:0,endNumber:100,labelFormat:"",splitLine:{fixRadius:0,splitNumber:10,width:30,color:"#FFFFFF",childNumber:5,childWidth:12},pointer:{width:24,color:"auto"}}}},candle:{type:"candle",color:Dn,padding:[15,15,0,15],enableScroll:!0,enableMarkLine:!0,dataLabel:!1,xAxis:{labelCount:4,itemCount:40,disableGrid:!0,gridColor:"#CCCCCC",gridType:"solid",dashLength:4,scrollShow:!0,scrollAlign:"left",scrollColor:"#A6A6A6",scrollBackgroundColor:"#EFEBEF"},yAxis:{},legend:{},extra:{candle:{color:{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},average:{show:!0,name:["MA5","MA10","MA30"],day:[5,10,20],color:["#1890ff","#2fc25b","#facc14"]}},markLine:{type:"dash",dashLength:5,data:[{value:2150,lineColor:"#f04864",showLabel:!0},{value:2350,lineColor:"#f04864",showLabel:!0}]}}},mix:{type:"mix",color:Dn,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{disabled:!1,disableGrid:!1,splitNumber:5,gridType:"dash",dashLength:4,gridColor:"#CCCCCC",padding:10,showTitle:!0,data:[]},legend:{},extra:{mix:{column:{width:20}}}},scatter:{type:"scatter",color:Dn,padding:[15,15,0,15],dataLabel:!1,xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0},yAxis:{disableGrid:!1,gridType:"dash"},legend:{},extra:{scatter:{}}},bubble:{type:"bubble",color:Dn,padding:[15,15,0,15],xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0,max:250},yAxis:{disableGrid:!1,gridType:"dash",data:[{min:0,max:150}]},legend:{},extra:{bubble:{border:2,opacity:.5}}}},Ln=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],Mn={type:["pie","ring","rose","funnel","line","column","area","radar","gauge","candle","demotype"],categories:["line","column","area","radar","gauge","candle","demotype"],instance:{},option:{},formatter:{tooltipDemo1:function(t){let e="";for(let i in t){0==i&&(e+=t[i].axisValueLabel+"年销售额");let a="--";null!==t[i].data&&(a=t[i].data),e+="\n"+t[i].seriesName+"："+a+" 万元"}return e},legendFormat:function(t){return"自定义图例+"+t},yAxisFormatDemo:function(t,e){return t+"元"},seriesFormatDemo:function(t){return t.name+"年"+t.value+"元"}},demotype:{color:Ln},column:{color:Ln,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"bar",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},line:{color:Ln,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},area:{color:Ln,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],areaStyle:{},label:{show:!0,color:"#666666",position:"top"}}},pie:{color:Ln,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:"50%",label:{show:!0,color:"#666666",position:"top"}}},ring:{color:Ln,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!0,color:"#666666",position:"top"},labelLine:{show:!0}}},rose:{color:Ln,title:{text:""},tooltip:{trigger:"item"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"pie",data:[],radius:"55%",center:["50%","50%"],roseType:"area"}},funnel:{color:Ln,title:{text:""},tooltip:{trigger:"item",formatter:"{b} : {c}%"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"funnel",left:"10%",top:60,bottom:60,width:"80%",min:0,max:100,minSize:"0%",maxSize:"100%",sort:"descending",gap:2,label:{show:!0,position:"inside"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{bordercolor:"#fff",borderwidth:1},emphasis:{label:{fontSize:20}},data:[]}},gauge:{color:Ln,tooltip:{formatter:"{a} <br/>{b} : {c}%"},seriesTemplate:{name:"业务指标",type:"gauge",detail:{formatter:"{value}%"},data:[{value:50,name:"完成率"}]}},candle:{xAxis:{data:[]},yAxis:{},color:Ln,title:{text:""},dataZoom:[{type:"inside",xAxisIndex:[0,1],start:10,end:100},{show:!0,xAxisIndex:[0,1],type:"slider",bottom:10,start:10,end:100}],seriesTemplate:{name:"",type:"k",data:[]}}};var Fn={},On=null;function En(t={},...e){for(let i in e)for(let a in e[i])e[i].hasOwnProperty(a)&&(t[a]=e[i][a]&&"object"==typeof e[i][a]?En(Array.isArray(e[i][a])?[]:{},t[a],e[i][a]):e[i][a]);return t}function Rn(t,e){for(let i in t)t.hasOwnProperty(i)&&null!==t[i]&&"object"==typeof t[i]?Rn(t[i],e):"format"===i&&"string"==typeof t[i]&&(t.formatter=e[t[i]]?e[t[i]]:void 0);return t}const Nn={data:()=>({rid:null}),mounted(){On={top:0,left:0};let t=document.querySelectorAll("uni-main")[0];void 0===t&&(t=document.querySelectorAll("uni-page-wrapper")[0]),On={top:t.offsetTop,left:t.offsetLeft},setTimeout((()=>{null===this.rid&&this.$ownerInstance&&this.$ownerInstance.callMethod("getRenderType")}),200)},destroyed(){delete In.option[this.rid],delete In.instance[this.rid],delete Mn.option[this.rid],delete Mn.instance[this.rid]},methods:{ecinit(t,e,i,a){let o=JSON.stringify(t.id);this.rid=o,Fn[o]=this.$ownerInstance||a;let n=JSON.parse(JSON.stringify(t)),r=n.type;r&&Mn.type.includes(r)?Mn.option[o]=En({},Mn[r],n):Mn.option[o]=En({},n);let s=n.chartData;if(s){Mn.option[o].xAxis&&Mn.option[o].xAxis.type&&"category"===Mn.option[o].xAxis.type&&(Mn.option[o].xAxis.data=s.categories),Mn.option[o].yAxis&&Mn.option[o].yAxis.type&&"category"===Mn.option[o].yAxis.type&&(Mn.option[o].yAxis.data=s.categories),Mn.option[o].series=[];for(var l=0;l<s.series.length;l++){Mn.option[o].seriesTemplate=Mn.option[o].seriesTemplate?Mn.option[o].seriesTemplate:{};let t=En({},Mn.option[o].seriesTemplate,s.series[l]);Mn.option[o].series.push(t)}}if("object"==typeof window.echarts)this.newEChart();else{const t=document.createElement("script"),e=window.location.origin,i=a.getDataset().directory;t.src=e+i+"uni_modules/qiun-data-charts/static/h5/echarts.min.js",t.onload=this.newEChart,document.head.appendChild(t)}},ecresize(t,e,i,a){Mn.instance[this.rid]&&Mn.instance[this.rid].resize()},newEChart(){let t=this.rid;void 0===Mn.instance[t]?(Mn.instance[t]=echarts.init(Fn[t].$el.children[0]),!0===Mn.option[t].ontap&&(Mn.instance[t].on("click",(e=>{let i=JSON.parse(JSON.stringify({x:e.event.offsetX,y:e.event.offsetY}));Fn[t].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:i,currentIndex:e.dataIndex,value:e.data,seriesName:e.seriesName,id:t}})})),Mn.instance[t].on("highlight",(e=>{Fn[t].callMethod("emitMsg",{name:"getHighlight",params:{type:"highlight",res:e,id:t}})}))),this.updataEChart(t,Mn.option[t])):this.updataEChart(t,Mn.option[t])},updataEChart(t,e){if((e=Rn(e,Mn.formatter)).tooltip&&(e.tooltip.show=!!e.tooltipShow,e.tooltip.position=this.tooltipPosition(),"string"==typeof e.tooltipFormat&&Mn.formatter[e.tooltipFormat]&&(e.tooltip.formatter=e.tooltip.formatter?e.tooltip.formatter:Mn.formatter[e.tooltipFormat])),e.series)for(let i in e.series){let t=e.series[i].linearGradient;t&&(e.series[i].color=new echarts.graphic.LinearGradient(t[0],t[1],t[2],t[3],t[4]))}Mn.instance[t].setOption(e,e.notMerge),Mn.instance[t].on("finished",(function(){Fn[t].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:t}}),Mn.instance[t]&&Mn.instance[t].off("finished")})),void 0!==Fn[t].$el.children[0].clientWidth&&(Math.abs(Fn[t].$el.children[0].clientWidth-Mn.instance[t].getWidth())>3||Math.abs(Fn[t].$el.children[0].clientHeight-Mn.instance[t].getHeight())>3)&&this.ecresize()},tooltipPosition:()=>(t,e,i,a,o)=>{let n=t[0],r=t[1],s=o.viewSize[0],l=o.viewSize[1],c=o.contentSize[0],h=o.contentSize[1],d=n+30,p=r+30;return d+c>s&&(d=n-c-30),p+h>l&&(p=r-h-30),[d,p]},ucinit(t,e,i,a){if(JSON.stringify(t)==JSON.stringify(e))return;if(!t.canvasId)return;let o=JSON.parse(JSON.stringify(t.canvasId));this.rid=o,Fn[o]=this.$ownerInstance||a,In.option[o]=JSON.parse(JSON.stringify(t)),In.option[o]=Rn(In.option[o],In.formatter);let n=document.getElementById(o);n&&n.children[0]&&(In.option[o].context=n.children[0].getContext("2d"),In.instance[o]&&In.option[o]&&!0===In.option[o].update?this.updataUChart():setTimeout((()=>{In.option[o].context.restore(),In.option[o].context.save(),this.newUChart()}),100))},newUChart(){let t=this.rid;In.instance[t]=new kn(In.option[t]),In.instance[t].addEventListener("renderComplete",(()=>{Fn[t].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:t,opts:In.instance[t].opts}}),In.instance[t].delEventListener("renderComplete")})),In.instance[t].addEventListener("scrollLeft",(()=>{Fn[t].callMethod("emitMsg",{name:"scrollLeft",params:{type:"scrollLeft",scrollLeft:!0,id:t,opts:In.instance[t].opts}})})),In.instance[t].addEventListener("scrollRight",(()=>{Fn[t].callMethod("emitMsg",{name:"scrollRight",params:{type:"scrollRight",scrollRight:!0,id:t,opts:In.instance[t].opts}})}))},updataUChart(){let t=this.rid;In.instance[t].updateData(In.option[t])},tooltipDefault(t,e,i,a){if(e){let i=t.data;return"object"==typeof t.data&&(i=t.data.value),e+" "+t.name+":"+i}return t.properties&&t.properties.name?t.properties.name:t.name+":"+t.data},showTooltip(t,e){let i=In.option[e].tooltipCustom;if(i&&null!=i){let a;i.x>=0&&i.y>=0&&(a={x:i.x,y:i.y+10}),In.instance[e].showToolTip(t,{index:i.index,offset:a,textList:i.textList,formatter:(t,i,a,o)=>"string"==typeof In.option[e].tooltipFormat&&In.formatter[In.option[e].tooltipFormat]?In.formatter[In.option[e].tooltipFormat](t,i,a,o):this.tooltipDefault(t,i,a,o)})}else In.instance[e].showToolTip(t,{formatter:(t,i,a,o)=>"string"==typeof In.option[e].tooltipFormat&&In.formatter[In.option[e].tooltipFormat]?In.formatter[In.option[e].tooltipFormat](t,i,a,o):this.tooltipDefault(t,i,a,o)})},tap(t){let e=this.rid,i=In.option[e].ontap,a=In.option[e].tooltipShow,o=In.option[e].tapLegend;if(0==i)return;let n=null,r=null,s=document.getElementById("UC"+e).getBoundingClientRect(),l={};l=t.detail.x?{x:t.detail.x-s.left,y:t.detail.y-s.top+On.top}:{x:t.clientX-s.left,y:t.clientY-s.top+On.top},t.changedTouches=[],t.changedTouches.unshift(l),n=In.instance[e].getCurrentDataIndex(t),r=In.instance[e].getLegendDataIndex(t),!0===o&&In.instance[e].touchLegend(t),1==a&&this.showTooltip(t,e),Fn[e].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:l,currentIndex:n,legendIndex:r,id:e,opts:In.instance[e].opts}})},touchStart(t){let e=this.rid;0!=In.option[e].ontouch&&(!0===In.option[e].enableScroll&&1==t.touches.length&&In.instance[e].scrollStart(t),Fn[e].callMethod("emitMsg",{name:"getTouchStart",params:{type:"touchStart",event:t.changedTouches[0],id:e,opts:In.instance[e].opts}}))},touchMove(t){let e=this.rid,i=In.option[e].ontouch;if(0!=i){if(!0===In.option[e].enableScroll&&1==t.changedTouches.length&&In.instance[e].scroll(t),!0===In.option[e].ontap&&!1===In.option[e].enableScroll&&!0===In.option[e].onmovetip){let i=document.getElementById("UC"+e).getBoundingClientRect(),a={x:t.changedTouches[0].clientX-i.left,y:t.changedTouches[0].clientY-i.top+On.top};t.changedTouches.unshift(a),!0===In.option[e].tooltipShow&&this.showTooltip(t,e)}!0===i&&!0===In.option[e].enableScroll&&!0===In.option[e].onzoom&&2==t.changedTouches.length&&In.instance[e].dobuleZoom(t),Fn[e].callMethod("emitMsg",{name:"getTouchMove",params:{type:"touchMove",event:t.changedTouches[0],id:e,opts:In.instance[e].opts}})}},touchEnd(t){let e=this.rid;0!=In.option[e].ontouch&&(!0===In.option[e].enableScroll&&0==t.touches.length&&In.instance[e].scrollEnd(t),Fn[e].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"touchEnd",event:t.changedTouches[0],id:e,opts:In.instance[e].opts}}))},mouseDown(t){let e=this.rid;if(0==In.option[e].onmouse)return;let i=document.getElementById("UC"+e).getBoundingClientRect(),a={};a={x:t.clientX-i.left,y:t.clientY-i.top+On.top},t.changedTouches=[],t.changedTouches.unshift(a),In.instance[e].scrollStart(t),In.option[e].mousedown=!0,Fn[e].callMethod("emitMsg",{name:"getTouchStart",params:{type:"mouseDown",event:a,id:e,opts:In.instance[e].opts}})},mouseMove(t){let e=this.rid,i=In.option[e].onmouse,a=In.option[e].tooltipShow;if(0==i)return;let o=document.getElementById("UC"+e).getBoundingClientRect(),n={};n={x:t.clientX-o.left,y:t.clientY-o.top+On.top},t.changedTouches=[],t.changedTouches.unshift(n),In.option[e].mousedown?(In.instance[e].scroll(t),Fn[e].callMethod("emitMsg",{name:"getTouchMove",params:{type:"mouseMove",event:n,id:e,opts:In.instance[e].opts}})):In.instance[e]&&1==a&&this.showTooltip(t,e)},mouseUp(t){let e=this.rid;if(0==In.option[e].onmouse)return;let i=document.getElementById("UC"+e).getBoundingClientRect(),a={};a={x:t.clientX-i.left,y:t.clientY-i.top+On.top},t.changedTouches=[],t.changedTouches.unshift(a),In.instance[e].scrollEnd(t),In.option[e].mousedown=!1,Fn[e].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"mouseUp",event:a,id:e,opts:In.instance[e].opts}})}}},Bn=t=>{t.$renderjs||(t.$renderjs=[]),t.$renderjs.push("rdcharts"),t.mixins||(t.mixins=[]),t.mixins.push({beforeCreate(){this.rdcharts=this},mounted(){this.$ownerInstance=this.$gcd(this,!0)}}),t.mixins.push(Nn)};function zn(t={},...e){for(let i in e)for(let a in e[i])e[i].hasOwnProperty(a)&&(t[a]=e[i][a]&&"object"==typeof e[i][a]?zn(Array.isArray(e[i][a])?[]:{},t[a],e[i][a]):e[i][a]);return t}function Un(t,e){for(let i in t)t.hasOwnProperty(i)&&null!==t[i]&&"object"==typeof t[i]?Un(t[i],e):"format"===i&&"string"==typeof t[i]&&(t.formatter=e[t[i]]?e[t[i]]:void 0);return t}const Wn={name:"qiun-data-charts",mixins:[ja.mixinDatacom],props:{type:{type:String,default:null},canvasId:{type:String,default:"uchartsid"},canvas2d:{type:Boolean,default:!1},background:{type:String,default:"rgba(0,0,0,0)"},animation:{type:Boolean,default:!0},chartData:{type:Object,default:()=>({categories:[],series:[]})},opts:{type:Object,default:()=>({})},eopts:{type:Object,default:()=>({})},loadingType:{type:Number,default:2},errorShow:{type:Boolean,default:!0},errorReload:{type:Boolean,default:!0},errorMessage:{type:String,default:null},inScrollView:{type:Boolean,default:!1},reshow:{type:Boolean,default:!1},reload:{type:Boolean,default:!1},disableScroll:{type:Boolean,default:!1},optsWatch:{type:Boolean,default:!0},onzoom:{type:Boolean,default:!1},ontap:{type:Boolean,default:!0},ontouch:{type:Boolean,default:!1},onmouse:{type:Boolean,default:!0},onmovetip:{type:Boolean,default:!1},echartsH5:{type:Boolean,default:!1},echartsApp:{type:Boolean,default:!1},tooltipShow:{type:Boolean,default:!0},tooltipFormat:{type:String,default:void 0},tooltipCustom:{type:Object,default:void 0},startDate:{type:String,default:void 0},endDate:{type:String,default:void 0},textEnum:{type:Array,default:()=>[]},groupEnum:{type:Array,default:()=>[]},pageScrollTop:{type:Number,default:0},directory:{type:String,default:"/"},tapLegend:{type:Boolean,default:!0},menus:{type:Array,default:()=>[]}},data:()=>({cid:"uchartsid",inWx:!1,inAli:!1,inTt:!1,inBd:!1,inH5:!1,inApp:!1,inWin:!1,type2d:!0,disScroll:!1,openmouse:!1,pixel:1,cWidth:375,cHeight:250,showchart:!1,echarts:!1,echartsResize:{state:!1},uchartsOpts:{},echartsOpts:{},drawData:{},lastDrawTime:null}),created(){if(this.cid=this.canvasId,"uchartsid"==this.canvasId||""==this.canvasId){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",e=t.length,i="";for(let a=0;a<32;a++)i+=t.charAt(Math.floor(Math.random()*e));this.cid=i}const t=y();"windows"!==t.platform&&"mac"!==t.platform||(this.inWin=!0),this.type2d=!1,this.disScroll=this.disableScroll},mounted(){this.inH5=!0,!0===this.inWin&&(this.openmouse=this.onmouse),!0===this.echartsH5&&(this.echarts=!0),this.$nextTick((()=>{this.beforeInit()}));const t=this.inH5?500:200,e=this;R(function(t,e){let i=!1;return function(){clearTimeout(i),i&&clearTimeout(i),i=setTimeout((()=>{i=!1,t.apply(this,arguments)}),e)}}((function(t){if(1==e.mixinDatacomLoading)return;let i=e.mixinDatacomErrorMessage;null!==i&&"null"!==i&&""!==i||(e.echarts?e.echartsResize.state=!e.echartsResize.state:e.resizeHandler())}),t))},destroyed(){!0===this.echarts?(delete Mn.option[this.cid],delete Mn.instance[this.cid]):(delete In.option[this.cid],delete In.instance[this.cid]),N((()=>{}))},watch:{chartDataProps:{handler(t,e){"object"==typeof t?JSON.stringify(t)!==JSON.stringify(e)&&(this._clearChart(),t.series&&t.series.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this.showchart=!1,this.mixinDatacomErrorMessage=null)):(this.mixinDatacomLoading=!1,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：chartData数据类型错误")},immediate:!1,deep:!0},localdata:{handler(t,e){JSON.stringify(t)!==JSON.stringify(e)&&(t.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage=null))},immediate:!1,deep:!0},optsProps:{handler(t,e){"object"==typeof t?JSON.stringify(t)!==JSON.stringify(e)&&!1===this.echarts&&1==this.optsWatch&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：opts数据类型错误")},immediate:!1,deep:!0},eoptsProps:{handler(t,e){"object"==typeof t?JSON.stringify(t)!==JSON.stringify(e)&&!0===this.echarts&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：eopts数据类型错误")},immediate:!1,deep:!0},reshow(t,e){!0===t&&!1===this.mixinDatacomLoading&&setTimeout((()=>{this.mixinDatacomErrorMessage=null,this.echartsResize.state=!this.echartsResize.state,this.checkData(this.drawData)}),200)},reload(t,e){!0===t&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())},mixinDatacomErrorMessage(t,e){t&&(this.emitMsg({name:"error",params:{type:"error",errorShow:this.errorShow,msg:t,id:this.cid}}),this.errorShow&&console.log("[秋云图表组件]"+t))},errorMessage(t,e){t&&this.errorShow&&null!==t&&"null"!==t&&""!==t?(this.showchart=!1,this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=t):(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())}},computed:{optsProps(){return JSON.parse(JSON.stringify(this.opts))},eoptsProps(){return JSON.parse(JSON.stringify(this.eopts))},chartDataProps(){return JSON.parse(JSON.stringify(this.chartData))}},methods:{beforeInit(){this.mixinDatacomErrorMessage=null,"object"==typeof this.chartData&&null!=this.chartData&&void 0!==this.chartData.series&&this.chartData.series.length>0?(this.drawData=zn({},this.chartData),this.mixinDatacomLoading=!1,this.showchart=!0,this.checkData(this.chartData)):this.localdata.length>0?(this.mixinDatacomLoading=!1,this.showchart=!0,this.localdataInit(this.localdata)):""!==this.collection?(this.mixinDatacomLoading=!1,this.getCloudData()):this.mixinDatacomLoading=!0},localdataInit(t){if(this.groupEnum.length>0)for(let h=0;h<t.length;h++)for(let e=0;e<this.groupEnum.length;e++)t[h].group===this.groupEnum[e].value&&(t[h].group=this.groupEnum[e].text);if(this.textEnum.length>0)for(let h=0;h<t.length;h++)for(let e=0;e<this.textEnum.length;e++)t[h].text===this.textEnum[e].value&&(t[h].text=this.textEnum[e].text);let e=!1,i={categories:[],series:[]},a=[],o=[];if(e=!0===this.echarts?Mn.categories.includes(this.type):In.categories.includes(this.type),!0===e){if(this.chartData&&this.chartData.categories&&this.chartData.categories.length>0)a=this.chartData.categories;else if(this.startDate&&this.endDate){let t=new Date(this.startDate),e=new Date(this.endDate);for(;t<=e;)a.push((r=void 0,s=void 0,l=void 0,r=(n=t).getFullYear(),s=n.getMonth()+1,l=n.getDate(),s>=1&&s<=9&&(s="0"+s),l>=0&&l<=9&&(l="0"+l),r+"-"+s+"-"+l)),t=t.setDate(t.getDate()+1),t=new Date(t)}else{let e={};t.map((function(t,i){null==t.text||e[t.text]||(a.push(t.text),e[t.text]=!0)}))}i.categories=a}var n,r,s,l;let c={};if(t.map((function(t,e){null==t.group||c[t.group]||(o.push({name:t.group,data:[]}),c[t.group]=!0)})),0==o.length)if(o=[{name:"默认分组",data:[]}],!0===e)for(let h=0;h<a.length;h++){let e=0;for(let i=0;i<t.length;i++)t[i].text==a[h]&&(e=t[i].value);o[0].data.push(e)}else for(let h=0;h<t.length;h++)o[0].data.push({name:t[h].text,value:t[h].value});else for(let h=0;h<o.length;h++)if(a.length>0)for(let e=0;e<a.length;e++){let i=0;for(let n=0;n<t.length;n++)o[h].name==t[n].group&&t[n].text==a[e]&&(i=t[n].value);o[h].data.push(i)}else for(let e=0;e<t.length;e++)o[h].name==t[e].group&&o[h].data.push(t[e].value);i.series=o,this.drawData=zn({},i),this.checkData(i)},reloading(){!1!==this.errorReload&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,""!==this.collection?(this.mixinDatacomLoading=!1,this.onMixinDatacomPropsChange(!0)):this.beforeInit())},checkData(t){let e=this.cid;!0===this.echarts?(Mn.option[e]=zn({},this.eopts),Mn.option[e].id=e,Mn.option[e].type=this.type):this.type&&In.type.includes(this.type)?(In.option[e]=zn({},In[this.type],this.opts),In.option[e].canvasId=e):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：props参数中type类型不正确");let i=zn({},t);void 0!==i.series&&i.series.length>0&&(this.mixinDatacomErrorMessage=null,!0===this.echarts?(Mn.option[e].chartData=i,this.$nextTick((()=>{this.init()}))):(In.option[e].categories=i.categories,In.option[e].series=i.series,this.$nextTick((()=>{this.init()}))))},resizeHandler(){let t=Date.now();t-(this.lastDrawTime?this.lastDrawTime:t-3e3)<1e3||B().in(this).select("#ChartBoxId"+this.cid).boundingClientRect((t=>{this.showchart=!0,t.width>0&&t.height>0&&(t.width===this.cWidth&&t.height===this.cHeight||this.checkData(this.drawData))})).exec()},getCloudData(){1!=this.mixinDatacomLoading&&(this.mixinDatacomLoading=!0,this.mixinDatacomGet().then((t=>{this.mixinDatacomResData=t.result.data,this.localdataInit(this.mixinDatacomResData)})).catch((t=>{this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="请求错误："+t})))},onMixinDatacomPropsChange(t,e){1==t&&""!==this.collection&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this._clearChart(),this.getCloudData())},_clearChart(){let t=this.cid;if(!0!==this.echarts&&In.option[t]&&In.option[t].context){const e=In.option[t].context;"object"!=typeof e||In.option[t].update||(e.clearRect(0,0,this.cWidth*this.pixel,this.cHeight*this.pixel),e.draw())}},init(){let t=this.cid;B().in(this).select("#ChartBoxId"+t).boundingClientRect((e=>{e.width>0&&e.height>0?(this.mixinDatacomLoading=!1,this.showchart=!0,this.lastDrawTime=Date.now(),this.cWidth=e.width,this.cHeight=e.height,!0!==this.echarts&&(In.option[t].background="rgba(0,0,0,0)"==this.background?"#FFFFFF":this.background,In.option[t].canvas2d=this.type2d,In.option[t].pixelRatio=this.pixel,In.option[t].animation=this.animation,In.option[t].width=e.width*this.pixel,In.option[t].height=e.height*this.pixel,In.option[t].onzoom=this.onzoom,In.option[t].ontap=this.ontap,In.option[t].ontouch=this.ontouch,In.option[t].onmouse=this.openmouse,In.option[t].onmovetip=this.onmovetip,In.option[t].tooltipShow=this.tooltipShow,In.option[t].tooltipFormat=this.tooltipFormat,In.option[t].tooltipCustom=this.tooltipCustom,In.option[t].inScrollView=this.inScrollView,In.option[t].lastDrawTime=this.lastDrawTime,In.option[t].tapLegend=this.tapLegend),this.inH5||this.inApp?1==this.echarts?(Mn.option[t].ontap=this.ontap,Mn.option[t].onmouse=this.openmouse,Mn.option[t].tooltipShow=this.tooltipShow,Mn.option[t].tooltipFormat=this.tooltipFormat,Mn.option[t].tooltipCustom=this.tooltipCustom,Mn.option[t].lastDrawTime=this.lastDrawTime,this.echartsOpts=zn({},Mn.option[t])):(In.option[t].rotateLock=In.option[t].rotate,this.uchartsOpts=zn({},In.option[t])):(In.option[t]=Un(In.option[t],In.formatter),this.mixinDatacomErrorMessage=null,this.mixinDatacomLoading=!1,this.showchart=!0,this.$nextTick((()=>{if(!0===this.type2d){B().in(this).select("#"+t).fields({node:!0,size:!0}).exec((i=>{if(i[0]){const a=i[0].node,o=a.getContext("2d");In.option[t].context=o,In.option[t].rotateLock=In.option[t].rotate,In.instance[t]&&In.option[t]&&!0===In.option[t].update?this._updataUChart(t):(a.width=e.width*this.pixel,a.height=e.height*this.pixel,a._width=e.width*this.pixel,a._height=e.height*this.pixel,setTimeout((()=>{In.option[t].context.restore(),In.option[t].context.save(),this._newChart(t)}),100))}else this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：开启2d模式后，未获取到dom节点，canvas-id:"+t}))}else this.inAli&&(In.option[t].rotateLock=In.option[t].rotate),In.option[t].context=z(t,this),In.instance[t]&&In.option[t]&&!0===In.option[t].update?this._updataUChart(t):setTimeout((()=>{In.option[t].context.restore(),In.option[t].context.save(),this._newChart(t)}),100)})))):(this.mixinDatacomLoading=!1,this.showchart=!1,1==this.reshow&&(this.mixinDatacomErrorMessage="布局错误：未获取到父元素宽高尺寸！canvas-id:"+t))})).exec()},saveImage(){U({canvasId:this.cid,success:t=>{var e=document.createElement("a");e.href=t.tempFilePath,e.download=this.cid,e.target="_blank",e.click()}},this)},getImage(){if(0==this.type2d)U({canvasId:this.cid,success:t=>{this.emitMsg({name:"getImage",params:{type:"getImage",base64:t.tempFilePath}})}},this);else{B().in(this).select("#"+this.cid).fields({node:!0,size:!0}).exec((t=>{if(t[0]){const e=t[0].node;this.emitMsg({name:"getImage",params:{type:"getImage",base64:e.toDataURL("image/png")}})}}))}},_error(t){this.mixinDatacomErrorMessage=t.detail.errMsg},emitMsg(t){this.$emit(t.name,t.params)},getRenderType(){!0===this.echarts&&!1===this.mixinDatacomLoading&&this.beforeInit()},toJSON(){return this}}};Bn(Wn);const qn=et(Wn,[["render",function(t,e,i,a,o,n){const r=tt(W("qiun-loading"),$a),s=L,l=tt(W("qiun-error"),Ha),c=H;return P(),k(s,{class:"chartsview",id:"ChartBoxId"+o.cid},{default:D((()=>[t.mixinDatacomLoading?(P(),k(s,{key:0},{default:D((()=>[I(r,{loadingType:i.loadingType},null,8,["loadingType"])])),_:1})):F("",!0),t.mixinDatacomErrorMessage&&i.errorShow?(P(),k(s,{key:1,onClick:n.reloading},{default:D((()=>[I(l,{errorMessage:i.errorMessage},null,8,["errorMessage"])])),_:1},8,["onClick"])):F("",!0),o.echarts?q((P(),k(s,{key:2,style:j([{background:i.background},{width:"100%",height:"100%"}]),"data-directory":i.directory,id:"EC"+o.cid,prop:o.echartsOpts,"change:prop":t.rdcharts.ecinit,resize:o.echartsResize,"change:resize":t.rdcharts.ecresize},null,8,["style","data-directory","id","prop","change:prop","resize","change:resize"])),[[$,o.showchart]]):(P(),k(s,{key:3,onClick:t.rdcharts.tap,onMousemove:t.rdcharts.mouseMove,onMousedown:t.rdcharts.mouseDown,onMouseup:t.rdcharts.mouseUp,onTouchstart:t.rdcharts.touchStart,onTouchmove:t.rdcharts.touchMove,onTouchend:t.rdcharts.touchEnd,id:"UC"+o.cid,prop:o.uchartsOpts,"change:prop":t.rdcharts.ucinit},{default:D((()=>[q(I(c,{id:o.cid,canvasId:o.cid,style:j({width:o.cWidth+"px",height:o.cHeight+"px",background:i.background}),"disable-scroll":i.disableScroll,onError:n._error},null,8,["id","canvasId","style","disable-scroll","onError"]),[[$,o.showchart]])])),_:1},8,["onClick","onMousemove","onMousedown","onMouseup","onTouchstart","onTouchmove","onTouchend","id","prop","change:prop"]))])),_:1},8,["id"])}],["__scopeId","data-v-d3533eac"]]),jn={components:{CustomNavbar:Z},data:()=>({totalIncome:42568.9,totalOrders:152,averageOrder:280.06,incomeChange:15.2,orderChange:8.7,avgChange:6.3,trendChartType:"line",paymentChartType:"pie",trendData:{},trendOpts:{color:["#5145F7","#91CB74"],padding:[15,10,15,10],enableScroll:!1,enableMarkLine:!1,dataLabel:!0,dataPointShape:!0,dataPointShapeType:"solid",legend:{show:!0,position:"top",lineHeight:25},xAxis:{boundaryGap:!0,disableGrid:!0,axisLine:!0,fontSize:10},yAxis:{gridType:"dash",dashLength:2,data:[{min:0}]},extra:{line:{type:"curve",width:2,activeType:"hollow"},column:{width:25,seriesGap:5,categoryGap:10,barBorderRadius:[3,3,0,0],activeBgColor:"#000000",activeBgOpacity:.08},tooltip:{showBox:!0,showArrow:!0,showCategory:!0,borderWidth:0,borderRadius:5,borderColor:"#000000",borderOpacity:.7,bgColor:"#000000",bgOpacity:.7,gridType:"solid",dashLength:4,gridColor:"#CCCCCC",fontColor:"#FFFFFF",horizentalLine:!1,xAxisLabel:!1,yAxisLabel:!1,labelBgColor:"#FFFFFF",labelBgOpacity:.7,labelFontColor:"#666666"}}},paymentData:{},paymentOpts:{color:["#4CD964","#1890FF","#9013FE","#FF6B22"],padding:[15,5,15,5],dataLabel:!0,enableScroll:!1,legend:{show:!1},extra:{pie:{activeOpacity:1,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:3,borderColor:"#FFFFFF"},ring:{ringWidth:30,activeOpacity:1,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:3,borderColor:"#FFFFFF"}}},productData:{},productOpts:{color:["#1890FF"],padding:[15,15,15,15],enableScroll:!1,legend:{show:!1},xAxis:{boundaryGap:"justify",disableGrid:!0,min:0,axisLine:!1},yAxis:{},extra:{bar:{type:"group",width:30,meterBorde:1,meterFillColor:"#FFFFFF",activeBgColor:"#000000",activeBgOpacity:.08,barBorderRadius:[0,3,3,0]}}},timeData:{},timeOpts:{color:["#5145F7"],padding:[15,10,15,10],enableScroll:!1,legend:{show:!1},xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2,data:[{min:0}]},extra:{area:{type:"curve",opacity:.2,addLine:!0,width:2,gradient:!0,activeType:"hollow"}}},loyaltyData:{},loyaltyOpts:{color:["#5145F7","#FF6B22","#4CD964"],padding:[15,15,0,5],enableScroll:!1,legend:{show:!0,position:"top",lineHeight:25},xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},extra:{column:{type:"group",width:30,activeBgColor:"#000000",activeBgOpacity:.08}}},newCustomers:87,returningCustomers:65,loyaltyRate:42.8,paymentColors:["#4CD964","#1890FF","#9013FE","#FF6B22"],paymentMethods:[{name:"微信支付",percentage:30,amount:12770.67},{name:"支付宝",percentage:40,amount:17027.56},{name:"云闪付",percentage:20,amount:8513.78},{name:"其他",percentage:10,amount:4256.89}],timeInsight:"您的高峰销售时段集中在11:00-14:00和17:00-20:00，建议在这些时段增加人手以提高服务效率。",pickerValue:[0,3],years:[2025,2026,2027],months:[1,2,3,4,5,6,7,8,9,10,11,12],selectedMonth:"2025年4月"}),onReady(){this.getTrendData(),this.getPaymentData(),this.getProductData(),this.getTimeData(),this.getLoyaltyData()},methods:{formatNumber:t=>t.toLocaleString("zh",{minimumFractionDigits:2,maximumFractionDigits:2}),showDatePicker(){s({title:"日期选择功能",icon:"none"})},shareReport(){s({title:"分享报表功能",icon:"none"})},exportData(){s({title:"报表数据导出中...",icon:"none"})},goToDiamondChart(){v({url:"/pages/report/diamond"})},getTrendData(){setTimeout((()=>{let t;t=(this.trendChartType,{categories:["1日","5日","10日","15日","20日","25日","30日"],series:[{name:"本月收入",data:[3800,4200,5600,6900,4800,3500,6300]},{name:"上月收入",data:[3200,3600,4800,5900,4200,3300,5800]}]}),this.trendData=JSON.parse(JSON.stringify(t))}),500)},getPaymentData(){setTimeout((()=>{let t={series:[{data:this.paymentMethods.map((t=>({name:t.name,value:t.percentage})))}]};this.paymentData=JSON.parse(JSON.stringify(t))}),500)},getProductData(){setTimeout((()=>{this.productData=JSON.parse(JSON.stringify({categories:["黑咖啡","拿铁","卡布奇诺","冰美式","抹茶拿铁"],series:[{name:"销售量",data:[235,187,165,134,122]}]}))}),500)},getTimeData(){setTimeout((()=>{this.timeData=JSON.parse(JSON.stringify({categories:["8:00","10:00","12:00","14:00","16:00","18:00","20:00","22:00"],series:[{name:"收入",data:[2300,5600,7800,4500,3900,6700,8100,3200]}]}))}),500)},getLoyaltyData(){setTimeout((()=>{let t={categories:["客户类型"],series:[{name:"新客户",data:[this.newCustomers]},{name:"回头客",data:[this.returningCustomers]}]};this.loyaltyData=JSON.parse(JSON.stringify(t))}),500)},onMonthSelect(t){const e=t.detail.value;this.pickerValue=e;const i=this.years[e[0]],a=this.months[e[1]];this.selectedMonth=`${i}年${a}月`,this.getTrendData(),this.getPaymentData(),this.getProductData(),this.getTimeData(),this.getLoyaltyData()},onColumnChange(t){},setTrendChartType(t){this.trendChartType!==t&&(this.trendData={},this.trendChartType=t,setTimeout((()=>{this.getTrendData()}),100))},setPaymentChartType(t){this.paymentChartType!==t&&(this.paymentData={},this.paymentChartType=t,setTimeout((()=>{this.getPaymentData()}),100))},onTrendChartClick(t){console.log("图表点击",t);const{type:e,series:i,categories:a,index:o}=t;s({title:`${a[o]}：${i[0].data[o]}`,icon:"none"})}}};const $n=et(jn,[["render",function(t,e,i,a,o,n){const r=V,s=L,l=tt(W("custom-navbar"),Z),c=Y,h=Q,d=tt(W("qiun-data-charts"),qn);return P(),k(s,{class:"container"},{default:D((()=>[I(l,{title:"数据报表","show-back":!1,shadow:!0},{right:D((()=>[I(s,{style:{display:"flex","align-items":"center"}},{default:D((()=>[I(s,{onClick:n.showDatePicker,style:{"margin-right":"20rpx"}},{default:D((()=>[I(r,{src:"/h5/static/home/<USER>",mode:"aspectFit",class:"icon-calendar"})])),_:1},8,["onClick"]),I(s,{onClick:n.shareReport},{default:D((()=>[I(r,{src:it,mode:"aspectFit",class:"icon-share"})])),_:1},8,["onClick"])])),_:1})])),_:1}),I(s,{class:"month-selector"},{default:D((()=>[I(c,{class:"month-label"},{default:D((()=>[O("本月")])),_:1}),I(h,{mode:"multiSelector",value:o.pickerValue,range:[o.years,o.months],onChange:n.onMonthSelect,onColumnchange:n.onColumnChange},{default:D((()=>[I(s,{class:"month-value"},{default:D((()=>[I(c,null,{default:D((()=>[O(E(o.selectedMonth),1)])),_:1}),I(r,{src:"/h5/static/home/<USER>",mode:"aspectFit",class:"icon-arrow"})])),_:1})])),_:1},8,["value","range","onChange","onColumnchange"])])),_:1}),I(s,{class:"summary-card"},{default:D((()=>[I(s,{class:"data-grid"},{default:D((()=>[I(s,{class:"data-item"},{default:D((()=>[I(c,{class:"data-value"},{default:D((()=>[O("¥ "+E(n.formatNumber(o.totalIncome)),1)])),_:1}),I(c,{class:"data-label"},{default:D((()=>[O("总收入")])),_:1}),I(c,{class:G(["data-change",o.incomeChange>=0?"up":"down"])},{default:D((()=>[O(E(o.incomeChange>=0?"+":"")+E(o.incomeChange)+"% ",1)])),_:1},8,["class"])])),_:1}),I(s,{class:"data-item"},{default:D((()=>[I(c,{class:"data-value"},{default:D((()=>[O(E(o.totalOrders),1)])),_:1}),I(c,{class:"data-label"},{default:D((()=>[O("订单数")])),_:1}),I(c,{class:G(["data-change",o.orderChange>=0?"up":"down"])},{default:D((()=>[O(E(o.orderChange>=0?"+":"")+E(o.orderChange)+"% ",1)])),_:1},8,["class"])])),_:1}),I(s,{class:"data-item"},{default:D((()=>[I(c,{class:"data-value"},{default:D((()=>[O("¥ "+E(n.formatNumber(o.averageOrder)),1)])),_:1}),I(c,{class:"data-label"},{default:D((()=>[O("客单价")])),_:1}),I(c,{class:G(["data-change",o.avgChange>=0?"up":"down"])},{default:D((()=>[O(E(o.avgChange>=0?"+":"")+E(o.avgChange)+"% ",1)])),_:1},8,["class"])])),_:1})])),_:1})])),_:1}),I(s,{class:"card"},{default:D((()=>[I(s,{class:"card-header"},{default:D((()=>[I(c,{class:"section-title"},{default:D((()=>[O("收入趋势")])),_:1}),I(s,{class:"card-actions"},{default:D((()=>[I(c,{class:G(["toggle-btn",{active:"line"===o.trendChartType}]),onClick:e[0]||(e[0]=t=>n.setTrendChartType("line"))},{default:D((()=>[O("折线图")])),_:1},8,["class"]),I(c,{class:G(["toggle-btn",{active:"column"===o.trendChartType}]),onClick:e[1]||(e[1]=t=>n.setTrendChartType("column"))},{default:D((()=>[O("柱状图")])),_:1},8,["class"])])),_:1})])),_:1}),I(s,{class:"chart-container"},{default:D((()=>[I(d,{type:o.trendChartType,opts:o.trendOpts,chartData:o.trendData,onGetIndex:n.onTrendChartClick},null,8,["type","opts","chartData","onGetIndex"])])),_:1})])),_:1}),I(s,{class:"card"},{default:D((()=>[I(s,{class:"card-header"},{default:D((()=>[I(c,{class:"section-title"},{default:D((()=>[O("支付方式分析")])),_:1}),I(s,{class:"card-actions"},{default:D((()=>[I(c,{class:G(["toggle-btn",{active:"pie"===o.paymentChartType}]),onClick:e[2]||(e[2]=t=>n.setPaymentChartType("pie"))},{default:D((()=>[O("饼图")])),_:1},8,["class"]),I(c,{class:G(["toggle-btn",{active:"ring"===o.paymentChartType}]),onClick:e[3]||(e[3]=t=>n.setPaymentChartType("ring"))},{default:D((()=>[O("环形图")])),_:1},8,["class"])])),_:1})])),_:1}),I(s,{class:"chart-container"},{default:D((()=>[I(d,{type:o.paymentChartType,opts:o.paymentOpts,chartData:o.paymentData},null,8,["type","opts","chartData"])])),_:1}),I(s,{class:"payment-total"},{default:D((()=>[I(c,null,{default:D((()=>[O("总交易额: ¥ "+E(n.formatNumber(o.totalIncome)),1)])),_:1})])),_:1}),I(s,{class:"payment-legend"},{default:D((()=>[(P(!0),K(X,null,J(o.paymentMethods,((t,e)=>(P(),k(s,{class:"legend-item",key:e},{default:D((()=>[I(s,{class:"legend-color",style:j({backgroundColor:o.paymentColors[e]})},null,8,["style"]),I(c,{class:"legend-name"},{default:D((()=>[O(E(t.name),1)])),_:2},1024),I(c,{class:"legend-percent"},{default:D((()=>[O(E(t.percentage)+"%",1)])),_:2},1024),I(c,{class:"legend-amount"},{default:D((()=>[O("¥ "+E(n.formatNumber(t.amount)),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1}),I(s,{class:"card"},{default:D((()=>[I(s,{class:"card-header"},{default:D((()=>[I(c,{class:"section-title"},{default:D((()=>[O("热销商品")])),_:1})])),_:1}),I(s,{class:"chart-container"},{default:D((()=>[I(d,{type:"bar",opts:o.productOpts,chartData:o.productData},null,8,["opts","chartData"])])),_:1})])),_:1}),I(s,{class:"card"},{default:D((()=>[I(s,{class:"card-header"},{default:D((()=>[I(c,{class:"section-title"},{default:D((()=>[O("收入时段分布")])),_:1})])),_:1}),I(s,{class:"chart-container"},{default:D((()=>[I(d,{type:"area",opts:o.timeOpts,chartData:o.timeData},null,8,["opts","chartData"])])),_:1}),I(s,{class:"insight"},{default:D((()=>[I(s,{class:"insight-icon"},{default:D((()=>[O("💡")])),_:1}),I(c,{class:"insight-text"},{default:D((()=>[O(E(o.timeInsight),1)])),_:1})])),_:1})])),_:1}),I(s,{class:"card"},{default:D((()=>[I(s,{class:"card-header"},{default:D((()=>[I(c,{class:"section-title"},{default:D((()=>[O("客户忠诚度")])),_:1})])),_:1}),I(s,{class:"chart-container"},{default:D((()=>[I(d,{type:"column",opts:o.loyaltyOpts,chartData:o.loyaltyData},null,8,["opts","chartData"])])),_:1}),I(s,{class:"customer-stats"},{default:D((()=>[I(s,{class:"customer-stat-item"},{default:D((()=>[I(c,{class:"customer-stat-value"},{default:D((()=>[O(E(o.newCustomers),1)])),_:1}),I(c,{class:"customer-stat-label"},{default:D((()=>[O("新客户")])),_:1})])),_:1}),I(s,{class:"customer-stat-item"},{default:D((()=>[I(c,{class:"customer-stat-value"},{default:D((()=>[O(E(o.returningCustomers),1)])),_:1}),I(c,{class:"customer-stat-label"},{default:D((()=>[O("回头客")])),_:1})])),_:1}),I(s,{class:"customer-stat-item"},{default:D((()=>[I(c,{class:"customer-stat-value"},{default:D((()=>[O(E(o.loyaltyRate)+"%",1)])),_:1}),I(c,{class:"customer-stat-label"},{default:D((()=>[O("忠诚度")])),_:1})])),_:1})])),_:1})])),_:1}),I(s,{class:"action-buttons"},{default:D((()=>[I(s,{class:"action-btn export-btn",onClick:n.exportData},{default:D((()=>[I(c,{class:"action-btn-text"},{default:D((()=>[O("导出报表数据")])),_:1})])),_:1},8,["onClick"]),I(s,{class:"action-btn diamond-btn",onClick:n.goToDiamondChart},{default:D((()=>[I(c,{class:"action-btn-text"},{default:D((()=>[O("多维数据分析")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})}],["__scopeId","data-v-ec5e02c8"]]);export{$n as default};
