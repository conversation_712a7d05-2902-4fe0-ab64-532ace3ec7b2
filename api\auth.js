// api/auth.js - 用户认证相关API
import { get, post } from '@/utils/request';
import { signParams } from '@/utils/sign';
import config from '@/config/index';
import { API_LIST } from '@/config/api';

/**
 * 用户登录
 * @param {Object} data - 登录信息
 * @param {String} data.username - 用户名/邮箱/手机号
 * @param {String} data.password - 密码
 * @param {Number} data.type - 登录类型：0-商户ID密钥登录，1-账号密码登录
 * @param {Object} captchaResult - 验证码结果（可选）
 * @returns {Promise} 登录结果
 */
export async function login(data, captchaResult = {}) {
  // 确保有CSRF Token
  if (!uni.getStorageSync('csrf_token')) {
    await getCsrfToken();
  }
  
  const params = {
    type: data.type || 1,
    user: data.username,
    pass: data.password,
    // 从localStorage获取csrf_token
    csrf_token: uni.getStorageSync('csrf_token') || '',
    // 添加session_id以支持跨域场景
    session_id: uni.getStorageSync('session_id') || '',
    ...captchaResult
  };
  
  console.log('登录请求参数:', params);
  console.log('登录请求地址:', config.baseUrl + '/user/ajax.php?act=login');
  
  // 将参数转换为url编码的表单格式
  let formData = '';
  for (let key in params) {
    if (params[key] !== undefined && params[key] !== null) {
      if (formData !== '') formData += '&';
      formData += encodeURIComponent(key) + '=' + encodeURIComponent(params[key]);
    }
  }
  
  // 使用正确的API路径
  return post(API_LIST.AUTH.LOGIN, params, {
    header: {
      'content-type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json, text/plain, */*'
    },
    showError: false // 自定义错误处理，不使用默认的错误提示
  }).then(response => {
    console.log('登录响应数据:', response);
    return response;
  }).catch(error => {
    console.error('登录请求错误:', error);
    throw error;
  });
}

/**
 * 商户注册
 * @param {Object} data - 注册信息
 * @param {String} data.email - 邮箱（邮箱验证时必填）
 * @param {String} data.phone - 手机号（手机验证时必填）
 * @param {String} data.code - 验证码
 * @param {String} data.pwd - 密码
 * @param {String} data.invitecode - 邀请码（可选）
 * @returns {Promise} 注册结果
 */
export async function register(data) {
  // 确保有CSRF Token
  if (!uni.getStorageSync('csrf_token')) {
    await getCsrfToken();
  }
  
  const params = {
    email: data.email || '',
    phone: data.phone || '',
    code: data.code,
    pwd: data.pwd,
    invitecode: data.invitecode || '',
    csrf_token: uni.getStorageSync('csrf_token') || '',
    session_id: uni.getStorageSync('session_id') || ''
  };
  
  return post(API_LIST.AUTH.REG, params);
}

/**
 * 发送验证码
 * @param {String} sendto - 发送目标（邮箱或手机号）
 * @param {Object} captchaResult - 验证码结果
 * @returns {Promise} 发送结果
 */
export async function sendCode(sendto, captchaResult = {}) {
  const params = {
    sendto: sendto,
    ...captchaResult
  };
  
  return post(API_LIST.AUTH.SENDCODE, params);
}

/**
 * 找回密码-发送验证码
 * @param {String} type - 验证类型（phone/email）
 * @param {String} sendto - 发送目标（邮箱或手机号）
 * @param {Object} captchaResult - 验证码结果
 * @returns {Promise} 发送结果
 */
export function sendCodeForReset(type, sendto, captchaResult = {}) {
  const params = {
    type: type,
    sendto: sendto,
    ...captchaResult
  };
  
  return post(API_LIST.AUTH.FORGOT, params);
}

/**
 * 获取验证码配置
 * @returns {Promise} 验证码配置
 */
export async function getCaptcha() {
  const res = await get(API_LIST.AUTH.CAPTCHA);
  if (res.success === 1) {
    return res;
  }
  throw new Error('获取验证码失败');
}

/**
 * 第三方登录
 * @param {String} type - 登录类型（alipay/wx/qq）
 * @param {Boolean} bind - 是否绑定现有账号
 * @returns {Promise} 登录结果，包含跳转链接
 */
export function connectLogin(type, bind = false) {
  return post('/user/ajax.php?act=connect', {
    type: type,
    bind: bind ? '1' : ''
  });
}

/**
 * 退出登录
 * @returns {Promise} 退出结果
 */
export function logout() {
  // 清除本地存储的登录信息
  uni.removeStorageSync('user_token');
  uni.removeStorageSync('user_uid');
  uni.removeStorageSync('user_key');
  uni.removeStorageSync('merchantId');
  uni.removeStorageSync('merchantKey');
  uni.removeStorageSync('csrf_token');
  uni.removeStorageSync('session_id');

  // 调用后端退出登录接口
  return get('/user/login.php?logout=1').then(response => {
    console.log('退出登录响应:', response);
    return {
      code: 0,
      msg: '退出成功'
    };
  }).catch(error => {
    console.log('退出登录请求失败，但本地清理已完成:', error);
    // 即使后端请求失败，本地清理已完成，仍然返回成功
    return {
      code: 0,
      msg: '退出成功'
    };
  });
}

/**
 * 保存CSRF Token
 * @param {String} token - CSRF Token
 */
export function saveCSRFToken(token) {
  uni.setStorageSync('csrf_token', token);
}

/**
 * 获取CSRF Token（从页面解析）
 * @param {String} html - 页面HTML
 * @returns {String} CSRF Token
 */
export function parseCSRFToken(html) {
  if(!html) return '';
  const match = html.match(/name="csrf_token" value="([^"]+)"/);
  if(match && match[1]) {
    saveCSRFToken(match[1]);
    return match[1];
  }
  return '';
}

// 获取CSRF Token
export async function getCsrfToken() {
  try {
    const res = await get(API_LIST.AUTH.GET_CSRF);
    if (res.code === 0) {
      uni.setStorageSync('csrf_token', res.csrf_token);
      // 如果返回了session_id，也保存起来
      if (res.session_id) {
        uni.setStorageSync('session_id', res.session_id);
      }
      console.log('获取CSRF Token成功:', res.csrf_token);
      return res.csrf_token;
    }
    throw new Error(res.msg || '获取CSRF Token失败');
  } catch (error) {
    console.error('获取CSRF Token失败:', error);
    throw error;
  }
}