import{y as a,a as c,d as s,e as l,w as e,i as t,o as d,f as o,h as r,l as i}from"./index-B1Q521gi.js";import{r as n,_ as u}from"./uni-app.es.DAfa8VxY.js";import{_ as f}from"./custom-navbar.DuzuSmPc.js";import{_}from"./_plugin-vue_export-helper.BCo6x5W8.js";const F=_({components:{CustomNavbar:f},data:()=>({}),methods:{goBack(){a()},searchTools(){c({title:"搜索营销工具",icon:"none"})},navigateTo(a){c({title:`即将前往${a}页面`,icon:"none"})}}},[["render",function(a,c,_,F,p,g){const k=n(s("uni-icons"),u),m=t,v=n(s("custom-navbar"),f),y=i;return d(),l(m,{class:"container"},{default:e((()=>[o(v,{title:"营销工具","show-back":!0,shadow:!0,onClickLeft:g.goBack},{right:e((()=>[o(m,{onClick:g.searchTools,style:{padding:"0 16rpx"}},{default:e((()=>[o(k,{type:"search",size:"22",color:"#FFFFFF"})])),_:1},8,["onClick"])])),_:1},8,["onClickLeft"]),o(m,{class:"content"},{default:e((()=>[o(m,{class:"section"},{default:e((()=>[o(m,{class:"section-title"},{default:e((()=>[r("活动营销")])),_:1}),o(m,{class:"card-grid"},{default:e((()=>[o(m,{class:"card",onClick:c[0]||(c[0]=a=>g.navigateTo("coupon"))},{default:e((()=>[o(m,{class:"card-icon icon-blue"},{default:e((()=>[o(k,{type:"gift",color:"#FFFFFF",size:"24"})])),_:1}),o(m,{class:"card-content"},{default:e((()=>[o(y,{class:"card-title"},{default:e((()=>[r("优惠券")])),_:1}),o(y,{class:"card-desc"},{default:e((()=>[r("会员折扣和优惠券管理")])),_:1})])),_:1})])),_:1}),o(m,{class:"card",onClick:c[1]||(c[1]=a=>g.navigateTo("discount"))},{default:e((()=>[o(m,{class:"card-icon icon-orange"},{default:e((()=>[o(k,{type:"star",color:"#FFFFFF",size:"24"})])),_:1}),o(m,{class:"card-content"},{default:e((()=>[o(y,{class:"card-title"},{default:e((()=>[r("满减活动")])),_:1}),o(y,{class:"card-desc"},{default:e((()=>[r("设置满减优惠活动")])),_:1})])),_:1})])),_:1})])),_:1}),o(m,{class:"card-grid"},{default:e((()=>[o(m,{class:"card",onClick:c[2]||(c[2]=a=>g.navigateTo("seckill"))},{default:e((()=>[o(m,{class:"card-icon icon-red"},{default:e((()=>[o(k,{type:"shop",color:"#FFFFFF",size:"24"})])),_:1}),o(m,{class:"card-content"},{default:e((()=>[o(y,{class:"card-title"},{default:e((()=>[r("限时秒杀")])),_:1}),o(y,{class:"card-desc"},{default:e((()=>[r("设置限时特价商品")])),_:1})])),_:1})])),_:1}),o(m,{class:"card",onClick:c[3]||(c[3]=a=>g.navigateTo("group"))},{default:e((()=>[o(m,{class:"card-icon icon-green"},{default:e((()=>[o(k,{type:"staff",color:"#FFFFFF",size:"24"})])),_:1}),o(m,{class:"card-content"},{default:e((()=>[o(y,{class:"card-title"},{default:e((()=>[r("拼团活动")])),_:1}),o(y,{class:"card-desc"},{default:e((()=>[r("多人拼团优惠")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),o(m,{class:"section"},{default:e((()=>[o(m,{class:"section-title"},{default:e((()=>[r("会员管理")])),_:1}),o(m,{class:"card-grid"},{default:e((()=>[o(m,{class:"card",onClick:c[4]||(c[4]=a=>g.navigateTo("member"))},{default:e((()=>[o(m,{class:"card-icon icon-purple"},{default:e((()=>[o(k,{type:"person",color:"#FFFFFF",size:"24"})])),_:1}),o(m,{class:"card-content"},{default:e((()=>[o(y,{class:"card-title"},{default:e((()=>[r("会员体系")])),_:1}),o(y,{class:"card-desc"},{default:e((()=>[r("管理会员等级和权益")])),_:1})])),_:1})])),_:1}),o(m,{class:"card",onClick:c[5]||(c[5]=a=>g.navigateTo("points"))},{default:e((()=>[o(m,{class:"card-icon icon-blue-light"},{default:e((()=>[o(k,{type:"medal",color:"#FFFFFF",size:"24"})])),_:1}),o(m,{class:"card-content"},{default:e((()=>[o(y,{class:"card-title"},{default:e((()=>[r("积分商城")])),_:1}),o(y,{class:"card-desc"},{default:e((()=>[r("会员积分兑换商品")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),o(m,{class:"section"},{default:e((()=>[o(m,{class:"section-title"},{default:e((()=>[r("社交营销")])),_:1}),o(m,{class:"card-grid"},{default:e((()=>[o(m,{class:"card",onClick:c[6]||(c[6]=a=>g.navigateTo("social"))},{default:e((()=>[o(m,{class:"card-icon icon-green-light"},{default:e((()=>[o(k,{type:"chat",color:"#FFFFFF",size:"24"})])),_:1}),o(m,{class:"card-content"},{default:e((()=>[o(y,{class:"card-title"},{default:e((()=>[r("社交分享")])),_:1}),o(y,{class:"card-desc"},{default:e((()=>[r("社交媒体营销工具")])),_:1})])),_:1})])),_:1}),o(m,{class:"card",onClick:c[7]||(c[7]=a=>g.navigateTo("invite"))},{default:e((()=>[o(m,{class:"card-icon icon-yellow"},{default:e((()=>[o(k,{type:"reload",color:"#FFFFFF",size:"24"})])),_:1}),o(m,{class:"card-content"},{default:e((()=>[o(y,{class:"card-title"},{default:e((()=>[r("邀请有礼")])),_:1}),o(y,{class:"card-desc"},{default:e((()=>[r("会员推荐奖励")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),o(m,{class:"section"},{default:e((()=>[o(m,{class:"section-title"},{default:e((()=>[r("营销分析")])),_:1}),o(m,{class:"card-grid"},{default:e((()=>[o(m,{class:"card",onClick:c[8]||(c[8]=a=>g.navigateTo("analysis"))},{default:e((()=>[o(m,{class:"card-icon icon-blue-dark"},{default:e((()=>[o(k,{type:"paperplane",color:"#FFFFFF",size:"24"})])),_:1}),o(m,{class:"card-content"},{default:e((()=>[o(y,{class:"card-title"},{default:e((()=>[r("营销分析")])),_:1}),o(y,{class:"card-desc"},{default:e((()=>[r("营销活动数据统计")])),_:1})])),_:1})])),_:1}),o(m,{class:"card",onClick:c[9]||(c[9]=a=>g.navigateTo("report"))},{default:e((()=>[o(m,{class:"card-icon icon-purple-dark"},{default:e((()=>[o(k,{type:"calendar",color:"#FFFFFF",size:"24"})])),_:1}),o(m,{class:"card-content"},{default:e((()=>[o(y,{class:"card-title"},{default:e((()=>[r("数据报表")])),_:1}),o(y,{class:"card-desc"},{default:e((()=>[r("营销效果数据报表")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-0347c268"]]);export{F as default};
