<?php
// 调试回调地址配置
include("./includes/common.php");

echo "<h2>回调地址配置调试</h2>";
echo "<p><strong>当前时间:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>HTTP_HOST:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>REQUEST_URI:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p><strong>siteurl:</strong> " . $siteurl . "</p>";

// 检查数据库中的localurl配置
$localurl_db = getSetting('localurl');
echo "<p><strong>数据库中的localurl:</strong> " . ($localurl_db ? $localurl_db : '(空)') . "</p>";

echo "<p><strong>最终使用的localurl:</strong> " . $conf['localurl'] . "</p>";

// 模拟一个订单号测试回调地址
$test_trade_no = '2025072605062650741';
$test_callback_url = $conf['localurl'] . 'pay/notify/' . $test_trade_no . '/';
echo "<p><strong>测试回调地址:</strong> " . $test_callback_url . "</p>";

// 检查easypay插件是否存在
$easypay_plugin_file = PLUGIN_ROOT . 'easypay/easypay_plugin.php';
echo "<p><strong>easypay插件文件:</strong> " . (file_exists($easypay_plugin_file) ? '存在' : '不存在') . "</p>";

// 测试回调URL是否可访问
echo "<h3>测试回调URL访问性</h3>";
$test_url = $test_callback_url;
echo "<p>测试URL: <a href='{$test_url}' target='_blank'>{$test_url}</a></p>";

// 显示当前配置的所有相关设置
echo "<h3>相关配置</h3>";
echo "<pre>";
echo "siteurl: " . $siteurl . "\n";
echo "localurl (数据库): " . ($localurl_db ? $localurl_db : '(空)') . "\n";
echo "localurl (最终): " . $conf['localurl'] . "\n";
echo "PLUGIN_ROOT: " . PLUGIN_ROOT . "\n";
echo "</pre>";
?>
