<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 WebSocket连接调试器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .warning { background: rgba(255, 152, 0, 0.3); }
        .info { background: rgba(33, 150, 243, 0.3); }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .log {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
        }
        .metric-value {
            font-weight: bold;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 WebSocket连接调试器</h1>
        <p>专门用于调试WebSocket连接和消息接收问题</p>

        <div id="status" class="status info">
            🔌 准备连接WebSocket服务器...
        </div>

        <div>
            <button onclick="connectWebSocket()">🔗 连接WebSocket</button>
            <button onclick="sendAuth()">🔐 发送认证</button>
            <button onclick="sendHeartbeat()">💓 发送心跳</button>
            <button onclick="testPaymentData()">💰 测试支付数据</button>
            <button onclick="clearLog()">🗑️ 清空日志</button>
        </div>

        <div class="grid">
            <div class="card">
                <h3>📊 连接统计</h3>
                <div class="metric">
                    <span>连接状态:</span>
                    <span class="metric-value" id="connectionStatus">未连接</span>
                </div>
                <div class="metric">
                    <span>消息总数:</span>
                    <span class="metric-value" id="messageCount">0</span>
                </div>
                <div class="metric">
                    <span>支付消息:</span>
                    <span class="metric-value" id="paymentCount">0</span>
                </div>
                <div class="metric">
                    <span>连接时长:</span>
                    <span class="metric-value" id="connectionTime">0秒</span>
                </div>
            </div>

            <div class="card">
                <h3>🎯 最新支付</h3>
                <div class="metric">
                    <span>订单号:</span>
                    <span class="metric-value" id="lastTradeNo">无</span>
                </div>
                <div class="metric">
                    <span>金额:</span>
                    <span class="metric-value" id="lastAmount">无</span>
                </div>
                <div class="metric">
                    <span>时间:</span>
                    <span class="metric-value" id="lastTime">无</span>
                </div>
                <div class="metric">
                    <span>状态:</span>
                    <span class="metric-value" id="lastStatus">无</span>
                </div>
            </div>
        </div>

        <div class="log" id="log">
            <div>📋 调试日志：</div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let messageCount = 0;
        let paymentCount = 0;
        let connectionStartTime = null;
        let connectionTimer = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#00bcd4',
                success: '#4caf50',
                error: '#f44336',
                warning: '#ff9800'
            };
            
            logDiv.innerHTML += `<div style="color: ${colors[type]};">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function updateMetrics() {
            document.getElementById('messageCount').textContent = messageCount;
            document.getElementById('paymentCount').textContent = paymentCount;
            document.getElementById('connectionStatus').textContent = isConnected ? '已连接' : '未连接';
            
            if (connectionStartTime && isConnected) {
                const elapsed = Math.floor((Date.now() - connectionStartTime) / 1000);
                document.getElementById('connectionTime').textContent = elapsed + '秒';
            }
        }

        function connectWebSocket() {
            if (ws && isConnected) {
                log('⚠️ WebSocket已连接', 'warning');
                return;
            }

            const wsUrl = 'ws://ceshi.huisas.com:8080';
            log(`🔗 正在连接: ${wsUrl}`, 'info');
            updateStatus('🔌 正在连接WebSocket...', 'warning');

            ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                isConnected = true;
                connectionStartTime = Date.now();
                log('✅ WebSocket连接成功！', 'success');
                updateStatus('✅ WebSocket已连接', 'success');
                
                // 启动连接时间计时器
                connectionTimer = setInterval(updateMetrics, 1000);
                
                // 自动发送认证
                setTimeout(sendAuth, 1000);
            };

            ws.onmessage = function(event) {
                messageCount++;
                log(`📨 收到消息: ${event.data}`, 'info');
                
                try {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                } catch (error) {
                    log(`⚠️ 消息解析失败: ${error.message}`, 'warning');
                }
                
                updateMetrics();
            };

            ws.onclose = function(event) {
                isConnected = false;
                log(`🔌 WebSocket连接关闭: code=${event.code}`, 'warning');
                updateStatus('🔌 WebSocket已断开', 'error');
                
                if (connectionTimer) {
                    clearInterval(connectionTimer);
                    connectionTimer = null;
                }
                
                updateMetrics();
            };

            ws.onerror = function(error) {
                log(`❌ WebSocket错误: ${error}`, 'error');
                updateStatus('❌ WebSocket连接错误', 'error');
            };
        }

        function handleMessage(data) {
            log(`📋 消息类型: ${data.type}`, 'info');
            
            switch (data.type) {
                case 'welcome':
                    log('👋 收到欢迎消息', 'success');
                    break;
                    
                case 'auth_result':
                    if (data.success) {
                        log('🔐 认证成功', 'success');
                    } else {
                        log('❌ 认证失败', 'error');
                    }
                    break;
                    
                case 'payment_notification':
                    paymentCount++;
                    log('💰 收到支付通知！', 'success');
                    handlePaymentNotification(data.data);
                    break;
                    
                case 'heartbeat':
                    log('💓 收到心跳响应', 'info');
                    break;
                    
                default:
                    log(`❓ 未知消息类型: ${data.type}`, 'warning');
            }
        }

        function handlePaymentNotification(paymentData) {
            const amount = paymentData.money || paymentData.amount || '0.00';
            const tradeNo = paymentData.trade_no || '未知';
            const status = paymentData.status || '未知';
            
            log(`💰 支付详情: 订单${tradeNo}, 金额${amount}元, 状态${status}`, 'success');
            
            // 更新最新支付信息
            document.getElementById('lastTradeNo').textContent = tradeNo;
            document.getElementById('lastAmount').textContent = amount + '元';
            document.getElementById('lastTime').textContent = new Date().toLocaleTimeString();
            document.getElementById('lastStatus').textContent = status;
            
            // 模拟语音播报
            log(`🎵 模拟语音播报: 收款${amount}元`, 'success');
        }

        function sendAuth() {
            if (!isConnected) {
                log('❌ WebSocket未连接，无法发送认证', 'error');
                return;
            }

            const authData = {
                type: 'auth',
                data: {
                    merchant_id: '1000',
                    staff_id: '',
                    token: 'test_token'
                }
            };

            ws.send(JSON.stringify(authData));
            log('🔐 发送认证信息', 'info');
        }

        function sendHeartbeat() {
            if (!isConnected) {
                log('❌ WebSocket未连接，无法发送心跳', 'error');
                return;
            }

            const heartbeatData = {
                type: 'heartbeat',
                timestamp: Date.now()
            };

            ws.send(JSON.stringify(heartbeatData));
            log('💓 发送心跳包', 'info');
        }

        function testPaymentData() {
            // 模拟接收到支付数据
            const testPayment = {
                type: 'payment_notification',
                data: {
                    trade_no: '2025072821322621783',
                    uid: '1000',
                    money: '0.01',
                    type: '1',
                    typename: '在线支付',
                    addtime: new Date().toISOString(),
                    api_trade_no: 'TEST_' + Date.now(),
                    buyer: '<EMAIL>',
                    status: 'success'
                }
            };

            log('🧪 模拟支付数据测试', 'warning');
            handleMessage(testPayment);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>📋 调试日志：</div>';
            messageCount = 0;
            paymentCount = 0;
            updateMetrics();
        }

        // 页面加载时自动连接
        window.onload = function() {
            log('🚀 WebSocket调试器已加载', 'info');
            updateMetrics();
            setTimeout(connectWebSocket, 1000);
        };
    </script>
</body>
</html>
