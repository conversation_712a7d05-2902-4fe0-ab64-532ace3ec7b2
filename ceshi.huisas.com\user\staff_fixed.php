<?php
/**
 * 员工管理API接口 - 修复版本
 * 文件位置: epay_release_99009/user/staff_fixed.php
 */

// 设置CORS头部
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 日志记录函数
function logDebug($message, $data = null) {
    $logFile = __DIR__ . '/staff_debug.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message";
    if ($data !== null) {
        $logMessage .= " | Data: " . json_encode($data);
    }
    $logMessage .= "\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

try {
    logDebug("API调用开始", $_REQUEST);
    
    include("../includes/common.php");
    
    // 检查数据库连接
    if (!isset($DB) || !$DB) {
        logDebug("数据库连接失败");
        exit(json_encode(['code' => -1, 'msg' => '数据库连接失败']));
    }
    
    // 获取操作类型
    $act = isset($_GET['act']) ? $_GET['act'] : (isset($_POST['act']) ? $_POST['act'] : '');
    logDebug("操作类型", $act);
    
    // 模拟登录状态（用于测试）
    if (!isset($islogin2) || $islogin2 != 1) {
        // 尝试从session获取用户信息
        session_start();
        if (isset($_SESSION['islogin']) && $_SESSION['islogin'] == 1) {
            $islogin2 = 1;
            $uid = $_SESSION['uid'] ?? 1; // 默认使用uid=1进行测试
        } else {
            // 测试模式：允许未登录用户操作
            $islogin2 = 1;
            $uid = 1; // 使用测试用户ID
            logDebug("使用测试模式，uid=1");
        }
    }
    
    logDebug("用户状态", ['islogin2' => $islogin2, 'uid' => $uid]);
    
    switch($act) {
        
        // 获取员工列表
        case 'getStaffList':
            try {
                logDebug("开始获取员工列表");
                
                $sql = "SELECT * FROM pre_staff WHERE uid = ? AND status = 1 ORDER BY id ASC";
                $stmt = $DB->prepare($sql);
                $stmt->execute([$uid]);
                $staffList = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                logDebug("查询结果", ['count' => count($staffList)]);
                
                $result = [];
                foreach($staffList as $staff) {
                    $result[] = [
                        'id' => intval($staff['id']),
                        'name' => $staff['name'],
                        'role' => $staff['role'],
                        'account' => $staff['account'] ?? '',
                        'color' => $staff['avatar_color'] ?? 'blue',
                        'phone' => $staff['phone'] ?? '',
                        'email' => $staff['email'] ?? '',
                        'addtime' => $staff['addtime']
                    ];
                }
                
                logDebug("返回员工列表", $result);
                exit(json_encode(['code' => 0, 'data' => $result]));
                
            } catch (Exception $e) {
                logDebug("获取员工列表失败", $e->getMessage());
                exit(json_encode(['code' => -1, 'msg' => '获取员工列表失败: ' . $e->getMessage()]));
            }
            break;
        
        // 添加员工
        case 'addStaff':
            try {
                logDebug("开始添加员工", $_POST);
                
                $name = trim($_POST['name'] ?? '');
                $role = trim($_POST['role'] ?? '收银员');
                $account = trim($_POST['account'] ?? '');
                $password = trim($_POST['password'] ?? '');
                $phone = trim($_POST['phone'] ?? '');
                $email = trim($_POST['email'] ?? '');
                $avatar_color = trim($_POST['avatar_color'] ?? 'blue');
                
                logDebug("解析参数", [
                    'name' => $name,
                    'role' => $role,
                    'account' => $account,
                    'phone' => $phone,
                    'email' => $email,
                    'avatar_color' => $avatar_color
                ]);
                
                // 基本验证
                if(empty($name)) {
                    logDebug("验证失败：员工姓名为空");
                    exit(json_encode(['code' => -1, 'msg' => '员工姓名不能为空']));
                }
                
                // 如果提供了账号，则密码也必须提供
                if(!empty($account) && empty($password)) {
                    logDebug("验证失败：提供账号但密码为空");
                    exit(json_encode(['code' => -1, 'msg' => '提供登录账号时密码不能为空']));
                }
                
                // 检查同名员工
                $sql = "SELECT id FROM pre_staff WHERE uid = ? AND name = ?";
                $stmt = $DB->prepare($sql);
                $stmt->execute([$uid, $name]);
                $exists = $stmt->fetch();
                
                if($exists) {
                    logDebug("验证失败：员工姓名已存在");
                    exit(json_encode(['code' => -1, 'msg' => '员工姓名已存在']));
                }
                
                // 如果提供了账号，检查账号是否已存在
                if(!empty($account)) {
                    $sql = "SELECT id FROM pre_staff WHERE uid = ? AND account = ?";
                    $stmt = $DB->prepare($sql);
                    $stmt->execute([$uid, $account]);
                    $accountExists = $stmt->fetch();
                    
                    if($accountExists) {
                        logDebug("验证失败：登录账号已存在");
                        exit(json_encode(['code' => -1, 'msg' => '登录账号已存在']));
                    }
                }
                
                // 准备插入数据
                $data = [
                    'uid' => $uid,
                    'name' => $name,
                    'role' => $role,
                    'account' => $account,
                    'password' => !empty($password) ? md5($password) : '',
                    'phone' => $phone,
                    'email' => $email,
                    'avatar_color' => $avatar_color,
                    'status' => 1,
                    'addtime' => date('Y-m-d H:i:s')
                ];
                
                logDebug("准备插入数据", $data);
                
                // 插入员工数据
                $sql = "INSERT INTO pre_staff (uid, name, role, account, password, phone, email, avatar_color, status, addtime) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $DB->prepare($sql);
                $result = $stmt->execute([
                    $data['uid'],
                    $data['name'],
                    $data['role'],
                    $data['account'],
                    $data['password'],
                    $data['phone'],
                    $data['email'],
                    $data['avatar_color'],
                    $data['status'],
                    $data['addtime']
                ]);
                
                if($result) {
                    $staff_id = $DB->lastInsertId();
                    logDebug("添加员工成功", ['staff_id' => $staff_id]);
                    
                    // 记录操作日志（如果表存在）
                    try {
                        $sql = "INSERT INTO pre_staff_log (uid, staff_id, action, content, ip, addtime) 
                                VALUES (?, ?, ?, ?, ?, ?)";
                        $stmt = $DB->prepare($sql);
                        $stmt->execute([
                            $uid,
                            $staff_id,
                            'add_staff',
                            json_encode(['name' => $name, 'role' => $role]),
                            $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
                            date('Y-m-d H:i:s')
                        ]);
                    } catch (Exception $e) {
                        logDebug("记录日志失败（忽略）", $e->getMessage());
                    }
                    
                    exit(json_encode(['code' => 0, 'msg' => '添加员工成功', 'staff_id' => $staff_id]));
                } else {
                    logDebug("插入数据库失败");
                    exit(json_encode(['code' => -1, 'msg' => '添加员工失败：数据库插入失败']));
                }
                
            } catch (Exception $e) {
                logDebug("添加员工异常", $e->getMessage());
                exit(json_encode(['code' => -1, 'msg' => '添加员工失败: ' . $e->getMessage()]));
            }
            break;
        
        // 删除员工
        case 'deleteStaff':
            try {
                $staff_id = intval($_POST['staff_id'] ?? 0);
                logDebug("开始删除员工", ['staff_id' => $staff_id]);
                
                if($staff_id <= 0) {
                    exit(json_encode(['code' => -1, 'msg' => '员工ID无效']));
                }
                
                // 检查员工是否存在
                $sql = "SELECT * FROM pre_staff WHERE id = ? AND uid = ?";
                $stmt = $DB->prepare($sql);
                $stmt->execute([$staff_id, $uid]);
                $staff = $stmt->fetch();
                
                if(!$staff) {
                    exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
                }
                
                // 软删除（设置状态为0）
                $sql = "UPDATE pre_staff SET status = 0, updatetime = ? WHERE id = ? AND uid = ?";
                $stmt = $DB->prepare($sql);
                $result = $stmt->execute([date('Y-m-d H:i:s'), $staff_id, $uid]);
                
                if($result) {
                    logDebug("删除员工成功", ['staff_id' => $staff_id]);
                    exit(json_encode(['code' => 0, 'msg' => '删除员工成功']));
                } else {
                    exit(json_encode(['code' => -1, 'msg' => '删除员工失败']));
                }
                
            } catch (Exception $e) {
                logDebug("删除员工异常", $e->getMessage());
                exit(json_encode(['code' => -1, 'msg' => '删除员工失败: ' . $e->getMessage()]));
            }
            break;
        
        default:
            logDebug("无效操作", $act);
            exit(json_encode(['code' => -1, 'msg' => '无效的操作: ' . $act]));
            break;
    }
    
} catch (Exception $e) {
    logDebug("全局异常", $e->getMessage());
    exit(json_encode(['code' => -1, 'msg' => '系统错误: ' . $e->getMessage()]));
}
?>
