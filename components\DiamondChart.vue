<template>
  <view class="diamond-chart-container">
    <canvas canvas-id="diamondChart" id="diamondChart" class="diamond-chart" @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd"></canvas>
    <view class="legend" v-if="showLegend">
      <view class="legend-item" v-for="(item, index) in legendData" :key="index">
        <view class="legend-color" :style="{ backgroundColor: item.color }"></view>
        <text class="legend-name">{{ item.name }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    // 图表数据
    chartData: {
      type: Object,
      default: () => ({
        categories: [],
        series: []
      })
    },
    // 配置项
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      ctx: null,
      canvasWidth: 300,
      canvasHeight: 300,
      centerX: 150,
      centerY: 150,
      maxRadius: 120,
      showLegend: true,
      legendData: [],
      defaultColors: ['#5145F7', '#1890FF', '#4CD964', '#FAAD14', '#FF4D4F', '#9013FE'],
      fontColor: '#666666',
      fontSize: 12,
      animation: true,
      animationDuration: 1000,
      currentStep: 0,
      totalSteps: 30,
      touchStartX: 0,
      touchStartY: 0,
      rotating: false,
      rotationAngle: 0,
      lastAngle: 0
    };
  },
  mounted() {
    // 初始化画布上下文
    this.$nextTick(() => {
      const query = uni.createSelectorQuery().in(this);
      query.select('#diamondChart').fields({ node: true, size: true }).exec((res) => {
        const canvas = res[0].node;
        this.ctx = canvas.getContext('2d');
        
        // 设置画布尺寸
        const pixelRatio = uni.getSystemInfoSync().pixelRatio;
        const containerWidth = res[0].width || 300;
        const containerHeight = res[0].height || 300;
        
        this.canvasWidth = containerWidth * pixelRatio;
        this.canvasHeight = containerHeight * pixelRatio;
        this.centerX = this.canvasWidth / 2;
        this.centerY = this.canvasHeight / 2;
        // 增加半径系数，使图表更大一些
        this.maxRadius = Math.min(this.centerX, this.centerY) * 0.85;
        
        canvas.width = this.canvasWidth;
        canvas.height = this.canvasHeight;
        
        // 处理图例数据
        this.processLegendData();
        
        // 绘制图表
        if (this.animation) {
          this.animateChart();
        } else {
          this.drawChart(1);
        }
      });
    });
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.processLegendData();
        if (this.ctx) {
          if (this.animation) {
            this.currentStep = 0;
            this.animateChart();
          } else {
            this.drawChart(1);
          }
        }
      }
    }
  },
  methods: {
    // 处理图例数据
    processLegendData() {
      if (this.chartData && this.chartData.series && this.chartData.series.length > 0) {
        this.legendData = this.chartData.series.map((item, index) => {
          return {
            name: item.name,
            color: item.color || this.defaultColors[index % this.defaultColors.length]
          };
        });
        this.showLegend = this.legendData.length > 0;
      } else {
        this.showLegend = false;
      }
    },
    
    // 动画绘制图表
    animateChart() {
      if (this.currentStep <= this.totalSteps) {
        const progress = this.currentStep / this.totalSteps;
        this.drawChart(progress);
        this.currentStep++;
        requestAnimationFrame(this.animateChart);
      }
    },
    
    // 绘制图表
    drawChart(progress) {
      const { ctx, centerX, centerY, maxRadius, rotationAngle } = this;
      const { categories, series } = this.chartData;
      
      if (!categories || !series || categories.length < 3 || series.length === 0) {
        return;
      }
      
      // 清空画布
      ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
      
      const categoryCount = categories.length;
      const angleStep = (Math.PI * 2) / categoryCount;
      
      // 绘制环形网格
      this.drawPolygonGrid(categoryCount, angleStep, progress, rotationAngle);
      
      // 绘制坐标轴
      this.drawAxes(categories, categoryCount, angleStep, rotationAngle);
      
      // 绘制数据多边形
      this.drawDataPolygons(series, categories, categoryCount, angleStep, progress, rotationAngle);
    },
    
    // 绘制环形网格
    drawPolygonGrid(categoryCount, angleStep, progress, rotationAngle) {
      const { ctx, centerX, centerY, maxRadius } = this;
      const gridCount = 5; // 环形网格数量
      
      ctx.lineWidth = 1;
      ctx.strokeStyle = '#EEEEEE';
      
      for (let i = 1; i <= gridCount; i++) {
        const radius = (maxRadius * i) / gridCount * progress;
        
        ctx.beginPath();
        for (let j = 0; j < categoryCount; j++) {
          const angle = j * angleStep + rotationAngle;
          const x = centerX + radius * Math.cos(angle);
          const y = centerY + radius * Math.sin(angle);
          
          if (j === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }
        ctx.closePath();
        ctx.stroke();
      }
    },
    
    // 绘制坐标轴
    drawAxes(categories, categoryCount, angleStep, rotationAngle) {
      const { ctx, centerX, centerY, maxRadius, fontColor, fontSize } = this;
      
      ctx.lineWidth = 1;
      ctx.strokeStyle = '#DDDDDD';
      ctx.fillStyle = fontColor;
      ctx.font = `${fontSize * 1.2}px Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      for (let i = 0; i < categoryCount; i++) {
        const angle = i * angleStep + rotationAngle;
        const x = centerX + maxRadius * Math.cos(angle);
        const y = centerY + maxRadius * Math.sin(angle);
        
        // 绘制轴线
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.lineTo(x, y);
        ctx.stroke();
        
        // 计算标签位置 - 适当增加距离使标签更清晰
        const labelDistance = maxRadius * 1.15;
        const labelX = centerX + labelDistance * Math.cos(angle);
        const labelY = centerY + labelDistance * Math.sin(angle);
        
        // 绘制标签背景 使标签更清晰
        const textWidth = ctx.measureText(categories[i]).width;
        ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
        ctx.fillRect(labelX - textWidth / 2 - 5, labelY - fontSize / 2 - 2, textWidth + 10, fontSize + 4);
        
        // 绘制标签
        ctx.fillStyle = fontColor;
        ctx.fillText(categories[i], labelX, labelY);
      }
    },
    
    // 绘制数据多边形
    drawDataPolygons(series, categories, categoryCount, angleStep, progress, rotationAngle) {
      const { ctx, centerX, centerY, maxRadius, defaultColors } = this;
      
      // 计算每个系列的最大值
      const maxValues = categories.map((_, categoryIndex) => {
        return Math.max(...series.map(serie => serie.data[categoryIndex] || 0));
      });
      
      // 绘制每个系列的多边形，从后往前绘制，这样最前面的系列会在最上层
      for (let serieIndex = series.length - 1; serieIndex >= 0; serieIndex--) {
        const serie = series[serieIndex];
        const color = serie.color || defaultColors[serieIndex % defaultColors.length];
        const points = [];
        
        // 设置填充样式
        ctx.fillStyle = this.hexToRgba(color, 0.25);
        ctx.strokeStyle = color;
        ctx.lineWidth = 3;
        
        // 绘制数据点和线
        ctx.beginPath();
        for (let i = 0; i < categoryCount; i++) {
          const value = serie.data[i] || 0;
          const maxValue = maxValues[i] || 1;
          const ratio = Math.min(value / maxValue, 1) * progress;
          const angle = i * angleStep + rotationAngle;
          const x = centerX + maxRadius * ratio * Math.cos(angle);
          const y = centerY + maxRadius * ratio * Math.sin(angle);
          
          points.push({ x, y });
          
          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        
        // 绘制数据点
        ctx.fillStyle = '#FFFFFF';
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        
        points.forEach(point => {
          ctx.beginPath();
          ctx.arc(point.x, point.y, 6, 0, Math.PI * 2);
          ctx.fill();
          ctx.stroke();
          
          // 添加内圆
          ctx.fillStyle = color;
          ctx.beginPath();
          ctx.arc(point.x, point.y, 3, 0, Math.PI * 2);
          ctx.fill();
        });
      }
    },
    
    // 颜色转换为带透明度的rgba
    hexToRgba(hex, alpha) {
      const r = parseInt(hex.slice(1, 3), 16);
      const g = parseInt(hex.slice(3, 5), 16);
      const b = parseInt(hex.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    },
    
    // 触摸事件处理
    touchStart(e) {
      const touch = e.touches[0];
      this.touchStartX = touch.x;
      this.touchStartY = touch.y;
      this.rotating = true;
      this.lastAngle = this.calculateAngle(touch.x, touch.y);
    },
    
    touchMove(e) {
      if (!this.rotating) return;
      
      const touch = e.touches[0];
      const currentAngle = this.calculateAngle(touch.x, touch.y);
      const angleDiff = currentAngle - this.lastAngle;
      
      // 更新旋转角度
      this.rotationAngle += angleDiff;
      this.lastAngle = currentAngle;
      
      // 重绘图表
      this.drawChart(1);
    },
    
    touchEnd() {
      this.rotating = false;
    },
    
    // 计算角度
    calculateAngle(x, y) {
      const { centerX, centerY } = this;
      return Math.atan2(y - centerY, x - centerX);
    }
  }
};
</script>

<style>
.diamond-chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.diamond-chart {
  width: 100%;
  height: 100%;
  flex: 1;
}

.legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 10rpx;
  padding: 10rpx;
  width: 100%;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 20rpx 10rpx 0;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 6rpx;
  margin-right: 8rpx;
}

.legend-name {
  font-size: 24rpx;
  color: #666;
}
</style> 