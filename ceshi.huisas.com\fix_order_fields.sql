-- 🔧 修复订单表缺少字段的SQL脚本
-- 修复开发系统与原系统的数据库表结构差异

-- 1. 添加缺失的bill_mch_trade_no字段（原系统有，开发系统缺失）
ALTER TABLE `pre_order` ADD COLUMN `bill_mch_trade_no` VARCHAR(150) DEFAULT NULL COMMENT '商户交易单号' AFTER `bill_trade_no`;

-- 2. 添加对应的索引
ALTER TABLE `pre_order` ADD INDEX `idx_bill_mch_trade_no` (`bill_mch_trade_no`);

-- 3. 修改addtime字段约束，与原系统保持一致
ALTER TABLE `pre_order` MODIFY COLUMN `addtime` DATETIME NOT NULL;

-- 4. 添加plugin字段用于存储支付插件名称（可选，用于后台显示）
ALTER TABLE `pre_order` ADD COLUMN `plugin` VARCHAR(50) DEFAULT NULL COMMENT '支付插件名称' AFTER `channel`;

-- 5. 更新现有订单的plugin字段（根据channel关联）
UPDATE `pre_order` o
LEFT JOIN `pre_channel` c ON o.channel = c.id
SET o.plugin = c.plugin
WHERE o.plugin IS NULL AND c.plugin IS NOT NULL;

-- 6. 添加plugin字段索引
ALTER TABLE `pre_order` ADD INDEX `idx_plugin` (`plugin`);

-- 7. 检查表结构
-- DESCRIBE `pre_order`;

-- 8. 验证数据更新
-- SELECT trade_no, channel, plugin, typename, bill_trade_no, bill_mch_trade_no FROM `pre_order` LIMIT 10;
