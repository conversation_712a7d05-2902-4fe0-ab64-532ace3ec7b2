<template>
	<view class="container">
		<!-- 顶部标题栏 -->
		<view class="header">
			<view class="header-left">
				<uni-icons type="left" color="#FFFFFF" size="24" @click="goBack"></uni-icons>
				<text class="header-title">对账单</text>
			</view>
			<view class="header-right">
				<uni-icons type="calendar" color="#FFFFFF" size="24"></uni-icons>
				<uni-icons type="download" color="#FFFFFF" size="24" class="margin-left"></uni-icons>
			</view>
		</view>
		
		<!-- 月份选择 -->
		<view class="month-selector">
			<text class="month-label">本月</text>
			<view class="date-picker" @click="openDatePicker">
				<text>{{ selectedMonth }}</text>
				<uni-icons type="bottom" size="16" color="#666666"></uni-icons>
			</view>
		</view>
		
		<!-- 总收入概览 -->
		<view class="income-summary">
			<view v-if="loading" class="loading-container">
				<text class="loading-text">加载中...</text>
			</view>
			<view v-else>
				<view class="summary-row">
					<view class="summary-item">
						<text class="item-label">总收入(元)</text>
						<text class="item-value">¥ {{ totalIncome }}</text>
					</view>
					<view class="summary-item">
						<text class="item-label">总笔数</text>
						<text class="item-value">{{ totalCount }}</text>
					</view>
				</view>
				<view class="summary-row">
					<view class="summary-item">
						<text class="item-label">已结算(元)</text>
						<text class="item-value income">¥ {{ settledAmount }}</text>
					</view>
					<view class="summary-item">
						<text class="item-label">待结算(元)</text>
						<text class="item-value pending">¥ {{ pendingAmount }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 支付渠道对账 -->
		<view class="channel-section">
			<view class="section-header">
				<text class="section-title">支付渠道对账</text>
				<view class="section-actions">
					<text class="action-text">筛选</text>
					<text class="action-text margin-left">导出</text>
				</view>
			</view>
			
			<!-- 微信支付 -->
			<view class="payment-channel">
				<view class="channel-left">
					<view class="channel-icon wechat">
						<image src="/static/home/<USER>" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
					</view>
					<view class="channel-info">
						<text class="channel-name">微信支付</text>
						<text class="channel-count">{{ wechatInfo.count }}笔交易</text>
					</view>
				</view>
				<view class="channel-right">
					<text class="channel-amount">¥ {{ wechatInfo.amount }}</text>
				</view>
			</view>
			<view class="settlement-info wechat">
				<text>已结算：¥ {{ wechatInfo.settled }}</text>
				<text>待结算：¥ {{ wechatInfo.pending }}</text>
			</view>
			
			<!-- 支付宝 -->
			<view class="payment-channel">
				<view class="channel-left">
					<view class="channel-icon alipay">
						<image src="/static/home/<USER>" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
					</view>
					<view class="channel-info">
						<text class="channel-name">支付宝</text>
						<text class="channel-count">{{ alipayInfo.count }}笔交易</text>
					</view>
				</view>
				<view class="channel-right">
					<text class="channel-amount">¥ {{ alipayInfo.amount }}</text>
				</view>
			</view>
			<view class="settlement-info alipay">
				<text>已结算：¥ {{ alipayInfo.settled }}</text>
				<text>待结算：¥ {{ alipayInfo.pending }}</text>
			</view>
			
			<!-- 云闪付 -->
			<view class="payment-channel">
				<view class="channel-left">
					<view class="channel-icon cloudpay">
						<image src="/static/home/<USER>" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
					</view>
					<view class="channel-info">
						<text class="channel-name">云闪付</text>
						<text class="channel-count">{{ cloudpayInfo.count }}笔交易</text>
					</view>
				</view>
				<view class="channel-right">
					<text class="channel-amount">¥ {{ cloudpayInfo.amount }}</text>
				</view>
			</view>
			<view class="settlement-info cloudpay">
				<text>已结算：¥ {{ cloudpayInfo.settled }}</text>
				<text>待结算：¥ {{ cloudpayInfo.pending }}</text>
			</view>
		</view>
		
		<!-- 日对账单 -->
		<view class="daily-section">
			<text class="daily-title">日对账单</text>
			
			<view v-for="(item, index) in dailyList" :key="index" class="daily-item" :class="{'last-item': index === dailyList.length - 1}">
				<view class="daily-left">
					<view class="daily-date">
						<text class="date-month">{{ item.month }}</text>
						<text class="date-day">{{ item.day }}</text>
					</view>
					<view class="daily-info">
						<text class="daily-weekday">{{ item.weekday }}</text>
						<text class="daily-count">{{ item.count }}笔交易</text>
					</view>
				</view>
				<view class="daily-right">
					<text class="daily-amount">¥ {{ item.amount }}</text>
					<text class="daily-status" :class="{settled: item.status === '已结算', pending: item.status === '待结算'}">{{ item.status }}</text>
				</view>
			</view>
		</view>
		
		<view class="view-all">
			<text>查看全部</text>
		</view>
		
		<!-- 月份选择器弹窗 -->
		<view class="month-picker-popup" v-if="showMonthPicker">
			<view class="month-picker-mask" @click="cancelMonthPicker"></view>
			<view class="month-picker-container">
				<view class="month-picker-header">
					<view class="month-picker-action" @click="cancelMonthPicker">取消</view>
					<view class="month-picker-title">选择月份</view>
					<view class="month-picker-action confirm" @click="confirmMonthPicker">确定</view>
				</view>
				<picker-view 
					class="month-picker-view" 
					:value="monthPickerValue" 
					@change="onMonthPickerChange"
				>
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in years" :key="'year-'+index">{{ item }}年</view>
					</picker-view-column>
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in months" :key="'month-'+index">{{ item }}月</view>
					</picker-view-column>
				</picker-view>
			</view>
		</view>
	</view>
</template>

<script>
import { request } from '@/utils/request.js'
import { checkLoginStatus, redirectToLogin } from '@/utils/auth.js'

export default {
	data() {
		const currentDate = new Date();
		const currentYear = currentDate.getFullYear();
		const currentMonth = currentDate.getMonth() + 1;

		return {
			dateValue: '',
			selectedMonth: `${currentYear}年${currentMonth}月`,
			startDate: '2024-01',
			endDate: '2025-12',
			// 统计数据
			totalIncome: '0.00',
			totalCount: 0,
			settledAmount: '0.00',
			pendingAmount: '0.00',
			// 加载状态
			loading: false,
			
			// 月份选择器相关数据
			showMonthPicker: false,
			monthPickerValue: [1, 3], // 默认选中2025年4月
			years: [2024, 2025, 2026, 2027, 2028],
			months: Array.from({length: 12}, (_, i) => i + 1),
			tempSelectedMonth: '',
			
			// 支付渠道信息
			wechatInfo: {
				count: 0,
				amount: '0.00',
				settled: '0.00',
				pending: '0.00'
			},
			alipayInfo: {
				count: 0,
				amount: '0.00',
				settled: '0.00',
				pending: '0.00'
			},
			cloudpayInfo: {
				count: 0,
				amount: '0.00',
				settled: '0.00',
				pending: '0.00'
			},
			
			// 日对账单列表
			dailyList: []
		}
	},
	async onLoad() {
		console.log('📊 对账单页面加载开始');

		// 检查登录状态
		const isLoggedIn = await checkLoginStatus();
		if (!isLoggedIn) {
			redirectToLogin();
			return;
		}

		// 设置当前日期为默认值
		const now = new Date();
		const currentYear = now.getFullYear();
		const currentMonth = now.getMonth() + 1;

		this.dateValue = `${currentYear}-${String(currentMonth).padStart(2, '0')}`;
		this.selectedMonth = `${currentYear}年${currentMonth}月`;

		// 初始化月份选择器的值
		const yearIndex = this.years.findIndex(y => y === currentYear);
		this.monthPickerValue = [
			yearIndex >= 0 ? yearIndex : 0,
			currentMonth - 1
		];

		// 加载初始数据
		await this.loadData(currentYear, currentMonth);
	},

	onShow() {
		console.log('🔄 对账单页面显示');
		// 页面显示时刷新数据
		const now = new Date();
		this.loadData(now.getFullYear(), now.getMonth() + 1);
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		openDatePicker() {
			// 打开月份选择器
			this.showMonthPicker = true;
		},
		onMonthPickerChange(e) {
			this.monthPickerValue = e.detail.value;
			this.updateTempSelectedMonth();
		},
		updateTempSelectedMonth() {
			const year = this.years[this.monthPickerValue[0]];
			const month = this.months[this.monthPickerValue[1]];
			
			// 格式化为YYYY-MM
			this.tempSelectedMonth = `${year}-${month.toString().padStart(2, '0')}`;
		},
		cancelMonthPicker() {
			this.showMonthPicker = false;
			this.tempSelectedMonth = '';
		},
		async confirmMonthPicker() {
			if (this.tempSelectedMonth) {
				const date = new Date(this.tempSelectedMonth);
				const year = date.getFullYear();
				const month = date.getMonth() + 1;
				this.selectedMonth = `${year}年${month}月`;
				this.dateValue = this.tempSelectedMonth;

				// 重新加载数据
				await this.loadData(year, month);
			}

			this.showMonthPicker = false;
			this.tempSelectedMonth = '';
		},
		onDateChange(e) {
			// 日期选择器值变化（废弃）
			if (e) {
				const date = new Date(e);
				const year = date.getFullYear();
				const month = date.getMonth() + 1;
				this.selectedMonth = `${year}年${month}月`;
				this.loadData(year, month);
			}
		},
		// 📊 加载对账数据
		async loadData(year, month) {
			try {
				console.log(`📊 加载${year}年${month}月的对账数据`);
				this.loading = true;

				// 构建查询时间范围
				const starttime = `${year}-${String(month).padStart(2, '0')}-01`;
				const endtime = `${year}-${String(month).padStart(2, '0')}-${new Date(year, month, 0).getDate()}`;

				// 并行加载多个数据
				await Promise.all([
					this.loadStatistics(starttime, endtime),
					this.loadChannelData(starttime, endtime),
					this.loadDailyData(starttime, endtime)
				]);

				console.log('✅ 对账数据加载完成');

			} catch (error) {
				console.error('❌ 加载对账数据失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 📈 加载统计数据
		async loadStatistics(starttime, endtime) {
			try {
				const response = await request({
					url: '/user/ajax2.php',
					method: 'POST',
					data: {
						act: 'statistics',
						starttime: starttime,
						endtime: endtime,
						dstatus: 1 // 只统计已支付订单
					}
				});

				console.log('📈 统计数据响应:', response);

				if (response.code === 0 && response.data) {
					const data = response.data;
					this.totalIncome = this.formatMoney(data.successMoney || 0);
					this.totalCount = parseInt(data.successCount || 0);

					// 计算已结算和待结算金额（这里需要额外的接口来获取结算状态）
					await this.loadSettlementData(starttime, endtime);
				}

			} catch (error) {
				console.error('❌ 加载统计数据失败:', error);
			}
		},

		// 💰 加载结算数据
		async loadSettlementData(starttime, endtime) {
			try {
				// 获取结算列表来计算已结算和待结算金额
				const response = await request({
					url: '/user/ajax2.php',
					method: 'POST',
					data: {
						act: 'settleList',
						dstatus: -1, // 获取所有状态的结算记录
						offset: 0,
						limit: 1000 // 获取足够多的记录来计算
					}
				});

				if (response.total !== undefined && response.rows) {
					let settled = 0;
					let pending = 0;

					response.rows.forEach(item => {
						const itemDate = item.addtime ? item.addtime.split(' ')[0] : '';
						if (itemDate >= starttime && itemDate <= endtime) {
							const money = parseFloat(item.money || 0);
							if (item.status === 1) {
								settled += money;
							} else if (item.status === 0) {
								pending += money;
							}
						}
					});

					this.settledAmount = this.formatMoney(settled);
					this.pendingAmount = this.formatMoney(pending);
				}

			} catch (error) {
				console.error('❌ 加载结算数据失败:', error);
			}
		},

		// 🏦 加载支付渠道数据
		async loadChannelData(starttime, endtime) {
			try {
				// 获取各支付渠道的统计数据
				const channels = [
					{ id: 'wechat', name: '微信支付', key: 'wechatInfo' },
					{ id: 'alipay', name: '支付宝', key: 'alipayInfo' },
					{ id: 'cloudpay', name: '云闪付', key: 'cloudpayInfo' }
				];

				for (const channel of channels) {
					try {
						// 这里可以根据实际的支付类型ID来查询
						// 暂时使用模拟数据，实际应该根据 pre_type 表的数据来查询
						const response = await request({
							url: '/user/ajax2.php',
							method: 'POST',
							data: {
								act: 'statistics',
								starttime: starttime,
								endtime: endtime,
								dstatus: 1
								// paytype: channelTypeId // 需要根据实际的支付类型ID
							}
						});

						if (response.code === 0 && response.data) {
							// 暂时平均分配数据，实际应该根据具体的支付类型来查询
							const totalMoney = parseFloat(response.data.successMoney || 0);
							const totalCount = parseInt(response.data.successCount || 0);

							// 简单的模拟分配（实际应该有具体的渠道统计）
							const ratio = channel.id === 'wechat' ? 0.5 : (channel.id === 'alipay' ? 0.3 : 0.2);

							this[channel.key] = {
								count: Math.floor(totalCount * ratio),
								amount: this.formatMoney(totalMoney * ratio),
								settled: this.formatMoney(totalMoney * ratio * 0.8),
								pending: this.formatMoney(totalMoney * ratio * 0.2)
							};
						}

					} catch (error) {
						console.error(`❌ 加载${channel.name}数据失败:`, error);
					}
				}

			} catch (error) {
				console.error('❌ 加载支付渠道数据失败:', error);
			}
		},

		// 📅 加载日对账数据
		async loadDailyData(starttime, endtime) {
			try {
				// 获取订单列表按日期分组
				const response = await request({
					url: '/user/ajax2.php',
					method: 'POST',
					data: {
						act: 'orderList',
						starttime: starttime,
						endtime: endtime,
						dstatus: 1, // 只查询已支付订单
						offset: 0,
						limit: 1000
					}
				});

				if (response.total !== undefined && response.rows) {
					// 按日期分组统计
					const dailyStats = {};

					response.rows.forEach(order => {
						const date = order.addtime ? order.addtime.split(' ')[0] : '';
						if (!dailyStats[date]) {
							dailyStats[date] = {
								count: 0,
								amount: 0
							};
						}
						dailyStats[date].count++;
						dailyStats[date].amount += parseFloat(order.money || 0);
					});

					// 转换为列表格式
					this.dailyList = Object.keys(dailyStats)
						.sort((a, b) => new Date(b) - new Date(a)) // 按日期倒序
						.slice(0, 10) // 只显示最近10天
						.map(date => {
							const dateObj = new Date(date);
							const today = new Date();
							const yesterday = new Date(today);
							yesterday.setDate(yesterday.getDate() - 1);

							let weekday = '';
							if (date === today.toISOString().split('T')[0]) {
								weekday = '今天';
							} else if (date === yesterday.toISOString().split('T')[0]) {
								weekday = '昨天';
							} else {
								const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
								weekday = weekdays[dateObj.getDay()];
							}

							return {
								month: `${dateObj.getMonth() + 1}月`,
								day: dateObj.getDate().toString(),
								weekday: weekday,
								count: dailyStats[date].count,
								amount: this.formatMoney(dailyStats[date].amount),
								status: '已结算' // 简化处理，实际需要查询结算状态
							};
						});
				}

			} catch (error) {
				console.error('❌ 加载日对账数据失败:', error);
			}
		},

		// 💰 格式化金额
		formatMoney(amount) {
			const num = parseFloat(amount || 0);
			return num.toLocaleString('zh-CN', {
				minimumFractionDigits: 2,
				maximumFractionDigits: 2
			});
		}
	}
}
</script>

<style>
page {
	font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
	background-color: #f5f5f5;
	font-weight: 450;
}

.container {
	width: 100%;
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 顶部标题栏 */
.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #5145F7;
	color: white;
	padding: 20rpx 32rpx;
	box-sizing: border-box;
}

.header-left {
	display: flex;
	align-items: center;
}

.header-title {
	font-size: 34rpx;
	font-weight: 550;
	margin-left: 16rpx;
}

.header-right {
	display: flex;
	align-items: center;
}

.margin-left {
	margin-left: 32rpx;
}

/* 月份选择器 */
.month-selector {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 32rpx;
	background-color: #FFFFFF;
	border-bottom: 1px solid #EEEEEE;
}

.month-label {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
}

.date-picker {
	display: flex;
	align-items: center;
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
}

/* 总收入概览 */
.income-summary {
	padding: 24rpx 32rpx;
	background-color: #FFFFFF;
	margin-bottom: 20rpx;
}

.summary-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.summary-row:last-child {
	margin-bottom: 0;
}

.summary-item {
	width: 48%;
}

.item-label {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 12rpx;
	display: block;
	font-weight: 500;
}

.item-value {
	font-size: 34rpx;
	color: #333333;
	font-weight: 650;
}

.item-value.income {
	color: #333333;
}

.item-value.pending {
	color: #333333;
}

/* 支付渠道对账 */
.channel-section {
	background-color: #FFFFFF;
	margin-bottom: 20rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 32rpx;
	border-bottom: 1px solid #EEEEEE;
}

.section-title {
	font-size: 30rpx;
	color: #333333;
	font-weight: 550;
}

.section-actions {
	display: flex;
	align-items: center;
}

.action-text {
	font-size: 26rpx;
	color: #5145F7;
	font-weight: 500;
}

.payment-channel {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 32rpx;
}

.channel-left {
	display: flex;
	align-items: center;
}

.channel-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.channel-icon.wechat {
	background-color: #E4FFEF;
}

.channel-icon.alipay {
	background-color: #E9EFFF;
}

.channel-icon.cloudpay {
	background-color: #FFE4E4;
}

.channel-info {
	display: flex;
	flex-direction: column;
}

.channel-name {
	font-size: 30rpx;
	color: #333333;
	margin-bottom: 8rpx;
	font-weight: 550;
}

.channel-count {
	font-size: 24rpx;
	color: #999999;
	font-weight: 450;
}

.channel-amount {
	font-size: 32rpx;
	color: #333333;
	font-weight: 600;
}

.settlement-info {
	display: flex;
	justify-content: space-between;
	padding: 0 32rpx 24rpx 130rpx;
	font-size: 24rpx;
	color: #999999;
	border-bottom: 1px solid #EEEEEE;
	font-weight: 450;
}

.settlement-info:last-child {
	border-bottom: none;
}

/* 日对账单 */
.daily-section {
	background-color: #FFFFFF;
	padding-bottom: 20rpx;
}

.daily-title {
	font-size: 30rpx;
	color: #333333;
	font-weight: 550;
	padding: 24rpx 32rpx;
	border-bottom: 1px solid #EEEEEE;
	display: block;
}

.daily-item {
	display: flex;
	justify-content: space-between;
	padding: 24rpx 32rpx;
	border-bottom: 1px solid #EEEEEE;
}

.daily-item.last-item {
	border-bottom: none;
}

.daily-left {
	display: flex;
	align-items: center;
}

.daily-date {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 24rpx;
}

.date-month {
	font-size: 26rpx;
	color: #666666;
}

.date-day {
	font-size: 36rpx;
	color: #5145F7;
	font-weight: 650;
}

.daily-info {
	display: flex;
	flex-direction: column;
}

.daily-weekday {
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 8rpx;
	font-weight: 500;
}

.daily-count {
	font-size: 24rpx;
	color: #999999;
}

.daily-right {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.daily-amount {
	font-size: 32rpx;
	color: #333333;
	font-weight: 600;
	margin-bottom: 8rpx;
}

.daily-status {
	font-size: 24rpx;
}

.daily-status.settled {
	color: #4CD964;
}

.daily-status.pending {
	color: #999999;
}

.view-all {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 24rpx 0;
	font-size: 28rpx;
	color: #5145F7;
	border-radius: 16rpx;
	margin: 20rpx 32rpx;
	background-color: #fff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	font-weight: 550;
}

/* 月份选择器弹窗样式 */
.month-picker-popup {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 999;
}

.month-picker-mask {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
}

.month-picker-container {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: white;
	border-radius: 24rpx 24rpx 0 0;
	overflow: hidden;
}

.month-picker-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 30rpx;
	border-bottom: 1px solid #eaeaea;
}

.month-picker-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.month-picker-action {
	font-size: 30rpx;
	color: #666;
}

.month-picker-action.confirm {
	color: #5145F7;
}

.month-picker-view {
	width: 100%;
	height: 480rpx;
}

.picker-item {
	line-height: 80rpx;
	text-align: center;
	font-size: 32rpx;
	color: #333;
}

/* 加载状态样式 */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 60rpx 0;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}

/* Hide any checkboxes or date selection elements that might appear at the bottom */
.uni-checkbox,
.uni-checkbox-wrapper,
[class*="checkbox"],
[class*="选择日期"] {
    display: none !important;
}
</style> 