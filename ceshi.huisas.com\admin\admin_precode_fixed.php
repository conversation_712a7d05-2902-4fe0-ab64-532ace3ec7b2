<?php
/**
 * 预制收款码管理 - 修复版本
 */
include("../includes/common.php");
$title='预制收款码管理';
include './head.php';
if($islogin==1){}else exit("<script language='javascript'>window.location.href='./login.php';</script>");

// 关闭错误显示
error_reporting(0);
ini_set('display_errors', 0);

// 检查并创建表
try {
    $DB->exec("CREATE TABLE IF NOT EXISTS `{$dbconfig['dbqz']}_precode` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `code` varchar(50) NOT NULL COMMENT '预制码',
        `qr_url` text COMMENT '完整的二维码URL',
        `uid` int(11) DEFAULT NULL COMMENT '绑定的商户ID',
        `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0未绑定 1已绑定',
        `addtime` datetime DEFAULT NULL COMMENT '生成时间',
        `bindtime` datetime DEFAULT NULL COMMENT '绑定时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `code` (`code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预制收款码';");
} catch(Exception $e) {
    // 忽略表已存在的错误
}

// 处理批量生成
if(isset($_POST['num'])){
    header('Content-Type: application/json; charset=utf-8');
    $num = intval($_POST['num']);
    if($num <= 0 || $num > 100){
        exit(json_encode(['code'=>-1, 'msg'=>'生成数量必须在1-100之间'], JSON_UNESCAPED_UNICODE));
    }
    
    $success = 0;
    $codes = array();
    
    try {
        for($i=0; $i<$num; $i++){
            $code = generatePreCode();
            if(!$code) continue;
            $qr_url = $siteurl . 'paypage/precode/' . $code;
            $result = $DB->exec("INSERT INTO `{$dbconfig['dbqz']}_precode` (`code`, `qr_url`, `addtime`) VALUES (?, ?, NOW())", [$code, $qr_url]);
            if($result){
                $success++;
                $codes[] = $code;
            }
        }
        $codes_str = implode(", ", $codes);
        exit(json_encode(['code'=>0, 'msg'=>"成功生成 {$success} 个预制码: {$codes_str}"], JSON_UNESCAPED_UNICODE));
    } catch (Exception $e) {
        exit(json_encode(['code'=>-1, 'msg'=>'生成失败：'.$e->getMessage()], JSON_UNESCAPED_UNICODE));
    }
}

// 处理删除
if(isset($_GET['action']) && $_GET['action']=='delete' && isset($_GET['id'])){
    $id = intval($_GET['id']);
    try {
        $row = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_precode` WHERE `id`=? LIMIT 1", [$id]);
        if(!$row){
            $msg = '预制码不存在';
        } elseif($row['status'] == 1){
            $msg = '已绑定的预制码不能删除';
        } else {
            $result = $DB->exec("DELETE FROM `{$dbconfig['dbqz']}_precode` WHERE `id`=?", [$id]);
            $msg = $result ? '删除成功' : '删除失败';
        }
    } catch(Exception $e) {
        $msg = '删除失败：' . $e->getMessage();
    }
}

// 分页和查询
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$pagesize = 20;
$offset = ($page - 1) * $pagesize;

try {
    $numrows = $DB->getColumn("SELECT COUNT(*) from `{$dbconfig['dbqz']}_precode`");
    $pages = ceil($numrows / $pagesize);
    $list = $DB->getAll("SELECT a.*,b.username FROM `{$dbconfig['dbqz']}_precode` a LEFT JOIN `{$dbconfig['dbqz']}_user` b ON a.uid=b.uid ORDER BY a.id DESC LIMIT $offset,$pagesize");
} catch(Exception $e) {
    $numrows = 0;
    $pages = 0;
    $list = [];
}
?>

<div class="container" style="padding-top:70px;">
<div class="col-md-12 center-block" style="float: none;">
<div class="panel panel-primary">
<div class="panel-heading"><h3 class="panel-title">预制收款码管理</h3></div>
<div class="panel-body">

<?php if(isset($msg)): ?>
<div class="alert alert-info"><?php echo htmlspecialchars($msg); ?></div>
<?php endif; ?>

<p>
    <button type="button" class="btn btn-success" onclick="showGenerateModal()">批量生成</button>
    <span class="pull-right">共有 <?php echo $numrows; ?> 条记录</span>
</p>

<div class="table-responsive">
<table class="table table-striped table-bordered">
<thead>
    <tr>
        <th>ID</th>
        <th>预制收款码</th>
        <th>绑定商户</th>
        <th>生成时间</th>
        <th>状态</th>
        <th>操作</th>
    </tr>
</thead>
<tbody>
<?php if($list): ?>
    <?php foreach($list as $row): ?>
    <tr>
        <td><?php echo $row['id']; ?></td>
        <td><?php echo htmlspecialchars($row['code']); ?></td>
        <td><?php echo $row['username'] ? htmlspecialchars($row['username']) : '未绑定'; ?></td>
        <td><?php echo $row['addtime']; ?></td>
        <td>
            <?php if($row['status'] == 1): ?>
                <span class="label label-success">已绑定</span>
            <?php else: ?>
                <span class="label label-default">未绑定</span>
            <?php endif; ?>
        </td>
        <td>
            <button class="btn btn-xs btn-info" onclick="copyCode('<?php echo $row['code']; ?>')">复制</button>
            <a href="<?php echo $row['qr_url']; ?>" target="_blank" class="btn btn-xs btn-success">查看</a>
            <?php if($row['status'] == 0): ?>
                <a href="?action=delete&id=<?php echo $row['id']; ?>" class="btn btn-xs btn-danger" onclick="return confirm('确定要删除此预制码吗？')">删除</a>
            <?php endif; ?>
        </td>
    </tr>
    <?php endforeach; ?>
<?php else: ?>
    <tr><td colspan="6" class="text-center">暂无记录</td></tr>
<?php endif; ?>
</tbody>
</table>
</div>

<!-- 简单分页 -->
<?php if($pages > 1): ?>
<nav>
    <ul class="pagination">
        <?php for($i = 1; $i <= $pages; $i++): ?>
            <li <?php echo $i == $page ? 'class="active"' : ''; ?>>
                <a href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
            </li>
        <?php endfor; ?>
    </ul>
</nav>
<?php endif; ?>

</div>
</div>
</div>
</div>

<!-- 生成模态框 -->
<div id="generateModal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:white; padding:20px; border:2px solid #ccc; border-radius:8px; z-index:9999;">
    <h4>批量生成预制码</h4>
    <p>
        <label>生成数量（1-100）：</label>
        <input type="number" id="generateNum" min="1" max="100" value="10" style="width:100px;">
    </p>
    <p>
        <button type="button" class="btn btn-primary" onclick="doGenerate()">生成</button>
        <button type="button" class="btn btn-default" onclick="hideGenerateModal()">取消</button>
    </p>
</div>

<script>
function showGenerateModal() {
    document.getElementById('generateModal').style.display = 'block';
}

function hideGenerateModal() {
    document.getElementById('generateModal').style.display = 'none';
}

function copyCode(code) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(code).then(function() {
            alert('复制成功：' + code);
        });
    } else {
        var temp = document.createElement('input');
        temp.value = code;
        document.body.appendChild(temp);
        temp.select();
        document.execCommand('copy');
        document.body.removeChild(temp);
        alert('复制成功：' + code);
    }
}

function doGenerate() {
    var num = document.getElementById('generateNum').value;
    if(!num || num < 1 || num > 100) {
        alert('请输入1-100之间的数字');
        return;
    }
    
    var formData = new FormData();
    formData.append('num', num);
    
    fetch('admin_precode_fixed.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if(data.code == 0) {
            alert('生成成功：' + data.msg);
            location.reload();
        } else {
            alert('生成失败：' + data.msg);
        }
    })
    .catch(error => {
        alert('请求失败：' + error.message);
    });
    
    hideGenerateModal();
}
</script>

</body>
</html>
