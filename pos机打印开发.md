
🖨️ 打印小票功能开发方案
📋 1. 功能需求分析
核心功能
✅ 支付成功后自动打印小票
✅ 手动补打小票功能
✅ 打印机设置和管理
✅ 小票模板自定义
✅ 打印历史记录
业务场景
🛒 顾客扫码支付后自动打印小票
📄 商户可以补打历史订单小票
⚙️ 支持多种打印机类型
🎨 可自定义小票内容和样式
🏗️ 2. 技术方案设计
A. 打印机支持方案
// 支持的打印机类型
const PRINTER_TYPES = {
  // 1. 网络打印机（推荐）
  NETWORK: {
    type: 'network',
    protocols: ['TCP', 'HTTP'],
    advantages: ['无需驱动', '跨平台', '远程打印'],
    examples: ['佳博GP-3120TU', '汉印D35', '芯烨XP-365B']
  },
  
  // 2. USB打印机
  USB: {
    type: 'usb',
    protocols: ['USB'],
    advantages: ['稳定可靠', '速度快'],
    limitations: ['需要驱动', '仅限本地']
  },
  
  // 3. 蓝牙打印机（移动端）
  BLUETOOTH: {
    type: 'bluetooth',
    protocols: ['Bluetooth'],
    advantages: ['移动便携', '无线连接'],
    platforms: ['APP', '小程序']
  },
  
  // 4. 云打印服务
  CLOUD: {
    type: 'cloud',
    services: ['飞鹅云打印', '易联云', '美团云打印'],
    advantages: ['无需本地设备', '远程管理']
  }
}

B. 技术实现方案

// 1. H5/Web端方案
const WEB_SOLUTIONS = {
  // 方案1：ESC/POS指令 + 网络打印机（推荐）
  escpos_network: {
    description: '通过TCP/HTTP发送ESC/POS指令到网络打印机',
    implementation: 'WebSocket/HTTP API',
    compatibility: '✅ 跨平台',
    setup_difficulty: '⭐⭐⭐'
  },
  
  // 方案2：浏览器打印API + 热敏打印机
  browser_print: {
    description: '使用window.print()配合CSS样式',
    implementation: 'CSS @media print',
    compatibility: '✅ 所有浏览器',
    setup_difficulty: '⭐⭐'
  },
  
  // 方案3：云打印服务
  cloud_print: {
    description: '通过第三方云打印服务',
    implementation: 'API调用',
    compatibility: '✅ 跨平台',
    setup_difficulty: '⭐'
  }
}

// 2. APP端方案
const APP_SOLUTIONS = {
  // 蓝牙打印
  bluetooth: {
    plugins: ['cordova-plugin-bluetooth-serial'],
    protocols: ['ESC/POS'],
    compatibility: '✅ iOS/Android'
  },
  
  // USB打印（Android）
  usb: {
    plugins: ['cordova-plugin-usb'],
    protocols: ['ESC/POS'],
    compatibility: '✅ Android'
  }
}

🎨 3. 小票模板设计
A. 标准小票模板
const RECEIPT_TEMPLATE = {
  // 小票头部
  header: {
    merchantName: '商户名称',
    merchantPhone: '联系电话',
    merchantAddress: '商户地址',
    logo: '商户LOGO（可选）'
  },
  
  // 订单信息
  order: {
    orderNo: '订单号',
    payTime: '支付时间',
    payMethod: '支付方式',
    amount: '支付金额',
    discountAmount: '优惠金额（可选）',
    actualAmount: '实付金额'
  },
  
  // 商品明细（可选）
  items: [
    {
      name: '商品名称',
      quantity: '数量',
      price: '单价',
      total: '小计'
    }
  ],
  
  // 小票尾部
  footer: {
    thankYou: '感谢您的惠顾',
    contact: '如有问题请联系商户',
    qrCode: '商户二维码（可选）',
    timestamp: '打印时间'
  }
}

B. 小票样式配置

const RECEIPT_STYLES = {
  // 纸张设置
  paper: {
    width: '58mm', // 58mm/80mm
    margin: '2mm',
    lineHeight: '24px'
  },
  
  // 字体设置
  fonts: {
    title: { size: '16px', weight: 'bold', align: 'center' },
    content: { size: '12px', weight: 'normal', align: 'left' },
    amount: { size: '14px', weight: 'bold', align: 'right' }
  },
  
  // 分割线
  divider: {
    style: 'dashed',
    length: '32'
  }
}

 4. 文件结构设计
 src/
├── components/
│   └── printer/
│       ├── PrinterSetup.vue          # 打印机设置
│       ├── ReceiptTemplate.vue       # 小票模板
│       ├── PrintPreview.vue          # 打印预览
│       └── PrintHistory.vue          # 打印历史
├── utils/
│   └── printer/
│       ├── printerManager.js         # 打印机管理
│       ├── receiptGenerator.js       # 小票生成
│       ├── escposCommands.js         # ESC/POS指令
│       └── cloudPrint.js             # 云打印服务
├── api/
│   └── printer.js                    # 打印相关API
├── pages/
│   └── settings/
│       └── printer/
│           ├── index.vue             # 打印设置主页
│           ├── setup.vue             # 打印机配置
│           └── template.vue          # 模板设置
└── mixins/
    └── printerMixin.js               # 打印功能混入

     5. 核心模块设计

     A. 打印机管理器

     // utils/printer/printerManager.js
class PrinterManager {
  constructor() {
    this.printers = new Map()
    this.currentPrinter = null
    this.config = this.loadConfig()
  }
  
  // 添加打印机
  async addPrinter(config) {
    const printer = new Printer(config)
    await printer.connect()
    this.printers.set(config.id, printer)
    return printer
  }
  
  // 设置默认打印机
  setDefaultPrinter(printerId) {
    this.currentPrinter = this.printers.get(printerId)
    this.saveConfig()
  }
  
  // 打印小票
  async printReceipt(orderData, template = 'default') {
    if (!this.currentPrinter) {
      throw new Error('未设置默认打印机')
    }
    
    const receipt = this.generateReceipt(orderData, template)
    return await this.currentPrinter.print(receipt)
  }
  
  // 生成小票内容
  generateReceipt(orderData, template) {
    const generator = new ReceiptGenerator(template)
    return generator.generate(orderData)
  }
}

B. 小票生成器
// utils/printer/receiptGenerator.js
class ReceiptGenerator {
  constructor(template) {
    this.template = template
    this.escpos = new ESCPOSCommands()
  }
  
  generate(orderData) {
    const commands = []
    
    // 初始化
    commands.push(this.escpos.init())
    
    // 打印头部
    commands.push(...this.generateHeader(orderData))
    
    // 打印订单信息
    commands.push(...this.generateOrderInfo(orderData))
    
    // 打印商品明细
    if (orderData.items) {
      commands.push(...this.generateItems(orderData.items))
    }
    
    // 打印尾部
    commands.push(...this.generateFooter(orderData))
    
    // 切纸
    commands.push(this.escpos.cut())
    
    return commands.join('')
  }
  
  generateHeader(data) {
    const commands = []
    
    // 商户名称
    commands.push(this.escpos.setAlign('center'))
    commands.push(this.escpos.setTextSize(2, 2))
    commands.push(this.escpos.text(data.merchantName))
    commands.push(this.escpos.newLine())
    
    // 分割线
    commands.push(this.escpos.setTextSize(1, 1))
    commands.push(this.escpos.text('--------------------------------'))
    commands.push(this.escpos.newLine())
    
    return commands
  }
}

C. ESC/POS指令封装

// utils/printer/escposCommands.js
class ESCPOSCommands {
  // 初始化
  init() {
    return '\x1B\x40' // ESC @
  }
  
  // 设置对齐方式
  setAlign(align) {
    const alignMap = {
      'left': '\x1B\x61\x00',
      'center': '\x1B\x61\x01',
      'right': '\x1B\x61\x02'
    }
    return alignMap[align] || alignMap.left
  }
  
  // 设置字体大小
  setTextSize(width, height) {
    const size = ((width - 1) << 4) | (height - 1)
    return '\x1D\x21' + String.fromCharCode(size)
  }
  
  // 打印文本
  text(content) {
    return content
  }
  
  // 换行
  newLine() {
    return '\x0A'
  }
  
  // 切纸
  cut() {
    return '\x1D\x56\x00'
  }
}

🔧 6. 集成到现有系统

A. 在WebSocket支付通知中集成

// 修改 mixins/websocketMixin.js
async handlePaymentNotification(paymentData) {
  try {
    // 现有的语音播报逻辑
    await this.playPaymentVoice(amount)
    
    // 新增：自动打印小票
    if (this.shouldAutoPrint()) {
      await this.printPaymentReceipt(paymentData)
    }
    
  } catch (error) {
    console.error('处理支付通知失败:', error)
  }
}

// 新增方法
async printPaymentReceipt(paymentData) {
  try {
    const printerManager = this.getPrinterManager()
    if (printerManager && printerManager.isEnabled()) {
      await printerManager.printReceipt(paymentData)
      console.log('✅ 小票打印成功')
    }
  } catch (error) {
    console.error('❌ 小票打印失败:', error)
  }
}

B. 设置页面集成

// 在设置页面添加打印机设置入口
const SETTINGS_MENU = [
  // 现有设置项...
  {
    title: '打印设置',
    icon: '🖨️',
    path: '/pages/settings/printer/index',
    description: '配置打印机和小票模板'
  }
]

📱 7. 不同平台实现策略

A. H5/Web端（推荐方案）
// 1. 网络打印机方案
const NetworkPrinter = {
  implementation: '通过HTTP/TCP发送ESC/POS指令',
  advantages: [
    '无需安装驱动',
    '跨平台兼容',
    '支持远程打印',
    '配置简单'
  ],
  setup: [
    '1. 购买支持网络的热敏打印机',
    '2. 连接到同一局域网',
    '3. 配置打印机IP地址',
    '4. 测试连接和打印'
  ]
}

// 2. 浏览器打印方案（备用）
const BrowserPrint = {
  implementation: '使用CSS样式优化的浏览器打印',
  advantages: [
    '无需额外设备',
    '兼容性最好',
    '成本最低'
  ],
  limitations: [
    '需要手动操作',
    '样式控制有限',
    '纸张浪费'
  ]
}

B. APP端


// 蓝牙打印方案
const BluetoothPrint = {
  plugins: ['cordova-plugin-bluetooth-serial'],
  implementation: '通过蓝牙发送ESC/POS指令',
  advantages: [
    '移动便携',
    '无需网络',
    '成本适中'
  ]
}

C. 小程序端

// 云打印方案
const CloudPrint = {
  services: ['飞鹅云打印', '易联云'],
  implementation: '通过API调用云打印服务',
  advantages: [
    '无需本地设备',
    '远程管理',
    '稳定可靠'
  ]
}

 9. 推荐实施方案
第一阶段：基础功能
✅ 实现网络打印机支持
✅ 基础小票模板
✅ 自动打印设置
✅ 手动补打功能
第二阶段：增强功能
✅ 自定义模板编辑器
✅ 多打印机管理
✅ 打印历史记录
✅ 云打印服务集成
第三阶段：高级功能
✅ 蓝牙打印支持（APP）
✅ 批量打印
✅ 打印统计分析
✅ 模板市场
🔍 10. 技术选型建议
最推荐方案: 网络热敏打印机 + ESC/POS指令

理由:

✅ 跨平台兼容性最好
✅ 无需安装驱动程序
✅ 支持远程打印
✅ 配置和维护简单
✅ 成本适中
✅ 打印速度快
✅ 小票质量好
这个方案怎么样？你觉得哪个方向最适合你的需求？我可以根据你的选择提供更详细的实现方案。