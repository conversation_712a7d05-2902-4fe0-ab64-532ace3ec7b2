<?php
include("../includes/common.php");
if($islogin2==1){}else exit("<script language='javascript'>window.location.href='./login.php';</script>");

// 添加CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

$title='付款码支付测试';
include './head.php';
?>

<div id="content" class="app-content" role="main">
    <div class="app-content-body">
        <div class="bg-light lter b-b wrapper-md hidden-print">
            <h1 class="m-n font-thin h3">付款码支付测试</h1>
        </div>
        <div class="wrapper-md control">
            <div class="row">
                <div class="col-md-8">
                    <div class="panel panel-default">
                        <div class="panel-heading font-bold">
                            <i class="fa fa-credit-card"></i>&nbsp;付款码支付测试
                        </div>
                        <div class="panel-body">
                            <form id="scanPayTestForm">
                                <div class="form-group">
                                    <label>收款金额</label>
                                    <input class="form-control" type="number" step="0.01" min="0.01" name="amount" id="amount" placeholder="请输入收款金额" required>
                                </div>
                                <div class="form-group">
                                    <label>商品名称</label>
                                    <input class="form-control" type="text" name="product_name" id="product_name" value="测试商品" placeholder="请输入商品名称">
                                </div>
                                <div class="form-group">
                                    <label>付款码</label>
                                    <input class="form-control" type="text" name="auth_code" id="auth_code" placeholder="请扫描客户付款码或手动输入18位数字" maxlength="18" pattern="\d{18}">
                                    <small class="help-block">
                                        支付宝：25-30开头，微信：10-15开头，银联：62开头
                                        <br>测试付款码示例：
                                        <br>支付宝：<code>280060801797506151</code>
                                        <br>微信：<code>134567890123456789</code>
                                        <br>银联：<code>621234567890123456</code>
                                    </small>
                                </div>
                                <div class="form-group">
                                    <button type="button" id="testScanPay" class="btn btn-success form-control">
                                        <i class="fa fa-qrcode"></i> 测试付款码支付
                                    </button>
                                </div>
                            </form>

                            <!-- 测试结果显示区域 -->
                            <div id="testResult" style="display:none; margin-top: 20px;">
                                <h4>测试结果</h4>
                                <div id="resultContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="panel panel-default">
                        <div class="panel-heading font-bold">
                            <i class="fa fa-info-circle"></i>&nbsp;测试说明
                        </div>
                        <div class="panel-body">
                            <h5>功能说明</h5>
                            <p>此页面用于测试付款码支付功能，验证：</p>
                            <ul>
                                <li>付款码格式识别</li>
                                <li>支付通道调用</li>
                                <li>参数传递正确性</li>
                                <li>错误处理机制</li>
                            </ul>
                            
                            <h5>测试步骤</h5>
                            <ol>
                                <li>输入测试金额</li>
                                <li>输入商品名称</li>
                                <li>输入测试付款码</li>
                                <li>点击测试按钮</li>
                                <li>查看返回结果</li>
                            </ol>
                            
                            <h5>注意事项</h5>
                            <p class="text-warning">
                                <i class="fa fa-warning"></i> 
                                这是测试环境，请使用测试付款码，避免产生实际扣费。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'foot.php';?>
<script src="<?php echo $cdnpublic?>layer/3.1.1/layer.min.js"></script>

<script>
$(document).ready(function(){
    // 测试付款码支付
    $('#testScanPay').click(function(){
        var amount = $('#amount').val();
        var product_name = $('#product_name').val();
        var auth_code = $('#auth_code').val();

        // 验证输入
        if(!amount || parseFloat(amount) <= 0) {
            layer.msg('请输入正确的收款金额', {icon: 2});
            return;
        }
        if(!product_name.trim()) {
            layer.msg('请输入商品名称', {icon: 2});
            return;
        }
        if(!auth_code || !/^\d{18}$/.test(auth_code)) {
            layer.msg('请输入正确的18位付款码', {icon: 2});
            return;
        }

        // 显示处理中状态
        $('#testScanPay').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 测试中...');
        $('#testResult').show();
        $('#resultContent').html('<div class="alert alert-info"><i class="fa fa-spinner fa-spin"></i> 正在测试付款码支付...</div>');

        // 调用测试接口
        $.ajax({
            url: 'ajax2.php?act=scan_pay',
            type: 'POST',
            dataType: 'json',
            data: {
                amount: amount,
                product_name: product_name,
                auth_code: auth_code,
                out_trade_no: 'TEST' + Date.now()
            },
            success: function(response) {
                console.log('Response:', response);
                
                if(response.code == 0) {
                    // 测试成功
                    var html = '<div class="alert alert-success">' +
                        '<h4><i class="fa fa-check-circle"></i> 测试成功！</h4>' +
                        '<p><strong>返回信息：</strong>' + response.msg + '</p>';
                    
                    if(response.submit_url) {
                        html += '<p><strong>跳转URL：</strong><br><code>' + response.submit_url + '</code></p>' +
                               '<p><a href="' + response.submit_url + '" class="btn btn-primary" target="_blank">打开支付页面</a></p>';
                    }
                    
                    if(response.data) {
                        html += '<p><strong>返回数据：</strong></p>' +
                               '<pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
                    }
                    
                    html += '</div>';
                    $('#resultContent').html(html);
                    layer.msg('测试成功！', {icon: 1});
                } else {
                    // 测试失败
                    $('#resultContent').html(
                        '<div class="alert alert-danger">' +
                        '<h4><i class="fa fa-times-circle"></i> 测试失败</h4>' +
                        '<p><strong>错误信息：</strong>' + response.msg + '</p>' +
                        '<p><strong>错误代码：</strong>' + response.code + '</p>' +
                        '</div>'
                    );
                    layer.msg(response.msg, {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                console.error('Ajax Error:', xhr.responseText);
                $('#resultContent').html(
                    '<div class="alert alert-danger">' +
                    '<h4><i class="fa fa-times-circle"></i> 网络错误</h4>' +
                    '<p><strong>状态：</strong>' + status + '</p>' +
                    '<p><strong>错误：</strong>' + error + '</p>' +
                    '<p><strong>响应：</strong></p>' +
                    '<pre>' + xhr.responseText + '</pre>' +
                    '</div>'
                );
                layer.msg('网络错误，请重试', {icon: 2});
            },
            complete: function() {
                $('#testScanPay').prop('disabled', false).html('<i class="fa fa-qrcode"></i> 测试付款码支付');
            }
        });
    });

    // 付款码输入框回车事件
    $('#auth_code').keypress(function(e) {
        if(e.which == 13) {
            $('#testScanPay').click();
        }
    });

    // 金额输入框回车事件
    $('#amount').keypress(function(e) {
        if(e.which == 13) {
            $('#product_name').focus();
        }
    });

    // 商品名称输入框回车事件
    $('#product_name').keypress(function(e) {
        if(e.which == 13) {
            $('#auth_code').focus();
        }
    });

    // 自动填充测试数据
    $('#amount').val('0.01');
    $('#product_name').val('测试商品');
});
</script>
