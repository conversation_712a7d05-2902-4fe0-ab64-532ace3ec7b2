/**
 * 语音播报通知 Mixin
 * 用于在各个页面中统一管理WebSocket连接和语音播报
 */
import websocketService from '@/utils/websocketService.js';
import AudioPlayer from '@/utils/audioPlayer.js';
import voiceQueue from '@/utils/voiceQueue.js';

export default {
    data() {
        return {
            // WebSocket连接状态
            wsConnected: false,
            wsConnecting: false,
            wsError: null,
            
            // 语音播报状态
            voiceEnabled: true,
            voiceVolume: 1.0,
            
            // 音频播放器实例
            audioPlayer: null,
            
            // 统计信息
            receivedNotifications: 0,
            playedVoices: 0
        };
    },

    created() {
        // 初始化音频播放器
        this.audioPlayer = new AudioPlayer();
        
        // 从本地存储加载语音设置
        this.loadVoiceSettings();
    },

    mounted() {
        // 页面显示时连接WebSocket
        this.connectWebSocket();
    },

    beforeDestroy() {
        // 页面销毁前断开WebSocket连接
        this.disconnectWebSocket();
        
        // 清理音频播放器
        if (this.audioPlayer) {
            this.audioPlayer.stop();
            this.audioPlayer = null;
        }
        
        // 清空语音队列
        voiceQueue.clear();
    },

    onShow() {
        // 页面显示时重新连接WebSocket（如果未连接）
        if (!this.wsConnected && !this.wsConnecting) {
            this.connectWebSocket();
        }
    },

    onHide() {
        // 页面隐藏时可以选择保持连接或断开
        // 这里选择保持连接以继续接收通知
        console.log('页面隐藏，保持WebSocket连接');
    },

    methods: {
        /**
         * 连接WebSocket
         */
        connectWebSocket() {
            if (this.wsConnected || this.wsConnecting) {
                return;
            }

            this.wsConnecting = true;
            this.wsError = null;

            websocketService.connect({
                onMessage: this.handleWebSocketMessage,
                onConnect: this.handleWebSocketConnect,
                onDisconnect: this.handleWebSocketDisconnect,
                onError: this.handleWebSocketError
            });
        },

        /**
         * 断开WebSocket连接
         */
        disconnectWebSocket() {
            websocketService.disconnect();
            this.wsConnected = false;
            this.wsConnecting = false;
        },

        /**
         * 处理WebSocket消息
         * @param {Object} data 消息数据
         */
        handleWebSocketMessage(data) {
            console.log('收到WebSocket消息:', data);
            this.receivedNotifications++;

            // 处理支付成功通知
            if (data.event === 'payment_success') {
                this.handlePaymentNotification(data.data || data);
            }

            // 处理其他类型的通知
            else if (data.event === 'order_update') {
                this.handleOrderUpdate(data.data || data);
            }

            // 处理自定义频道消息
            else if (data.channel && data.event) {
                console.log(`频道 ${data.channel} 收到事件 ${data.event}:`, data.data);
                this.handleChannelMessage(data);
            }

            // 触发自定义事件，让页面可以监听
            this.$emit('websocket-message', data);
        },

        /**
         * 处理WebSocket连接成功
         */
        handleWebSocketConnect() {
            console.log('WebSocket连接成功');
            this.wsConnected = true;
            this.wsConnecting = false;
            this.wsError = null;

            // 订阅支付通知频道
            this.subscribeToPaymentChannel();

            // 触发连接成功事件
            this.$emit('websocket-connected');

            // 显示连接成功提示
            uni.showToast({
                title: '语音通知已开启',
                icon: 'success',
                duration: 2000
            });
        },

        /**
         * 处理WebSocket连接断开
         */
        handleWebSocketDisconnect() {
            console.log('WebSocket连接断开');
            this.wsConnected = false;
            this.wsConnecting = false;
            
            // 触发断开连接事件
            this.$emit('websocket-disconnected');
        },

        /**
         * 处理WebSocket错误
         * @param {string} type 错误类型
         * @param {Object} error 错误对象
         */
        handleWebSocketError(type, error) {
            console.error('WebSocket错误:', type, error);
            this.wsError = { type, error };
            this.wsConnecting = false;
            
            // 触发错误事件
            this.$emit('websocket-error', { type, error });
        },

        /**
         * 处理支付通知
         * @param {Object} data 支付数据
         */
        handlePaymentNotification(data) {
            if (!this.voiceEnabled) {
                console.log('语音播报已禁用，跳过播报');
                return;
            }

            const amount = data.amount;
            const orderId = data.order_id || '';
            const payType = data.pay_type || '';

            console.log(`收到支付通知: ${amount}元, 订单: ${orderId}, 支付方式: ${payType}`);

            // 添加到语音播报队列
            voiceQueue.add(
                () => this.playPaymentVoice(amount),
                {
                    priority: 10, // 支付通知优先级最高
                    description: `支付播报: ${amount}元`,
                    timeout: 15000
                }
            ).then(() => {
                this.playedVoices++;
                console.log(`支付语音播报完成: ${amount}元`);
            }).catch((error) => {
                console.error(`支付语音播报失败: ${amount}元`, error);
            });

            // 触发支付通知事件
            this.$emit('payment-notification', data);
        },

        /**
         * 处理订单更新
         * @param {Object} data 订单数据
         */
        handleOrderUpdate(data) {
            console.log('收到订单更新:', data);
            
            // 触发订单更新事件
            this.$emit('order-update', data);
        },

        /**
         * 播放支付语音
         * @param {string|number} amount 支付金额
         */
        async playPaymentVoice(amount) {
            if (!this.audioPlayer) {
                throw new Error('音频播放器未初始化');
            }

            try {
                await this.audioPlayer.playPaymentSuccess(amount);
                
                // 播放完成后的震动反馈
                uni.vibrateShort({
                    type: 'light'
                });
                
            } catch (error) {
                console.error('播放支付语音失败:', error);
                throw error;
            }
        },

        /**
         * 切换语音播报开关
         */
        toggleVoiceEnabled() {
            this.voiceEnabled = !this.voiceEnabled;
            this.saveVoiceSettings();
            
            uni.showToast({
                title: this.voiceEnabled ? '语音播报已开启' : '语音播报已关闭',
                icon: this.voiceEnabled ? 'success' : 'none',
                duration: 2000
            });
        },

        /**
         * 设置语音音量
         * @param {number} volume 音量 (0-1)
         */
        setVoiceVolume(volume) {
            this.voiceVolume = Math.max(0, Math.min(1, volume));
            this.saveVoiceSettings();
        },

        /**
         * 保存语音设置到本地存储
         */
        saveVoiceSettings() {
            const settings = {
                voiceEnabled: this.voiceEnabled,
                voiceVolume: this.voiceVolume
            };
            
            uni.setStorageSync('voiceSettings', settings);
        },

        /**
         * 从本地存储加载语音设置
         */
        loadVoiceSettings() {
            try {
                const settings = uni.getStorageSync('voiceSettings');
                if (settings) {
                    this.voiceEnabled = settings.voiceEnabled !== false; // 默认开启
                    this.voiceVolume = settings.voiceVolume || 1.0;
                }
            } catch (error) {
                console.error('加载语音设置失败:', error);
            }
        },

        /**
         * 获取WebSocket连接状态
         */
        getWebSocketStatus() {
            return {
                connected: this.wsConnected,
                connecting: this.wsConnecting,
                error: this.wsError,
                receivedNotifications: this.receivedNotifications,
                playedVoices: this.playedVoices
            };
        },

        /**
         * 获取语音队列状态
         */
        getVoiceQueueStatus() {
            return voiceQueue.getStatus();
        },

        /**
         * 订阅支付通知频道
         */
        subscribeToPaymentChannel() {
            // 订阅通用支付频道
            websocketService.subscribe('payment_channel');

            // 如果有用户ID，订阅用户专属频道
            const userInfo = uni.getStorageSync('userInfo');
            if (userInfo && userInfo.id) {
                websocketService.subscribe(`payment_user_${userInfo.id}`);
            }

            // 如果有商户ID，订阅商户专属频道
            const merchantInfo = uni.getStorageSync('merchantInfo');
            if (merchantInfo && merchantInfo.id) {
                websocketService.subscribe(`payment_merchant_${merchantInfo.id}`);
            }
        },

        /**
         * 处理频道消息
         * @param {Object} data 频道消息数据
         */
        handleChannelMessage(data) {
            const { channel, event, data: messageData } = data;

            console.log(`频道 ${channel} 事件 ${event}:`, messageData);

            // 根据事件类型处理
            switch (event) {
                case 'payment_success':
                    this.handlePaymentNotification(messageData);
                    break;
                case 'payment_failed':
                    this.handlePaymentFailed(messageData);
                    break;
                case 'refund_success':
                    this.handleRefundNotification(messageData);
                    break;
                default:
                    console.log('未处理的频道事件:', event);
            }
        },

        /**
         * 处理支付失败通知
         * @param {Object} data 支付失败数据
         */
        handlePaymentFailed(data) {
            console.log('支付失败通知:', data);

            // 显示失败提示
            uni.showToast({
                title: '支付失败',
                icon: 'error',
                duration: 3000
            });

            // 触发支付失败事件
            this.$emit('payment-failed', data);
        },

        /**
         * 处理退款通知
         * @param {Object} data 退款数据
         */
        handleRefundNotification(data) {
            console.log('退款通知:', data);

            // 播报退款语音
            if (this.voiceEnabled && data.amount) {
                const voiceText = `退款${data.amount}元`;
                this.playVoiceNotification(voiceText, data.amount);
            }

            // 显示退款提示
            uni.showToast({
                title: `退款${data.amount}元`,
                icon: 'success',
                duration: 3000
            });

            // 触发退款事件
            this.$emit('refund-success', data);
        },

        /**
         * 测试语音播报
         * @param {string|number} amount 测试金额
         */
        testVoiceNotification(amount = '123.45') {
            this.handlePaymentNotification({
                event: 'payment_success',
                amount: amount,
                order_id: 'TEST_' + Date.now(),
                pay_type: 'test'
            });
        }
    }
};
