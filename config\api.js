// API 基础配置
export const API_CONFIG = {
  // 开发环境 - 使用相对路径，通过代理解决跨域
  development: {
    baseURL: '',  // 使用相对路径，通过manifest.json代理
    timeout: 15000
  },
  // 生产环境
  production: {
    baseURL: '',  // 使用相对路径
    timeout: 15000
  }
}

// API 接口列表
export const API_LIST = {
  // 认证相关
  AUTH: {
    LOGIN: '/user/ajax.php?act=login',        // 登录接口
    GET_CSRF: '/user/ajax.php?act=getcsrf',   // 获取CSRF Token的接口
    CAPTCHA: '/user/ajax.php?act=captcha',    // 获取验证码的接口
    SENDCODE: '/user/ajax.php?act=sendcode',  // 发送验证码的接口
    REG: '/user/ajax.php?act=reg',            // 注册接口
    FORGOT: '/user/ajax.php?act=sendcode2'    // 忘记密码-发送验证码接口
  },
  
  // 订单相关
  ORDER: {
    LIST: '/user/ajax2.php?act=orderList',        // 订单列表
    DETAIL: '/user/ajax2.php?act=order',          // 订单详情
    STATISTICS: '/user/ajax2.php?act=statistics', // 订单统计
    RECORD_LIST: '/user/ajax2.php?act=recordList' // 资金明细
  },
  
  // 支付相关
  PAY: {
    CREATE: '/pay/create',
    QUERY: '/pay/query',
    REFUND: '/pay/refund'
  },
  
  // 商户相关
  MERCHANT: {
    INFO: '/merchant/info',
    UPDATE: '/merchant/update',
    BANK_CARDS: '/merchant/bank-cards'
  },

  // 员工管理相关 - 正式接口
  STAFF: {
    LIST: '/user/staff.php?act=getStaffList',           // 获取员工列表
    ADD: '/user/staff.php?act=addStaff',                // 添加员工
    EDIT: '/user/staff.php?act=editStaff',              // 编辑员工
    DELETE: '/user/staff.php?act=deleteStaff',          // 删除员工
    LOGIN: '/user/staff.php?act=staffLogin',            // 员工登录
    SAVE_QR_CONFIG: '/user/staff.php?act=saveQRConfig', // 保存收款码配置
    GET_QR_CONFIG: '/user/staff.php?act=getQRConfigList' // 获取收款码配置列表
  },
  
  // 报表相关
  REPORT: {
    DAILY: '/report/daily',
    MONTHLY: '/report/monthly',
    YEARLY: '/report/yearly'
  }
}

// 获取当前环境的配置
export const getCurrentConfig = () => {
  return API_CONFIG[process.env.NODE_ENV] || API_CONFIG.development
}

// 获取完整的 API 地址
export const getFullUrl = (path) => {
  const config = getCurrentConfig()
  return `${config.baseURL}${path}`
} 