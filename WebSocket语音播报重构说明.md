# WebSocket语音播报重构说明

## 🎯 重构目标

解决之前WebSocket连接混乱、重复连接、消息格式错误等问题，采用测试页面的成功方法重新设计语音播报功能。

## 🔧 主要改动

### 1. **移除页面级WebSocket连接**

**之前的问题：**
- 多个页面都使用 `websocketMixin`
- 每个页面显示时都会自动连接WebSocket
- 导致多个连接同时存在，消息混乱

**解决方案：**
- 禁用所有页面的 `websocketMixin`
- 统一在语音设置页面管理WebSocket连接
- 避免重复连接和资源浪费

**修改的文件：**
- `pages/index/index.vue` - 移除websocketMixin
- `pages/scan/index.vue` - 移除websocketMixin  
- `pages/staff-qrcode/index.vue` - 移除websocketMixin
- `pages/staff-orders/index.vue` - 移除websocketMixin
- `pages/bill/index.vue` - 移除websocketMixin
- `mixins/websocketMixin.js` - 禁用自动连接功能

### 2. **重新设计语音设置页面**

**采用测试页面的成功方法：**
- 使用 `pages/test/websocket-voice-simple.vue` 的连接逻辑
- 直接使用 `uni.connectSocket` API
- 完整的消息处理和错误处理
- 实时日志显示

**新功能：**
- 🌐 **WebSocket连接状态** - 实时显示连接状态
- 🔊 **语音设置** - 音量调节、开关控制
- 🧪 **测试功能** - 连接测试、语音测试、模拟支付
- 📊 **今日统计** - 支付笔数、金额统计
- 📝 **连接日志** - 实时显示连接和消息日志

### 3. **核心WebSocket实现**

**连接逻辑：**
```javascript
// 连接WebSocket
connectWebSocket() {
  const wsUrl = 'ws://ceshi.huisas.com:8080'
  this.socketTask = uni.connectSocket({ url: wsUrl })
  
  // 连接成功后发送认证和订阅
  this.socketTask.onOpen(() => {
    this.sendAuth()
    this.subscribePaymentChannel()
  })
}

// 处理支付通知
handleMessage(data) {
  if (data.type === 'payment_notification') {
    this.playPaymentVoice(data.data)
    this.updateStats(data.data)
  }
}
```

**认证和订阅：**
```javascript
// 发送认证
sendAuth() {
  this.sendMessage({
    type: 'auth',
    data: { merchant_id: '1000', token: 'test_token' }
  })
}

// 订阅支付频道
subscribePaymentChannel() {
  this.sendMessage({
    type: 'subscribe',
    data: { channel: 'merchant_1000_payment' }
  })
}
```

## 🎵 语音播报流程

1. **用户在语音设置页面开启语音播报**
2. **自动连接WebSocket服务器**
3. **发送认证信息**
4. **订阅商户专属支付频道**
5. **收到支付通知时自动播放语音**

## 📱 页面使用说明

### 语音设置页面 (`/pages/settings/voice`)

**主要功能：**
- ✅ 统一管理WebSocket连接
- ✅ 语音开关和音量控制
- ✅ 实时连接状态显示
- ✅ 测试功能（连接、语音、模拟支付）
- ✅ 今日统计数据
- ✅ 详细的连接日志

**使用方法：**
1. 打开语音设置页面
2. 开启"语音播报"开关
3. 系统自动连接WebSocket
4. 可以使用测试功能验证
5. 实时查看连接日志

### 其他页面

**简化处理：**
- 移除了所有WebSocket相关代码
- 页面更加简洁和稳定
- 避免了连接冲突问题

## 🔍 测试验证

**测试步骤：**
1. 访问 `http://localhost:5173/#/pages/settings/voice`
2. 开启语音播报开关
3. 观察连接状态变为"已连接"
4. 点击"测试语音"验证音频播放
5. 点击"模拟支付"测试完整流程
6. 查看连接日志确认消息正常

**预期结果：**
- ✅ WebSocket连接成功
- ✅ 认证和订阅成功
- ✅ 语音播放正常
- ✅ 支付通知处理正确
- ✅ 统计数据更新
- ✅ 日志显示完整

## 🚀 优势

1. **统一管理** - 所有WebSocket连接在一个页面管理
2. **避免冲突** - 不再有多个连接同时存在
3. **实时监控** - 详细的日志和状态显示
4. **易于调试** - 清晰的错误信息和连接状态
5. **用户友好** - 直观的界面和测试功能

## 📋 后续优化建议

1. **持久化连接** - 考虑在后台保持连接
2. **重连机制** - 网络断开时自动重连
3. **消息队列** - 处理离线期间的消息
4. **性能优化** - 减少不必要的日志输出
5. **用户体验** - 添加更多的状态提示

---

**重构完成时间：** 2025-01-27  
**测试状态：** ✅ 通过  
**部署状态：** 🟡 待部署
