# 🧹 WebSocket服务清理建议

## 📋 **当前状况**

项目中存在**两套WebSocket服务**，造成混乱：

### ✅ **正在使用的服务**
- **文件**: `ceshi.huisas.com/swoole_websocket_server.php`
- **技术**: Swoole WebSocket (高性能)
- **端口**: 8080
- **协议**: 标准WebSocket协议
- **连接**: `ws://ceshi.huisas.com:8080`

### ❌ **废弃的服务**
- **目录**: `ceshi.huisas.com/payment_websocket_v2/`
- **技术**: Workerman + Pusher协议
- **端口**: 8080 + 8081
- **协议**: Pusher协议
- **连接**: `ws://ceshi.huisas.com:8080/app/payment_websocket_2024`

## 🗑️ **建议删除的文件**

### 1. **主要目录**
```
ceshi.huisas.com/payment_websocket_v2/
├── PaymentNotifier.php
├── README.md
├── config.php
├── deploy.sh
├── deploy_instructions.md
├── frontend_example.html
├── logs/
├── payment_service.tar.gz
├── service/
├── service.rar
├── start.php
├── start_payment.php
├── test_websocket.html
└── 部署说明.md
```

### 2. **相关的废弃文件**
```
ceshi.huisas.com/
├── websocket_api.php (基于Workerman的API)
├── simple_websocket_server.php
├── restart_websocket.php
├── merchant_websocket_demo.html
└── includes/websocket_notify_workerman.php
```

### 3. **测试文件**
```
ceshi.huisas.com/
├── test_websocket_class.php
├── test_websocket_message.php
├── test_websocket_notify.php
├── test_websocket_payment.php
└── payment_websocket_v2/test_websocket.html
```

## 🔧 **需要更新的文件**

### 1. **后端集成文件**
- `includes/functions.php` - 移除Workerman相关代码
- `includes/payment_callback_integration.php` - 统一使用Swoole服务

### 2. **前端配置文件**
- 确认所有前端文件都连接到Swoole服务 (端口8080)
- 移除Pusher协议相关代码

## ✅ **保留的核心文件**

### Swoole WebSocket服务
```
ceshi.huisas.com/
├── swoole_websocket_server.php ✅ (主服务器)
├── start_swoole_websocket.php ✅ (启动脚本)
├── SWOOLE_WEBSOCKET_README.md ✅ (文档)
└── test_swoole_websocket_simple.html ✅ (测试页面)
```

### 前端WebSocket客户端
```
utils/
├── paymentWebSocket.js ✅ (主要客户端)
├── websocketService.js ✅ (服务管理器)
├── websocket.js ✅ (基础WebSocket库)
└── voicePlayer.js ✅ (语音播报)
```

## 🚀 **清理后的优势**

1. **🎯 架构清晰**: 只保留Swoole WebSocket服务
2. **⚡ 性能更好**: Swoole协程比Workerman性能更高
3. **🔧 维护简单**: 减少代码冗余，降低维护成本
4. **📦 体积更小**: 删除大量无用文件
5. **🐛 减少Bug**: 避免两套服务冲突

## ⚠️ **清理注意事项**

1. **备份重要数据**: 清理前备份 `payment_websocket_v2/logs/` 中的日志
2. **检查依赖**: 确认没有其他代码依赖Workerman服务
3. **测试验证**: 清理后测试WebSocket连接和支付通知功能
4. **文档更新**: 更新相关文档，移除Workerman相关说明

## 🗑️ **具体删除文件清单**

### 1. **主要废弃目录**
```bash
# 完整删除 payment_websocket_v2 目录
rm -rf ceshi.huisas.com/payment_websocket_v2/
```

### 2. **废弃的WebSocket文件**
```bash
# 删除Workerman相关文件
rm ceshi.huisas.com/websocket_api.php
rm ceshi.huisas.com/simple_websocket_server.php
rm ceshi.huisas.com/restart_websocket.php
rm ceshi.huisas.com/merchant_websocket_demo.html
rm ceshi.huisas.com/includes/websocket_notify_workerman.php
```

### 3. **废弃的测试文件**
```bash
# 删除旧的测试文件
rm ceshi.huisas.com/test_websocket_class.php
rm ceshi.huisas.com/test_websocket_message.php
rm ceshi.huisas.com/test_websocket_notify.php
rm ceshi.huisas.com/test_websocket_payment.php
```

## 🔧 **需要修改的文件**

### 1. **includes/common.php** - 移除Workerman引用
```php
// 删除这段代码 (第88-102行)
// 引入WebSocket通知功能 - 多路径检测
$websocket_notify_paths = [
    SYSTEM_ROOT."websocket_notify_workerman.php",   // Workerman版本（优先）
    ROOT."websocket_notify.php",                    // 根目录
    // ... 其他路径
];
```

### 2. **includes/functions.php** - 简化WebSocket通知
```php
// 保留sendWebSocketPaymentNotification函数
// 但移除Workerman相关的复杂逻辑，只保留Swoole HTTP API调用
```

## 📝 **清理步骤**

### 第一步：备份重要数据
```bash
# 备份日志文件
cp -r ceshi.huisas.com/payment_websocket_v2/logs/ ./backup_websocket_logs/
cp ceshi.huisas.com/logs/websocket_notifications.log ./backup_websocket_logs/
```

### 第二步：停止Workerman服务
```bash
# 如果Workerman服务在运行，先停止
cd ceshi.huisas.com/payment_websocket_v2/service/
php start.php stop
```

### 第三步：删除废弃文件
```bash
# 删除主目录
rm -rf ceshi.huisas.com/payment_websocket_v2/

# 删除相关文件
rm ceshi.huisas.com/websocket_api.php
rm ceshi.huisas.com/simple_websocket_server.php
rm ceshi.huisas.com/restart_websocket.php
rm ceshi.huisas.com/merchant_websocket_demo.html
rm ceshi.huisas.com/includes/websocket_notify_workerman.php
rm ceshi.huisas.com/test_websocket_*.php
```

### 第四步：更新代码引用
1. 修改 `includes/common.php`
2. 简化 `includes/functions.php`
3. 确认前端连接Swoole服务

### 第五步：测试验证
```bash
# 启动Swoole服务
php ceshi.huisas.com/swoole_websocket_server.php start

# 测试连接
curl http://ceshi.huisas.com/test_swoole_websocket_simple.html
```
