{
	"pages": [
		{
			"path": "pages/login/index",
			"style": {
				"navigationBarTitleText": "登录",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "商家收款",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/code/index",
			"style": {
				"navigationBarTitleText": "收款码",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/bill/index",
			"style": {
				"navigationBarTitleText": "交易记录",
				"navigationStyle": "custom",
				"enablePullDownRefresh": true,
				"onReachBottomDistance": 50
			}
		},
		{
			"path": "pages/scan/index",
			"style": {
				"navigationBarTitleText": "一码通",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/dynamic-code/index",
			"style": {
				"navigationBarTitleText": "动态收款码",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/report/index",
			"style": {
				"navigationBarTitleText": "报表",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/report/diamond",
			"style": {
				"navigationBarTitleText": "多维数据分析",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/mine/index",
			"style": {
				"navigationBarTitleText": "我的",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/settings/voice",
			"style": {
				"navigationBarTitleText": "语音播报设置",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/test/websocket-test",
			"style": {
				"navigationBarTitleText": "WebSocket测试",
				"navigationStyle": "custom"
			}
		},

		{
			"path": "pages/wallet/index",
			"style": {
				"navigationBarTitleText": "我的钱包",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wallet/bank-cards",
			"style": {
				"navigationBarTitleText": "银行卡管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/merchant/index",
			"style": {
				"navigationBarTitleText": "商家信息",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/duizhang/index",
			"style": {
				"navigationBarTitleText": "对账单",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/jiesuan/index",
			"style": {
				"navigationBarTitleText": "结算记录",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/xiaoxi/index",
			"style": {
				"navigationBarTitleText": "消息通知",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/xiaoxi/detail",
			"style": {
				"navigationBarTitleText": "消息详情",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/yuangong/index",
			"style": {
				"navigationBarTitleText": "员工管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/yingxiao/index",
			"style": {
				"navigationBarTitleText": "营销工具",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/bill/date-range",
			"style": {
				"navigationBarTitleText": "日期范围选择",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/pay/mini-payment",
			"style": {
				"navigationBarTitleText": "收银台",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/pay/result",
			"style": {
				"navigationBarTitleText": "支付结果",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/webview/index",
			"style": {
				"navigationBarTitleText": "支付页面",
				"navigationStyle": "custom"
			}
		},
		            {
		                "path": "pages/staff-login/index",
		                "style": {
		                    "navigationBarTitleText": "员工登录",
		                    "navigationStyle": "custom"
		                }
		            },
		            {
		                "path": "pages/staff-home/index",
		                "style": {
		                    "navigationBarTitleText": "员工首页",
		                    "navigationStyle": "custom"
		                }
		            },
		            {
		                "path": "pages/staff-qrcode/index",
		                "style": {
		                    "navigationBarTitleText": "我的收款码",
		                    "navigationStyle": "custom"
		                }
		            },
		            {
		                "path": "pages/staff-orders/index",
		                "style": {
		                    "navigationBarTitleText": "我的订单",
		                    "navigationStyle": "custom"
		                }
		            },
		            {
		                "path": "pages/staff-stats/index",
		                "style": {
		                    "navigationBarTitleText": "收款统计",
		                    "navigationStyle": "custom"
		                }
		            },
		            {
		                "path": "pages/staff-profile/index",
		                "style": {
		                    "navigationBarTitleText": "个人设置",
		                    "navigationStyle": "custom"
		                }
		            },
		            {
		                "path": "pages/debug/storage",
		                "style": {
		                    "navigationBarTitleText": "存储调试",
		                    "navigationStyle": "custom"
		                }
		            },
            {
                "path": "pages/test/scan-pay-test",
                "style": {
                    "navigationBarTitleText": "付款码测试",
                    "navigationStyle": "custom"
                }
            },
            {
                "path": "pages/test/scan-pay-fix-test",
                "style": {
                    "navigationBarTitleText": "反扫支付修复测试",
                    "navigationStyle": "custom"
                }
            },
            {
                "path": "pages/test/storage-test",
                "style": {
                    "navigationBarTitleText": "存储测试",
                    "navigationStyle": "custom"
                }
            },
            {
                "path": "pages/test/voice-test",
                "style": {
                    "navigationBarTitleText": "语音播报测试",
                    "navigationStyle": "custom"
                }
            },

            {
                "path": "pages/test/websocket-port-test",
                "style": {
                    "navigationBarTitleText": "端口连接测试",
                    "navigationStyle": "custom"
                }
            },

            {
                "path": "pages/test/websocket-voice-simple",
                "style": {
                    "navigationBarTitleText": "WebSocket语音简化测试",
                    "navigationStyle": "custom"
                }
            },
            {
                "path": "pages/test/swoole-websocket-test",
                "style": {
                    "navigationBarTitleText": "Swoole WebSocket测试",
                    "navigationStyle": "custom"
                }
            },
            {
                "path": "pages_A/user/index",
                "style": {
                    "navigationBarTitleText": "会员中心",
                    "navigationStyle": "custom"
                }
            }
	],
	"globalStyle": {
		"navigationBarTextStyle": "white",
		"navigationBarTitleText": "商家收款",
		"navigationBarBackgroundColor": "#5145F7",
		"backgroundColor": "#F8F8F8"
	},
	"easycom": {
		"autoscan": true,
		"custom": {
			"^uni-(.*)": "@/uni_modules/uni-$1/components/uni-$1/uni-$1.vue",
			"^custom-(.*)": "@/components/custom-$1.vue"
		}
	},
	"tabBar": {
		"custom": false,
		"color": "#999999",
		"selectedColor": "#5145F7",
		"backgroundColor": "#FFFFFF",
		"position": "bottom",
		"height": "50px",
		"list": [
			{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "static/tab/home.png",
				"selectedIconPath": "static/tab/home-active.png"
			},
			{
				"pagePath": "pages/bill/index",
				"text": "账单",
				"iconPath": "static/tab/bill.png",
				"selectedIconPath": "static/tab/bill-active.png"
			},
			{
				"pagePath": "pages/scan/index",
				"text": "一码通",
				"iconPath": "static/tab/code.png",
				"selectedIconPath": "static/tab/code-active.png"
			},
			{
				"pagePath": "pages/report/index",
				"text": "报表",
				"iconPath": "static/tab/report.png",
				"selectedIconPath": "static/tab/report-active.png"
			},
			{
				"pagePath": "pages/mine/index",
				"text": "我的",
				"iconPath": "static/tab/mine.png",
				"selectedIconPath": "static/tab/mine-active.png"
			},	
		]
	},
	"uniIdRouter": {}
}