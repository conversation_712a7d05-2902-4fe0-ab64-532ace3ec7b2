<template>
  <view class="custom-navbar" :style="navbarContainerStyle">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="statusBarStyle"></view>

    <!-- 导航栏内容 -->
    <view class="navbar-content" :style="navbarContentStyle">
      <!-- 左侧内容 -->
      <view class="navbar-left" :style="navbarLeftStyle">
        <!-- 返回按钮 -->
        <view
          class="back-button"
          v-if="showBack"
          @click="handleBack"
          :style="backButtonStyle"
        >
          <text class="back-icon">{{ backIcon }}</text>
        </view>

        <!-- 标题区域 -->
        <view class="title-section" :style="titleSectionStyle">
          <text class="navbar-title" :style="titleStyle">{{ title }}</text>
          <text
            class="navbar-subtitle"
            v-if="subtitle"
            :style="subtitleStyle"
          >{{ subtitle }}</text>
        </view>
      </view>

      <!-- 中间内容插槽 -->
      <view class="navbar-center" v-if="$slots.center">
        <slot name="center"></slot>
      </view>

      <!-- 右侧内容 -->
      <view class="navbar-right" :style="navbarRightStyle">
        <!-- 通知按钮 -->
        <view
          class="notification-btn"
          v-if="showNotification"
          @click="handleNotification"
          :style="notificationBtnStyle"
        >
          <view class="notification-icon">
            <text class="bell-icon">{{ notificationIcon }}</text>
            <view class="notification-badge" v-if="badgeCount > 0" :style="badgeStyle">
              <text class="badge-text">{{ badgeCount > 99 ? '99+' : badgeCount }}</text>
            </view>
          </view>
        </view>

        <!-- 自定义右侧内容 -->
        <view class="custom-right" v-if="$slots.right">
          <slot name="right"></slot>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getNavBarStyleConfig,
  getNavBarRightSafeDistance,
  getPlatformInfo
} from '@/utils/navbar.js'

export default {
  name: 'CustomNavbar',
  props: {
    // 标题
    title: {
      type: String,
      default: '标题'
    },
    // 副标题
    subtitle: {
      type: String,
      default: ''
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      default: false
    },
    // 返回按钮图标
    backIcon: {
      type: String,
      default: '←'
    },
    // 是否显示通知按钮
    showNotification: {
      type: Boolean,
      default: false
    },
    // 通知图标
    notificationIcon: {
      type: String,
      default: '🔔'
    },
    // 通知徽章数量
    badgeCount: {
      type: Number,
      default: 0
    },
    // 背景色
    backgroundColor: {
      type: String,
      default: '#5145F7'
    },
    // 文字颜色
    textColor: {
      type: String,
      default: '#FFFFFF'
    },
    // 是否固定定位
    fixed: {
      type: Boolean,
      default: true
    },
    // 是否显示阴影
    shadow: {
      type: Boolean,
      default: true
    },
    // 标题字体大小
    titleSize: {
      type: String,
      default: '18px'
    },
    // 副标题字体大小
    subtitleSize: {
      type: String,
      default: '12px'
    },
    // 自定义样式
    customStyle: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      navbarConfig: null,
      platformInfo: null
    }
  },

  computed: {
    // 导航栏容器样式
    navbarContainerStyle() {
      const baseStyle = {
        position: this.fixed ? 'fixed' : 'relative',
        top: this.fixed ? '0' : 'auto',
        left: '0',
        right: '0',
        zIndex: this.fixed ? '999' : 'auto',
        backgroundColor: this.backgroundColor,
        boxShadow: this.shadow ? '0 2px 10px rgba(81, 69, 247, 0.15)' : 'none'
      };

      return { ...baseStyle, ...this.customStyle };
    },

    // 状态栏样式
    statusBarStyle() {
      return {
        height: this.navbarConfig?.statusBarHeight + 'px' || '20px',
        backgroundColor: 'transparent'
      };
    },

    // 导航栏内容样式
    navbarContentStyle() {
      return {
        height: this.navbarConfig?.navBarHeight + 'px' || '44px',
        paddingLeft: '16px',
        paddingRight: Math.max(16, this.navbarConfig?.rightSafeDistance || 0) + 'px'
      };
    },

    // 左侧区域样式
    navbarLeftStyle() {
      return {
        flex: '1',
        minWidth: '0'
      };
    },

    // 右侧区域样式
    navbarRightStyle() {
      return {
        flexShrink: '0'
      };
    },

    // 返回按钮样式
    backButtonStyle() {
      return {
        color: this.textColor
      };
    },

    // 标题区域样式
    titleSectionStyle() {
      return {
        marginLeft: this.showBack ? '8px' : '0'
      };
    },

    // 标题样式
    titleStyle() {
      return {
        fontSize: this.titleSize,
        color: this.textColor,
        fontWeight: '600'
      };
    },

    // 副标题样式
    subtitleStyle() {
      return {
        fontSize: this.subtitleSize,
        color: this.textColor,
        opacity: '0.8'
      };
    },

    // 通知按钮样式
    notificationBtnStyle() {
      return {
        color: this.textColor
      };
    },

    // 徽章样式
    badgeStyle() {
      return {
        backgroundColor: '#FF3B30',
        color: '#FFFFFF'
      };
    }
  },

  mounted() {
    this.initNavbar();
  },

  methods: {
    // 初始化导航栏
    initNavbar() {
      this.navbarConfig = getNavBarStyleConfig();
      this.platformInfo = getPlatformInfo();
    },

    // 返回按钮点击
    handleBack() {
      this.$emit('back');

      // 默认行为：返回上一页
      const pages = getCurrentPages();
      if (pages.length > 1) {
        uni.navigateBack();
      } else {
        // 如果是首页，可以自定义行为
        uni.switchTab({
          url: '/pages/index/index'
        });
      }
    },

    // 通知按钮点击
    handleNotification() {
      this.$emit('notification');
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-navbar {
  width: 100%;

  .status-bar {
    width: 100%;
  }

  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    .navbar-left {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;

      .back-button {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 16px;
        background: rgba(255, 255, 255, 0.15);
        transition: all 0.2s ease;
        flex-shrink: 0;

        &:active {
          background: rgba(255, 255, 255, 0.25);
          transform: scale(0.95);
        }

        .back-icon {
          font-size: 18px;
          font-weight: bold;
          line-height: 1;
        }
      }

      .title-section {
        flex: 1;
        min-width: 0;

        .navbar-title {
          font-weight: 600;
          line-height: 1.2;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .navbar-subtitle {
          line-height: 1.2;
          margin-top: 2px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .navbar-center {
      flex-shrink: 0;
      margin: 0 16px;
    }
		
    .navbar-right {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-shrink: 0;

      .notification-btn {
        position: relative;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 16px;
        background: rgba(255, 255, 255, 0.15);
        transition: all 0.2s ease;

        &:active {
          background: rgba(255, 255, 255, 0.25);
          transform: scale(0.95);
        }

        .notification-icon {
          position: relative;

          .bell-icon {
            font-size: 18px;
            line-height: 1;
          }

          .notification-badge {
            position: absolute;
            top: -6px;
            right: -6px;
            min-width: 16px;
            height: 16px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #FFFFFF;

            .badge-text {
              font-size: 10px;
              font-weight: bold;
              line-height: 1;
              padding: 0 4px;
            }
          }
        }
      }

      .custom-right {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }
}

/* 多端适配 */
/* #ifdef MP-WEIXIN */
.custom-navbar {
  .navbar-content {
    .navbar-right {
      margin-right: 8px; /* 避免与胶囊按钮重叠 */
    }
  }
}
/* #endif */

/* #ifdef H5 */
.custom-navbar {
  .navbar-content {
    .back-button {
      &:hover {
        background: rgba(255, 255, 255, 0.25);
      }
    }

    .notification-btn {
      &:hover {
        background: rgba(255, 255, 255, 0.25);
      }
    }
  }
}
/* #endif */
</style>
