import{ac as a,r as t,a as e,A as o,B as s,S as n,d as i,e as l,w as d,i as r,o as c,f as m,h as p,t as f,C as h,D as u,F as y,k as I,l as g,I as _,p as w,z as M,j as P}from"./index-B1Q521gi.js";import{_ as x}from"./custom-navbar.DuzuSmPc.js";import{r as b}from"./uni-app.es.DAfa8VxY.js";import{_ as v}from"./_plugin-vue_export-helper.BCo6x5W8.js";const U=v({data:()=>({merchantId:"",merchantInfo:{name:"加载中...",logo:"/static/images/default-logo.png"},amount:"",orderId:"",paymentMethods:[{id:"wxpay",name:"微信支付",icon:"/static/images/wxpay.png"},{id:"alipay",name:"支付宝支付",icon:"/static/images/alipay.png"}],selectedPayMethod:"wxpay",platformInfo:{platform:"",openid:"",appid:""},isUserInfoReady:!1}),computed:{currentPayMethod(){const a="alipay"===this.platformInfo.platform?"alipay":"wxpay";return this.paymentMethods.find((t=>t.id===a))||this.paymentMethods[0]}},onLoad(a){this.detectPlatform(),a.merchantId&&(this.merchantId=a.merchantId,this.loadMerchantInfo()),a.amount&&(this.amount=a.amount),a.orderId&&(this.orderId=a.orderId),"h5"!==this.platformInfo.platform?this.getUserInfo():this.isUserInfoReady=!0},methods:{detectPlatform(){this.platformInfo.platform="h5",console.log("当前平台:",this.platformInfo.platform)},getUserInfo(){"weixin"===this.platformInfo.platform?this.getWechatOpenid():"alipay"===this.platformInfo.platform?this.getAlipayUserId():this.isUserInfoReady=!0},getWechatOpenid(){a({provider:"weixin",success:a=>{a.code&&t({url:"/api/user/wx-openid",data:{code:a.code},success:a=>{0===a.data.code?(this.platformInfo.openid=a.data.data.openid,this.platformInfo.appid=a.data.data.appid,this.isUserInfoReady=!0,console.log("获取微信openid成功:",this.platformInfo.openid)):e({title:"获取用户ID失败",icon:"none"})},fail:()=>{e({title:"网络异常，请重试",icon:"none"})}})}})},getAlipayUserId(){this.isUserInfoReady=!0},loadMerchantInfo(){o({title:"加载中..."}),t({url:"/api/merchant/info",data:{merchantId:this.merchantId},success:a=>{0===a.data.code?this.merchantInfo=a.data.data:e({title:"获取商家信息失败",icon:"none"})},fail:()=>{e({title:"网络异常，请重试",icon:"none"})},complete:()=>{s()}})},selectPayMethod(a){this.selectedPayMethod=a},handlePay(){if(!this.amount||isNaN(parseFloat(this.amount))||parseFloat(this.amount)<=0)return void e({title:"请输入有效金额",icon:"none"});if("h5"!==this.platformInfo.platform&&!this.isUserInfoReady)return void e({title:"正在获取用户信息，请稍候",icon:"none"});o({title:"处理中..."});const a="h5"===this.platformInfo.platform?this.selectedPayMethod:"alipay"===this.platformInfo.platform?"alipay":"wxpay",n={merchantId:this.merchantId,amount:parseFloat(this.amount),payMethod:a};this.orderId&&(n.orderId=this.orderId),"h5"!==this.platformInfo.platform&&(n.openid=this.platformInfo.openid,n.appid=this.platformInfo.appid,n.platform=this.platformInfo.platform),t({url:"/api/pay/create",method:"POST",data:n,success:t=>{if(s(),0===t.data.code){const e=t.data.data;if("wxpay"===a){if(e.mweb_url)return void(window.location.href=e.mweb_url)}else if("alipay"===a&&e.form){const a=document.createElement("div");return a.innerHTML=e.form,document.body.appendChild(a),void document.forms[0].submit()}}else e({title:t.data.msg||"创建支付订单失败",icon:"none"})},fail:()=>{s(),e({title:"网络异常，请重试",icon:"none"})}})},handlePaymentSuccess(a){n({url:`/pages/pay/result?orderId=${a}&status=success`})},handlePaymentFail(a){console.log("支付失败:",a),e({title:"支付失败，请重试",icon:"none"})}}},[["render",function(a,t,e,o,s,n){const v=b(i("custom-navbar"),x),U=I,k=r,C=g,R=_,j=w;return c(),l(k,{class:"container"},{default:d((()=>[m(v,{title:"收银台",shadow:!0}),m(k,{class:"merchant-info"},{default:d((()=>[m(U,{class:"merchant-logo",src:s.merchantInfo.logo},null,8,["src"]),m(k,{class:"merchant-name"},{default:d((()=>[p(f(s.merchantInfo.name),1)])),_:1})])),_:1}),m(k,{class:"amount-input"},{default:d((()=>[m(k,{class:"amount-label"},{default:d((()=>[p("消费金额： "),m(C,{class:"currency"},{default:d((()=>[p("¥")])),_:1})])),_:1}),m(k,{class:"amount-wrapper"},{default:d((()=>[m(R,{class:"amount",type:"digit",modelValue:s.amount,"onUpdate:modelValue":t[0]||(t[0]=a=>s.amount=a),placeholder:"请输入金额",disabled:!!s.orderId},null,8,["modelValue","disabled"])])),_:1})])),_:1}),m(k,{class:"payment-methods"},{default:d((()=>[m(k,{class:"section-title"},{default:d((()=>[p("选择支付方式")])),_:1}),(c(!0),h(y,null,u(s.paymentMethods,((a,t)=>(c(),l(k,{key:t,class:M(["payment-method-item",{active:s.selectedPayMethod===a.id}]),onClick:t=>n.selectPayMethod(a.id)},{default:d((()=>[m(U,{class:"payment-icon",src:a.icon},null,8,["src"]),m(k,{class:"payment-name"},{default:d((()=>[p(f(a.name),1)])),_:2},1024),s.selectedPayMethod===a.id?(c(),l(k,{key:0,class:"payment-check"},{default:d((()=>[p("✓")])),_:1})):P("",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1}),m(k,{class:"pay-action"},{default:d((()=>[m(j,{class:"pay-button",onClick:n.handlePay},{default:d((()=>[p(" 立即支付 "+f(s.amount?"¥"+s.amount:""),1)])),_:1},8,["onClick"])])),_:1})])),_:1})}],["__scopeId","data-v-14185abe"]]);export{U as default};
