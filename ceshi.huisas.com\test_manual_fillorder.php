<?php
/**
 * 简单测试手动补单修复
 */

include_once './includes/common.php';

// 测试订单号
$test_trade_no = '2025072717481113375';

echo "🧪 测试手动补单修复\n";
echo "测试订单: {$test_trade_no}\n\n";

// 1. 先重置订单状态为未支付
echo "1. 重置订单状态为未支付...\n";
$resetResult = $DB->exec("UPDATE pay_order SET status = '0', endtime = NULL WHERE trade_no = ?", [$test_trade_no]);
if ($resetResult !== false) {
    echo "✅ 订单状态已重置\n";
} else {
    echo "❌ 重置失败: " . $DB->error() . "\n";
    exit;
}

// 2. 模拟手动补单请求
echo "\n2. 模拟手动补单请求...\n";

// 模拟POST数据
$_POST['trade_no'] = $test_trade_no;

// 模拟AJAX请求处理逻辑
$trade_no = trim($_POST['trade_no']);
$row = $DB->getRow("SELECT A.*,B.name typename,B.showname typeshowname FROM pay_order A left join pay_type B on A.type=B.id WHERE trade_no=:trade_no limit 1", [':trade_no'=>$trade_no]);

if (!$row) {
    echo "❌ 订单不存在\n";
    exit;
}

if ($row['status'] > 0) {
    echo "❌ 订单不是未完成状态，当前状态: {$row['status']}\n";
    exit;
}

echo "📋 补单前状态: {$row['status']}\n";

// 执行状态更新
$date = date('Y-m-d H:i:s');
if ($DB->exec("update `pay_order` set `status` ='1' where `trade_no`='$trade_no'")) {
    echo "✅ 订单状态更新为已支付\n";
    
    $DB->exec("update `pay_order` set `endtime` ='$date',`date` =NOW() where `trade_no`='$trade_no'");
    echo "✅ 订单时间更新完成\n";
    
    $channel = \lib\Channel::get($row['channel']);
    echo "📡 获取渠道信息: " . ($channel ? "成功" : "失败") . "\n";
    
    // 🔥 使用修复后的逻辑：重新获取更新后的订单数据
    echo "🔄 重新获取更新后的订单数据...\n";
    $updatedRow = $DB->getRow("SELECT A.*,B.name typename,B.showname typeshowname FROM pay_order A left join pay_type B on A.type=B.id WHERE trade_no=:trade_no limit 1", [':trade_no'=>$trade_no]);
    
    if ($updatedRow) {
        echo "✅ 重新获取成功，状态: {$updatedRow['status']}\n";
        
        // 添加测试标记到日志
        \lib\Payment::writePaymentLog('manual_fillorder_test', [
            'message' => '手动补单修复测试',
            'trade_no' => $trade_no,
            'status_before' => $row['status'],
            'status_after' => $updatedRow['status'],
            'test_time' => date('Y-m-d H:i:s')
        ]);
        
        echo "📞 调用 processOrder...\n";
        try {
            processOrder($updatedRow);
            echo "✅ processOrder 调用成功\n";
        } catch (Exception $e) {
            echo "❌ processOrder 调用失败: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "⚠️ 重新获取失败，使用备用方案\n";
        $row['status'] = '1';
        $row['endtime'] = $date;
        $row['date'] = date('Y-m-d H:i:s');
        processOrder($row);
        echo "✅ 备用方案执行完成\n";
    }
    
    echo '{"code":200}';
} else {
    echo "❌ 订单状态更新失败: " . $DB->error() . "\n";
    echo '{"code":400,"msg":"修改订单失败！"}';
}

echo "\n\n3. 检查最终结果...\n";
$finalOrder = $DB->getRow("SELECT * FROM pay_order WHERE trade_no = ?", [$test_trade_no]);
if ($finalOrder) {
    echo "最终状态: {$finalOrder['status']}\n";
    echo "结束时间: " . ($finalOrder['endtime'] ?: '未设置') . "\n";
}

echo "\n🎉 测试完成！请检查日志文件查看 WebSocket 通知是否发送成功。\n";
?>
