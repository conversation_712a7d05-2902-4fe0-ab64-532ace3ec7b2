<template>
  <view class="container">
    <!-- 简化导航栏 -->
    <view class="navbar">
      <text class="navbar-title">反扫支付修复测试</text>
    </view>

    <view class="content">
      <view class="form-section">
        <text class="title">反扫支付修复测试</text>
        <text class="subtitle">测试修复后的反扫支付功能，检查submit_url是否使用绝对路径</text>
        
        <view class="form-group">
          <text class="label">支付金额：</text>
          <input 
            class="input" 
            type="digit" 
            v-model="amount" 
            placeholder="请输入金额"
          />
        </view>
        
        <view class="form-group">
          <text class="label">付款码：</text>
          <input 
            class="input" 
            type="text" 
            v-model="authCode" 
            placeholder="请输入18位付款码"
            maxlength="18"
          />
          <text class="hint">测试用付款码：281711908879118544（支付宝格式）</text>
        </view>
        
        <view class="form-group">
          <text class="label">商品名称：</text>
          <input 
            class="input" 
            type="text" 
            v-model="productName" 
            placeholder="请输入商品名称"
          />
        </view>
        
        <button class="test-button" @click="testScanPay" :disabled="isLoading">
          {{ isLoading ? '测试中...' : '测试反扫支付' }}
        </button>
      </view>
      
      <view class="result-section" v-if="result">
        <text class="result-title">测试结果：</text>
        <view :class="['result-content', resultType]">
          <text class="result-text">{{ result }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// import { post } from '@/utils/request'

export default {
  data() {
    return {
      amount: '0.01',
      authCode: '281711908879118544',
      productName: '测试商品',
      isLoading: false,
      result: '',
      resultType: 'info'
    }
  },
  mounted() {
    console.log('反扫支付测试页面已加载')
  },
  methods: {
    async testScanPay() {
      console.log('测试按钮被点击')

      // 验证输入
      if (!this.amount || parseFloat(this.amount) <= 0) {
        this.showResult('请输入有效的金额', 'error')
        return
      }
      
      if (!/^\d{18}$/.test(this.authCode)) {
        this.showResult('付款码格式不正确，必须是18位数字', 'error')
        return
      }
      
      if (!this.productName.trim()) {
        this.showResult('请输入商品名称', 'error')
        return
      }
      
      this.isLoading = true
      this.showResult('正在测试反扫支付...', 'info')
      
      try {
        // 生成订单号
        const outTradeNo = 'SCAN' + Date.now() + Math.floor(Math.random() * 1000)
        
        // 构建请求参数
        const params = {
          amount: this.amount,
          auth_code: this.authCode,
          out_trade_no: outTradeNo,
          product_name: this.productName
        }
        
        console.log('反扫支付测试参数:', params)

        // 调用反扫支付接口
        const response = await this.callAPI(params)

        console.log('反扫支付测试响应:', response)
        
        if (response.code === 0) {
          const submitUrl = response.submit_url || ''
          const isAbsoluteUrl = submitUrl.startsWith('http')
          
          let resultText = `✅ 反扫支付接口调用成功！\n\n`
          resultText += `响应数据：\n${JSON.stringify(response, null, 2)}\n\n`
          resultText += `关键信息：\n`
          resultText += `- 订单创建成功\n`
          resultText += `- submit_url: ${submitUrl}\n`
          resultText += `- 是否为绝对路径: ${isAbsoluteUrl ? '是' : '否'}\n\n`
          resultText += `下一步：\n`
          resultText += `1. 前端会跳转到webview页面\n`
          resultText += `2. webview加载submit_url进行支付处理\n`
          resultText += `3. submit2.php会识别auth_code参数并调用反扫支付`
          
          this.showResult(resultText, 'success')
          
          // 模拟前端跳转逻辑
          const webviewUrl = `/pages/webview/index?url=${encodeURIComponent(submitUrl)}&title=付款码支付`
          console.log('前端跳转URL:', webviewUrl)
          
          // 显示跳转信息
          setTimeout(() => {
            let extendedResult = this.result + `\n\n🔗 前端跳转信息：\n`
            extendedResult += `- webview页面URL: ${webviewUrl}\n`
            extendedResult += `- 编码后的submit_url: ${encodeURIComponent(submitUrl)}\n`
            extendedResult += `- 解码验证: ${decodeURIComponent(encodeURIComponent(submitUrl))}`
            this.result = extendedResult
          }, 2000)
          
        } else {
          let resultText = `❌ 反扫支付失败：\n\n`
          resultText += `错误信息：${response.msg}\n`
          resultText += `错误代码：${response.code}\n\n`
          resultText += `完整响应：\n${JSON.stringify(response, null, 2)}`
          
          this.showResult(resultText, 'error')
        }
        
      } catch (error) {
        console.error('反扫支付测试失败:', error)
        this.showResult(`❌ 请求失败：${error.message || error.msg || '未知错误'}`, 'error')
      } finally {
        this.isLoading = false
      }
    },

    // API调用方法
    async callAPI(params) {
      return new Promise((resolve, reject) => {
        // 构建FormData
        const formData = new FormData()
        Object.keys(params).forEach(key => {
          formData.append(key, params[key])
        })

        uni.request({
          url: 'http://ceshi.huisas.com/user/ajax2.php?act=scan_pay',
          method: 'POST',
          data: params,
          header: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          success: (res) => {
            console.log('API原始响应:', res)
            try {
              if (typeof res.data === 'string') {
                const jsonData = JSON.parse(res.data)
                resolve(jsonData)
              } else {
                resolve(res.data)
              }
            } catch (error) {
              console.error('JSON解析失败:', error)
              reject(new Error('响应格式错误'))
            }
          },
          fail: (error) => {
            console.error('API请求失败:', error)
            reject(new Error(error.errMsg || '网络请求失败'))
          }
        })
      })
    },

    showResult(message, type) {
      this.result = message
      this.resultType = type
    }
  }
}
</script>

<style>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.navbar {
  background-color: #5145F7;
  padding: 20rpx;
  padding-top: 60rpx;
  text-align: center;
}

.navbar-title {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

.content {
  padding: 20rpx;
}

.form-section {
  background: white;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  display: block;
  line-height: 1.5;
}

.form-group {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.input:focus {
  border-color: #5145F7;
}

.hint {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  display: block;
}

.test-button {
  width: 100%;
  background-color: #5145F7;
  color: white;
  padding: 24rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.test-button:disabled {
  background-color: #ccc;
}

.result-section {
  background: white;
  padding: 30rpx;
  border-radius: 12rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.result-content {
  padding: 20rpx;
  border-radius: 8rpx;
  border: 2rpx solid;
}

.result-content.success {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.result-content.error {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.result-content.info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

.result-text {
  font-size: 26rpx;
  line-height: 1.6;
  white-space: pre-wrap;
  font-family: monospace;
}
</style>
