<template>
  <view class="staff-home-container">
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <view class="navbar-content">
        <view class="user-info">
          <view class="avatar">
            <text class="avatar-text">{{ staffInfo?.name?.charAt(0) || 'S' }}</text>
          </view>
          <view class="user-details">
            <text class="user-name">{{ staffInfo?.name || '员工' }}</text>
            <text class="user-role">{{ staffInfo?.role || '收银员' }}</text>
          </view>
        </view>
        
        <view class="navbar-actions">
          <view class="action-btn" @tap="showQRCode">
            <text class="action-icon">📱</text>
          </view>
          <view class="action-btn" @tap="logout">
            <text class="action-icon">🚪</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 统计卡片区域 -->
    <view class="stats-section">
      <view class="stats-grid">
        <view class="stat-card">
          <view class="stat-value">{{ todayStats.amount || '0.00' }}</view>
          <view class="stat-label">今日收款</view>
        </view>
        <view class="stat-card">
          <view class="stat-value">{{ todayStats.count || '0' }}</view>
          <view class="stat-label">今日订单</view>
        </view>
        <view class="stat-card">
          <view class="stat-value">{{ monthStats.amount || '0.00' }}</view>
          <view class="stat-label">本月收款</view>
        </view>
        <view class="stat-card">
          <view class="stat-value">{{ monthStats.count || '0' }}</view>
          <view class="stat-label">本月订单</view>
        </view>
      </view>
    </view>

    <!-- 功能菜单区域 -->
    <view class="menu-section">
      <view class="menu-title">快捷功能</view>
      <view class="menu-grid">
        <view class="menu-item" @tap="navigateTo('/pages/staff-qrcode/index')">
          <view class="menu-icon">💳</view>
          <text class="menu-text">我的收款码</text>
        </view>
        <view class="menu-item" @tap="navigateTo('/pages/staff-orders/index')">
          <view class="menu-icon">📋</view>
          <text class="menu-text">我的订单</text>
        </view>
        <view class="menu-item" @tap="navigateTo('/pages/staff-stats/index')">
          <view class="menu-icon">📊</view>
          <text class="menu-text">收款统计</text>
        </view>
        <view class="menu-item" @tap="navigateTo('/pages/staff-profile/index')">
          <view class="menu-icon">⚙️</view>
          <text class="menu-text">个人设置</text>
        </view>
      </view>
    </view>

    <!-- 最近订单区域 -->
    <view class="recent-section">
      <view class="section-header">
        <text class="section-title">最近订单</text>
        <text class="section-more" @tap="navigateTo('/pages/staff-orders/index')">查看全部</text>
      </view>
      
      <view class="order-list">
        <view v-if="recentOrders.length === 0" class="empty-state">
          <text class="empty-text">暂无订单记录</text>
        </view>
        
        <view v-for="order in recentOrders" :key="order.id" class="order-item">
          <view class="order-info">
            <view class="order-amount">¥{{ order.amount }}</view>
            <view class="order-details">
              <text class="order-id">订单号：{{ order.trade_no }}</text>
              <text class="order-time">{{ formatTime(order.create_time) }}</text>
            </view>
          </view>
          <view class="order-status" :class="getStatusClass(order.status)">
            {{ getStatusText(order.status) }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { request } from '@/utils/request.js'

export default {
  name: 'StaffHome',
  data() {
    return {
      staffInfo: null,
      todayStats: {
        amount: '0.00',
        count: 0
      },
      monthStats: {
        amount: '0.00',
        count: 0
      },
      recentOrders: [],
      loading: false
    }
  },
  
  onLoad() {
    console.log('📱 员工首页加载')
    this.initPage()
  },
  
  onShow() {
    // 页面显示时刷新数据
    this.loadPageData()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      this.checkLoginStatus()
      this.loadStaffInfo()
      this.loadPageData()
    },
    
    // 检查登录状态
    checkLoginStatus() {
      const staffToken = uni.getStorageSync('staff_token')
      const staffInfo = uni.getStorageSync('staff_info')
      
      if (!staffToken || !staffInfo) {
        console.log('❌ 员工未登录，跳转到登录页')
        uni.reLaunch({
          url: '/pages/staff-login/index'
        })
        return false
      }
      
      return true
    },
    
    // 加载员工信息
    loadStaffInfo() {
      const staffInfo = uni.getStorageSync('staff_info')
      if (staffInfo) {
        this.staffInfo = staffInfo
        console.log('✅ 员工信息加载成功:', staffInfo)
      }
    },
    
    // 加载页面数据
    async loadPageData() {
      if (!this.checkLoginStatus()) return
      
      try {
        this.loading = true
        
        // 并行加载数据
        await Promise.all([
          this.loadStats(),
          this.loadRecentOrders()
        ])
        
        console.log('✅ 员工首页数据加载完成')
        
      } catch (error) {
        console.error('❌ 加载员工首页数据失败:', error)
        uni.showToast({
          title: '数据加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 加载统计数据
    async loadStats() {
      try {
        const response = await request({
          url: '/user/staff.php?act=getStats',
          method: 'POST',
          data: {
            staff_id: this.staffInfo?.id
          }
        })
        
        if (response && response.code === 0) {
          this.todayStats = response.data.today || this.todayStats
          this.monthStats = response.data.month || this.monthStats
          console.log('✅ 统计数据加载成功')
        }
      } catch (error) {
        console.error('❌ 加载统计数据失败:', error)
      }
    },
    
    // 加载最近订单
    async loadRecentOrders() {
      try {
        const response = await request({
          url: '/user/staff.php?act=getRecentOrders',
          method: 'POST',
          data: {
            staff_id: this.staffInfo?.id,
            limit: 5
          }
        })
        
        if (response && response.code === 0) {
          this.recentOrders = response.data || []
          console.log('✅ 最近订单加载成功')
        }
      } catch (error) {
        console.error('❌ 加载最近订单失败:', error)
      }
    },
    
    // 显示收款码
    showQRCode() {
      uni.navigateTo({
        url: '/pages/staff-qrcode/index'
      })
    },
    
    // 页面导航
    navigateTo(url) {
      uni.navigateTo({
        url: url
      })
    },
    
    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return ''
      
      const date = new Date(timestamp * 1000)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前'
      } else {
        return date.toLocaleDateString()
      }
    },
    
    // 获取订单状态样式
    getStatusClass(status) {
      switch (status) {
        case 1: return 'status-success'
        case 0: return 'status-pending'
        case -1: return 'status-failed'
        default: return 'status-unknown'
      }
    },
    
    // 获取订单状态文本
    getStatusText(status) {
      switch (status) {
        case 1: return '已完成'
        case 0: return '待支付'
        case -1: return '已失败'
        default: return '未知'
      }
    },
    
    // 退出登录
    logout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除登录信息
            uni.removeStorageSync('staff_token')
            uni.removeStorageSync('staff_info')
            uni.removeStorageSync('staff_merchant_uid')
            
            // 跳转到登录页
            uni.reLaunch({
              url: '/pages/staff-login/index'
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.staff-home-container {
  min-height: 100vh;
  background: #f5f6fa;
}

.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  color: #ffffff;
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.avatar-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-role {
  font-size: 24rpx;
  opacity: 0.8;
}

.navbar-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 28rpx;
}

.stats-section {
  padding: 40rpx;
  margin-top: -20rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
}

.stat-label {
  font-size: 28rpx;
  color: #666666;
}

.menu-section {
  padding: 0 40rpx 40rpx;
}

.menu-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
}

.menu-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 30rpx;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.menu-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.menu-text {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
}

.recent-section {
  padding: 0 40rpx 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.section-more {
  font-size: 28rpx;
  color: #667eea;
}

.order-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.empty-state {
  padding: 80rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-info {
  flex: 1;
}

.order-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.order-id,
.order-time {
  font-size: 24rpx;
  color: #999999;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-success {
  background: #e8f5e8;
  color: #52c41a;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-failed {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-unknown {
  background: #f0f0f0;
  color: #999999;
}
</style>
