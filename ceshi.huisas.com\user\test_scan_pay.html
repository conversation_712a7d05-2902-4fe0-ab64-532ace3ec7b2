<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>反扫支付测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #5145F7;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #4139d9;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .quick-amounts {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .quick-amount {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .quick-amount:hover {
            background-color: #e9ecef;
        }
        .auth-code-examples {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        .auth-code-example {
            background-color: #f8f9fa;
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 3px;
            cursor: pointer;
        }
        .auth-code-example:hover {
            background-color: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 反扫支付测试</h1>
        <p>测试商户登录状态下的反扫支付功能</p>
        
        <form id="scanPayForm">
            <div class="form-group">
                <label for="amount">收款金额 (元):</label>
                <input type="number" id="amount" step="0.01" min="0.01" placeholder="请输入收款金额" required>
                <div class="quick-amounts">
                    <div class="quick-amount" onclick="setAmount(1)">1元</div>
                    <div class="quick-amount" onclick="setAmount(10)">10元</div>
                    <div class="quick-amount" onclick="setAmount(50)">50元</div>
                    <div class="quick-amount" onclick="setAmount(100)">100元</div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="authCode">客户付款码 (18位数字):</label>
                <input type="text" id="authCode" maxlength="18" placeholder="请输入18位付款码" required>
                <div class="auth-code-examples">
                    <div>测试付款码示例 (点击使用):</div>
                    <div class="auth-code-example" onclick="setAuthCode('280123456789012345')">支付宝: 280123456789012345</div>
                    <div class="auth-code-example" onclick="setAuthCode('130123456789012345')">微信: 130123456789012345</div>
                    <div class="auth-code-example" onclick="setAuthCode('910123456789012345')">QQ钱包: 910123456789012345</div>
                    <div class="auth-code-example" onclick="setAuthCode('620123456789012345')">银联: 620123456789012345</div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="productName">商品名称:</label>
                <input type="text" id="productName" value="商户收款" placeholder="商品名称">
            </div>
            
            <button type="submit">🔄 发起反扫支付</button>
            <button type="button" onclick="clearResult()">清空结果</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        // 设置快捷金额
        function setAmount(amount) {
            document.getElementById('amount').value = amount;
        }
        
        // 设置付款码
        function setAuthCode(code) {
            document.getElementById('authCode').value = code;
            
            // 识别支付方式
            const prefix = code.substring(0, 2);
            let payType = '';
            if (['25', '26', '27', '28', '29', '30'].includes(prefix)) {
                payType = '支付宝';
            } else if (['10', '11', '12', '13', '14', '15'].includes(prefix)) {
                payType = '微信支付';
            } else if (prefix === '91') {
                payType = 'QQ钱包';
            } else if (prefix === '62') {
                payType = '银联支付';
            }
            
            if (payType) {
                showResult(`已识别支付方式: ${payType}`, 'info');
            }
        }
        
        // 清空结果
        function clearResult() {
            document.getElementById('result').innerHTML = '';
        }
        
        // 显示结果
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
        
        // 表单提交处理
        document.getElementById('scanPayForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const amount = document.getElementById('amount').value;
            const authCode = document.getElementById('authCode').value;
            const productName = document.getElementById('productName').value;
            
            // 验证输入
            if (!amount || parseFloat(amount) <= 0) {
                showResult('请输入有效的收款金额', 'error');
                return;
            }
            
            if (!authCode || !/^\d{18}$/.test(authCode)) {
                showResult('请输入正确的18位付款码', 'error');
                return;
            }
            
            // 生成订单号
            const outTradeNo = 'SCAN' + Date.now() + Math.floor(Math.random() * 1000);
            
            // 构建请求参数
            const params = new URLSearchParams({
                act: 'scanPay',
                amount: amount,
                auth_code: authCode,
                out_trade_no: outTradeNo,
                product_name: productName
            });
            
            showResult('正在处理反扫支付请求...', 'info');
            
            try {
                const response = await fetch('http://ceshi.huisas.com/user/ajax2.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    credentials: 'include', // 包含cookies用于登录状态
                    body: params
                });
                
                const result = await response.json();
                
                if (result.code === 0) {
                    const successMessage = `✅ 反扫支付成功！
订单号: ${result.trade_no}
收款金额: ¥${result.amount}
支付方式: ${result.pay_type}
买家信息: ${result.buyer || '未知'}
第三方订单号: ${result.api_trade_no || '未知'}`;
                    showResult(successMessage, 'success');
                } else {
                    showResult(`❌ 反扫支付失败: ${result.msg}`, 'error');
                }
            } catch (error) {
                console.error('请求失败:', error);
                showResult(`❌ 网络请求失败: ${error.message}`, 'error');
            }
        });
        
        // 页面加载时的提示
        window.onload = function() {
            showResult('请先登录商户后台，然后输入收款金额和客户付款码进行测试', 'info');
        };
    </script>
</body>
</html>
