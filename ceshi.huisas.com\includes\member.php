<?php
$clientip=real_ip($conf['ip_type']?$conf['ip_type']:0);

if(isset($_COOKIE["admin_token"]))
{
	$token=authcode(daddslashes($_COOKIE['admin_token']), 'DECODE', SYS_KEY);
	list($user, $sid, $expiretime) = explode("\t", $token);
	$session=md5($conf['admin_user'].$conf['admin_pwd'].$password_hash);
	if($session==$sid && $expiretime>time()) {
		$islogin=1;
	}
}
// 支持多种方式获取user_token：Cookie、POST数据、GET参数
$user_token = '';
if(isset($_COOKIE["user_token"])) {
	$user_token = $_COOKIE["user_token"];
	error_log("从Cookie获取user_token: " . substr($user_token, 0, 20) . "...");
} elseif(isset($_POST["user_token"])) {
	$user_token = $_POST["user_token"];
	error_log("从POST获取user_token: " . substr($user_token, 0, 20) . "...");
} elseif(isset($_GET["user_token"])) {
	$user_token = $_GET["user_token"];
	error_log("从GET获取user_token: " . substr($user_token, 0, 20) . "...");
}

if(!empty($user_token))
{
	$token=authcode(daddslashes($user_token), 'DECODE', SYS_KEY);
	error_log("解码后的token: " . $token);
	list($uid, $sid, $expiretime) = explode("\t", $token);
	$uid = intval($uid);
	error_log("解析出的UID: $uid, 过期时间: $expiretime, 当前时间: " . time());
	$userrow=$DB->getRow("SELECT * FROM pre_user WHERE uid=:uid limit 1", [':uid'=>$uid]);
	if($userrow) {
		$session=md5($userrow['uid'].$userrow['key'].$password_hash);
		error_log("计算的session: $session, 传入的sid: $sid");
		if($session==$sid && $expiretime>time()) {
			$islogin2=1;
			error_log("用户认证成功 - UID: $uid");
		} else {
			error_log("用户认证失败 - session不匹配或token过期");
		}
	} else {
		error_log("用户不存在 - UID: $uid");
	}
} else {
	error_log("未找到user_token，Cookie内容: " . print_r($_COOKIE, true));
}
?>