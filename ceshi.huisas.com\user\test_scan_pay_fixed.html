<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>反扫支付测试 - 修复版</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🔧 反扫支付测试 - 修复版</h1>
    
    <div class="info">
        <h3>修复内容：</h3>
        <ul>
            <li>✅ 修复数据库表前缀 (pre_ → pay_)</li>
            <li>✅ 修复API返回格式问题</li>
            <li>✅ 优化支付插件调用逻辑</li>
            <li>✅ 修复case名称 (scanPay → scan_pay)</li>
        </ul>
    </div>

    <form id="scanPayForm">
        <div class="form-group">
            <label for="amount">收款金额 (元):</label>
            <input type="number" id="amount" name="amount" value="1" step="0.01" min="0.01" required>
        </div>

        <div class="form-group">
            <label for="pay_type">支付方式:</label>
            <select id="pay_type" name="pay_type" required>
                <option value="alipay" selected>支付宝</option>
                <option value="wxpay" disabled>微信支付(暂不可用)</option>
            </select>
        </div>

        <div class="form-group">
            <label for="auth_code">客户付款码 (18位数字):</label>
            <input type="text" id="auth_code" name="auth_code" placeholder="280123456789012345" maxlength="18" required>
        </div>

        <div class="form-group">
            <label for="out_trade_no">商户订单号:</label>
            <input type="text" id="out_trade_no" name="out_trade_no" readonly required>
        </div>

        <div class="form-group">
            <label for="product_name">商品名称:</label>
            <input type="text" id="product_name" name="product_name" value="商户收款" required>
        </div>

        <button type="submit">🔍 发起反扫支付</button>
    </form>

    <div id="result"></div>

    <script>
        document.getElementById('scanPayForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            // 重新生成订单号确保唯一性
            generateOutTradeNo();

            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">正在处理反扫支付...</div>';

            const formData = new FormData(this);

            // 调试：显示发送的参数
            console.log('发送的参数:');
            for (let [key, value] of formData.entries()) {
                console.log(key + ': ' + value);
            }

            try {
                const response = await fetch('/user/ajax2.php?act=scan_pay', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include'
                });
                
                const text = await response.text();
                console.log('原始响应:', text);
                
                let result;
                try {
                    result = JSON.parse(text);
                } catch (parseError) {
                    throw new Error('API返回格式错误: ' + text);
                }
                
                if (result.code === 0) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ 反扫支付成功！</h3>
                            <p><strong>订单号:</strong> ${result.trade_no}</p>
                            <p><strong>支付金额:</strong> ¥${result.amount}</p>
                            <p><strong>支付方式:</strong> ${result.pay_type}</p>
                            <p><strong>买家信息:</strong> ${result.buyer || '未获取'}</p>
                            <p><strong>第三方订单号:</strong> ${result.api_trade_no || '未获取'}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ 反扫支付失败</h3>
                            <p><strong>错误信息:</strong> ${result.msg}</p>
                            <p><strong>错误代码:</strong> ${result.code}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('请求错误:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ 请求异常</h3>
                        <p><strong>错误信息:</strong> ${error.message}</p>
                    </div>
                `;
            }
        });

        // 自动填充测试数据
        document.addEventListener('DOMContentLoaded', function() {
            // 生成测试付款码
            const testAuthCode = '280123456789012345';
            document.getElementById('auth_code').value = testAuthCode;

            // 生成商户订单号
            generateOutTradeNo();
        });

        // 生成商户订单号
        function generateOutTradeNo() {
            const timestamp = Date.now();
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            const outTradeNo = 'TEST' + timestamp + random;
            document.getElementById('out_trade_no').value = outTradeNo;
        }
    </script>
</body>
</html>
