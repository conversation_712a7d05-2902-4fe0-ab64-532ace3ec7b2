<template>
	<view class="container">
		<!-- 顶部标题栏 -->
		<view class="header">
			<view class="header-left">
				<uni-icons type="back" size="24" color="#FFFFFF" @click="goBack"></uni-icons>
			</view>
			<view class="header-center">
				<text class="header-title">我的钱包</text>
			</view>
			<view class="header-right">
				<image src="/static/mine/qianbao/more.png" mode="aspectFit" class="icon-more"></image>
			</view>
		</view>
		
		<!-- 余额展示 -->
		<view class="balance-card">
			<text class="balance-label">当前余额(元)</text>
			<text class="balance-amount">¥ 5,83.30</text>
			
			<!-- 操作按钮 -->
			<view class="action-buttons">
				<view class="action-button withdraw">
					<image src="/static/mine/qianbao/tixianka.png" mode="aspectFit" class="button-icon"></image>
					<text class="button-text">提现到银行卡</text>
				</view>
				<view class="action-button manage">
					<image src="/static/mine/qianbao/guanlika.png" mode="aspectFit" class="button-icon"></image>
					<text class="button-text">管理银行卡</text>
				</view>
			</view>
		</view>
		
		<!-- 收入信息 -->
		<view class="income-info">
			<view class="income-item">
				<view class="income-icon-container">
					<image src="/static/mine/qianbao/jinrishouru.png" mode="aspectFit" class="income-icon"></image>
				</view>
				<view class="income-detail">
					<text class="income-label">今日收入</text>
					<text class="income-amount">¥ 382.50</text>
				</view>
			</view>
			
			<view class="income-item">
				<view class="income-icon-container month">
					<image src="/static/mine/qianbao/jinrishouru.png" mode="aspectFit" class="income-icon"></image>
				</view>
				<view class="income-detail">
					<text class="income-label">本月收入</text>
					<text class="income-amount">¥ 12,583.40</text>
				</view>
			</view>
			
			<view class="income-item">
				<view class="income-icon-container pending">
					<image src="/static/mine/qianbao/daijiesuan.png" mode="aspectFit" class="income-icon"></image>
				</view>
				<view class="income-detail">
					<text class="income-label">待结算</text>
					<text class="income-amount">¥ 1,283.30</text>
				</view>
			</view>
		</view>
		
		<!-- 交易明细 -->
		<view class="transaction-history">
			<view class="history-header">
				<text class="history-title">收支明细</text>
				<text class="view-all">全部 ></text>
			</view>
			
			<!-- 今日交易 -->
			<view class="date-group">
				<text class="date-header">今天 (4月14日)</text>
				
				<!-- 交易项 -->
				<view class="transaction-item">
					<view class="transaction-left">
						<view class="transaction-icon income">
							<image src="/static/mine/qianbao/shouru.png" mode="aspectFit"></image>
						</view>
						<view class="transaction-info">
							<text class="transaction-type">收款</text>
							<text class="transaction-time">15:23:45</text>
						</view>
					</view>
					<text class="transaction-amount income">+ ¥ 128.00</text>
				</view>
				
				<view class="transaction-item">
					<view class="transaction-left">
						<view class="transaction-icon income">
							<image src="/static/mine/qianbao/shouru.png" mode="aspectFit"></image>
						</view>
						<view class="transaction-info">
							<text class="transaction-type">收款</text>
							<text class="transaction-time">13:05:22</text>
						</view>
					</view>
					<text class="transaction-amount income">+ ¥ 85.50</text>
				</view>
				
				<view class="transaction-item">
					<view class="transaction-left">
						<view class="transaction-icon withdraw">
							<image src="/static/mine/qianbao/tixian.png" mode="aspectFit"></image>
						</view>
						<view class="transaction-info">
							<text class="transaction-type">提现</text>
							<text class="transaction-time">10:18:36</text>
						</view>
					</view>
					<text class="transaction-amount withdraw">- ¥ 1,000.00</text>
				</view>
			</view>
			
			<!-- 昨日交易 -->
			<view class="date-group">
				<text class="date-header">昨天 (4月13日)</text>
				
				<!-- 交易项 -->
				<view class="transaction-item">
					<view class="transaction-left">
						<view class="transaction-icon income">
							<image src="/static/mine/qianbao/shouru.png" mode="aspectFit"></image>
						</view>
						<view class="transaction-info">
							<text class="transaction-type">收款</text>
							<text class="transaction-time">18:45:12</text>
						</view>
					</view>
					<text class="transaction-amount income">+ ¥ 156.00</text>
				</view>
				
				<view class="transaction-item">
					<view class="transaction-left">
						<view class="transaction-icon income">
							<image src="/static/mine/qianbao/shouru.png" mode="aspectFit"></image>
						</view>
						<view class="transaction-info">
							<text class="transaction-type">收款</text>
							<text class="transaction-time">14:23:50</text>
						</view>
					</view>
					<text class="transaction-amount income">+ ¥ 95.80</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons'

export default {
	components: {
		uniIcons
	},
	data() {
		return {
			
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		}
	}
}
</script>

<style>
page {
	font-family: 'Segoe UI', sans-serif;
	background-color: #f5f5f5;
}

.container {
	width: 100%;
	min-height: 100vh;
}

/* 顶部标题栏 */
.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #5145F7;
	color: white;
	padding: 32rpx;
	position: relative;
}

.header-left {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
}

.header-center {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
}

.header-right {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

.icon-more {
	width: 48rpx;
	height: 48rpx;
}

/* 余额卡片 */
.balance-card {
	background-color: #5145F7;
	padding: 32rpx;
	color: white;
	padding-bottom: 100rpx;
}

.balance-label {
	font-size: 28rpx;
	opacity: 0.8;
	margin-bottom: 16rpx;
	display: block;
}

.balance-amount {
	font-size: 60rpx;
	font-weight: bold;
	margin-bottom: 32rpx;
	display: block;
}

/* 操作按钮 */
.action-buttons {
	display: flex;
	margin: 0 -16rpx;
}

.action-button {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: white;
	height: 80rpx;
	border-radius: 8rpx;
	margin: 0 16rpx;
}

.button-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 12rpx;
}

.button-text {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

/* 收入信息 */
.income-info {
	margin: -60rpx 32rpx 32rpx;
	background-color: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.income-item {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1px solid #f1f1f1;
}

.income-item:last-child {
	border-bottom: none;
}

.income-icon-container {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	background-color: #E9EFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.income-icon-container.month {
	background-color: #E4FFEF;
}

.income-icon-container.pending {
	background-color: #EFE4FF;
}

.income-icon {
	width: 32rpx;
	height: 32rpx;
}

.income-detail {
	flex: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.income-label {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

.income-amount {
	font-size: 32rpx;
	font-weight: bold;
}

/* 交易明细 */
.transaction-history {
	margin: 32rpx;
	background-color: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.history-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 0 32rpx;
	border-bottom: 1px solid #f1f1f1;
}

.history-title {
	font-size: 32rpx;
	font-weight: bold;
}

.view-all {
	font-size: 28rpx;
	color: #999;
}

.date-group {
	margin-top: 24rpx;
}

.date-header {
	font-size: 26rpx;
	color: #999;
	margin-bottom: 16rpx;
}

.transaction-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1px solid #f5f5f5;
}

.transaction-item:last-child {
	border-bottom: none;
}

.transaction-left {
	display: flex;
	align-items: center;
}

.transaction-icon {
	width: 72rpx;
	height: 72rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.transaction-icon.income {
	background-color: #E9EFFF;
}

.transaction-icon.withdraw {
	background-color: #FFE4E4;
}

.transaction-icon image {
	width: 40rpx;
	height: 40rpx;
}

.transaction-info {
	display: flex;
	flex-direction: column;
}

.transaction-type {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.transaction-time {
	font-size: 24rpx;
	color: #999;
}

.transaction-amount {
	font-size: 32rpx;
	font-weight: bold;
}

.transaction-amount.income {
	color: #4CD964;
}

.transaction-amount.withdraw {
	color: #FF3B30;
}
</style>
</style>

