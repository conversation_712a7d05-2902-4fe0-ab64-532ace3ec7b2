<?php
// 测试回调处理
echo "<h2>测试EasyPay回调处理</h2>";

// 模拟一个真实的订单号（你可以从数据库中找一个测试订单号）
$test_trade_no = '2025072605062650741'; // 替换为实际的测试订单号

echo "<h3>测试信息</h3>";
echo "<p><strong>测试订单号:</strong> {$test_trade_no}</p>";

// 构建回调URL
$notify_url = "http://ceshi.huisas.com/pay.php?s=notify/{$test_trade_no}/";
echo "<p><strong>回调URL:</strong> <a href='{$notify_url}' target='_blank'>{$notify_url}</a></p>";

// 模拟回调数据（EasyPay的JSON格式）
$callback_data = [
    'reqHeader' => [
        'version' => '1.0',
        'reqTime' => date('YmdHis'),
        'orgCode' => 'test_org'
    ],
    'reqBody' => [
        'respOrderInfo' => [
            'orgTrace' => $test_trade_no,
            'outTrace' => 'easypay_' . time(),
            'transAmount' => '1.00',
            'userId' => 'test_user',
            'pcTrace' => 'pc_' . time()
        ],
        'respStateInfo' => [
            'transState' => '1', // 1表示成功
            'respCode' => '000000',
            'respMsg' => 'Success'
        ]
    ],
    'reqSign' => 'test_signature'
];

echo "<h3>模拟回调数据</h3>";
echo "<pre>" . json_encode($callback_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

echo "<h3>测试步骤</h3>";
echo "<ol>";
echo "<li>点击上面的回调URL链接，看是否能正常访问</li>";
echo "<li>检查是否有错误信息</li>";
echo "<li>查看日志文件 easypay_notify_debug.log</li>";
echo "<li>如果需要完整测试，需要从EasyPay服务商发送真实的回调数据</li>";
echo "</ol>";

echo "<h3>日志文件位置</h3>";
echo "<p>日志文件路径: <code>" . realpath('.') . "/easypay_notify_debug.log</code></p>";

// 检查日志文件是否存在
$log_file = './easypay_notify_debug.log';
if (file_exists($log_file)) {
    echo "<p style='color: green;'>✓ 日志文件存在</p>";
    
    // 显示最近的日志内容
    $log_content = file_get_contents($log_file);
    $log_lines = explode("\n", $log_content);
    $recent_lines = array_slice($log_lines, -20); // 显示最后20行
    
    echo "<h4>最近的日志内容（最后20行）:</h4>";
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars(implode("\n", $recent_lines));
    echo "</pre>";
} else {
    echo "<p style='color: orange;'>⚠ 日志文件不存在，可能还没有回调请求</p>";
}

echo "<h3>下一步</h3>";
echo "<p>1. 如果回调URL能正常访问，说明路由配置正确</p>";
echo "<p>2. 创建一个真实的支付订单进行测试</p>";
echo "<p>3. 联系EasyPay服务商确认他们的回调配置是否正确</p>";
echo "<p>4. 检查服务器防火墙是否允许EasyPay的回调请求</p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
pre { background: #f8f8f8; padding: 10px; border-radius: 4px; }
a { color: #007cba; }
</style>
