<?php
/**
 * 快速检查WebSocket修复状态
 */

require_once './includes/common.php';

echo "<h1>🔧 WebSocket修复状态检查</h1>";

// 检查修复是否生效
echo "<h2>📋 修复状态检查</h2>";

// 1. 检查Payment.php是否包含修复代码
$paymentFile = SYSTEM_ROOT . 'lib/Payment.php';
$paymentContent = file_get_contents($paymentFile);

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h3>✅ Payment.php修复检查</h3>";

$hasFixCode = strpos($paymentContent, 'shouldProcessOrder') !== false;
echo "<p><strong>包含修复代码:</strong> " . ($hasFixCode ? '✅ 是' : '❌ 否') . "</p>";

$hasOrderCheck = strpos($paymentContent, 'order_already_paid') !== false;
echo "<p><strong>包含订单状态检查:</strong> " . ($hasOrderCheck ? '✅ 是' : '❌ 否') . "</p>";

$hasForceWebSocket = strpos($paymentContent, '关键修复：确保WebSocket通知被执行') !== false;
echo "<p><strong>包含强制WebSocket通知:</strong> " . ($hasForceWebSocket ? '✅ 是' : '❌ 否') . "</p>";

echo "</div>";

// 2. 检查WebSocket通知函数
echo "<div style='background: #f8f8f8; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h3>🔧 WebSocket功能检查</h3>";

$websocketFile = SYSTEM_ROOT . "websocket_notify_workerman.php";
echo "<p><strong>WebSocket文件存在:</strong> " . (file_exists($websocketFile) ? '✅ 是' : '❌ 否') . "</p>";

if (file_exists($websocketFile)) {
    include_once $websocketFile;
}

echo "<p><strong>通知函数存在:</strong> " . (function_exists('sendWebSocketPaymentNotification') ? '✅ 是' : '❌ 否') . "</p>";
echo "</div>";

// 3. 手动测试WebSocket通知
if (function_exists('sendWebSocketPaymentNotification')) {
    echo "<div style='background: #e6f3ff; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>🧪 手动测试WebSocket通知</h3>";
    
    $testData = [
        'trade_no' => 'FIX_TEST_' . date('YmdHis'),
        'uid' => '1000',
        'money' => '0.01',
        'type' => 'alipay',
        'typename' => '修复测试',
        'addtime' => date('Y-m-d H:i:s'),
        'api_trade_no' => 'FIX_API_' . time(),
        'buyer' => '<EMAIL>',
        'status' => 'success'
    ];
    
    try {
        $result = sendWebSocketPaymentNotification($testData);
        if ($result) {
            echo "<p>✅ 手动WebSocket通知发送成功</p>";
            echo "<p>🎉 WebSocket功能正常，修复应该有效</p>";
        } else {
            echo "<p>❌ 手动WebSocket通知发送失败</p>";
            echo "<p>⚠️ WebSocket服务可能有问题</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ 手动WebSocket通知异常: " . $e->getMessage() . "</p>";
    }
    
    echo "</div>";
}

// 4. 修复说明
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h3>🔧 修复说明</h3>";
echo "<p><strong>问题原因:</strong> 真实支付回调时，如果订单状态更新失败（比如订单已经是已支付状态），原代码会跳过WebSocket通知</p>";
echo "<p><strong>修复方案:</strong> 即使订单状态更新失败，也要检查订单是否已经是已支付状态，如果是则继续执行WebSocket通知</p>";
echo "<p><strong>关键改进:</strong></p>";
echo "<ul>";
echo "<li>增加了 <code>shouldProcessOrder</code> 变量来控制是否执行WebSocket通知</li>";
echo "<li>在订单状态更新失败时，检查订单是否已经是已支付状态</li>";
echo "<li>确保已支付的订单都能触发WebSocket通知</li>";
echo "</ul>";
echo "</div>";

// 5. 测试建议
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h3>📝 测试建议</h3>";
echo "<ol>";
echo "<li><strong>进行真实支付测试:</strong> 使用0.01元进行真实支付测试</li>";
echo "<li><strong>观察日志记录:</strong> 查看 <code>payment_debug_" . date('Y-m-d') . ".log</code> 是否包含WebSocket相关日志</li>";
echo "<li><strong>检查前端语音:</strong> 确认前端是否收到语音播报</li>";
echo "<li><strong>监控WebSocket日志:</strong> 查看 <code>logs/websocket_integration.log</code> 是否有新的通知记录</li>";
echo "</ol>";
echo "</div>";

// 6. 快速访问链接
echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h3>🔗 快速访问链接</h3>";
echo "<p><a href='test_real_payment_websocket.php' target='_blank'>🧪 详细测试页面</a></p>";
echo "<p><a href='test_websocket_notify.php' target='_blank'>🔔 WebSocket通知测试</a></p>";
echo "<p><a href='payment_monitor.php' target='_blank'>📊 支付监控页面</a></p>";
echo "</div>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
p { margin: 8px 0; }
ol, ul { margin: 10px 0; padding-left: 30px; }
li { margin: 5px 0; }
code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
