<?php
/**
 * WebSocket服务状态检查工具
 * 检查Swoole WebSocket服务是否正常运行
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔧 WebSocket服务状态检查</h1>";
echo "<p><strong>检查时间:</strong> " . date('Y-m-d H:i:s') . "</p>";

// 检查Swoole扩展
echo "<h2>📋 环境检查</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";

if (extension_loaded('swoole')) {
    echo "<p>✅ <strong>Swoole扩展:</strong> 已安装 (版本: " . swoole_version() . ")</p>";
} else {
    echo "<p>❌ <strong>Swoole扩展:</strong> 未安装</p>";
}

echo "<p>✅ <strong>PHP版本:</strong> " . PHP_VERSION . "</p>";
echo "</div>";

// 检查端口占用
echo "<h2>🔌 端口状态检查</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";

function checkPort($host, $port, $timeout = 3) {
    $connection = @fsockopen($host, $port, $errno, $errstr, $timeout);
    if ($connection) {
        fclose($connection);
        return true;
    }
    return false;
}

$ports = [
    8080 => 'WebSocket主端口',
    8081 => 'WebSocket API端口'
];

foreach ($ports as $port => $description) {
    $isOpen = checkPort('127.0.0.1', $port);
    $status = $isOpen ? '✅ 开放' : '❌ 关闭';
    echo "<p><strong>端口 {$port} ({$description}):</strong> {$status}</p>";
}

echo "</div>";

// 检查进程
echo "<h2>⚙️ 进程状态检查</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";

$processes = [];
exec("ps aux | grep swoole", $processes);

$swooleProcesses = array_filter($processes, function($line) {
    return strpos($line, 'swoole') !== false && strpos($line, 'grep') === false;
});

if (empty($swooleProcesses)) {
    echo "<p>❌ <strong>Swoole进程:</strong> 未找到运行中的Swoole进程</p>";
    echo "<p>💡 <strong>建议:</strong> 启动WebSocket服务</p>";
} else {
    echo "<p>✅ <strong>Swoole进程:</strong> 找到 " . count($swooleProcesses) . " 个进程</p>";
    foreach ($swooleProcesses as $process) {
        echo "<p style='font-family: monospace; font-size: 12px;'>" . htmlspecialchars($process) . "</p>";
    }
}

echo "</div>";

// 检查日志文件
echo "<h2>📄 日志文件检查</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";

$logFiles = [
    __DIR__ . '/logs/swoole_websocket.log' => 'Swoole WebSocket日志',
    __DIR__ . '/logs/swoole_websocket.pid' => 'Swoole PID文件'
];

foreach ($logFiles as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        $modified = date('Y-m-d H:i:s', filemtime($file));
        echo "<p>✅ <strong>{$description}:</strong> 存在 ({$size} bytes, 修改时间: {$modified})</p>";
        
        if (strpos($file, '.log') !== false && $size > 0) {
            echo "<details style='margin: 10px 0;'>";
            echo "<summary>📖 查看最后10行日志</summary>";
            echo "<pre style='background: #000; color: #00ff00; padding: 10px; border-radius: 5px; font-size: 12px;'>";
            $lines = file($file);
            $lastLines = array_slice($lines, -10);
            echo htmlspecialchars(implode('', $lastLines));
            echo "</pre>";
            echo "</details>";
        }
    } else {
        echo "<p>❌ <strong>{$description}:</strong> 不存在</p>";
    }
}

echo "</div>";

// 检查配置文件
echo "<h2>⚙️ 配置文件检查</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";

$configFiles = [
    __DIR__ . '/swoole_websocket_server.php' => 'Swoole WebSocket服务器',
    __DIR__ . '/start_swoole_websocket.php' => 'Swoole启动脚本'
];

foreach ($configFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<p>✅ <strong>{$description}:</strong> 存在</p>";
    } else {
        echo "<p>❌ <strong>{$description}:</strong> 不存在</p>";
    }
}

echo "</div>";

// 网络连接测试
echo "<h2>🌐 网络连接测试</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";

$testUrls = [
    'http://ceshi.huisas.com:8080' => 'HTTP端口测试',
    'http://127.0.0.1:8080' => '本地HTTP端口测试'
];

foreach ($testUrls as $url => $description) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'method' => 'GET'
        ]
    ]);
    
    $result = @file_get_contents($url, false, $context);
    $status = $result !== false ? '✅ 可访问' : '❌ 无法访问';
    echo "<p><strong>{$description}:</strong> {$status}</p>";
}

echo "</div>";

// 操作建议
echo "<h2>🚀 操作建议</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #007bff;'>";

if (!extension_loaded('swoole')) {
    echo "<p>❗ <strong>紧急:</strong> 需要安装Swoole扩展</p>";
    echo "<p>💡 在宝塔面板 → PHP管理 → 安装扩展 → 找到Swoole并安装</p>";
} else {
    $port8080Open = checkPort('127.0.0.1', 8080);
    
    if (!$port8080Open) {
        echo "<p>🚀 <strong>启动WebSocket服务:</strong></p>";
        echo "<pre style='background: #000; color: #00ff00; padding: 10px; border-radius: 5px;'>";
        echo "cd " . __DIR__ . "\n";
        echo "php swoole_websocket_server.php start\n";
        echo "# 或者使用启动脚本\n";
        echo "php start_swoole_websocket.php\n";
        echo "</pre>";
    } else {
        echo "<p>✅ <strong>WebSocket服务正在运行</strong></p>";
        echo "<p>🔗 <strong>测试连接:</strong></p>";
        echo "<p><a href='websocket_url_test.html' target='_blank'>📱 打开WebSocket连接测试工具</a></p>";
    }
}

echo "<p>🔧 <strong>故障排除:</strong></p>";
echo "<ul>";
echo "<li>如果端口被占用，使用 <code>netstat -tlnp | grep 8080</code> 查看占用进程</li>";
echo "<li>如果启动失败，检查 <code>logs/swoole_websocket.log</code> 日志文件</li>";
echo "<li>确保防火墙开放8080端口</li>";
echo "<li>检查服务器内存和CPU资源是否充足</li>";
echo "</ul>";

echo "</div>";

// 快速测试按钮
echo "<h2>🧪 快速测试</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<p><a href='websocket_url_test.html' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 WebSocket连接测试</a></p>";
echo "<p><a href='test_websocket_notify.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📤 支付通知测试</a></p>";
echo "</div>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f5f5f5;
}

h1, h2 {
    color: #333;
}

code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}

pre {
    overflow-x: auto;
}

details {
    cursor: pointer;
}

summary {
    font-weight: bold;
    padding: 5px 0;
}

a {
    color: #007bff;
}
</style>
