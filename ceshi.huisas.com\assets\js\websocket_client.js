/**
 * WebSocket客户端 - 支付语音播报
 * 用于前端页面连接WebSocket服务并处理支付通知
 */

class PaymentWebSocketClient {
    constructor(options = {}) {
        this.options = {
            url: 'ws://160.202.244.93:8080',
            reconnectInterval: 5000,
            heartbeatInterval: 30000,
            maxReconnectAttempts: 10,
            debug: false,
            ...options
        };
        
        this.ws = null;
        this.reconnectAttempts = 0;
        this.heartbeatTimer = null;
        this.reconnectTimer = null;
        this.isConnected = false;
        this.connectionId = null;
        
        // 事件回调
        this.callbacks = {
            onConnect: [],
            onDisconnect: [],
            onMessage: [],
            onPaymentNotification: [],
            onError: []
        };
        
        this.init();
    }
    
    /**
     * 初始化WebSocket连接
     */
    init() {
        this.connect();
    }
    
    /**
     * 连接WebSocket服务器
     */
    connect() {
        try {
            this.log('正在连接WebSocket服务器...');
            
            this.ws = new WebSocket(this.options.url);
            
            this.ws.onopen = (event) => {
                this.onOpen(event);
            };
            
            this.ws.onmessage = (event) => {
                this.onMessage(event);
            };
            
            this.ws.onclose = (event) => {
                this.onClose(event);
            };
            
            this.ws.onerror = (event) => {
                this.onError(event);
            };
            
        } catch (error) {
            this.log('WebSocket连接异常:', error);
            this.scheduleReconnect();
        }
    }
    
    /**
     * 连接成功处理
     */
    onOpen(event) {
        this.log('WebSocket连接成功');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // 启动心跳
        this.startHeartbeat();
        
        // 触发连接成功回调
        this.trigger('onConnect', event);
    }
    
    /**
     * 接收消息处理
     */
    onMessage(event) {
        try {
            const data = JSON.parse(event.data);
            this.log('收到消息:', data);
            
            // 处理不同类型的消息
            switch (data.type) {
                case 'welcome':
                    this.connectionId = data.data.connection_id;
                    this.log('连接ID:', this.connectionId);
                    break;
                    
                case 'pong':
                    this.log('心跳响应正常');
                    break;
                    
                case 'payment_notification':
                    this.handlePaymentNotification(data.data);
                    break;
                    
                case 'heartbeat':
                    // 服务器主动发送的心跳，回复pong
                    this.send({type: 'pong', timestamp: Date.now()});
                    break;
                    
                case 'error':
                    this.log('服务器错误:', data.message);
                    break;
                    
                default:
                    this.log('未知消息类型:', data.type);
            }
            
            // 触发消息回调
            this.trigger('onMessage', data);
            
        } catch (error) {
            this.log('解析消息失败:', error);
        }
    }
    
    /**
     * 连接关闭处理
     */
    onClose(event) {
        this.log('WebSocket连接关闭:', event.code, event.reason);
        this.isConnected = false;
        this.connectionId = null;
        
        // 停止心跳
        this.stopHeartbeat();
        
        // 触发断开连接回调
        this.trigger('onDisconnect', event);
        
        // 尝试重连
        if (event.code !== 1000) { // 非正常关闭
            this.scheduleReconnect();
        }
    }
    
    /**
     * 错误处理
     */
    onError(event) {
        this.log('WebSocket错误:', event);
        this.trigger('onError', event);
    }
    
    /**
     * 处理支付通知
     */
    handlePaymentNotification(data) {
        this.log('收到支付通知:', data);
        
        // 播放语音提示
        this.playPaymentSound(data);
        
        // 显示通知
        this.showPaymentNotification(data);
        
        // 触发支付通知回调
        this.trigger('onPaymentNotification', data);
    }
    
    /**
     * 播放支付语音
     */
    playPaymentSound(data) {
        try {
            // 构建语音文本
            const amount = parseFloat(data.money || 0);
            const payType = this.getPayTypeName(data.type);
            const text = `${payType}到账${amount}元`;
            
            this.log('播放语音:', text);
            
            // 使用浏览器语音合成API
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = 'zh-CN';
                utterance.rate = 0.8;
                utterance.pitch = 1.0;
                utterance.volume = 0.8;
                
                speechSynthesis.speak(utterance);
            } else {
                this.log('浏览器不支持语音合成');
            }
            
        } catch (error) {
            this.log('播放语音失败:', error);
        }
    }
    
    /**
     * 显示支付通知
     */
    showPaymentNotification(data) {
        try {
            const amount = parseFloat(data.money || 0);
            const payType = this.getPayTypeName(data.type);
            const time = data.addtime || new Date().toLocaleString();
            
            // 如果页面有layer.js，使用layer通知
            if (typeof layer !== 'undefined') {
                layer.msg(`💰 ${payType}收款 ¥${amount}`, {
                    icon: 1,
                    time: 3000,
                    area: ['300px', '100px']
                });
            }
            // 如果支持浏览器通知
            else if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('收款通知', {
                    body: `${payType}收款 ¥${amount}`,
                    icon: '/favicon.ico',
                    tag: 'payment-' + data.trade_no
                });
            }
            // 否则使用alert
            else {
                console.log(`💰 收款通知: ${payType} ¥${amount}`);
            }
            
        } catch (error) {
            this.log('显示通知失败:', error);
        }
    }
    
    /**
     * 获取支付方式名称
     */
    getPayTypeName(type) {
        const typeMap = {
            'alipay': '支付宝',
            'wxpay': '微信支付',
            'qqpay': 'QQ钱包',
            'bank': '网银',
            'test': '测试'
        };
        
        return typeMap[type] || '未知支付方式';
    }
    
    /**
     * 发送消息
     */
    send(data) {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            const message = JSON.stringify(data);
            this.ws.send(message);
            this.log('发送消息:', data);
            return true;
        } else {
            this.log('WebSocket未连接，无法发送消息');
            return false;
        }
    }
    
    /**
     * 启动心跳
     */
    startHeartbeat() {
        this.stopHeartbeat();
        
        this.heartbeatTimer = setInterval(() => {
            this.send({
                type: 'ping',
                timestamp: Date.now()
            });
        }, this.options.heartbeatInterval);
    }
    
    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }
    
    /**
     * 计划重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
            this.log('达到最大重连次数，停止重连');
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.options.reconnectInterval * this.reconnectAttempts;
        
        this.log(`${delay/1000}秒后尝试第${this.reconnectAttempts}次重连...`);
        
        this.reconnectTimer = setTimeout(() => {
            this.connect();
        }, delay);
    }
    
    /**
     * 手动重连
     */
    reconnect() {
        this.log('手动重连...');
        this.reconnectAttempts = 0;
        
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        this.disconnect();
        setTimeout(() => {
            this.connect();
        }, 1000);
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        this.log('断开WebSocket连接');
        
        this.stopHeartbeat();
        
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        if (this.ws) {
            this.ws.close(1000, 'Client disconnect');
            this.ws = null;
        }
        
        this.isConnected = false;
        this.connectionId = null;
    }
    
    /**
     * 添加事件监听
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }
    
    /**
     * 移除事件监听
     */
    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
    }
    
    /**
     * 触发事件
     */
    trigger(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    this.log('事件回调执行失败:', error);
                }
            });
        }
    }
    
    /**
     * 日志输出
     */
    log(...args) {
        if (this.options.debug) {
            console.log('[WebSocket]', ...args);
        }
    }
    
    /**
     * 获取连接状态
     */
    getStatus() {
        return {
            connected: this.isConnected,
            connectionId: this.connectionId,
            reconnectAttempts: this.reconnectAttempts,
            readyState: this.ws ? this.ws.readyState : -1
        };
    }
}

// 全局WebSocket客户端实例
let paymentWebSocket = null;

/**
 * 初始化支付WebSocket客户端
 */
function initPaymentWebSocket(options = {}) {
    // 请求通知权限
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }
    
    // 创建WebSocket客户端
    paymentWebSocket = new PaymentWebSocketClient({
        debug: true,
        ...options
    });
    
    // 添加事件监听
    paymentWebSocket.on('onConnect', () => {
        console.log('✅ WebSocket连接成功');
    });
    
    paymentWebSocket.on('onDisconnect', () => {
        console.log('❌ WebSocket连接断开');
    });
    
    paymentWebSocket.on('onPaymentNotification', (data) => {
        console.log('💰 收到支付通知:', data);
    });
    
    paymentWebSocket.on('onError', (error) => {
        console.error('❌ WebSocket错误:', error);
    });
    
    return paymentWebSocket;
}

/**
 * 获取WebSocket客户端实例
 */
function getPaymentWebSocket() {
    return paymentWebSocket;
}

// 页面卸载时断开连接
window.addEventListener('beforeunload', () => {
    if (paymentWebSocket) {
        paymentWebSocket.disconnect();
    }
});

// 导出到全局
window.PaymentWebSocketClient = PaymentWebSocketClient;
window.initPaymentWebSocket = initPaymentWebSocket;
window.getPaymentWebSocket = getPaymentWebSocket;
