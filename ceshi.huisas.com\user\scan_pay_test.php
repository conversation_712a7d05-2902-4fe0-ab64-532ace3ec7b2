<?php
/**
 * 反扫支付测试API（简化版）
 */
include("../includes/common.php");

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json; charset=utf-8');

if($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 错误处理函数
function returnError($msg, $code = -1) {
    echo json_encode(['code' => $code, 'msg' => $msg], JSON_UNESCAPED_UNICODE);
    exit();
}

// 成功返回函数
function returnSuccess($data = [], $msg = 'success') {
    $result = ['code' => 1, 'msg' => $msg];
    if(!empty($data)) {
        $result = array_merge($result, $data);
    }
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // 检查请求方法
    if($_SERVER['REQUEST_METHOD'] !== 'POST') {
        returnError('只支持POST请求');
    }

    // 获取参数
    $amount = floatval($_POST['amount'] ?? 0);
    $auth_code = trim($_POST['auth_code'] ?? '');
    $subject = trim($_POST['subject'] ?? '测试商品');

    // 参数验证
    if($amount <= 0) {
        returnError('金额必须大于0');
    }
    
    if(empty($auth_code)) {
        returnError('付款码不能为空');
    }
    
    if(!preg_match('/^\d{18}$/', $auth_code)) {
        returnError('付款码格式错误，应为18位数字');
    }

    // 使用测试商户ID
    $test_uid = 1000;
    
    // 检查数据库连接和表
    $debug_info = [];
    
    // 1. 检查商户表
    try {
        $userrow = $DB->getRow("SELECT * FROM pay_user WHERE uid='$test_uid' LIMIT 1");
        if($userrow) {
            $debug_info['merchant'] = "商户存在: {$userrow['user']}, 状态: {$userrow['status']}";
        } else {
            $debug_info['merchant'] = "商户不存在";
        }
    } catch(Exception $e) {
        $debug_info['merchant'] = "查询商户错误: " . $e->getMessage();
    }
    
    // 2. 检查支付方式表
    try {
        $types = $DB->getAll("SELECT * FROM pay_type WHERE status=1");
        $debug_info['types'] = "支付方式数量: " . count($types);
    } catch(Exception $e) {
        $debug_info['types'] = "查询支付方式错误: " . $e->getMessage();
    }
    
    // 3. 检查通道表
    try {
        $channels = $DB->getAll("SELECT * FROM pay_channel WHERE status=1");
        $debug_info['channels'] = "通道数量: " . count($channels);
    } catch(Exception $e) {
        $debug_info['channels'] = "查询通道错误: " . $e->getMessage();
    }
    
    // 4. 检查订单表
    try {
        $order_count = $DB->getColumn("SELECT COUNT(*) FROM pay_order");
        $debug_info['orders'] = "订单总数: " . $order_count;
    } catch(Exception $e) {
        $debug_info['orders'] = "查询订单错误: " . $e->getMessage();
    }
    
    // 5. 测试Channel类
    try {
        if(class_exists('\lib\Channel')) {
            $submitData = \lib\Channel::submit('alipay', $test_uid, 2, $amount);
            if($submitData) {
                $debug_info['channel_test'] = "通道测试成功: 插件={$submitData['plugin']}, 通道ID={$submitData['channel']}";
            } else {
                $debug_info['channel_test'] = "通道测试失败: 无可用通道";
            }
        } else {
            $debug_info['channel_test'] = "Channel类不存在";
        }
    } catch(Exception $e) {
        $debug_info['channel_test'] = "通道测试错误: " . $e->getMessage();
    }
    
    // 生成测试订单号
    $trade_no = date('YmdHis') . rand(1000, 9999);
    
    // 返回调试信息
    returnSuccess([
        'trade_no' => $trade_no,
        'amount' => $amount,
        'auth_code' => $auth_code,
        'subject' => $subject,
        'debug_info' => $debug_info,
        'timestamp' => date('Y-m-d H:i:s')
    ], '测试API调用成功');

} catch(Exception $e) {
    returnError('系统错误: ' . $e->getMessage());
} catch(Error $e) {
    returnError('系统错误: ' . $e->getMessage());
}
?>
