<?php
/**
 * 反扫支付API接口
 */
include("../includes/common.php");

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json; charset=utf-8');

if($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 错误处理函数
function returnError($msg, $code = -1) {
    echo json_encode(['code' => $code, 'msg' => $msg], JSON_UNESCAPED_UNICODE);
    exit();
}

// 成功返回函数
function returnSuccess($data = [], $msg = 'success') {
    $result = ['code' => 1, 'msg' => $msg];
    if(!empty($data)) {
        $result = array_merge($result, $data);
    }
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // 检查请求方法
    if($_SERVER['REQUEST_METHOD'] !== 'POST') {
        returnError('只支持POST请求');
    }

    // 获取参数
    $amount = floatval($_POST['amount'] ?? 0);
    $auth_code = trim($_POST['auth_code'] ?? '');
    $subject = trim($_POST['subject'] ?? '测试商品');

    // 参数验证
    if($amount <= 0) {
        returnError('金额必须大于0');
    }
    
    if(empty($auth_code)) {
        returnError('付款码不能为空');
    }
    
    if(!preg_match('/^\d{18}$/', $auth_code)) {
        returnError('付款码格式错误，应为18位数字');
    }

    // 使用测试商户ID（从之前的测试结果得知）
    $test_uid = 1000;
    
    // 获取商户信息
    $userrow = $DB->getRow("SELECT * FROM pay_user WHERE uid='$test_uid' LIMIT 1");
    if(!$userrow) {
        returnError('商户不存在');
    }

    if($userrow['status'] != 1) {
        returnError('商户已被禁用');
    }

    // 生成订单号
    $trade_no = date('YmdHis') . rand(1000, 9999);

    // 获取支付宝通道
    $pay_type = 'alipay';
    $submitData = \lib\Channel::submit($pay_type, $userrow['uid'], $userrow['gid'], $amount);

    if(!$submitData) {
        returnError('支付宝通道不可用');
    }

    // 准备支付参数 - 使用正确的字段类型
    $addtime = date('Y-m-d H:i:s');
    $endtime = date('Y-m-d H:i:s', time() + 300); // 5分钟超时
    $date = date('Y-m-d');

    // 获取支付方式ID
    $type_id = $DB->getColumn("SELECT id FROM pay_type WHERE name='$pay_type' LIMIT 1");
    if(!$type_id) {
        returnError('支付方式不存在');
    }

    // 插入订单记录 - 使用正确的表名和字段
    $insert_sql = "INSERT INTO pay_order (trade_no, out_trade_no, uid, tid, type, channel, name, money, addtime, endtime, date, status) VALUES (
        '$trade_no',
        '$trade_no',
        '$test_uid',
        '0',
        '$type_id',
        '{$submitData['channel']}',
        '$subject',
        '$amount',
        '$addtime',
        '$endtime',
        '$date',
        '0'
    )";

    if(!$DB->exec($insert_sql)) {
        returnError('订单创建失败: ' . implode(', ', $DB->errorInfo()));
    }

    // 调用支付插件进行反扫支付
    $plugin_name = $submitData['plugin'];
    $plugin_file = "../plugins/{$plugin_name}/submit.php";

    if(!file_exists($plugin_file)) {
        returnError("支付插件不存在: {$plugin_name}");
    }

    // 设置插件需要的全局变量
    $channel = $submitData['channel'];

    // 构建订单数据
    $order = [
        'trade_no' => $trade_no,
        'out_trade_no' => $trade_no,
        'uid' => $test_uid,
        'type' => $type_id,
        'channel' => $channel,
        'name' => $subject,
        'money' => $amount,
        'addtime' => $addtime,
        'endtime' => $endtime,
        'status' => 0,
        'auth_code' => $auth_code  // 反扫支付的关键参数
    ];

    // 尝试调用插件
    try {
        // 包含插件文件
        ob_start();
        include($plugin_file);
        $plugin_output = ob_get_clean();

        // 检查插件输出
        if(!empty($plugin_output)) {
            // 尝试解析JSON响应
            $plugin_result = json_decode($plugin_output, true);
            if($plugin_result && isset($plugin_result['code'])) {
                if($plugin_result['code'] == 1) {
                    // 支付成功
                    returnSuccess([
                        'trade_no' => $trade_no,
                        'money' => $amount,
                        'pay_type' => $pay_type,
                        'plugin' => $plugin_name,
                        'channel' => $channel,
                        'plugin_result' => $plugin_result
                    ], '反扫支付成功');
                } else {
                    // 支付失败
                    returnError("支付失败: " . ($plugin_result['msg'] ?? '未知错误'));
                }
            } else {
                // 非JSON输出，可能是错误信息
                returnError("支付插件错误: " . strip_tags($plugin_output));
            }
        } else {
            // 无输出，可能是成功但没有返回信息
            returnSuccess([
                'trade_no' => $trade_no,
                'money' => $amount,
                'pay_type' => $pay_type,
                'plugin' => $plugin_name,
                'channel' => $channel
            ], '反扫支付请求已提交');
        }
    } catch(Exception $e) {
        returnError("插件执行错误: " . $e->getMessage());
    }

} catch(Exception $e) {
    returnError('系统错误: ' . $e->getMessage());
} catch(Error $e) {
    returnError('系统错误: ' . $e->getMessage());
}
?>
