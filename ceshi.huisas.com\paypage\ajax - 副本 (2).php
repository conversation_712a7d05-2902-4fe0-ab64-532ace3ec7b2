<?php
include("./inc.php");
$act=isset($_GET['act'])?daddslashes($_GET['act']):null;

// 添加跨域支持
$allowed_origins = [
    'http://localhost:8080',
    'http://127.0.0.1:8080',
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://localhost:5173',  // 添加Vite开发服务器端口
    'http://127.0.0.1:5173',  // 添加Vite开发服务器端口
    'http://ceshi.huisas.com',
    'https://ceshi.huisas.com',
    'null' // 本地文件访问
];

$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
if (in_array($origin, $allowed_origins) || $origin === '') {
    if ($origin) {
        header("Access-Control-Allow-Origin: " . $origin);
    } else {
        header("Access-Control-Allow-Origin: *");
    }
    header("Access-Control-Allow-Credentials: true");
    header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, X-Requested-With, Authorization, X-CSRF-Token");
}

// 设置Session Cookie参数以支持跨域
ini_set('session.cookie_samesite', 'None');
ini_set('session.cookie_secure', '0'); // 如果是HTTPS则设置为1
ini_set('session.cookie_httponly', '1');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 修改跨域检查逻辑
function checkRefererHostWithCORS(){
    global $allowed_origins;

    // 获取请求来源
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
    $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

    // 如果是直接访问（没有 Origin 和 Referer），允许通过
    if(!$origin && !$referer) return true;

    $http_host = $_SERVER['HTTP_HOST'];
    if(strpos($http_host,':'))$http_host = substr($http_host, 0, strpos($http_host, ':'));

    // 检查 Origin
    if($origin) {
        // 检查是否在允许的跨域列表中
        if(in_array($origin, $allowed_origins)) return true;

        // 检查是否是同域名
        $origin_parts = parse_url($origin);
        if(isset($origin_parts['host']) && $origin_parts['host'] === $http_host) return true;
    }

    // 检查 Referer
    if($referer) {
        $referer_parts = parse_url($referer);
        if(isset($referer_parts['host'])) {
            // 检查是否是同域名
            if($referer_parts['host'] === $http_host) return true;

            // 构建 referer origin
            $referer_origin = $referer_parts['scheme'] . '://' . $referer_parts['host'];
            if(isset($referer_parts['port'])) {
                $referer_origin .= ':' . $referer_parts['port'];
            }

            // 检查是否在允许的跨域列表中
            if(in_array($referer_origin, $allowed_origins)) return true;
        }
    }

    return false;
}

if(!checkRefererHostWithCORS())exit('{"code":403,"msg":"跨域请求被拒绝"}');

@header('Content-Type: application/json; charset=UTF-8');

$uid=intval($_POST['uid']);
$money=daddslashes($_POST['money']);
$payer=daddslashes($_POST['payer']);
$paytype=$_POST['paytype'];
$direct=intval($_POST['direct']);
$param=!empty($_POST['remark'])?htmlspecialchars(daddslashes($_POST['remark'])):null;

// 处理员工信息
$staff_id = isset($_POST['staff_id']) ? intval($_POST['staff_id']) : 0;
$staff_name = isset($_POST['staff_name']) ? daddslashes($_POST['staff_name']) : '';
$staff_role = isset($_POST['staff_role']) ? daddslashes($_POST['staff_role']) : '';

// 验证员工信息
if($staff_id > 0) {
    if($staff_id != $_SESSION['paypage_staff_id']) {
        showerrorjson('员工信息验证失败');
    }
    // 可以在备注中添加员工信息
    $staff_info = "收款员工：{$staff_name}({$staff_role})";
    $param = $param ? $param . " | " . $staff_info : $staff_info;
}
if($_POST['token']!=$_SESSION['paypage_token'])showerrorjson('CSRF TOKEN ERROR');
if(!$uid || $uid!=$_SESSION['paypage_uid'])showerrorjson('收款方信息无效');
if($money<=0 || !is_numeric($money) || !preg_match('/^[0-9.]+$/', $money))showerrorjson('金额不合法');
if($conf['pay_maxmoney']>0 && $money>$conf['pay_maxmoney'])showerrorjson('最大支付金额是'.$conf['pay_maxmoney'].'元');
if($conf['pay_minmoney']>0 && $money<$conf['pay_minmoney'])showerrorjson('最小支付金额是'.$conf['pay_minmoney'].'元');

$blackip = $DB->find('blacklist', '*', ['type'=>1, 'content'=>$clientip], null, 1);
if($blackip)showerrorjson('系统异常无法完成付款');
if($payer){
	$black = $DB->find('blacklist', '*', ['type'=>0, 'content'=>$payer], null, 1);
	if($black)showerrorjson('系统异常无法完成付款');
}

if(!empty($paytype) && isset($_SESSION['paypage_typeid']) && isset($_SESSION['paypage_paymax']) && isset($_SESSION['paypage_paymin'])){
	if(!empty($_SESSION['paypage_paymin']) && $_SESSION['paypage_paymin']>0 && $money<$_SESSION['paypage_paymin']){
		showerrorjson('当前支付通道最大支付金额是'.$_SESSION['paypage_paymin'].'元');
	}
	if(!empty($_SESSION['paypage_paymax']) && $_SESSION['paypage_paymax']>0 && $money>$_SESSION['paypage_paymax']){
		showerrorjson('当前支付通道最小支付金额是'.$_SESSION['paypage_paymax'].'元');
	}
}

$userrow = $DB->getRow("SELECT `mode`,`ordername`,`channelinfo`,`money` FROM `pre_user` WHERE `uid`='{$uid}' LIMIT 1");

$trade_no=date("YmdHis").rand(11111,99999);
$return_url=$siteurl.'paypage/success.php?trade_no='.$trade_no;
$domain=getdomain($return_url);

// 构建插入订单的SQL，支持员工ID
$insert_sql = "INSERT INTO `pre_order` (`trade_no`,`out_trade_no`,`uid`,`tid`,`addtime`,`name`,`money`,`notify_url`,`return_url`,`param`,`domain`,`ip`,`buyer`,`status`";
$insert_values = "VALUES (:trade_no, :out_trade_no, :uid, 3, NOW(), :name, :money, :notify_url, :return_url, :param, :domain, :clientip, :buyer, 0";
$insert_params = [
    ':trade_no'=>$trade_no,
    ':out_trade_no'=>$trade_no,
    ':uid'=>$uid,
    ':name'=>'在线收款',
    ':money'=>$money,
    ':notify_url'=>$return_url,
    ':return_url'=>$return_url,
    ':param'=>$param,
    ':domain'=>$domain,
    ':clientip'=>$clientip,
    ':buyer'=>$payer
];

// 如果有员工ID且数据库支持operator_staff_id字段，添加到订单中
if($staff_id > 0) {
    // 验证员工是否存在且属于该商户
    $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :staff_id AND uid = :uid AND status = 1",
        [':staff_id' => $staff_id, ':uid' => $uid]);

    if($staff) {
        // 检查订单表是否有operator_staff_id字段
        try {
            $columns = $DB->query("SHOW COLUMNS FROM `pre_order` LIKE 'operator_staff_id'");
            if($columns && count($columns) > 0) {
                $insert_sql .= ",`operator_staff_id`";
                $insert_values .= ", :operator_staff_id";
                $insert_params[':operator_staff_id'] = $staff_id;
            }
        } catch (Exception $e) {
            // 字段不存在，继续使用关联表方式
        }
    }
}

$insert_sql .= ") " . $insert_values . ")";

if(!$DB->exec($insert_sql, $insert_params))showerrorjson('创建订单失败，请返回重试！');

// 如果有员工信息，创建订单员工关联记录（作为备用方案）
if($staff_id > 0 && !empty($staff_name)) {
    try {
        $DB->exec("INSERT INTO `{$dbconfig['dbqz']}_order_staff` (`trade_no`, `staff_id`, `staff_name`, `addtime`) VALUES (:trade_no, :staff_id, :staff_name, NOW())",
            [':trade_no'=>$trade_no, ':staff_id'=>$staff_id, ':staff_name'=>$staff_name]);
    } catch (Exception $e) {
        // 员工关联记录创建失败不影响主流程，但记录日志
        error_log("创建订单员工关联失败: " . $e->getMessage());
    }
}

$_SESSION['paypage_trade_no'] = $trade_no;

$result=[];
$result['code']=0;
$result['msg']='succ';
$result['trade_no']=$trade_no;
$result['direct']=$direct;

if(!empty($paytype) && isset($_SESSION['paypage_typeid']) && isset($_SESSION['paypage_channel']) && isset($_SESSION['paypage_rate'])){
	$typeid = intval($_SESSION['paypage_typeid']);
	$channelid = intval($_SESSION['paypage_channel']);
	$subchannelid = intval($_SESSION['paypage_subchannel']);
	if($direct==1){
		if($userrow['mode']==1){
			$realmoney = round($money*(100+100-$_SESSION['paypage_rate'])/100,2);
			$getmoney = $money;
		}else{
			$realmoney = $money;
			$getmoney = round($money*$_SESSION['paypage_rate']/100,2);
		}
		if($_SESSION['paypage_mode']==1 && $realmoney-$getmoney>$userrow['money']){
			showerrorjson('当前商户余额不足，无法完成支付，请商户登录用户中心充值余额');
		}

		if(!empty($conf['pay_payaddstart'])&&$conf['pay_payaddstart']!=0&&!empty($conf['pay_payaddmin'])&&$conf['pay_payaddmin']!=0&&!empty($conf['pay_payaddmax'])&&$conf['pay_payaddmax']!=0&&$realmoney>=$conf['pay_payaddstart'])$realmoney = round($realmoney + randomFloat(round($conf['pay_payaddmin'],2),round($conf['pay_payaddmax'],2)), 2);

		$DB->update('order', ['type'=>$typeid, 'channel'=>$channelid, 'subchannel'=>$subchannelid, 'realmoney'=>$realmoney, 'getmoney'=>$getmoney], ['trade_no'=>$trade_no]);

		$ordername = 'onlinepay'.time();
		if(!empty($userrow['ordername']))$conf['ordername']=$userrow['ordername'];
		$ordername = !empty($conf['ordername'])?ordername_replace($conf['ordername'],$ordername,$uid,$trade_no,$trade_no):$ordername;
		$channel = $subchannelid > 0 ? \lib\Channel::getSub($subchannelid) : \lib\Channel::get($channelid, $userrow['channelinfo']);
		if(!$channel)showerrorjson('支付通道不存在');
		$channel['apptype'] = explode(',',$channel['apptype']);

		$method = 'jsapi';
		$order['trade_no'] = $trade_no;
		$order['out_trade_no'] = $trade_no;
		$order['uid'] = $uid;
		$order['addtime'] = $date;
		$order['name'] = '在线收款';
		$order['realmoney'] = $realmoney;
		$order['type'] = $typeid;
		$order['channel'] = $channelid;
		$order['typename'] = $paytype;
		$order['profits'] = \lib\Payment::updateOrderProfits($order, $channel['plugin']);
		$order['sub_openid'] = $payer;
		
		try{
			$paydata = \lib\Plugin::loadClass($channel['plugin'],'mapi',$trade_no);
		}catch(Exception $e){
			showerrorjson($e->getMessage());
		}

		if($paydata['type'] == 'jsapi'){
			if(is_array($paydata['data'])) $paydata['data'] = json_encode($paydata['data']);
			$result['paydata'] = $paydata['data'];
		}elseif($paydata['type'] == 'error'){
			showerrorjson($paydata['msg']);
		}else{
			$result['direct'] = 0;
			$result['url'] = $paydata['url'];
		}
	}else{
		$result['url'] = '/submit2.php?typeid='.$typeid.'&trade_no='.$trade_no;
	}
}else{
	$result['url'] = '/cashier.php?trade_no='.$trade_no;
}

exit(json_encode($result));