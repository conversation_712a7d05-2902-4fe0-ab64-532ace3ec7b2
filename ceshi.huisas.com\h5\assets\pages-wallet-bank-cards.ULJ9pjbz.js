import{y as a,E as e,a as l,A as t,B as n,n as s,e as i,w as c,i as d,o,f as r,h as u,C as h,D as m,F as p,t as f,z as b,j as k,k as w,l as y,I as _,a7 as C,p as g,a8 as A}from"./index-B1Q521gi.js";import{_ as N}from"./back.CcwIJs7W.js";import{_ as x}from"./more.BlcQRyo0.js";import{_ as B}from"./alipay.TVRnAsOv.js";import{_ as D}from"./_plugin-vue_export-helper.BCo6x5W8.js";const F=D({data:()=>({bankCards:[{bankName:"中国工商银行",bankIcon:"/static/mine/qianbao/bank-icbc.png",cardType:"储蓄卡",lastFourDigits:"8352",holderName:"张三",isDefault:!0},{bankName:"招商银行",bankIcon:"/static/mine/qianbao/bank-cmb.png",cardType:"储蓄卡",lastFourDigits:"2671",holderName:"张三",isDefault:!1}],showWithdrawForm:!1,withdrawAmount:"",selectedBankIndex:0,withdrawChannel:"bank",alipayAccount:null,showBindAlipay:!1,newAlipayAccount:"",newAlipayName:"",showDevelopingTip:!1,showAddCardForm:!1,showBankSelector:!1,bankSearchKey:"",newCard:{holderName:"",cardNumber:"",bankName:"",bankIcon:"",cardType:"储蓄卡",cvv:"",expiryMonth:"",expiryYear:"",isDefault:!1},bankList:[{name:"中国工商银行",icon:"/static/mine/qianbao/bank-icbc.png"},{name:"中国农业银行",icon:"/static/mine/qianbao/bank-abc.png"},{name:"中国银行",icon:"/static/mine/qianbao/bank-boc.png"},{name:"中国建设银行",icon:"/static/mine/qianbao/bank-ccb.png"},{name:"招商银行",icon:"/static/mine/qianbao/bank-cmb.png"},{name:"交通银行",icon:"/static/mine/qianbao/bank-bocom.png"},{name:"中信银行",icon:"/static/mine/qianbao/bank-citic.png"},{name:"浦发银行",icon:"/static/mine/qianbao/bank-spdb.png"},{name:"民生银行",icon:"/static/mine/qianbao/bank-cmbc.png"},{name:"广发银行",icon:"/static/mine/qianbao/bank-cgb.png"},{name:"兴业银行",icon:"/static/mine/qianbao/bank-cib.png"},{name:"华夏银行",icon:"/static/mine/qianbao/bank-hxb.png"},{name:"光大银行",icon:"/static/mine/qianbao/bank-ceb.png"},{name:"邮储银行",icon:"/static/mine/qianbao/bank-psbc.png"}]}),computed:{canWithdraw(){const a=parseFloat(this.withdrawAmount);return!(isNaN(a)||a<=0)&&("bank"===this.withdrawChannel?null!==this.selectedBankIndex:"alipay"===this.withdrawChannel&&null!==this.alipayAccount)},canBindAlipay(){return""!==this.newAlipayAccount.trim()&&""!==this.newAlipayName.trim()},canAddCard(){const a=this.newCard,e=""!==a.holderName.trim()&&a.cardNumber.trim().length>10&&""!==a.bankName;return"信用卡"===a.cardType?e&&a.cvv.length>0&&a.expiryMonth.length>0&&a.expiryYear.length>0:e},filteredBanks(){return this.bankSearchKey?this.bankList.filter((a=>a.name.includes(this.bankSearchKey))):this.bankList}},onLoad(a){"withdraw"===a.action&&(this.showWithdrawForm=!0,"alipay"===a.channel&&(this.withdrawChannel="alipay"))},methods:{goBack(){a()},setDefault(a){this.bankCards.forEach(((e,l)=>{e.isDefault=l===a}))},deleteCard(a){e({title:"删除银行卡",content:"确定要删除该银行卡吗？",success:e=>{e.confirm&&(this.bankCards.splice(a,1),this.bankCards.length>0&&!this.bankCards.some((a=>a.isDefault))&&(this.bankCards[0].isDefault=!0),this.selectedBankIndex===a?this.selectedBankIndex=this.bankCards.length>0?0:null:this.selectedBankIndex>a&&this.selectedBankIndex--)}})},toggleBankSelector(){this.bankCards.length>0&&(this.selectedBankIndex=(this.selectedBankIndex+1)%this.bankCards.length)},processWithdraw(){const a=parseFloat(this.withdrawAmount);isNaN(a)||a<=0?l({title:"请输入有效金额",icon:"none"}):"alipay"!==this.withdrawChannel||this.alipayAccount?(t({title:"处理中"}),setTimeout((()=>{n();let a="bank"===this.withdrawChannel?"提现申请已提交，将转入您的银行卡":"提现申请已提交，将转入您的支付宝";l({title:a,icon:"success",duration:2e3}),this.showWithdrawForm=!1,this.withdrawAmount=""}),1500)):l({title:"请先绑定支付宝账号",icon:"none"})},bindAlipay(){this.canBindAlipay?(t({title:"绑定中"}),setTimeout((()=>{n();let a=this.newAlipayAccount;a.length>7&&(a=a.substring(0,3)+"****"+a.substring(a.length-4)),this.alipayAccount={name:this.newAlipayName,account:a},this.showBindAlipay=!1,this.newAlipayAccount="",this.newAlipayName="",l({title:"支付宝账号绑定成功",icon:"success"})}),1e3)):l({title:"请填写完整的支付宝信息",icon:"none"})},navigateToAddCard(){this.resetNewCardForm(),this.showAddCardForm=!0},resetNewCardForm(){this.newCard={holderName:"",cardNumber:"",bankName:"",bankIcon:"",cardType:"储蓄卡",cvv:"",expiryMonth:"",expiryYear:"",isDefault:!1}},formatCardNumber(a){let e=a.detail.value.replace(/\D/g,"");e.length>19&&(e=e.slice(0,19)),this.newCard.cardNumber=e},toggleBankListSelector(){this.bankSearchKey="",this.showBankSelector=!0},selectBank(a){this.newCard.bankName=a.name,this.newCard.bankIcon=a.icon,this.showBankSelector=!1},addNewCard(){this.canAddCard?(t({title:"添加中"}),setTimeout((()=>{const a=this.newCard.cardNumber.length,e=this.newCard.cardNumber.substring(a-4);this.newCard.isDefault&&this.bankCards.forEach((a=>{a.isDefault=!1})),this.bankCards.push({bankName:this.newCard.bankName,bankIcon:this.newCard.bankIcon,cardType:this.newCard.cardType,lastFourDigits:e,holderName:this.newCard.holderName,isDefault:this.newCard.isDefault}),n(),l({title:"银行卡添加成功",icon:"success"}),this.showAddCardForm=!1}),1500)):l({title:"请完善银行卡信息",icon:"none"})},navigateToDuizhang(){s({url:"/pages/duizhang/index"})},navigateToDuizhangList(){s({url:"/pages/duizhang/index"})}}},[["render",function(a,e,l,t,n,s){const D=w,F=y,V=d,v=_,T=C,I=g,q=A;return o(),i(V,{class:"container"},{default:c((()=>[r(V,{class:"header"},{default:c((()=>[r(V,{class:"header-left",onClick:s.goBack},{default:c((()=>[r(D,{src:N,mode:"aspectFit",class:"icon-back"}),r(F,{class:"header-title"},{default:c((()=>[u("银行卡管理")])),_:1})])),_:1},8,["onClick"]),r(V,{class:"header-right"},{default:c((()=>[r(V,{class:"header-btn",onClick:s.navigateToDuizhangList},{default:c((()=>[u("对账单")])),_:1},8,["onClick"]),r(D,{src:x,mode:"aspectFit",class:"icon-more"})])),_:1})])),_:1}),n.bankCards.length>0?(o(),i(V,{key:0,class:"card-list"},{default:c((()=>[(o(!0),h(p,null,m(n.bankCards,((a,e)=>(o(),i(V,{key:e,class:b(["bank-card",{"default-card":a.isDefault}])},{default:c((()=>[r(V,{class:"card-top"},{default:c((()=>[r(V,{class:"bank-info"},{default:c((()=>[r(D,{src:a.bankIcon,mode:"aspectFit",class:"bank-icon"},null,8,["src"]),r(V,{class:"bank-details"},{default:c((()=>[r(F,{class:"bank-name"},{default:c((()=>[u(f(a.bankName),1)])),_:2},1024),r(F,{class:"card-type"},{default:c((()=>[u(f(a.cardType),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),a.isDefault?(o(),i(V,{key:0,class:"default-tag"},{default:c((()=>[u("默认")])),_:1})):k("",!0)])),_:2},1024),r(V,{class:"card-number"},{default:c((()=>[r(F,null,{default:c((()=>[u("**** **** **** "+f(a.lastFourDigits),1)])),_:2},1024)])),_:2},1024),r(V,{class:"card-bottom"},{default:c((()=>[r(F,{class:"owner-name"},{default:c((()=>[u(f(a.holderName),1)])),_:2},1024),r(V,{class:"card-actions"},{default:c((()=>[r(F,{class:"action-btn",onClick:a=>s.setDefault(e)},{default:c((()=>[u(f(a.isDefault?"已设为默认":"设为默认"),1)])),_:2},1032,["onClick"]),r(F,{class:"action-btn account",onClick:s.navigateToDuizhang},{default:c((()=>[u("对账")])),_:1},8,["onClick"]),r(F,{class:"action-btn delete",onClick:a=>s.deleteCard(e)},{default:c((()=>[u("删除")])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1})):(o(),i(V,{key:1,class:"empty-state"},{default:c((()=>[r(D,{src:"/h5/static/mine/qianbao/empty-card.png",mode:"aspectFit",class:"empty-icon"}),r(F,{class:"empty-text"},{default:c((()=>[u("您还没有添加银行卡")])),_:1}),r(F,{class:"empty-subtext"},{default:c((()=>[u("添加银行卡后可以进行提现操作")])),_:1})])),_:1})),n.showAddCardForm?(o(),i(V,{key:2,class:"add-card-form"},{default:c((()=>[r(V,{class:"form-header"},{default:c((()=>[r(F,{class:"form-title"},{default:c((()=>[u("添加银行卡")])),_:1}),r(F,{class:"close-btn",onClick:e[0]||(e[0]=a=>n.showAddCardForm=!1)},{default:c((()=>[u("×")])),_:1})])),_:1}),r(V,{class:"form-group"},{default:c((()=>[r(F,{class:"label"},{default:c((()=>[u("持卡人")])),_:1}),r(v,{type:"text",modelValue:n.newCard.holderName,"onUpdate:modelValue":e[1]||(e[1]=a=>n.newCard.holderName=a),placeholder:"请输入持卡人姓名",class:"card-input"},null,8,["modelValue"])])),_:1}),r(V,{class:"form-group"},{default:c((()=>[r(F,{class:"label"},{default:c((()=>[u("银行卡号")])),_:1}),r(v,{type:"number",modelValue:n.newCard.cardNumber,"onUpdate:modelValue":e[2]||(e[2]=a=>n.newCard.cardNumber=a),placeholder:"请输入银行卡号",class:"card-input",maxlength:"19",onInput:s.formatCardNumber},null,8,["modelValue","onInput"])])),_:1}),r(V,{class:"form-group"},{default:c((()=>[r(F,{class:"label"},{default:c((()=>[u("发卡银行")])),_:1}),r(V,{class:"bank-selector",onClick:s.toggleBankListSelector},{default:c((()=>[n.newCard.bankName?(o(),i(F,{key:0},{default:c((()=>[u(f(n.newCard.bankName),1)])),_:1})):(o(),i(F,{key:1,class:"placeholder"},{default:c((()=>[u("请选择发卡银行")])),_:1})),r(F,{class:"arrow-down"},{default:c((()=>[u("▼")])),_:1})])),_:1},8,["onClick"])])),_:1}),r(V,{class:"form-group"},{default:c((()=>[r(F,{class:"label"},{default:c((()=>[u("卡片类型")])),_:1}),r(V,{class:"type-selector"},{default:c((()=>[r(V,{class:b(["type-option",{selected:"储蓄卡"===n.newCard.cardType}]),onClick:e[3]||(e[3]=a=>n.newCard.cardType="储蓄卡")},{default:c((()=>[r(F,null,{default:c((()=>[u("储蓄卡")])),_:1})])),_:1},8,["class"]),r(V,{class:b(["type-option",{selected:"信用卡"===n.newCard.cardType}]),onClick:e[4]||(e[4]=a=>n.newCard.cardType="信用卡")},{default:c((()=>[r(F,null,{default:c((()=>[u("信用卡")])),_:1})])),_:1},8,["class"])])),_:1})])),_:1}),"信用卡"===n.newCard.cardType?(o(),i(V,{key:0,class:"form-group"},{default:c((()=>[r(F,{class:"label"},{default:c((()=>[u("安全码 (CVV)")])),_:1}),r(v,{type:"number",modelValue:n.newCard.cvv,"onUpdate:modelValue":e[5]||(e[5]=a=>n.newCard.cvv=a),placeholder:"卡片背面3位数字",class:"card-input",maxlength:"3"},null,8,["modelValue"])])),_:1})):k("",!0),"信用卡"===n.newCard.cardType?(o(),i(V,{key:1,class:"form-group"},{default:c((()=>[r(F,{class:"label"},{default:c((()=>[u("有效期")])),_:1}),r(V,{class:"expiry-input"},{default:c((()=>[r(v,{type:"number",modelValue:n.newCard.expiryMonth,"onUpdate:modelValue":e[6]||(e[6]=a=>n.newCard.expiryMonth=a),placeholder:"月",class:"month-input",maxlength:"2"},null,8,["modelValue"]),r(F,{class:"expiry-separator"},{default:c((()=>[u("/")])),_:1}),r(v,{type:"number",modelValue:n.newCard.expiryYear,"onUpdate:modelValue":e[7]||(e[7]=a=>n.newCard.expiryYear=a),placeholder:"年",class:"year-input",maxlength:"2"},null,8,["modelValue"])])),_:1})])),_:1})):k("",!0),r(V,{class:"form-group"},{default:c((()=>[r(F,{class:"label"},{default:c((()=>[u("设为默认卡")])),_:1}),r(T,{checked:n.newCard.isDefault,onChange:e[8]||(e[8]=a=>n.newCard.isDefault=!n.newCard.isDefault),color:"#5145F7"},null,8,["checked"])])),_:1}),r(V,{class:"confirm-area"},{default:c((()=>[r(I,{class:"confirm-add-btn",disabled:!s.canAddCard,onClick:s.addNewCard},{default:c((()=>[u("确认添加")])),_:1},8,["disabled","onClick"]),r(F,{class:"hint-text"},{default:c((()=>[u("请确保填写的信息准确无误")])),_:1})])),_:1})])),_:1})):k("",!0),n.showBankSelector?(o(),i(V,{key:3,class:"bank-list-selector"},{default:c((()=>[r(V,{class:"form-header"},{default:c((()=>[r(F,{class:"form-title"},{default:c((()=>[u("选择银行")])),_:1}),r(F,{class:"close-btn",onClick:e[9]||(e[9]=a=>n.showBankSelector=!1)},{default:c((()=>[u("×")])),_:1})])),_:1}),r(V,{class:"search-bar"},{default:c((()=>[r(D,{src:"/h5/static/mine/qianbao/search.png",mode:"aspectFit",class:"search-icon"}),r(v,{type:"text",modelValue:n.bankSearchKey,"onUpdate:modelValue":e[10]||(e[10]=a=>n.bankSearchKey=a),placeholder:"搜索银行",class:"search-input"},null,8,["modelValue"])])),_:1}),r(q,{"scroll-y":"",class:"bank-list"},{default:c((()=>[(o(!0),h(p,null,m(s.filteredBanks,((a,e)=>(o(),i(V,{class:"bank-item",key:e,onClick:e=>s.selectBank(a)},{default:c((()=>[r(D,{src:a.icon,mode:"aspectFit",class:"bank-icon"},null,8,["src"]),r(F,{class:"bank-name"},{default:c((()=>[u(f(a.name),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):k("",!0),n.showDevelopingTip?(o(),i(V,{key:4,class:"developing-tip",onClick:e[11]||(e[11]=a=>n.showDevelopingTip=!1)},{default:c((()=>[r(V,{class:"developing-tip-content"},{default:c((()=>[u(" 添加银行卡功能正在开发中 ")])),_:1})])),_:1})):k("",!0),n.showWithdrawForm?(o(),i(V,{key:5,class:"withdraw-form"},{default:c((()=>[r(V,{class:"form-header"},{default:c((()=>[r(F,{class:"form-title"},{default:c((()=>[u("提现")])),_:1}),r(F,{class:"close-btn",onClick:e[12]||(e[12]=a=>n.showWithdrawForm=!1)},{default:c((()=>[u("×")])),_:1})])),_:1}),r(V,{class:"withdraw-channels"},{default:c((()=>[r(V,{class:b(["channel-tab",{active:"bank"===n.withdrawChannel}]),onClick:e[13]||(e[13]=a=>n.withdrawChannel="bank")},{default:c((()=>[r(F,null,{default:c((()=>[u("银行卡")])),_:1})])),_:1},8,["class"]),r(V,{class:b(["channel-tab",{active:"alipay"===n.withdrawChannel}]),onClick:e[14]||(e[14]=a=>n.withdrawChannel="alipay")},{default:c((()=>[r(F,null,{default:c((()=>[u("支付宝")])),_:1})])),_:1},8,["class"])])),_:1}),r(V,{class:"form-group"},{default:c((()=>[r(F,{class:"label"},{default:c((()=>[u("提现金额")])),_:1}),r(V,{class:"amount-input"},{default:c((()=>[r(F,{class:"currency"},{default:c((()=>[u("¥")])),_:1}),r(v,{type:"digit",modelValue:n.withdrawAmount,"onUpdate:modelValue":e[15]||(e[15]=a=>n.withdrawAmount=a),placeholder:"请输入提现金额"},null,8,["modelValue"])])),_:1}),r(F,{class:"balance-hint"},{default:c((()=>[u("可提现余额: ¥5,833.30")])),_:1})])),_:1}),"bank"===n.withdrawChannel?(o(),i(V,{key:0,class:"form-group"},{default:c((()=>[r(F,{class:"label"},{default:c((()=>[u("提现到")])),_:1}),r(V,{class:"bank-selector",onClick:s.toggleBankSelector},{default:c((()=>[null!==n.selectedBankIndex?(o(),i(V,{key:0,class:"selected-bank"},{default:c((()=>[r(D,{src:n.bankCards[n.selectedBankIndex].bankIcon,mode:"aspectFit",class:"mini-bank-icon"},null,8,["src"]),r(F,null,{default:c((()=>[u(f(n.bankCards[n.selectedBankIndex].bankName)+" ("+f(n.bankCards[n.selectedBankIndex].lastFourDigits)+")",1)])),_:1})])),_:1})):(o(),i(F,{key:1},{default:c((()=>[u("请选择银行卡")])),_:1})),r(F,{class:"arrow-down"},{default:c((()=>[u("▼")])),_:1})])),_:1},8,["onClick"])])),_:1})):k("",!0),"alipay"===n.withdrawChannel?(o(),i(V,{key:1,class:"form-group"},{default:c((()=>[r(F,{class:"label"},{default:c((()=>[u("提现到支付宝")])),_:1}),n.alipayAccount?(o(),i(V,{key:0,class:"alipay-account"},{default:c((()=>[r(V,{class:"alipay-account-info"},{default:c((()=>[r(D,{src:B,mode:"aspectFit",class:"mini-alipay-icon"}),r(V,{class:"alipay-details"},{default:c((()=>[r(F,{class:"alipay-name"},{default:c((()=>[u(f(n.alipayAccount.name),1)])),_:1}),r(F,{class:"alipay-number"},{default:c((()=>[u(f(n.alipayAccount.account),1)])),_:1})])),_:1})])),_:1}),r(F,{class:"alipay-change",onClick:e[16]||(e[16]=a=>n.showBindAlipay=!0)},{default:c((()=>[u("更换")])),_:1})])),_:1})):(o(),i(V,{key:1,class:"bind-alipay",onClick:e[17]||(e[17]=a=>n.showBindAlipay=!0)},{default:c((()=>[r(F,{class:"bind-alipay-text"},{default:c((()=>[u("+ 绑定支付宝账号")])),_:1})])),_:1}))])),_:1})):k("",!0),r(I,{class:"confirm-btn",disabled:!s.canWithdraw,onClick:s.processWithdraw},{default:c((()=>[u("确认提现")])),_:1},8,["disabled","onClick"]),r(F,{class:"withdraw-hint"},{default:c((()=>[u("预计1-2个工作日到账，具体以到账时间为准")])),_:1})])),_:1})):k("",!0),n.showBindAlipay?(o(),i(V,{key:6,class:"bind-alipay-form"},{default:c((()=>[r(V,{class:"form-header"},{default:c((()=>[r(F,{class:"form-title"},{default:c((()=>[u("绑定支付宝")])),_:1}),r(F,{class:"close-btn",onClick:e[18]||(e[18]=a=>n.showBindAlipay=!1)},{default:c((()=>[u("×")])),_:1})])),_:1}),r(V,{class:"form-group"},{default:c((()=>[r(F,{class:"label"},{default:c((()=>[u("支付宝账号")])),_:1}),r(v,{type:"text",modelValue:n.newAlipayAccount,"onUpdate:modelValue":e[19]||(e[19]=a=>n.newAlipayAccount=a),placeholder:"请输入支付宝账号",class:"alipay-input"},null,8,["modelValue"])])),_:1}),r(V,{class:"form-group"},{default:c((()=>[r(F,{class:"label"},{default:c((()=>[u("真实姓名")])),_:1}),r(v,{type:"text",modelValue:n.newAlipayName,"onUpdate:modelValue":e[20]||(e[20]=a=>n.newAlipayName=a),placeholder:"请输入真实姓名",class:"alipay-input"},null,8,["modelValue"])])),_:1}),r(I,{class:"confirm-btn",disabled:!s.canBindAlipay,onClick:s.bindAlipay},{default:c((()=>[u("确认绑定")])),_:1},8,["disabled","onClick"]),r(F,{class:"withdraw-hint"},{default:c((()=>[u("请确保支付宝账号信息准确无误")])),_:1})])),_:1})):k("",!0),r(V,{class:"add-card-btn",onClick:s.navigateToAddCard},{default:c((()=>[r(F,{class:"add-icon"},{default:c((()=>[u("+")])),_:1}),r(F,null,{default:c((()=>[u("添加银行卡")])),_:1})])),_:1},8,["onClick"])])),_:1})}],["__scopeId","data-v-8dfd539f"]]);export{F as default};
