<?php
/**
 * 数据库检查工具
 */

// 设置CORS头部
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

include("../includes/common.php");

$result = [
    'database_connection' => false,
    'tables' => [],
    'errors' => []
];

try {
    // 检查数据库连接
    if ($DB) {
        $result['database_connection'] = true;
        
        // 检查表是否存在
        $tables_to_check = ['pre_staff', 'pre_staff_log', 'pre_qrcode_config', 'pre_order_staff'];
        
        foreach ($tables_to_check as $table) {
            try {
                $exists = $DB->getAll("SHOW TABLES LIKE '{$table}'");
                $result['tables'][$table] = [
                    'exists' => count($exists) > 0,
                    'structure' => []
                ];
                
                if (count($exists) > 0) {
                    // 获取表结构
                    $structure = $DB->getAll("DESCRIBE {$table}");
                    $result['tables'][$table]['structure'] = $structure;
                }
            } catch (Exception $e) {
                $result['tables'][$table] = [
                    'exists' => false,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        // 检查pre_staff表的数据
        if ($result['tables']['pre_staff']['exists']) {
            try {
                $count = $DB->getColumn("SELECT COUNT(*) FROM pre_staff WHERE uid = 99009");
                $result['staff_count'] = intval($count);
                
                $sample_data = $DB->getAll("SELECT * FROM pre_staff WHERE uid = 99009 LIMIT 3");
                $result['sample_staff'] = $sample_data;
            } catch (Exception $e) {
                $result['errors'][] = 'Error checking staff data: ' . $e->getMessage();
            }
        }
        
    } else {
        $result['errors'][] = 'Database connection failed';
    }
    
} catch (Exception $e) {
    $result['errors'][] = 'General error: ' . $e->getMessage();
}

echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
