<?php
// 修复localurl配置
include("./includes/common.php");

echo "<h2>修复localurl配置</h2>";

// 检查当前配置
$current_localurl = getSetting('localurl');
echo "<p><strong>当前数据库中的localurl:</strong> " . ($current_localurl ? $current_localurl : '(空)') . "</p>";
echo "<p><strong>当前siteurl:</strong> " . $siteurl . "</p>";
echo "<p><strong>最终使用的localurl:</strong> " . $conf['localurl'] . "</p>";

// 如果localurl为空或不正确，设置正确的值
$correct_localurl = 'http://ceshi.huisas.com/';

if (empty($current_localurl) || $current_localurl !== $correct_localurl) {
    echo "<p><strong>需要修复localurl配置</strong></p>";
    
    // 更新localurl
    $result = saveSetting('localurl', $correct_localurl);
    
    if ($result) {
        echo "<p style='color: green;'>✓ localurl已更新为: {$correct_localurl}</p>";
        
        // 清除缓存
        $CACHE->clear('config');
        $CACHE->update();
        
        echo "<p style='color: green;'>✓ 缓存已清除并更新</p>";
        
        // 重新检查
        $new_localurl = getSetting('localurl', true); // 强制从数据库读取
        echo "<p><strong>更新后的localurl:</strong> " . $new_localurl . "</p>";
        
    } else {
        echo "<p style='color: red;'>✗ 更新localurl失败</p>";
    }
} else {
    echo "<p style='color: green;'>✓ localurl配置正确，无需修复</p>";
}

// 测试回调地址
$test_trade_no = '2025072605062650741';
$test_callback_url = $correct_localurl . 'pay.php?s=notify/' . $test_trade_no . '/';
echo "<h3>测试回调地址</h3>";
echo "<p><strong>正确的回调地址格式:</strong> <a href='{$test_callback_url}' target='_blank'>{$test_callback_url}</a></p>";

// 检查easypay插件配置
echo "<h3>EasyPay插件检查</h3>";
$easypay_plugin = PLUGIN_ROOT . 'easypay/easypay_plugin.php';
if (file_exists($easypay_plugin)) {
    echo "<p style='color: green;'>✓ EasyPay插件文件存在</p>";
    
    // 检查插件中的回调地址生成
    $plugin_content = file_get_contents($easypay_plugin);
    if (strpos($plugin_content, "backUrl") !== false) {
        echo "<p style='color: green;'>✓ 插件包含回调地址配置</p>";
    } else {
        echo "<p style='color: orange;'>⚠ 插件可能缺少回调地址配置</p>";
    }
} else {
    echo "<p style='color: red;'>✗ EasyPay插件文件不存在</p>";
}

echo "<h3>建议的修复步骤</h3>";
echo "<ol>";
echo "<li>确保数据库中localurl设置为: {$correct_localurl}</li>";
echo "<li>确保回调地址格式为: http://ceshi.huisas.com/pay.php?s=notify/订单号/</li>";
echo "<li>检查easypay插件的回调处理逻辑</li>";
echo "<li>查看日志文件: easypay_notify_debug.log</li>";
echo "</ol>";
?>
