// utils/voiceConfig.js - 语音配置文件

// 语音播报配置
export const voiceConfig = {
  // 默认设置
  defaults: {
    enabled: true,
    volume: 0.8,
    largeAmountAlert: true,
    largeAmountThreshold: 1000
  },
  
  // 多端配置
  platforms: {
    // H5端配置
    h5: {
      engine: 'speechSynthesis',
      lang: 'zh-CN',
      rate: 1.0,
      pitch: 1.0,
      voice: null // 使用默认语音
    },
    
    // App端配置
    app: {
      engine: 'plus.speech',
      speed: 1.0,
      pitch: 1.0
    },
    
    // 小程序端配置
    mp: {
      engine: 'audio',
      preloadAudios: true,
      audioBaseUrl: 'https://your-domain.com/audio/' // 音频文件基础URL
    }
  },
  
  // 页面监听策略
  pageStrategies: {
    'pages/index/index': {
      interval: 3000,
      priority: 'normal',
      description: '首页常规监听'
    },
    'pages/scan/index': {
      interval: 3000,
      priority: 'normal',
      description: '收款码页常规监听'
    },
    'pages/dynamic-code/index': {
      interval: 2000,
      priority: 'high',
      checkAfterQRGenerate: true,
      description: '动态收款码高频监听'
    },
    'pages/pay/mini-payment': {
      interval: 1000,
      priority: 'urgent',
      checkAfterScan: true,
      description: '反扫收银台超高频监听'
    },
    'pages/bill/index': {
      interval: 5000,
      priority: 'low',
      description: '账单页低频监听'
    }
  },
  
  // 性能配置
  performance: {
    maxOrdersToCheck: 5, // 每次最多检查订单数
    recentTimeMinutes: 5, // 检查最近几分钟的订单
    visibilityMultiplier: 2, // 页面不可见时的间隔倍数
    networkRetryDelay: 1000 // 网络恢复后的延迟检查时间
  }
};

// 支付方式配置
export const paymentTypes = {
  1: {
    id: 1,
    name: 'alipay',
    displayName: '支付宝',
    voiceName: '支付宝',
    icon: '/static/payment/alipay.png'
  },
  2: {
    id: 2,
    name: 'wxpay',
    displayName: '微信支付',
    voiceName: '微信支付',
    icon: '/static/payment/wxpay.png'
  },
  3: {
    id: 3,
    name: 'qqpay',
    displayName: 'QQ钱包',
    voiceName: 'QQ钱包',
    icon: '/static/payment/qqpay.png'
  },
  4: {
    id: 4,
    name: 'unionpay',
    displayName: '银联支付',
    voiceName: '银联支付',
    icon: '/static/payment/unionpay.png'
  },
  5: {
    id: 5,
    name: 'cloudpay',
    displayName: '云闪付',
    voiceName: '云闪付',
    icon: '/static/payment/cloudpay.png'
  }
};

// 语音模板配置
export const voiceTemplates = {
  // 标准模板
  normal: {
    id: 'normal',
    name: '标准模式',
    template: '收款成功，{payType}，金额{amount}元',
    description: '简洁明了的播报内容'
  },
  
  // 详细模板
  detailed: {
    id: 'detailed',
    name: '详细模式',
    template: '收款成功，{payType}，{productName}，金额{amount}元',
    description: '包含商品名称的详细播报'
  },
  
  // 反扫模板
  scan: {
    id: 'scan',
    name: '反扫模式',
    template: '反扫收款成功，{payType}，金额{amount}元',
    description: '专用于反扫收款场景'
  },
  
  // 大额模板
  large: {
    id: 'large',
    name: '大额提醒',
    template: '大额收款成功，{payType}，金额{amount}元，请注意核实',
    description: '大额收款的特殊提醒'
  },
  
  // 简洁模板
  simple: {
    id: 'simple',
    name: '简洁模式',
    template: '收款{amount}元',
    description: '最简洁的播报内容'
  }
};

// 获取当前平台配置
export function getPlatformConfig() {
  // #ifdef H5
  return voiceConfig.platforms.h5;
  // #endif
  
  // #ifdef APP-PLUS
  return voiceConfig.platforms.app;
  // #endif
  
  // #ifdef MP
  return voiceConfig.platforms.mp;
  // #endif
  
  // 默认返回H5配置
  return voiceConfig.platforms.h5;
}

// 获取支付方式信息
export function getPaymentType(typeId) {
  return paymentTypes[typeId] || {
    id: typeId,
    name: 'unknown',
    displayName: '未知支付',
    voiceName: '在线支付',
    icon: '/static/payment/default.png'
  };
}

// 获取语音模板
export function getVoiceTemplate(templateId) {
  return voiceTemplates[templateId] || voiceTemplates.normal;
}

// 格式化金额为语音播报格式
export function formatAmountForVoice(amount) {
  const num = parseFloat(amount || 0);
  
  if (num >= 10000) {
    const wan = (num / 10000).toFixed(1);
    return wan.endsWith('.0') ? `${parseInt(wan)}万` : `${wan}万`;
  } else if (num >= 1000) {
    const qian = (num / 1000).toFixed(1);
    return qian.endsWith('.0') ? `${parseInt(qian)}千` : `${qian}千`;
  }
  
  // 处理小数
  if (num % 1 === 0) {
    return num.toString();
  } else {
    return num.toFixed(2);
  }
}

// 检查是否支持语音播报
export function checkVoiceSupport() {
  const support = {
    h5: false,
    app: false,
    mp: false
  };
  
  // #ifdef H5
  support.h5 = typeof window !== 'undefined' && 'speechSynthesis' in window;
  // #endif
  
  // #ifdef APP-PLUS
  support.app = typeof plus !== 'undefined' && plus.speech;
  // #endif
  
  // #ifdef MP
  support.mp = true; // 小程序端通过音频文件播放
  // #endif
  
  return support;
}

// 存储键名配置
export const storageKeys = {
  VOICE_ENABLED: 'voice_enabled',
  VOICE_VOLUME: 'voice_volume',
  VOICE_TEMPLATE: 'voice_template',
  VOICE_LARGE_ALERT: 'voice_large_alert',
  LAST_ORDER_ID: 'last_order_id',
  VOICE_STATISTICS: 'voice_statistics'
};

// 默认导出配置对象
export default {
  voiceConfig,
  paymentTypes,
  voiceTemplates,
  getPlatformConfig,
  getPaymentType,
  getVoiceTemplate,
  formatAmountForVoice,
  checkVoiceSupport,
  storageKeys
};
