// store/index.js - Vuex状态管理入口文件
import Vue from 'vue';
import Vuex from 'vuex';
import merchant from './modules/merchant';
import order from './modules/order';
import setting from './modules/setting';
import wallet from './modules/wallet';
import auth from './modules/auth';

Vue.use(Vuex);

export default new Vuex.Store({
  modules: {
    merchant,
    order,
    setting,
    wallet,
    auth
  },
  // 全局状态
  state: {
    // 应用加载状态
    isLoading: false,
    // 全局消息
    message: '',
    // 消息类型 success/error/info
    messageType: 'info',
    // 是否显示消息
    showMessage: false
  },
  // 修改状态的方法
  mutations: {
    // 设置加载状态
    SET_LOADING(state, isLoading) {
      state.isLoading = isLoading;
    },
    // 设置消息
    SET_MESSAGE(state, { message, type = 'info' }) {
      state.message = message;
      state.messageType = type;
      state.showMessage = true;
    },
    // 清除消息
    CLEAR_MESSAGE(state) {
      state.showMessage = false;
      state.message = '';
    }
  },
  // 异步操作
  actions: {
    // 显示加载
    showLoading({ commit }) {
      commit('SET_LOADING', true);
    },
    // 隐藏加载
    hideLoading({ commit }) {
      commit('SET_LOADING', false);
    },
    // 显示消息
    showMessage({ commit }, { message, type = 'info' }) {
      commit('SET_MESSAGE', { message, type });
      // 3秒后自动清除消息
      setTimeout(() => {
        commit('CLEAR_MESSAGE');
      }, 3000);
    }
  },
  // 获取状态的方法
  getters: {
    isLoading: state => state.isLoading,
    message: state => state.message,
    messageType: state => state.messageType,
    showMessage: state => state.showMessage
  }
}); 