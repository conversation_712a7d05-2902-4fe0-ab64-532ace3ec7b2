# 自定义导航栏组件 (Custom Navigation Bar)

## 概述

`custom-navbar` 是基于 uni-nav-bar 组件的自定义导航栏封装，用于替换系统原生导航栏，提供统一的UI风格和交互体验。

## 前提条件

1. 需要在 `pages.json` 中设置页面的 `navigationStyle` 为 `"custom"`，以隐藏系统原生导航栏
2. 项目需要安装 `uni-nav-bar` 组件（已在 uni_modules 中提供）

## 使用方法

### 基本用法

```vue
<template>
  <view class="container">
    <custom-navbar
      title="页面标题"
      :show-back="true"
      :shadow="true"
    ></custom-navbar>
    
    <!-- 页面其他内容 -->
  </view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar.vue'

export default {
  components: {
    CustomNavbar
  }
}
</script>
```

### 自定义左侧/右侧/中间内容

通过插槽可以自定义导航栏的左侧、右侧或中间内容：

```vue
<custom-navbar :show-back="true">
  <!-- 自定义中间内容 -->
  <template #default>
    <view class="custom-title">
      <text>自定义标题</text>
    </view>
  </template>
  
  <!-- 自定义左侧内容 -->
  <template #left>
    <view class="custom-left">
      <image src="/static/icon.png"></image>
    </view>
  </template>
  
  <!-- 自定义右侧内容 -->
  <template #right>
    <view class="custom-right">
      <button size="mini">操作</button>
    </view>
  </template>
</custom-navbar>
```

## 属性说明

| 属性 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| title | String | '' | 导航栏标题 |
| showBack | Boolean | true | 是否显示返回按钮 |
| rightText | String | '' | 右侧文字 |
| rightIcon | String | '' | 右侧图标 |
| color | String | '#ffffff' | 导航栏文字颜色 |
| backgroundColor | String | '#5145F7' | 导航栏背景色 |
| shadow | Boolean | false | 是否显示导航栏阴影 |

## 事件说明

| 事件名 | 说明 | 参数 |
| --- | --- | --- |
| clickLeft | 点击左侧按钮时触发 | - |
| clickRight | 点击右侧按钮时触发 | - |
| clickTitle | 点击标题时触发 | - |

## 示例

### 首页（无返回按钮）
```vue
<custom-navbar
  title="商家收款"
  :show-back="false"
  :shadow="true"
></custom-navbar>
```

### 子页面（带返回按钮）
```vue
<custom-navbar
  title="我的钱包"
  :show-back="true"
  :shadow="true"
></custom-navbar>
```

### 自定义右侧操作按钮
```vue
<custom-navbar
  title="商家信息"
  :show-back="true"
  :shadow="true"
>
  <template #right>
    <view @click="handleEdit">
      <uni-icons type="edit" size="24" color="#ffffff"></uni-icons>
    </view>
  </template>
</custom-navbar>
``` 