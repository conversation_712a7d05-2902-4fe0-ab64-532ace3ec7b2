<template>
  <view class="staff-orders-container">
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <view class="navbar-content">
        <view class="navbar-left" @tap="goBack">
          <text class="back-icon">←</text>
          <text class="navbar-title">我的订单</text>
        </view>
        
        <view class="navbar-right">
          <view class="action-btn" @tap="refreshOrders">
            <text class="action-icon">🔄</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 筛选区域 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view 
          v-for="(tab, index) in filterTabs" 
          :key="index"
          class="filter-tab"
          :class="{ 'active': currentTab === index }"
          @tap="switchTab(index)"
        >
          {{ tab.name }}
        </view>
      </view>
      
      <view class="date-filter">
        <picker mode="date" @change="onStartDateChange" :value="dateRange.start">
          <view class="date-picker">
            <text class="date-text">{{ dateRange.start || '开始日期' }}</text>
          </view>
        </picker>
        <text class="date-separator">至</text>
        <picker mode="date" @change="onEndDateChange" :value="dateRange.end">
          <view class="date-picker">
            <text class="date-text">{{ dateRange.end || '结束日期' }}</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="orders-section">
      <view v-if="loading" class="loading-state">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      
      <view v-else-if="orderList.length === 0" class="empty-state">
        <text class="empty-icon">📋</text>
        <text class="empty-text">暂无订单记录</text>
        <text class="empty-tip">{{ getEmptyTip() }}</text>
      </view>
      
      <view v-else class="order-list">
        <view v-for="order in orderList" :key="order.id" class="order-item" @tap="viewOrderDetail(order)">
          <view class="order-header">
            <view class="order-amount">¥{{ order.amount }}</view>
            <view class="order-status" :class="getStatusClass(order.status)">
              {{ getStatusText(order.status) }}
            </view>
          </view>
          
          <view class="order-info">
            <view class="info-row">
              <text class="info-label">订单号：</text>
              <text class="info-value">{{ order.trade_no }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">支付方式：</text>
              <text class="info-value">{{ getPayTypeText(order.pay_type) }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">创建时间：</text>
              <text class="info-value">{{ formatDateTime(order.create_time) }}</text>
            </view>
            <view v-if="order.pay_time" class="info-row">
              <text class="info-label">支付时间：</text>
              <text class="info-value">{{ formatDateTime(order.pay_time) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view v-if="hasMore && !loading" class="load-more" @tap="loadMoreOrders">
      <text class="load-more-text">加载更多</text>
    </view>
    
    <view v-if="!hasMore && orderList.length > 0" class="no-more">
      <text class="no-more-text">没有更多数据了</text>
    </view>
  </view>
</template>

<script>
import { request } from '@/utils/request.js'

export default {
  // 🔧 移除WebSocket Mixin，改用语音设置页面统一管理
  // mixins: [websocketMixin],
  name: 'StaffOrders',
  data() {
    return {
      staffInfo: null,
      currentTab: 0,
      filterTabs: [
        { name: '全部', status: '' },
        { name: '待支付', status: 0 },
        { name: '已完成', status: 1 },
        { name: '已失败', status: -1 }
      ],
      dateRange: {
        start: '',
        end: ''
      },
      orderList: [],
      loading: false,
      hasMore: true,
      currentPage: 1,
      pageSize: 20
    }
  },
  
  onLoad() {
    console.log('📱 员工订单页面加载')
    this.initPage()
  },
  
  onShow() {
    // 页面显示时刷新数据
    this.refreshOrders()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      this.checkLoginStatus()
      this.loadStaffInfo()
      this.initDateRange()
      this.loadOrders()
    },
    
    // 检查登录状态
    checkLoginStatus() {
      const staffToken = uni.getStorageSync('staff_token')
      const staffInfo = uni.getStorageSync('staff_info')
      
      if (!staffToken || !staffInfo) {
        console.log('❌ 员工未登录，跳转到登录页')
        uni.reLaunch({
          url: '/pages/staff-login/index'
        })
        return false
      }
      
      return true
    },
    
    // 加载员工信息
    loadStaffInfo() {
      const staffInfo = uni.getStorageSync('staff_info')
      if (staffInfo) {
        this.staffInfo = staffInfo
        console.log('✅ 员工信息加载成功:', staffInfo)
      }
    },
    
    // 初始化日期范围
    initDateRange() {
      const today = new Date()
      const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      
      this.dateRange.start = this.formatDate(lastWeek)
      this.dateRange.end = this.formatDate(today)
    },
    
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    
    // 格式化日期时间
    formatDateTime(timestamp) {
      if (!timestamp) return ''
      
      const date = new Date(timestamp * 1000)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },
    
    // 切换筛选标签
    switchTab(index) {
      this.currentTab = index
      this.refreshOrders()
    },
    
    // 开始日期变化
    onStartDateChange(e) {
      this.dateRange.start = e.detail.value
      this.refreshOrders()
    },
    
    // 结束日期变化
    onEndDateChange(e) {
      this.dateRange.end = e.detail.value
      this.refreshOrders()
    },
    
    // 刷新订单列表
    refreshOrders() {
      this.currentPage = 1
      this.hasMore = true
      this.orderList = []
      this.loadOrders()
    },
    
    // 加载订单列表
    async loadOrders() {
      if (!this.checkLoginStatus() || this.loading) return
      
      try {
        this.loading = true
        console.log('🔄 加载员工订单列表...')
        
        const response = await request({
          url: '/user/staff.php?act=getStaffOrders',
          method: 'POST',
          data: {
            staff_id: this.staffInfo?.id,
            status: this.filterTabs[this.currentTab].status,
            start_date: this.dateRange.start,
            end_date: this.dateRange.end,
            page: this.currentPage,
            limit: this.pageSize
          }
        })
        
        console.log('📥 订单列表响应:', response)
        
        if (response && response.code === 0) {
          const newOrders = response.data.list || []
          
          if (this.currentPage === 1) {
            this.orderList = newOrders
          } else {
            this.orderList = [...this.orderList, ...newOrders]
          }
          
          this.hasMore = newOrders.length >= this.pageSize
          console.log('✅ 订单列表加载成功')
          
        } else {
          throw new Error(response?.msg || '加载订单失败')
        }
        
      } catch (error) {
        console.error('❌ 加载订单列表失败:', error)
        
        uni.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 加载更多订单
    loadMoreOrders() {
      if (this.hasMore && !this.loading) {
        this.currentPage++
        this.loadOrders()
      }
    },
    
    // 查看订单详情
    viewOrderDetail(order) {
      // 这里可以跳转到订单详情页面
      uni.showModal({
        title: '订单详情',
        content: `订单号：${order.trade_no}\n金额：¥${order.amount}\n状态：${this.getStatusText(order.status)}`,
        showCancel: false
      })
    },
    
    // 获取订单状态样式
    getStatusClass(status) {
      switch (status) {
        case 1: return 'status-success'
        case 0: return 'status-pending'
        case -1: return 'status-failed'
        default: return 'status-unknown'
      }
    },
    
    // 获取订单状态文本
    getStatusText(status) {
      switch (status) {
        case 1: return '已完成'
        case 0: return '待支付'
        case -1: return '已失败'
        default: return '未知'
      }
    },
    
    // 获取支付方式文本
    getPayTypeText(payType) {
      switch (payType) {
        case 'alipay': return '支付宝'
        case 'wxpay': return '微信支付'
        case 'qqpay': return 'QQ钱包'
        default: return '未知'
      }
    },
    
    // 获取空状态提示
    getEmptyTip() {
      const tabName = this.filterTabs[this.currentTab].name
      return `暂无${tabName}订单，试试调整筛选条件`
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.staff-orders-container {
  min-height: 100vh;
  background: #f5f6fa;
}

.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  color: #ffffff;
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
}

.navbar-right {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 28rpx;
}

.filter-section {
  background: #ffffff;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-tabs {
  display: flex;
  margin-bottom: 30rpx;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666666;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s;
}

.filter-tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
  font-weight: bold;
}

.date-filter {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.date-picker {
  flex: 1;
  height: 60rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #e9ecef;
}

.date-text {
  font-size: 28rpx;
  color: #333333;
}

.date-separator {
  font-size: 28rpx;
  color: #666666;
}

.orders-section {
  flex: 1;
  padding: 20rpx 40rpx;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #999999;
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.order-amount {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.status-success {
  background: #e8f5e8;
  color: #52c41a;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-failed {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-unknown {
  background: #f0f0f0;
  color: #666666;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-row {
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 28rpx;
  color: #666666;
  width: 160rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
}

.load-more {
  text-align: center;
  padding: 40rpx;
}

.load-more-text {
  font-size: 28rpx;
  color: #667eea;
}

.no-more {
  text-align: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #999999;
}
</style>
