<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单WebSocket测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .log { background: #000; color: #0f0; padding: 10px; height: 400px; overflow-y: auto; font-family: monospace; }
        .btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .btn:disabled { background: #ccc; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; font-weight: bold; }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>简单WebSocket测试</h1>
    
    <div id="status" class="status disconnected">未连接</div>
    
    <button id="connectBtn" class="btn">连接</button>
    <button id="disconnectBtn" class="btn" disabled>断开</button>
    <button id="authBtn" class="btn" disabled>认证</button>
    <button id="subscribeBtn" class="btn" disabled>订阅</button>
    <button id="testBtn" class="btn" disabled>测试支付</button>
    <button id="clearBtn" class="btn">清空日志</button>
    
    <div id="log" class="log"></div>

    <script>
        let ws = null;
        let isConnected = false;
        
        const statusEl = document.getElementById('status');
        const logEl = document.getElementById('log');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const authBtn = document.getElementById('authBtn');
        const subscribeBtn = document.getElementById('subscribeBtn');
        const testBtn = document.getElementById('testBtn');
        const clearBtn = document.getElementById('clearBtn');
        
        function log(message, color = '#0f0') {
            const time = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.style.color = color;
            div.textContent = `[${time}] ${message}`;
            logEl.appendChild(div);
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        function updateStatus(connected) {
            isConnected = connected;
            statusEl.textContent = connected ? '已连接' : '未连接';
            statusEl.className = `status ${connected ? 'connected' : 'disconnected'}`;
            
            connectBtn.disabled = connected;
            disconnectBtn.disabled = !connected;
            authBtn.disabled = !connected;
            subscribeBtn.disabled = !connected;
            testBtn.disabled = !connected;
        }
        
        function connect() {
            if (ws) return;
            
            log('正在连接到 ws://ceshi.huisas.com:8080');
            ws = new WebSocket('ws://ceshi.huisas.com:8080');
            
            ws.onopen = function() {
                log('WebSocket连接成功！', '#0f0');
                updateStatus(true);
            };
            
            ws.onmessage = function(event) {
                log(`收到消息: ${event.data}`, '#ff0');

                // 尝试解析JSON
                try {
                    const data = JSON.parse(event.data);
                    log(`解析后: ${JSON.stringify(data, null, 2)}`, '#0ff');

                    // 处理不同类型的消息
                    switch (data.type) {
                        case 'welcome':
                            log('👋 收到欢迎消息', '#0f0');
                            break;
                        case 'auth_result':
                            if (data.data?.success) {
                                log('🔐 认证成功', '#0f0');
                            } else {
                                log(`❌ 认证失败: ${data.data?.message}`, '#f00');
                            }
                            break;
                        case 'payment_notification':
                            log('💰 收到支付通知', '#0f0');
                            const amount = data.data?.amount || data.data?.extra_data?.money || '0.00';
                            const payType = data.data?.extra_data?.typename || '未知支付方式';
                            log(`💰 支付金额: ${amount}元 (${payType})`, '#0f0');
                            break;
                        case 'pong':
                            log('💓 收到心跳响应', '#0ff');
                            break;
                        case 'heartbeat':
                            log('💓 收到心跳消息', '#0ff');
                            break;
                        default:
                            log(`📨 未知消息类型: ${data.type}`, '#f80');
                    }
                } catch (e) {
                    log('非JSON消息', '#f80');
                }
            };
            
            ws.onclose = function(event) {
                log(`连接关闭: code=${event.code}, reason=${event.reason}`, '#f00');
                updateStatus(false);
                ws = null;
            };
            
            ws.onerror = function(error) {
                log(`连接错误: ${error}`, '#f00');
                updateStatus(false);
            };
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
                updateStatus(false);
                log('主动断开连接');
            }
        }
        
        function sendMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const json = JSON.stringify(message);
                ws.send(json);
                log(`发送: ${json}`, '#ff0');
                return true;
            } else {
                log('WebSocket未连接', '#f00');
                return false;
            }
        }
        
        function authenticate() {
            // 尝试不同的认证格式
            const authFormats = [
                // 格式1: 标准认证格式（根据后端代码）
                {
                    type: 'auth',
                    data: {
                        merchant_id: '1000',
                        staff_id: '',
                        token: 'test_token_1234567890'
                    }
                },
                // 格式2: 简单认证
                {
                    type: 'auth',
                    uid: '1000',
                    merchant_id: '1000'
                }
            ];

            // 先尝试标准格式
            sendMessage(authFormats[0]);
            log('🔐 发送认证信息 (标准格式)');

            // 1秒后尝试简化格式
            setTimeout(() => {
                sendMessage(authFormats[1]);
                log('🔐 发送认证信息 (简化格式)');
            }, 1000);
        }
        
        function subscribe() {
            // 尝试不同的订阅格式
            const subscribeFormats = [
                // 格式1: 商户专属频道（根据后端代码）
                {
                    type: 'subscribe',
                    data: {
                        channel: 'merchant_1000_payment'
                    }
                },
                // 格式2: 简化订阅格式
                {
                    type: 'subscribe',
                    channel: 'merchant_1000_payment'
                },
                // 格式3: 通用支付频道
                {
                    type: 'subscribe',
                    data: {
                        channel: 'payment_channel'
                    }
                }
            ];

            // 尝试所有格式
            subscribeFormats.forEach((format, index) => {
                setTimeout(() => {
                    sendMessage(format);
                    log(`📡 订阅支付频道 (格式${index + 1}): ${format.data?.channel || format.channel}`);
                }, index * 500);
            });
        }
        
        function testPayment() {
            // 调用后端测试接口
            fetch('http://ceshi.huisas.com/test_websocket_notify.php')
                .then(response => response.text())
                .then(data => {
                    log('测试支付通知已发送', '#0f0');
                })
                .catch(error => {
                    log(`测试支付失败: ${error}`, '#f00');
                });
        }
        
        function clearLog() {
            logEl.innerHTML = '';
        }
        
        // 绑定事件
        connectBtn.onclick = connect;
        disconnectBtn.onclick = disconnect;
        authBtn.onclick = authenticate;
        subscribeBtn.onclick = subscribe;
        testBtn.onclick = testPayment;
        clearBtn.onclick = clearLog;
        
        // 初始化
        updateStatus(false);
        log('WebSocket测试器已初始化');
        
        // 自动连接
        setTimeout(connect, 1000);
    </script>
</body>
</html>
