<?php
include("../includes/common.php");
if($islogin2==1){}else exit("<script language='javascript'>window.location.href='./login.php';</script>");
$title='聚合收款';
include './head.php';
?>
<?php
if(!$conf['onecode'])exit('未开启聚合收款');

$merchant = authcode($uid, 'ENCODE', SYS_KEY);
$code_url = $siteurl.'paypage/?merchant='.urlencode($merchant);
?>
 <div id="content" class="app-content" role="main">
    <div class="app-content-body ">

<div class="bg-light lter b-b wrapper-md hidden-print">
  <h1 class="m-n font-thin h3">聚合收款</h1>
</div>
<div class="wrapper-md control">
<?php if(isset($msg)){?>
<div class="alert alert-info">
	<?php echo $msg?>
</div>
<?php }?>
	<div class="row">
	<div class="col-md-7">
	<div class="panel panel-default">
		<div class="panel-heading font-bold">
			<i class="fa fa-qrcode"></i>&nbsp;聚合收款
		</div>
		<div class="panel-body">
			<form class="form-horizontal devform">
				<div class="form-group">
					<label class="col-sm-3 control-label">收款方名称</label>
					<div class="col-sm-8">
						<input class="form-control" type="text" name="codename" value="<?php echo $userrow['codename']?>" placeholder="留空默认显示“<?php echo $userrow['username']?>”" onkeydown="if(event.keyCode==13){$('#editName').click()}">
					</div>
				</div>
				<div class="form-group">
				  <div class="col-sm-offset-3 col-sm-8"><input type="button" id="editName" value="保存" class="btn btn-primary form-control"/><br/>
				 </div>
				</div>
			</form>
		<p>你可以将收款链接发到QQ、微信等聊天工具，别人点击后可以直接输入金额付款。</p>
			<div class="form-group">
				<input class="form-control" type="text" id="code_url" value="<?php echo $code_url?>" readonly>
			</div>
			<p class="text-center"><a href="javascript:;" class="btn btn-success copy-btn" data-clipboard-text="<?php echo $code_url?>" title="点击复制">点击复制</a></p>
			<input type="hidden" id="recName" value="<?php echo $userrow['codename']?$userrow['codename']:$userrow['username']?>">
			<div class="form-group">
				<div class="input-group">
					<span class="input-group-addon">选择收款码风格</span>
					<select class="form-control" id="styleName">
					<option value="native">H5原生</option>
					<option value="pikaqiu">风格2-皮卡丘</option>
					<option value="kanuobudingmao">风格3-布叮猫</option>
					<option value="niannianyouyu">风格4-年年有余</option>
					<option value="xiaohuangren">风格5-小黄人</option>
					<option value="qitao">风格6-乞讨</option>
					<option value="baobei">风格7-宝贝</option>
					<option value="toushi">风格8-投食</option>
					<option value="gongzhu">风格9-公主</option>
					<option value="qiuzanzhu">风格10-求赞助</option>
					<option value="huanyingdashang">风格11-欢迎打赏</option>
					<option value="yinlian">风格12-银联</option>
					<option value="yitiji">风格13-一体机</option>
					<option value="maomi">风格14-猫咪</option>
					<option value="longmao">风格15-龙猫</option>
					</select>
				</div>
			</div>
			<div id="load"><img src="/assets/img/loading.gif">&nbsp;正在生成中</div>
			<div id="qrcode" style="display:none;"><img class="img-responsive center-block" alt="收款码" id="endImg" style="max-width: 300px;"/><br/>
			<div class="center-block text-center"><a href="javascript:;" id="downImg" class="btn btn-success btn-sm">长按图片或点我保存</a></div>
			</div>
			<div class="hide">
				<div id="code"></div>
				<canvas id="canvas"></canvas>
			</div>
		</div>
	</div>
	<div class="panel panel-default">

	</div>
	</div>
	<div class="col-md-5">
	<div class="panel panel-default">
		<div class="panel-heading font-bold">
			<i class="fa fa-qrcode"></i>&nbsp;动态收款
		</div>
		<div class="panel-body">
		<div class="row">
		    <div class="col-md-2"></div>
		    <div class="col-md-8 text-center">
		        <iframe src="test2.php" style="width:100%;height:250px;" frameborder="0" scrolling="no" allowtransparency="yes"></iframe>
		        <img id="dtmxsq">
		    </div>
		</div>
		</div>
	</div>
	<!-- 反扫付款码功能面板 -->
	<div class="panel panel-default">
		<div class="panel-heading font-bold">
			<i class="fa fa-credit-card"></i>&nbsp;反扫付款码
		</div>
		<div class="panel-body">
			<form class="form-horizontal" id="scanPayForm">
				<div class="form-group">
					<label class="col-sm-3 control-label">收款金额</label>
					<div class="col-sm-8">
						<input class="form-control" type="number" step="0.01" min="0.01" name="scan_amount" id="scan_amount" placeholder="请输入收款金额" required>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-3 control-label">商品名称</label>
					<div class="col-sm-8">
						<input class="form-control" type="text" name="scan_product" id="scan_product" value="商户收款" placeholder="请输入商品名称">
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-3 control-label">付款码</label>
					<div class="col-sm-8">
						<input class="form-control" type="text" name="scan_auth_code" id="scan_auth_code" placeholder="请扫描客户付款码或手动输入18位数字" maxlength="18" pattern="\d{18}">
						<small class="help-block">支持支付宝、微信、银联付款码</small>
					</div>
				</div>
				<div class="form-group">
					<div class="col-sm-offset-3 col-sm-8">
						<button type="button" id="startScanPay" class="btn btn-success form-control">
							<i class="fa fa-qrcode"></i> 开始收款
						</button>
					</div>
				</div>
			</form>

			<!-- 支付结果显示区域 -->
			<div id="scanPayResult" style="display:none;">
				<div class="alert alert-info">
					<h4><i class="fa fa-spinner fa-spin"></i> 正在处理付款...</h4>
					<p>请稍候，正在验证付款码并处理支付...</p>
				</div>
			</div>
		</div>
	</div>
	</div>
	</div>
	</div>
    </div>
  </div>
<iframe id="dtmxs" style="display:none;"></iframe>
<?php include 'foot.php';?>
<script src="<?php echo $cdnpublic?>layer/3.1.1/layer.min.js"></script>
<script src="<?php echo $cdnpublic?>clipboard.js/1.7.1/clipboard.min.js"></script>
<script src="<?php echo $cdnpublic?>jquery.qrcode/1.0/jquery.qrcode.min.js"></script>
<script src="<?php echo $cdnpublic?>jquery-cookie/1.4.1/jquery.cookie.min.js"></script>
<script src="./assets/js/onecode.js"></script>
<script>
    setInterval(function(){
        try{
        document.querySelector('#dtmxsq').src = document.querySelector('#dtmxs').contentWindow.document.querySelector('#qrcode > canvas').toDataURL();
        }catch(e){}
    },100);

    // 反扫付款码功能
    $(document).ready(function(){
        // 开始收款按钮点击事件
        $('#startScanPay').click(function(){
            var amount = $('#scan_amount').val();
            var product = $('#scan_product').val();
            var authCode = $('#scan_auth_code').val();

            // 验证输入
            if(!amount || parseFloat(amount) <= 0) {
                layer.msg('请输入正确的收款金额', {icon: 2});
                return;
            }
            if(!product.trim()) {
                layer.msg('请输入商品名称', {icon: 2});
                return;
            }
            if(!authCode || !/^\d{18}$/.test(authCode)) {
                layer.msg('请输入正确的18位付款码', {icon: 2});
                return;
            }

            // 显示处理中状态
            $('#scanPayResult').show();
            $('#startScanPay').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 处理中...');

            // 调用反扫支付接口
            $.ajax({
                url: 'ajax2.php?act=scan_pay',
                type: 'POST',
                dataType: 'json',
                data: {
                    amount: amount,
                    product_name: product,
                    auth_code: authCode,
                    out_trade_no: 'SCAN' + Date.now()
                },
                success: function(response) {
                    if(response.code == 0) {
                        // 订单创建成功，跳转到submit2.php处理支付
                        if(response.submit_url) {
                            $('#scanPayResult').html(
                                '<div class="alert alert-info">' +
                                '<h4><i class="fa fa-spinner fa-spin"></i> 正在处理支付...</h4>' +
                                '<p>订单已创建，正在跳转到支付处理页面...</p>' +
                                '</div>'
                            );

                            // 跳转到submit2.php处理支付
                            setTimeout(function() {
                                window.location.href = response.submit_url;
                            }, 1000);
                        } else {
                            // 直接支付成功（兼容旧格式）
                            $('#scanPayResult').html(
                                '<div class="alert alert-success">' +
                                '<h4><i class="fa fa-check-circle"></i> 收款成功！</h4>' +
                                '<p>订单号：' + (response.trade_no || '') + '</p>' +
                                '<p>收款金额：￥' + (response.amount || amount) + '</p>' +
                                '<p>支付方式：' + (response.pay_type || '') + '</p>' +
                                (response.buyer ? '<p>付款人：' + response.buyer + '</p>' : '') +
                                '</div>'
                            );

                            // 清空表单
                            $('#scan_amount').val('');
                            $('#scan_auth_code').val('');

                            layer.msg('收款成功！', {icon: 1});
                        }
                    } else {
                        // 支付失败
                        $('#scanPayResult').html(
                            '<div class="alert alert-danger">' +
                            '<h4><i class="fa fa-times-circle"></i> 收款失败</h4>' +
                            '<p>' + response.msg + '</p>' +
                            '</div>'
                        );
                        layer.msg(response.msg, {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    $('#scanPayResult').html(
                        '<div class="alert alert-danger">' +
                        '<h4><i class="fa fa-times-circle"></i> 系统错误</h4>' +
                        '<p>网络连接失败，请检查网络后重试</p>' +
                        '</div>'
                    );
                    layer.msg('网络错误，请重试', {icon: 2});
                },
                complete: function() {
                    $('#startScanPay').prop('disabled', false).html('<i class="fa fa-qrcode"></i> 开始收款');
                }
            });
        });

        // 付款码输入框回车事件
        $('#scan_auth_code').keypress(function(e) {
            if(e.which == 13) {
                $('#startScanPay').click();
            }
        });

        // 金额输入框回车事件
        $('#scan_amount').keypress(function(e) {
            if(e.which == 13) {
                $('#scan_product').focus();
            }
        });

        // 商品名称输入框回车事件
        $('#scan_product').keypress(function(e) {
            if(e.which == 13) {
                $('#scan_auth_code').focus();
            }
        });

        // 加载收款历史记录
        function loadScanHistory() {
            $.ajax({
                url: 'ajax_scan.php?act=get_scan_history',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if(response.code == 0 && response.data.orders.length > 0) {
                        var html = '';
                        $.each(response.data.orders, function(i, order) {
                            var statusText = order.status == 1 ? '<span class="label label-success">已支付</span>' : '<span class="label label-warning">未支付</span>';
                            html += '<tr>' +
                                '<td>' + order.trade_no + '</td>' +
                                '<td>￥' + order.money + '</td>' +
                                '<td>' + statusText + '</td>' +
                                '<td>' + order.addtime + '</td>' +
                                '</tr>';
                        });
                        $('#scanHistoryList').html(html);
                    }
                }
            });
        }

        // 页面加载时获取历史记录
        loadScanHistory();
    });
</script>