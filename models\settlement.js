// models/settlement.js - 结算数据模型

/**
 * 结算模型类
 */
export default class SettlementModel {
  // 结算状态枚举
  static STATUS = {
    PENDING: 0,  // 处理中
    SUCCESS: 1,  // 已完成
    FAILED: 2    // 失败
  };
  
  /**
   * 构造函数
   * @param {Object} data - 结算数据
   */
  constructor(data = {}) {
    this.id = data.id || 0;                // 结算ID
    this.batch = data.batch || '';         // 批次号
    this.uid = data.uid || 0;              // 商户ID
    this.type = data.type || '';           // 结算渠道
    this.account = data.account || '';     // 结算账号
    this.username = data.username || '';   // 收款姓名
    this.money = data.money || '0.00';     // 结算金额
    this.fee = data.fee || '0.00';         // 手续费
    this.realmoney = data.realmoney || '0.00'; // 实际到账
    this.addtime = data.addtime || '';     // 申请时间
    this.endtime = data.endtime || '';     // 完成时间
    this.status = data.status !== undefined ? data.status : SettlementModel.STATUS.PENDING; // 结算状态
  }
  
  /**
   * 获取结算状态文本
   * @returns {String} 状态文本
   */
  getStatusText() {
    const statusMap = {
      [SettlementModel.STATUS.PENDING]: '处理中',
      [SettlementModel.STATUS.SUCCESS]: '已完成',
      [SettlementModel.STATUS.FAILED]: '失败'
    };
    return statusMap[this.status] || '未知状态';
  }
  
  /**
   * 获取结算方式文本
   * @returns {String} 结算方式文本
   */
  getTypeText() {
    const typeMap = {
      'alipay': '支付宝',
      'wxpay': '微信',
      'bank': '银行卡',
      'qqpay': 'QQ钱包'
    };
    
    return typeMap[this.type] || this.type || '未知方式';
  }
  
  /**
   * 从API响应数据转换为模型数组
   * @param {Array} data - API响应数据
   * @returns {Array<SettlementModel>} 结算模型数组
   */
  static fromApiList(data) {
    if (!Array.isArray(data)) return [];
    
    return data.map(item => new SettlementModel(item));
  }
} 