<?php
/**
 * 检查收款码表结构
 */

// 设置CORS头部
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

include("../includes/common.php");

try {
    // 检查qrcode_config表是否存在
    $tableExists = $DB->getColumn("SHOW TABLES LIKE 'qrcode_config'");

    $result = [
        'table_exists' => !empty($tableExists),
        'table_name' => 'qrcode_config'
    ];
    
    if ($tableExists) {
        // 获取表结构
        $columns = $DB->getAll("DESCRIBE qrcode_config");
        $result['columns'] = $columns;

        // 获取表中的数据数量
        $count = $DB->getColumn("SELECT COUNT(*) FROM qrcode_config");
        $result['record_count'] = intval($count);

        // 获取最近的几条记录
        $recent = $DB->getAll("SELECT * FROM qrcode_config ORDER BY id DESC LIMIT 3");
        $result['recent_records'] = $recent;
    } else {
        // 表不存在，尝试创建
        $createSQL = "CREATE TABLE `qrcode_config` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `uid` int(11) NOT NULL COMMENT '商户ID',
            `staff_id` int(11) DEFAULT NULL COMMENT '绑定员工ID',
            `name` varchar(100) NOT NULL COMMENT '收款码名称',
            `qr_style` varchar(50) DEFAULT 'native' COMMENT '二维码样式',
            `amount` decimal(10,2) DEFAULT NULL COMMENT '固定金额',
            `description` text COMMENT '描述信息',
            `qr_url` text COMMENT '收款码URL',
            `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
            `addtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `idx_uid` (`uid`),
            KEY `idx_staff_id` (`staff_id`),
            KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收款码配置表'";
        
        $createResult = $DB->exec($createSQL);
        $result['create_table'] = $createResult !== false;
        $result['create_sql'] = $createSQL;
        
        if ($createResult !== false) {
            $result['table_exists'] = true;
            $result['message'] = '表创建成功';
        } else {
            $result['message'] = '表创建失败: ' . $DB->error();
        }
    }
    
    // 检查staff表是否存在
    $staffTableExists = $DB->getColumn("SHOW TABLES LIKE 'staff'");
    $result['staff_table_exists'] = !empty($staffTableExists);
    
    exit(json_encode(['code' => 0, 'data' => $result]));
    
} catch (Exception $e) {
    exit(json_encode(['code' => -1, 'msg' => '检查失败: ' . $e->getMessage()]));
}
?>
