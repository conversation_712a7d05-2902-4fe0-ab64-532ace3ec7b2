/**
 * WebSocket Mixin - 参考CSDN文章实现
 * 为指定页面注入WebSocket功能
 */
import AudioPlayer from '@/utils/audioPlayer.js'

let isSocketClose = false // 是否关闭socket
let heartbeatInterval = null // 心跳定时器
let socketTask = null // websocket对象
let againTimer = null // 断线重连定时器
let onReFn = null
let onSucFn = null
let onErrFn = null

export const websocketMixin = {
  data() {
    return {
      // WebSocket状态
      wsConnected: false,
      wsConnecting: false,
      wsError: false,
      wsErrorMessage: '',
      
      // 音频播放器
      audioPlayer: null,

      // 语音播报状态（从存储中读取）
      voiceEnabled: uni.getStorageSync('voice_enabled') !== false,

      // 消息去重（防止重复播放）
      processedMessages: new Set(),
      messageCleanupTimer: null
    }
  },

  onLoad() {
    console.log('🎵 WebSocket Mixin: 页面加载，初始化音频播放器')
    this.audioPlayer = new AudioPlayer()
    this.updateVoiceSettings()
  },

  onShow() {
    console.log('🔄 WebSocket Mixin: 页面显示')
    // 🔧 使用全局单例连接，避免重复连接
    this.checkGlobalConnection()
  },

  onUnload() {
    console.log('🔌 WebSocket Mixin: 页面卸载，关闭WebSocket连接')
    this.closeWebSocket()
    this.cleanup()
  },

  onHide() {
    console.log('👁️ WebSocket Mixin: 页面隐藏')
    // 🔧 页面隐藏时不关闭全局连接，只解绑事件
    this.unbindGlobalEvents()
  },

  beforeDestroy() {
    console.log('💀 WebSocket Mixin: 组件销毁，清理事件监听')
    this.unbindGlobalEvents()
    this.cleanup()
  },

  methods: {
    /**
     * 检查全局连接状态
     */
    checkGlobalConnection() {
      // 🔧 检查是否已有全局连接
      if (getApp().globalData && getApp().globalData.websocketConnected) {
        console.log('✅ WebSocket Mixin: 使用现有全局连接')
        this.wsConnected = true
        this.bindGlobalEvents()
        return
      }

      // 🔧 如果没有全局连接，创建新连接
      setTimeout(() => {
        this.initWebSocket()
      }, 500)
    },

    /**
     * 绑定全局WebSocket事件
     */
    bindGlobalEvents() {
      // 🔧 只有创建连接的页面才处理音频播放，其他页面只监听状态
      if (getApp().globalData.websocketCreator === this) {
        console.log('🎵 WebSocket Mixin: 主页面绑定音频处理事件')
        uni.$on('websocket-message', this.handleWebSocketMessage)
      } else {
        console.log('🔇 WebSocket Mixin: 从页面只监听连接状态，不处理音频')
        uni.$on('websocket-message', this.handleWebSocketMessageSilent)
      }

      uni.$on('websocket-error', this.handleWebSocketError)
      uni.$on('websocket-close', this.handleWebSocketClose)
    },

    /**
     * 解绑全局WebSocket事件
     */
    unbindGlobalEvents() {
      // 解绑全局WebSocket消息
      uni.$off('websocket-message', this.handleWebSocketMessage)
      uni.$off('websocket-message', this.handleWebSocketMessageSilent)
      uni.$off('websocket-error', this.handleWebSocketError)
      uni.$off('websocket-close', this.handleWebSocketClose)
    },

    /**
     * 初始化WebSocket连接
     */
    initWebSocket() {
      console.log('🔧 WebSocket Mixin: 初始化WebSocket连接')

      // 检查用户登录状态
      const token = uni.getStorageSync('user_token')
      const uid = uni.getStorageSync('user_uid')

      if (!token || !uid) {
        console.log('⚠️ WebSocket Mixin: 用户未登录，跳过连接')
        return
      }

      console.log('🚀 WebSocket Mixin: 初始化WebSocket连接')
      this.connectWebSocket()
    },

    /**
     * 连接WebSocket（参考CSDN文章实现）
     */
    connectWebSocket() {
      // 🔧 检查全局连接状态，避免重复连接
      if (getApp().globalData && getApp().globalData.websocketConnected) {
        console.log('⚠️ WebSocket Mixin: 全局连接已存在，跳过重复连接')
        this.wsConnected = true
        this.bindGlobalEvents()
        return
      }

      const uid = uni.getStorageSync('user_uid')
      const wsUrl = `ws://ceshi.huisas.com:8080?id=${uid}`

      console.log('🚀 WebSocket Mixin: 创建全局连接到:', wsUrl)

      // 🔧 标记全局连接状态
      if (!getApp().globalData) {
        getApp().globalData = {}
      }
      getApp().globalData.websocketConnecting = true
      getApp().globalData.websocketCreator = this // 标记创建连接的页面

      // 🔧 安全设置全局回调函数，确保不为undefined
      onReFn = this.handleWebSocketMessage.bind(this)
      onErrFn = this.handleWebSocketError.bind(this)
      onSucFn = this.handleWebSocketSuccess.bind(this)

      isSocketClose = false

      // 判断是否有websocket对象，有的话清空
      if (socketTask) {
        socketTask.close()
        socketTask = null
        clearInterval(heartbeatInterval)
      }

      console.log(`🔗 WebSocket Mixin: 正在连接 ${wsUrl}`)
      this.wsConnecting = true

      // 连接WebSocket
      socketTask = uni.connectSocket({
        url: wsUrl,
        success: (data) => {
          console.log('✅ WebSocket Mixin: 连接请求发送成功')
          clearInterval(againTimer) // 清除断线重连定时器
        },
        fail: (err) => {
          console.error('❌ WebSocket Mixin: 连接失败', err)
          this.wsConnecting = false
        }
      })

      // 连接打开
      socketTask.onOpen((res) => {
        console.log('🎉 WebSocket Mixin: 全局连接已打开', res)
        this.wsConnected = true
        this.wsConnecting = false
        this.wsError = false

        // 🔧 设置全局连接状态
        getApp().globalData.websocketConnected = true
        getApp().globalData.websocketConnecting = false

        // 更新存储状态
        this.updateStorageStatus()

        clearInterval(againTimer) // 清除断线重连定时器
        this.handleWebSocketSuccess({ isShow: false })

        // 启动心跳
        this.startHeartbeat()

        // 发送认证
        this.sendAuth()
      })

      // 监听连接失败
      socketTask.onError((err) => {
        console.error('❌ WebSocket Mixin: 全局连接错误', err)
        this.wsConnected = false
        this.wsConnecting = false
        this.wsError = true

        // 🔧 更新全局连接状态
        getApp().globalData.websocketConnected = false
        getApp().globalData.websocketConnecting = false

        // 🔧 广播错误事件
        uni.$emit('websocket-error', err)

        // 更新存储状态
        this.updateStorageStatus()

        // 停止发送心跳
        clearInterval(heartbeatInterval)

        // 如果不是人为关闭的话，进行重连
        if (!isSocketClose) {
          this.reconnectWebSocket()
        }
      })

      // 监听连接关闭
      socketTask.onClose((e) => {
        console.log('🔌 WebSocket Mixin: 全局连接已关闭')
        this.wsConnected = false
        this.wsConnecting = false

        // 🔧 更新全局连接状态
        getApp().globalData.websocketConnected = false
        getApp().globalData.websocketConnecting = false

        // 🔧 广播关闭事件
        uni.$emit('websocket-close', e)

        // 更新存储状态
        this.updateStorageStatus()

        clearInterval(heartbeatInterval)

        if (!isSocketClose) {
          this.reconnectWebSocket()
        }
      })

      // 监听收到信息
      socketTask.onMessage((res) => {
        try {
          const serverData = JSON.parse(res.data)
          console.log('📨 WebSocket Mixin: 收到全局消息', serverData)

          // 🔧 只广播消息到所有页面，避免重复处理
          uni.$emit('websocket-message', serverData)

          // 🔧 移除直接调用，避免重复处理同一条消息
          // this.handleWebSocketMessage(serverData)
        } catch (error) {
          console.error('❌ WebSocket Mixin: 解析消息失败', error)
        }
      })
    },

    /**
     * 断线重连
     */
    reconnectWebSocket() {
      console.log('🔄 WebSocket Mixin: 开始断线重连')
      clearInterval(againTimer)
      clearInterval(heartbeatInterval)
      
      if (socketTask) {
        socketTask.close()
        socketTask = null
      }
      
      this.handleWebSocketError({
        isShow: true,
        message: 'WebSocket服务正在重连...'
      })
      
      // 5秒后重连
      againTimer = setInterval(() => {
        console.log('🔄 WebSocket Mixin: 正在重新连接...')
        this.connectWebSocket()
      }, 5000)
    },

    /**
     * 启动心跳
     */
    startHeartbeat() {
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval)
      }
      
      // 10秒发送一次心跳
      heartbeatInterval = setInterval(() => {
        if (socketTask && this.wsConnected) {
          this.sendMessage('心跳ing')
        }
      }, 10000)
    },

    /**
     * 发送消息
     */
    sendMessage(msg) {
      if (!socketTask || !this.wsConnected) {
        console.log('⚠️ WebSocket Mixin: 连接未建立，无法发送消息')
        return
      }
      
      try {
        const message = typeof msg === 'string' ? msg : JSON.stringify(msg)
        socketTask.send({
          data: message
        })
      } catch (e) {
        console.error('❌ WebSocket Mixin: 发送消息失败', e)
        if (!isSocketClose) {
          this.reconnectWebSocket()
        }
      }
    },

    /**
     * 发送认证消息（参考测试页面实现）
     */
    sendAuth() {
      // 🔧 使用与测试页面相同的认证方式
      const merchantId = uni.getStorageSync('user_uid') || '1000'
      const token = uni.getStorageSync('user_token') || 'test_token_1234567890'

      console.log('🔐 WebSocket Mixin: 发送认证消息，商户ID:', merchantId, '令牌:', token)

      const authMessage = {
        type: 'auth',
        data: {
          merchant_id: merchantId,
          staff_id: '',
          token: token
        }
      }

      this.sendMessage(authMessage)

      // 认证后立即订阅支付频道（参考测试页面）
      setTimeout(() => {
        this.subscribeToMerchantChannel(merchantId)
      }, 100) // 缩短延迟时间
    },

    /**
     * 订阅商户专属频道（参考测试页面实现）
     */
    subscribeToMerchantChannel(merchantId) {
      const channel = `merchant_${merchantId}_payment`

      console.log('📡 WebSocket Mixin: 订阅商户专属频道:', channel)

      const subscribeMessage = {
        type: 'subscribe',
        data: {
          channel: channel
        }
      }

      this.sendMessage(subscribeMessage)
    },

    /**
     * 关闭WebSocket连接
     */
    closeWebSocket() {
      console.log('🔌 WebSocket Mixin: 关闭WebSocket连接', {
        wsConnected: this.wsConnected,
        wsConnecting: this.wsConnecting,
        hasSocketTask: !!socketTask
      })

      isSocketClose = true

      // 清理定时器
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval)
        heartbeatInterval = null
        console.log('🔌 WebSocket Mixin: 清理心跳定时器')
      }

      if (againTimer) {
        clearInterval(againTimer)
        againTimer = null
        console.log('🔌 WebSocket Mixin: 清理重连定时器')
      }

      // 关闭连接
      if (socketTask) {
        socketTask.close()
        socketTask = null
        console.log('🔌 WebSocket Mixin: 连接已关闭')
      }

      this.wsConnected = false
      this.wsConnecting = false
    },

    /**
     * 静默处理WebSocket消息（不播放音频）
     */
    handleWebSocketMessageSilent(data) {
      console.log('🔇 WebSocket Mixin: 静默处理消息（不播放音频）', data)

      // 只更新连接状态，不播放音频
      switch (data.type) {
        case 'welcome':
        case 'auth_result':
        case 'pong':
        case 'heartbeat':
          // 这些消息只记录日志，不做其他处理
          break
        case 'payment_notification':
          console.log('💰 WebSocket Mixin: 收到支付通知（静默模式）:', data.data)
          // 不播放音频，只记录
          break
        default:
          console.log(`ℹ️ WebSocket Mixin: 收到未知类型消息（静默模式）: ${data.type}`, data)
      }
    },

    /**
     * 处理WebSocket消息（子类需要重写）
     */
    handleWebSocketMessage(data) {
      console.log('📨 WebSocket Mixin: 处理消息（默认实现）', data)
      
      switch (data.type) {
        case 'welcome':
          console.log('🎉 WebSocket Mixin: 收到服务器欢迎消息')
          break
        case 'auth_result':
          if (data.data.success) {
            console.log('✅ WebSocket Mixin: 认证成功', data.data)
          } else {
            console.error(`❌ WebSocket Mixin: 认证失败: ${data.data.message}`, data.data)
          }
          break
        case 'payment_notification':
          console.log('💰 WebSocket Mixin: 收到支付通知:', data.data)
          this.handlePaymentNotification(data.data)
          break
        case 'pong':
          console.log('💓 WebSocket Mixin: 收到心跳响应')
          break
        case 'heartbeat':
          console.log('💓 WebSocket Mixin: 收到服务器心跳')
          break
        default:
          console.log(`ℹ️ WebSocket Mixin: 收到未知类型消息: ${data.type}`, data)
      }
    },

    /**
     * 处理支付通知
     */
    async handlePaymentNotification(paymentData) {
      try {
        // 🔧 支持多种金额字段格式，包括嵌套的extra_data
        const amount = paymentData.amount ||
                      paymentData.money ||
                      paymentData.extra_data?.money ||
                      paymentData.extra_data?.amount ||
                      paymentData.realmoney || '0.00'

        // 生成消息唯一标识（用于去重）
        const messageId = this.generateMessageId(paymentData)

        // 检查是否已处理过此消息
        if (this.processedMessages.has(messageId)) {
          console.log(`⚠️ WebSocket Mixin: 消息已处理过，跳过重复播放: ${messageId}`)
          return
        }

        // 标记消息已处理
        this.processedMessages.add(messageId)

        // 定时清理已处理消息（防止内存泄漏）
        this.scheduleMessageCleanup()

        console.log(`🎵 WebSocket Mixin: 播放支付语音: 收款${amount}元 (ID: ${messageId})`)
        console.log('💰 WebSocket Mixin: 支付数据:', paymentData)
        console.log('💰 WebSocket Mixin: 提取的金额:', amount)

        // 检查语音设置
        if (!this.voiceEnabled) {
          console.log('🔇 语音播报已关闭，跳过播放')
          return
        }

        // 播放语音
        if (this.audioPlayer) {
          await this.audioPlayer.playPaymentSuccess(amount)
        }

        // 显示Toast提示
        uni.showToast({
          title: `收款 ¥${amount}`,
          icon: 'success',
          duration: 3000
        })

        console.log(`✅ WebSocket Mixin: 语音播放完成: 收款${amount}元`)

      } catch (error) {
        console.error('❌ WebSocket Mixin: 处理支付通知失败:', error)
      }
    },

    /**
     * 生成消息唯一标识
     */
    generateMessageId(paymentData) {
      const timestamp = paymentData.timestamp ||
                       paymentData.time ||
                       paymentData.created_at ||
                       Date.now()
      const amount = paymentData.amount ||
                    paymentData.money ||
                    paymentData.realmoney || '0'
      const orderId = paymentData.order_id ||
                     paymentData.merchant_id ||
                     paymentData.id ||
                     Math.random().toString(36).substr(2, 9)

      return `${timestamp}_${amount}_${orderId}`
    },

    /**
     * 定时清理已处理消息
     */
    scheduleMessageCleanup() {
      if (this.messageCleanupTimer) {
        clearTimeout(this.messageCleanupTimer)
      }

      // 30秒后清理消息记录
      this.messageCleanupTimer = setTimeout(() => {
        const oldSize = this.processedMessages.size
        this.processedMessages.clear()
        console.log(`🧹 WebSocket Mixin: 清理消息记录 (${oldSize} -> 0)`)
      }, 30000)
    },

    /**
     * 更新语音设置
     */
    updateVoiceSettings() {
      const voiceEnabled = uni.getStorageSync('voice_enabled')
      this.voiceEnabled = voiceEnabled !== false // 默认启用
      console.log(`🔊 WebSocket Mixin: 语音设置更新: ${this.voiceEnabled ? '启用' : '禁用'}`)
    },

    /**
     * 更新存储中的WebSocket状态
     */
    updateStorageStatus() {
      try {
        const status = {
          connected: this.wsConnected,
          connecting: this.wsConnecting,
          timestamp: Date.now()
        }
        uni.setStorageSync('websocket_status', status)
        console.log('💾 WebSocket状态已保存到存储:', status)
      } catch (error) {
        console.error('❌ 保存WebSocket状态失败:', error)
      }
    },

    /**
     * 处理WebSocket错误
     */
    handleWebSocketError(error) {
      console.log('❌ WebSocket Mixin: 连接错误', error)
      this.wsError = true
      this.wsErrorMessage = error.message || '连接失败'
      
      if (error.isShow) {
        // 可以在这里显示错误提示
        // uni.showToast({
        //   title: error.message || '连接失败',
        //   icon: 'none'
        // })
      }
    },

    /**
     * 处理WebSocket成功
     */
    handleWebSocketSuccess(success) {
      console.log('✅ WebSocket Mixin: 连接成功', success)
      this.wsError = false
      this.wsErrorMessage = ''
    },

    /**
     * 清理资源
     */
    cleanup() {
      // 清理消息记录
      if (this.processedMessages) {
        this.processedMessages.clear()
      }

      // 清理定时器
      if (this.messageCleanupTimer) {
        clearTimeout(this.messageCleanupTimer)
        this.messageCleanupTimer = null
      }

      // 清理音频播放器
      if (this.audioPlayer) {
        this.audioPlayer.clearQueue()
      }

      console.log('🧹 WebSocket Mixin: 资源清理完成')
    }
  }
}
