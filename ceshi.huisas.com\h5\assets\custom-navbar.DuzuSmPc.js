import{R as t,o as e,e as l,w as i,a9 as a,a1 as o,i as n,v as r,d as s,f as c,z as h,j as d,h as u,t as f,l as g,J as k,y,ae as b}from"./index-B1Q521gi.js";import{r as p,_}from"./uni-app.es.DAfa8VxY.js";import{_ as m}from"./_plugin-vue_export-helper.BCo6x5W8.js";const C=t=>"number"==typeof t?t+"px":t;const v=m({name:"UniNavBar",components:{statusBar:m({name:"UniStatusBar",data:()=>({statusBarHeight:t().statusBarHeight+"px"})},[["render",function(t,r,s,c,h,d){const u=n;return e(),l(u,{style:o({height:h.statusBarHeight}),class:"uni-status-bar"},{default:i((()=>[a(t.$slots,"default",{},void 0,!0)])),_:3},8,["style"])}],["__scopeId","data-v-4539ef65"]])},emits:["clickLeft","clickRight","clickTitle"],props:{dark:{type:Boolean,default:!1},title:{type:String,default:""},leftText:{type:String,default:""},rightText:{type:String,default:""},leftIcon:{type:String,default:""},rightIcon:{type:String,default:""},fixed:{type:[Boolean,String],default:!1},color:{type:String,default:""},backgroundColor:{type:String,default:""},statusBar:{type:[Boolean,String],default:!1},shadow:{type:[Boolean,String],default:!1},border:{type:[Boolean,String],default:!0},height:{type:[Number,String],default:44},leftWidth:{type:[Number,String],default:60},rightWidth:{type:[Number,String],default:60},stat:{type:[Boolean,String],default:""}},computed:{themeBgColor(){return this.dark?this.backgroundColor?this.backgroundColor:this.dark?"#333":"#FFF":this.backgroundColor||"#FFF"},themeColor(){return this.dark?this.color?this.color:this.dark?"#fff":"#333":this.color||"#333"},navbarHeight(){return C(this.height)},leftIconWidth(){return C(this.leftWidth)},rightIconWidth(){return C(this.rightWidth)}},mounted(){uni.report&&this.stat&&""!==this.title&&uni.report("title",this.title)},methods:{onClickLeft(){this.$emit("clickLeft")},onClickRight(){this.$emit("clickRight")},onClickTitle(){this.$emit("clickTitle")}}},[["render",function(t,k,y,b,m,C){const v=r("status-bar"),x=p(s("uni-icons"),_),B=n,S=g;return e(),l(B,{class:h(["uni-navbar",{"uni-dark":y.dark,"uni-nvue-fixed":y.fixed}])},{default:i((()=>[c(B,{class:h(["uni-navbar__content",{"uni-navbar--fixed":y.fixed,"uni-navbar--shadow":y.shadow,"uni-navbar--border":y.border}]),style:o({"background-color":C.themeBgColor,"border-bottom-color":C.themeColor})},{default:i((()=>[y.statusBar?(e(),l(v,{key:0})):d("",!0),c(B,{style:o({color:C.themeColor,backgroundColor:C.themeBgColor,height:C.navbarHeight}),class:"uni-navbar__header"},{default:i((()=>[c(B,{onClick:C.onClickLeft,class:"uni-navbar__header-btns uni-navbar__header-btns-left",style:o({width:C.leftIconWidth})},{default:i((()=>[a(t.$slots,"left",{},(()=>[y.leftIcon.length>0?(e(),l(B,{key:0,class:"uni-navbar__content_view"},{default:i((()=>[c(x,{color:C.themeColor,type:y.leftIcon,size:"20"},null,8,["color","type"])])),_:1})):d("",!0),y.leftText.length?(e(),l(B,{key:1,class:h([{"uni-navbar-btn-icon-left":!y.leftIcon.length>0},"uni-navbar-btn-text"])},{default:i((()=>[c(S,{style:o({color:C.themeColor,fontSize:"12px"})},{default:i((()=>[u(f(y.leftText),1)])),_:1},8,["style"])])),_:1},8,["class"])):d("",!0)]),!0)])),_:3},8,["onClick","style"]),c(B,{class:"uni-navbar__header-container",onClick:C.onClickTitle},{default:i((()=>[a(t.$slots,"default",{},(()=>[y.title.length>0?(e(),l(B,{key:0,class:"uni-navbar__header-container-inner"},{default:i((()=>[c(S,{class:"uni-nav-bar-text uni-ellipsis-1",style:o({color:C.themeColor})},{default:i((()=>[u(f(y.title),1)])),_:1},8,["style"])])),_:1})):d("",!0)]),!0)])),_:3},8,["onClick"]),c(B,{onClick:C.onClickRight,class:"uni-navbar__header-btns uni-navbar__header-btns-right",style:o({width:C.rightIconWidth})},{default:i((()=>[a(t.$slots,"right",{},(()=>[y.rightIcon.length?(e(),l(B,{key:0},{default:i((()=>[c(x,{color:C.themeColor,type:y.rightIcon,size:"22"},null,8,["color","type"])])),_:1})):d("",!0),y.rightText.length&&!y.rightIcon.length?(e(),l(B,{key:1,class:"uni-navbar-btn-text"},{default:i((()=>[c(S,{class:"uni-nav-bar-right-text",style:o({color:C.themeColor})},{default:i((()=>[u(f(y.rightText),1)])),_:1},8,["style"])])),_:1})):d("",!0)]),!0)])),_:3},8,["onClick","style"])])),_:3},8,["style"])])),_:3},8,["class","style"]),y.fixed?(e(),l(B,{key:0,class:"uni-navbar__placeholder"},{default:i((()=>[y.statusBar?(e(),l(v,{key:0})):d("",!0),c(B,{class:"uni-navbar__placeholder-view",style:o({height:C.navbarHeight})},null,8,["style"])])),_:1})):d("",!0)])),_:3},8,["class"])}],["__scopeId","data-v-ed8f08f1"]]);const x=m({name:"CustomNavbar",props:{title:{type:String,default:""},showBack:{type:Boolean,default:!0},rightText:{type:String,default:""},rightIcon:{type:String,default:""},color:{type:String,default:"#ffffff"},backgroundColor:{type:String,default:"#5145F7"},shadow:{type:Boolean,default:!1}},methods:{onClickLeft(){if(this.showBack){k().length>1?y():this.$emit("clickLeft")}else this.$emit("clickLeft")},onClickRight(){this.$emit("clickRight")},onClickTitle(){this.$emit("clickTitle")}}},[["render",function(t,o,r,h,d,u){const f=p(s("uni-nav-bar"),v),g=n;return e(),l(g,{class:"custom-navbar"},{default:i((()=>[c(f,{title:r.title,"left-text":r.showBack?"返回":"","right-text":r.rightText,"left-icon":r.showBack?"left":"","right-icon":r.rightIcon,color:r.color,"background-color":r.backgroundColor,"status-bar":!0,fixed:!0,shadow:r.shadow,onClickLeft:u.onClickLeft,onClickRight:u.onClickRight,onClickTitle:u.onClickTitle},b({_:2},[t.$slots.default?{name:"default",fn:i((()=>[a(t.$slots,"default",{},void 0,!0)])),key:"0"}:void 0,t.$slots.left?{name:"left",fn:i((()=>[a(t.$slots,"left",{},void 0,!0)])),key:"1"}:void 0,t.$slots.right?{name:"right",fn:i((()=>[a(t.$slots,"right",{},void 0,!0)])),key:"2"}:void 0]),1032,["title","left-text","right-text","left-icon","right-icon","color","background-color","shadow","onClickLeft","onClickRight","onClickTitle"])])),_:3})}],["__scopeId","data-v-9e0e7d64"]]);export{x as _};
