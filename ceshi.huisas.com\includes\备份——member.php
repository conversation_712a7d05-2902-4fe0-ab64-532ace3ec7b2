<?php
// 确保 password_hash 变量已定义
if (!isset($password_hash)) {
    $password_hash = '!@#%!s!0';
}

$clientip=real_ip($conf['ip_type']?$conf['ip_type']:0);

if(isset($_COOKIE["admin_token"]))
{
	$token=authcode(daddslashes($_COOKIE['admin_token']), 'DECODE', SYS_KEY);
	list($user, $sid, $expiretime) = explode("\t", $token);
	$session=md5($conf['admin_user'].$conf['admin_pwd'].$password_hash);
	if($session==$sid && $expiretime>time()) {
		$islogin=1;
	}
}
// 支持多种方式获取user_token：Cookie、POST数据、GET参数
$user_token = '';
if(isset($_COOKIE["user_token"])) {
	$user_token = $_COOKIE["user_token"];
} elseif(isset($_POST["user_token"])) {
	$user_token = $_POST["user_token"];
} elseif(isset($_GET["user_token"])) {
	$user_token = $_GET["user_token"];
}

if(!empty($user_token))
{
	$token=authcode(daddslashes($user_token), 'DECODE', SYS_KEY);
	list($uid, $sid, $expiretime) = explode("\t", $token);
	$uid = intval($uid);
	$userrow=$DB->getRow("SELECT * FROM pre_user WHERE uid=:uid limit 1", [':uid'=>$uid]);
	$session=md5($userrow['uid'].$userrow['key'].$password_hash);
	if($userrow && $session==$sid && $expiretime>time()) {
		$islogin2=1;
	}
}
?>