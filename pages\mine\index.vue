<template>
  <view class="container">
    <!-- 优雅现代导航栏 -->
    <view class="elegant-navbar">
      <!-- 状态栏占位 -->
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

      <!-- 导航栏主体 -->
      <view class="navbar-main">
        <!-- 左侧：标题区域 -->
        <view class="navbar-title-section">
          <text class="page-title">我的</text>
          <text class="page-subtitle">个人中心</text>
        </view>

        <!-- 右侧：设置按钮 -->
        <view class="navbar-actions">
          <view class="action-btn" @click="navigateToSettings">
            <view class="action-icon-wrapper">
              <text class="action-icon">⚙️</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 导航栏占位 - 自适应安全区域 -->
    <view class="navbar-placeholder" :style="{ height: navbarPlaceholderHeight + 'px' }"></view>

    <view class="content">
      <view class="user-info">
        <view class="avatar-container">
          <image src="/static/mine/my.png" class="avatar" mode="aspectFit" />
        </view>
        <view class="user-details">
          <text class="user-name">{{ merchantInfo.username || '商家名称' }}</text>
          <text class="user-id">ID: {{ merchantInfo.pid || '未获取' }}</text>
        </view>
        <view class="edit-profile" @click="navigateToScan">
          <text>收款码</text>
        </view>
      </view>
      
      <!-- 账户余额 -->
      <view class="wallet-card">
        <view class="wallet-title">
          <text>账户余额(元)</text>
          <text class="detail-link">明细</text>
        </view>
        <view class="wallet-amount">
          <text>¥ {{ formatMoney(merchantInfo.money || 0) }}</text>
        </view>
        <view class="wallet-actions">
          <view class="action-btn primary">
            <text>提现</text>
          </view>
          <view class="action-btn secondary">
            <text>对账</text>
          </view>
        </view>
      </view>
      
      <!-- 我的工具 -->
      <view class="tools-section">
        <text class="section-title">我的工具</text>
        <view class="tools-grid">
          <view class="tool-item" @click="navigateToWallet">
            <view class="tool-icon blue-bg">
              <image src="/static/mine/qianbao.png" mode="aspectFit"></image>
            </view>
            <text class="tool-text">钱包</text>
          </view>
          <view class="tool-item" @click="navigateToMerchant">
            <view class="tool-icon green-bg">
              <image src="/static/mine/shangjia.png" mode="aspectFit"></image>
            </view>
            <text class="tool-text">商家信息</text>
          </view>
          <view class="tool-item" @click="navigateToDuizhang">
            <view class="tool-icon purple-bg">
              <image src="/static/mine/duizhangdan.png" mode="aspectFit"></image>
            </view>
            <text class="tool-text">对账单</text>
          </view>
          <view class="tool-item" @click="navigateToJiesuan">
            <view class="tool-icon red-bg">
              <image src="/static/mine/jiezhangjilu.png" mode="aspectFit"></image>
            </view>
            <text class="tool-text">结算记录</text>
          </view>
          
          <view class="tool-item" @click="navigateToYuangong">
            <view class="tool-icon yellow-bg">
              <image src="/static/mine/yuangong.png" mode="aspectFit"></image>
            </view>
            <text class="tool-text">员工管理</text>
          </view>
          <view class="tool-item" @click="navigateToYingxiao">
            <view class="tool-icon blue-light-bg">
              <image src="/static/mine/yingxiao.png" mode="aspectFit"></image>
            </view>
            <text class="tool-text">营销工具</text>
          </view>
          <view class="tool-item" @click="navigateToXiaoxi">
            <view class="tool-icon pink-bg">
              <image src="/static/mine/tongzhi.png" mode="aspectFit"></image>
            </view>
            <text class="tool-text">消息通知</text>
          </view>
          <view class="tool-item">
            <view class="tool-icon gray-bg">
              <image src="/static/mine/gengduo.png" mode="aspectFit"></image>
            </view>
            <text class="tool-text">更多</text>
          </view>
        </view>
      </view>
      
      <view class="menu-list">
     
        <view class="menu-item" @click="showAccountSecurity">
          <image src="/static/mine/anquan.png" class="menu-icon" mode="aspectFit" />
          <text class="menu-text">账号安全</text>
          <text class="menu-arrow">></text>
        </view>
        <view class="menu-item" @click="navigateToVoiceSettings">
          <image src="/static/mine/tongzhi.png" class="menu-icon" mode="aspectFit" />
          <text class="menu-text">语音播报</text>
          <view class="menu-status">
            <text class="status-text" :class="{ active: voiceEnabled }">
              {{ voiceEnabled ? '已开启' : '已关闭' }}
            </text>
          </view>
          <text class="menu-arrow">></text>
        </view>
        <view class="menu-item">
          <image src="/static/tab/mine.png" class="menu-icon" mode="aspectFit" />
          <text class="menu-text">系统设置</text>
          <text class="menu-arrow">></text>
        </view>
        <view class="menu-item">
          <image src="/static/mine/kefu.png" class="menu-icon" mode="aspectFit" />
          <text class="menu-text">帮助中心</text>
          <text class="menu-arrow">></text>
        </view>
        <view class="menu-item">
          <image src="/static/mine/women.png" class="menu-icon" mode="aspectFit" />
          <text class="menu-text">关于我们</text>
          <text class="menu-arrow">></text>
        </view>
      </view>
      
      <!-- 调试按钮 -->
      <view class="debug-button" @click="goToDebug">
        <text>🔍 存储调试</text>
      </view>

      <!-- 退出登录按钮 -->
      <view class="logout-button" @click="handleLogout">
        <text>退出登录</text>
      </view>
    </view>
  </view>

  <!-- 🔐 修改密码弹窗 -->
  <view class="modal change-password-modal" v-if="showPasswordModal">
    <view class="modal-mask" @click="cancelChangePassword"></view>
    <view class="modal-content password-modal-content">
      <view class="modal-header">
        <text class="modal-title">修改密码</text>
        <text class="close-icon" @click="cancelChangePassword">×</text>
      </view>
      <view class="modal-body password-modal-body">
        <view class="form-group">
          <text class="form-label">旧密码</text>
          <input
            class="form-input"
            type="password"
            v-model="passwordForm.oldPassword"
            placeholder="请输入当前密码"
            maxlength="20"
          />
        </view>
        <view class="form-group">
          <text class="form-label">新密码</text>
          <input
            class="form-input"
            type="password"
            v-model="passwordForm.newPassword"
            placeholder="请输入新密码（6-20位）"
            maxlength="20"
          />
        </view>
        <view class="form-group">
          <text class="form-label">确认密码</text>
          <input
            class="form-input"
            type="password"
            v-model="passwordForm.confirmPassword"
            placeholder="请再次输入新密码"
            maxlength="20"
          />
        </view>
      </view>
      <view class="modal-footer password-modal-footer">
        <button class="cancel-btn" @click="cancelChangePassword">取消</button>
        <button class="confirm-btn" @click="confirmChangePassword" :disabled="isChangingPassword">
          <text v-if="isChangingPassword">修改中...</text>
          <text v-else>确认修改</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar.vue'
import { checkLoginStatus, clearLoginInfo } from '@/utils/auth.js'
import { request } from '@/utils/request.js'
import voiceManager from '@/utils/voiceManager.js'
import globalWebSocketService from '@/utils/globalWebSocketService.js'
import AudioPlayer from '@/utils/audioPlayer.js'

export default {
  components: {
    CustomNavbar
  },
  data() {
    return {
      // 系统信息
      statusBarHeight: 20,
      // 商户信息
      merchantInfo: {
        pid: '',
        username: '',
        money: 0,
        active: 1
      },
      // 加载状态
      loading: false,
      // 修改密码相关
      showPasswordModal: false,
      isChangingPassword: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      // 语音播报状态
      voiceEnabled: false,

      // 🚀 独立WebSocket连接（参考测试页面）
      wsConnected: false,
      wsConnecting: false,
      socketTask: null,
      heartbeatTimer: null
    }
  },

  onLoad() {
    console.log('🏠 Mine页面加载开始');
    // 初始化系统信息
    this.initSystemInfo();
    // 检查登录状态并加载数据
    this.checkLoginAndLoadData();
    // 🚀 初始化音频播放器
    this.audioPlayer = new AudioPlayer();
  },

  onShow() {
    console.log('🔄 Mine页面显示');
    // 页面显示时检查登录状态并刷新数据
    this.checkLoginAndLoadData();
    // 更新语音播报状态
    this.updateVoiceStatus();
    // 🚀 启动独立WebSocket连接（参考测试页面方案）
    this.initDirectWebSocket();
  },

  onUnload() {
    console.log('🔄 Mine页面卸载');
    // 断开WebSocket连接
    this.disconnectWebSocket();
  },

  computed: {
    // 导航栏占位高度
    navbarPlaceholderHeight() {
      // 状态栏高度 + 导航栏高度(44px)
      return this.statusBarHeight + 44;
    }
  },
  methods: {
    // 初始化系统信息
    initSystemInfo() {
      const systemInfo = uni.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight || 20;

      // 特殊处理iPhone 14 Pro Max等设备
      const model = systemInfo.model || '';
      const platform = systemInfo.platform || '';

      console.log('设备信息:', {
        model,
        platform,
        statusBarHeight: this.statusBarHeight,
        safeAreaInsets: systemInfo.safeAreaInsets
      });

      // iPhone 14 Pro Max的状态栏通常是47px
      if (model.includes('iPhone') && this.statusBarHeight > 40) {
        console.log('检测到iPhone Pro Max系列设备，状态栏高度:', this.statusBarHeight);
      }

      // 设置CSS变量，供样式使用
      const app = document.documentElement || document.body;
      if (app && app.style) {
        app.style.setProperty('--status-bar-height', this.statusBarHeight + 'px');
      }
    },

    // 检查登录状态并加载数据
    async checkLoginAndLoadData() {
      try {
        console.log('🔍 检查登录状态...');

        // 检查本地token
        const token = uni.getStorageSync('user_token');
        const uid = uni.getStorageSync('user_uid');

        console.log('存储的Token:', token ? '***' + token.slice(-6) : '无');
        console.log('存储的UID:', uid);

        if (!token || !uid) {
          console.log('❌ 未找到登录信息，跳转登录页');
          this.redirectToLogin();
          return;
        }

        // 验证登录状态
        const isLogin = await checkLoginStatus();
        if (!isLogin) {
          console.log('❌ 登录状态无效，跳转登录页');
          this.redirectToLogin();
          return;
        }

        console.log('✅ 登录状态有效，加载商户信息');
        // 加载商户信息
        await this.loadMerchantInfo();

      } catch (error) {
        console.error('❌ 检查登录状态失败:', error);
        this.redirectToLogin();
      }
    },

    // 加载商户信息
    async loadMerchantInfo() {
      if (this.loading) return;

      try {
        this.loading = true;
        console.log('🔄 开始加载商户信息...');

        // 使用统一的request方法调用后端接口
        const response = await request({
          url: '/user/ajax2.php?act=getcount',
          method: 'POST',
          data: {},
          loading: false
        });

        console.log('📊 商户信息响应:', response);
        console.log('📊 响应详细信息:');
        console.log('  - response.code:', response?.code);
        console.log('  - response.uid:', response?.uid);
        console.log('  - response.pid:', response?.pid);
        console.log('  - response.username:', response?.username);
        console.log('  - response.account:', response?.account);
        console.log('  - response.money:', response?.money);
        console.log('  - response.msg:', response?.msg);

        if (response && response.code === 0) {
          // 后端返回成功
          this.merchantInfo = {
            pid: response.uid || response.pid || '未获取',
            username: response.username || response.account || '商户',
            money: parseFloat(response.money || 0),
            active: response.active || 1,
            orders: response.orders || 0,
            orders_today: response.orders_today || 0
          };

          console.log('✅ 商户信息加载成功:', this.merchantInfo);
          console.log('✅ 最终显示的PID:', this.merchantInfo.pid);
        } else if (response && response.code === -3) {
          // 登录状态失效
          console.log('❌ 登录状态失效，清除登录信息并跳转');
          clearLoginInfo();
          this.redirectToLogin();
        } else {
          console.error('❌ 商户信息加载失败:', response?.msg);
          uni.showToast({
            title: response?.msg || '获取商户信息失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('❌ 商户信息加载异常:', error);

        // 如果是登录状态失效错误
        if (error && error.code === -3) {
          console.log('❌ 登录状态失效，清除登录信息并跳转');
          clearLoginInfo();
          this.redirectToLogin();
        } else {
          uni.showToast({
            title: '网络错误，请稍后重试',
            icon: 'none'
          });
        }
      } finally {
        this.loading = false;
      }
    },

    // 跳转到登录页
    redirectToLogin() {
      console.log('🔄 跳转到登录页面');
      uni.reLaunch({
        url: '/pages/login/index'
      });
    },

    // 格式化金额显示
    formatMoney(amount) {
      const num = parseFloat(amount || 0);
      return num.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    // 前往调试页面
    goToDebug() {
      uni.navigateTo({
        url: '/pages/debug/storage'
      });
    },

    // 处理退出登录
    async handleLogout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({ title: '退出中...' });

              // 调用后端退出登录接口
              await request({
                url: '/user/login.php?logout',
                method: 'GET',
                loading: false
              });

              // 清除本地登录信息
              clearLoginInfo();

              console.log('✅ 退出登录成功');
              uni.hideLoading();

              uni.showToast({
                title: '已退出登录',
                icon: 'success'
              });

              // 跳转到登录页面
              setTimeout(() => {
                uni.reLaunch({
                  url: '/pages/login/index'
                });
              }, 1500);

            } catch (error) {
              console.error('❌ 退出登录失败:', error);
              uni.hideLoading();

              // 即使退出失败，也清除本地登录信息
              clearLoginInfo();

              uni.showToast({
                title: '已退出登录',
                icon: 'success'
              });

              // 跳转到登录页面
              setTimeout(() => {
                uni.reLaunch({
                  url: '/pages/login/index'
                });
              }, 1500);
            }
          }
        }
      });
    },

    navigateToSettings() {
      uni.showToast({
        title: '设置功能开发中',
        icon: 'none'
      });
    },

    navigateToWallet() {
      uni.navigateTo({
        url: '/pages/wallet/index'
      });
    },
    navigateToMerchant() {
      uni.navigateTo({
        url: '/pages/merchant/index'
      });
    },
    navigateToDuizhang() {
      uni.navigateTo({
        url: '/pages/duizhang/index'
      });
    },
    navigateToJiesuan() {
      uni.navigateTo({
        url: '/pages/jiesuan/index'
      });
    },
    navigateToXiaoxi() {
      uni.navigateTo({
        url: '/pages/xiaoxi/index'
      });
    },
    navigateToYuangong() {
      uni.navigateTo({
        url: '/pages/yuangong/index'
      });
    },
    navigateToYingxiao() {
      uni.navigateTo({
        url: '/pages/yingxiao/index'
      });
    },
    navigateToScan() {
      uni.navigateTo({
        url: '/pages/scan/index'
      });
    },

    // 🔐 显示账号安全弹窗
    showAccountSecurity() {
      this.showPasswordModal = true;
    },

    // 🔐 取消修改密码
    cancelChangePassword() {
      this.showPasswordModal = false;
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
    },

    // 🔐 确认修改密码
    async confirmChangePassword() {
      try {
        // 表单验证
        if (!this.passwordForm.oldPassword) {
          uni.showToast({
            title: '请输入旧密码',
            icon: 'none'
          });
          return;
        }

        if (!this.passwordForm.newPassword) {
          uni.showToast({
            title: '请输入新密码',
            icon: 'none'
          });
          return;
        }

        if (this.passwordForm.newPassword.length < 6) {
          uni.showToast({
            title: '新密码至少6位',
            icon: 'none'
          });
          return;
        }

        if (!this.passwordForm.confirmPassword) {
          uni.showToast({
            title: '请确认新密码',
            icon: 'none'
          });
          return;
        }

        if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
          uni.showToast({
            title: '两次密码不一致',
            icon: 'none'
          });
          return;
        }

        if (this.passwordForm.oldPassword === this.passwordForm.newPassword) {
          uni.showToast({
            title: '新旧密码不能相同',
            icon: 'none'
          });
          return;
        }

        this.isChangingPassword = true;

        // 调用修改密码接口
        const response = await request({
          url: '/user/ajax2.php?act=edit_pwd',
          method: 'POST',
          data: {
            oldpwd: this.passwordForm.oldPassword,
            newpwd: this.passwordForm.newPassword,
            newpwd2: this.passwordForm.confirmPassword
          }
        });

        console.log('🔐 修改密码响应:', response);

        if (response.code === 1) {
          uni.showToast({
            title: '密码修改成功',
            icon: 'success'
          });

          // 关闭弹窗
          this.cancelChangePassword();

          // 提示重新登录
          setTimeout(() => {
            uni.showModal({
              title: '密码已修改',
              content: '密码修改成功，请重新登录',
              showCancel: false,
              success: () => {
                // 清除登录信息并跳转到登录页
                clearLoginInfo();
                uni.reLaunch({
                  url: '/pages/login/index'
                });
              }
            });
          }, 1500);

        } else {
          uni.showToast({
            title: response.msg || '修改失败',
            icon: 'none'
          });
        }

      } catch (error) {
        console.error('❌ 修改密码失败:', error);
        uni.showToast({
          title: '修改失败，请重试',
          icon: 'none'
        });
      } finally {
        this.isChangingPassword = false;
      }
    },

    // 🔊 更新语音播报状态
    updateVoiceStatus() {
      try {
        // 直接从VoiceManager获取状态
        const voiceStatus = voiceManager.getStatus();
        this.voiceEnabled = voiceStatus.enabled;
        console.log('🔊 主页语音状态更新:', voiceStatus.enabled);
      } catch (error) {
        console.error('❌ 获取语音状态失败:', error);
      }
    },

    // 🚀 初始化独立WebSocket连接（参考测试页面）
    async initDirectWebSocket() {
      try {
        // 检查用户登录状态
        const token = uni.getStorageSync('user_token');
        const uid = uni.getStorageSync('user_uid');

        if (!token || !uid) {
          console.log('⚠️ 用户未登录，跳过WebSocket连接');
          return;
        }

        console.log('🚀 Mine页面：启动独立WebSocket连接');
        this.connectWebSocket();

      } catch (error) {
        console.error('❌ 初始化WebSocket连接失败:', error);
      }
    },

    // 🔗 连接WebSocket（参考测试页面实现）
    connectWebSocket() {
      if (this.wsConnected || this.wsConnecting) {
        console.log('⚠️ WebSocket已经连接或正在连接中');
        return;
      }

      this.wsConnecting = true;
      const wsUrl = 'ws://ceshi.huisas.com:8080';

      console.log(`🔗 Mine页面：正在连接WebSocket: ${wsUrl}`);

      this.socketTask = uni.connectSocket({
        url: wsUrl,
        success: () => {
          console.log('🚀 Mine页面：WebSocket连接请求已发送');
        },
        fail: (error) => {
          this.wsConnecting = false;
          console.error(`❌ Mine页面：WebSocket连接失败:`, error);
        }
      });

      // 连接成功
      this.socketTask.onOpen(() => {
        this.wsConnected = true;
        this.wsConnecting = false;
        console.log('✅ Mine页面：WebSocket连接成功！');

        // 启动心跳
        this.startHeartbeat();

        // 发送认证
        this.sendAuth();
      });

      // 接收消息
      this.socketTask.onMessage((res) => {
        try {
          const data = JSON.parse(res.data);
          console.log('📨 Mine页面：收到WebSocket消息:', data);
          this.handleWebSocketMessage(data);
        } catch (error) {
          console.error('❌ Mine页面：解析消息失败:', error);
        }
      });

      // 连接关闭
      this.socketTask.onClose(() => {
        this.wsConnected = false;
        this.wsConnecting = false;
        console.log('🔌 Mine页面：WebSocket连接已关闭');

        // 清理心跳
        if (this.heartbeatTimer) {
          clearInterval(this.heartbeatTimer);
          this.heartbeatTimer = null;
        }
      });

      // 连接错误
      this.socketTask.onError((error) => {
        this.wsConnected = false;
        this.wsConnecting = false;
        console.error(`❌ Mine页面：WebSocket连接错误:`, error);
      });
    },

    // 🔌 断开WebSocket连接
    disconnectWebSocket() {
      if (this.socketTask) {
        this.socketTask.close();
        this.socketTask = null;
      }
      this.wsConnected = false;
      this.wsConnecting = false;

      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }

      console.log('🔌 Mine页面：WebSocket连接已断开');
    },

    // 💓 启动心跳
    startHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
      }

      this.heartbeatTimer = setInterval(() => {
        if (this.wsConnected && this.socketTask) {
          this.socketTask.send({
            data: JSON.stringify({
              type: 'ping',
              data: { timestamp: Date.now() }
            })
          });
        }
      }, 30000); // 30秒心跳
    },

    // 🔐 发送认证消息
    sendAuth() {
      const token = uni.getStorageSync('user_token') || 'anonymous';
      const merchantId = uni.getStorageSync('user_uid');

      console.log('🔐 Mine页面：发送认证消息，商户ID:', merchantId);

      this.socketTask.send({
        data: JSON.stringify({
          type: 'auth',
          data: {
            token: token,
            merchant_id: merchantId,
            client_type: 'uniapp_mine_page',
            version: '1.0.0'
          }
        })
      });

      // 认证后订阅商户专属频道
      if (merchantId) {
        setTimeout(() => {
          this.subscribeToMerchantChannel(merchantId);
        }, 1000);
      }
    },

    // 📡 订阅商户专属频道
    subscribeToMerchantChannel(merchantId) {
      const channel = `merchant_${merchantId}_payment`;

      console.log('📡 Mine页面：订阅商户专属频道:', channel);

      this.socketTask.send({
        data: JSON.stringify({
          type: 'subscribe',
          data: {
            channel: channel
          }
        })
      });
    },

    // 📨 处理WebSocket消息
    async handleWebSocketMessage(data) {
      switch (data.type) {
        case 'welcome':
          console.log('🎉 Mine页面：收到服务器欢迎消息');
          break;
        case 'auth_result':
          if (data.data.success) {
            console.log('✅ Mine页面：认证成功');
          } else {
            console.error(`❌ Mine页面：认证失败: ${data.data.message}`);
          }
          break;
        case 'payment_notification':
          console.log('💰 Mine页面：收到支付通知:', data.data);
          await this.handlePaymentNotification(data.data);
          break;
        case 'pong':
          console.log('💓 Mine页面：收到心跳响应');
          break;
        default:
          console.log(`ℹ️ Mine页面：收到未知类型消息: ${data.type}`);
      }
    },

    // 💰 处理支付通知
    async handlePaymentNotification(paymentData) {
      try {
        const amount = paymentData.amount || '0.00';

        console.log(`🎵 Mine页面：播放支付语音: 收款${amount}元`);

        // 检查语音设置
        if (!this.voiceEnabled) {
          console.log('🔇 语音播报已关闭，跳过播放');
          return;
        }

        // 播放语音
        await this.audioPlayer.playPaymentSuccess(amount);

        // 显示Toast提示
        uni.showToast({
          title: `收款 ¥${amount}`,
          icon: 'success',
          duration: 3000
        });

        console.log(`✅ Mine页面：语音播放完成: 收款${amount}元`);

      } catch (error) {
        console.error('❌ Mine页面：处理支付通知失败:', error);
      }
    },

    // 🔊 导航到语音设置页面
    navigateToVoiceSettings() {
      uni.navigateTo({
        url: '/pages/settings/voice'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  font-family: 'Segoe UI', sans-serif;
  background-color: #f5f5f5;
}

.container {
  width: 100%;
  position: relative;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 优雅现代导航栏样式 */
.elegant-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 50%, #8B5CF6 100%);
  box-shadow: 0 4rpx 20rpx rgba(81, 69, 247, 0.15);
}

.status-bar {
  width: 100%;
}

.navbar-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  min-height: 88rpx;
}

.navbar-title-section {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.page-title {
  font-size: 36rpx;
  color: #fff;
  font-weight: 600;
  margin-bottom: 4rpx;
  letter-spacing: 0.5rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.action-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}

.action-icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 32rpx;
  line-height: 1;
}

/* 导航栏占位 - 自适应各种设备 */
.navbar-placeholder {
  width: 100%;
  /* 针对iPhone 14 Pro Max等设备的特殊处理 */
  height: calc(var(--status-bar-height, 47px) + 44px);
  /* 使用安全区域，兼容刘海屏和动态岛 */
  height: calc(env(safe-area-inset-top, 47px) + 44px);
  /* 最小高度保证，适配iPhone 14 Pro Max */
  min-height: 91px;
  flex-shrink: 0;
}

/* 针对不同平台的适配 */
/* #ifdef H5 */
.navbar-placeholder {
  /* H5环境，针对iPhone 14 Pro Max的动态岛 */
  height: calc(env(safe-area-inset-top, 47px) + 44px);
  min-height: 91px;
}
/* #endif */

/* #ifdef MP */
.navbar-placeholder {
  height: calc(var(--status-bar-height, 47px) + 44px);
  min-height: 91px;
}
/* #endif */

/* #ifdef APP-PLUS */
.navbar-placeholder {
  height: calc(var(--status-bar-height, 47px) + 44px);
  min-height: 91px;
}
/* #endif */

.content {
  padding: 30rpx;
}

.user-info {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.avatar-container {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24rpx;
}

.avatar {
  width: 110rpx;
  height: 110rpx;
  border-radius: 55rpx;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999;
}

.edit-profile {
  font-size: 26rpx;
  color: #5145F7;
  background-color: #F5F5F5;
  padding: 8rpx 16rpx;
  border-radius: 10rpx;
}

.menu-list {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 24rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-arrow {
  font-size: 30rpx;
  color: #ccc;
}

/* 语音播报状态样式 */
.menu-status {
  margin-left: auto;
  margin-right: 16rpx;
}

.status-text {
  font-size: 24rpx;
  color: #999;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
}

.status-text.active {
  color: #52c41a;
  background: #f6ffed;
  border: 1rpx solid #b7eb8f;
}

/* 调试按钮样式 */
.debug-button {
  background-color: #f0f0f0;
  margin: 24rpx 0;
  border-radius: 16rpx;
  padding: 32rpx;
  text-align: center;
  border: 2rpx dashed #ccc;
}

.debug-button text {
  color: #666;
  font-size: 28rpx;
}

/* 退出登录按钮样式 */
.logout-button {
  background-color: white;
  margin: 24rpx 0;
  border-radius: 16rpx;
  padding: 32rpx;
  text-align: center;
  color: #FF3B30;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 钱包卡片样式 */
.wallet-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.wallet-title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #333;
}

.detail-link {
  color: #5145F7;
}

.wallet-amount {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
}

.wallet-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  padding: 20rpx 0;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.primary {
  background-color: #5145F7;
  color: white;
}

.secondary {
  background-color: #f5f5f5;
  color: #333;
}

/* 工具部分样式 */
.tools-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.tools-grid {
  display: flex;
  flex-wrap: wrap;
}

.tool-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24rpx;
}

.tool-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12rpx;
}

.tool-icon image {
  width: 40rpx;
  height: 40rpx;
}

.tool-text {
  font-size: 24rpx;
  color: #333;
}

/* 图标背景色 */
.blue-bg {
  background-color: #E9F1FF;
}

.green-bg {
  background-color: #E4FFEF;
}

.purple-bg {
  background-color: #F0E5FF;
}

.red-bg {
  background-color: #FFE8E8;
}

.yellow-bg {
  background-color: #FFF8E0;
}

.blue-light-bg {
  background-color: #E5F1FF;
}

.pink-bg {
  background-color: #FFE4F4;
}

.gray-bg {
  background-color: #F0F0F0;
}

/* 🔐 修改密码弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.3s ease-out;
}

.password-modal-content {
  width: 600rpx;
  max-width: 90vw;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 100%);
  color: #fff;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
}

.close-icon {
  font-size: 40rpx;
  font-weight: 300;
  cursor: pointer;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-icon:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.9);
}

.password-modal-body {
  padding: 40rpx 32rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #5145F7;
  box-shadow: 0 0 0 4rpx rgba(81, 69, 247, 0.1);
}

.password-modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:active {
  background: #e5e5e5;
  transform: scale(0.98);
}

.confirm-btn {
  background: #5145F7;
  color: #fff;
}

.confirm-btn:active {
  background: #4338CA;
  transform: scale(0.98);
}

.confirm-btn:disabled {
  background: #ccc;
  color: #999;
  cursor: not-allowed;
}

.confirm-btn:disabled:active {
  transform: none;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>