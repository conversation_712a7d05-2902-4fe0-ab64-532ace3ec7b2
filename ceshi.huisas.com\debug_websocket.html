<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 连接调试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .status-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .status-card.connected {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn:hover { opacity: 0.8; transform: translateY(-1px); }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .log-container {
            background: #1e1e1e;
            color: #fff;
            padding: 20px;
            border-radius: 8px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-info { color: #17a2b8; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-send { color: #6f42c1; }
        .log-receive { color: #20c997; }
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .input-group input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 WebSocket 连接调试工具</h1>
            <p>用于调试 Swoole WebSocket 服务器连接问题</p>
        </div>

        <!-- 状态面板 -->
        <div class="status-panel">
            <div class="status-card" id="connection-status">
                <h4>连接状态</h4>
                <div id="status-text">未连接</div>
                <div id="status-details"></div>
            </div>
            <div class="status-card" id="server-info">
                <h4>服务器信息</h4>
                <div>地址: ws://ceshi.huisas.com:8080</div>
                <div id="server-status">检查中...</div>
            </div>
            <div class="status-card" id="message-stats">
                <h4>消息统计</h4>
                <div>发送: <span id="sent-count">0</span></div>
                <div>接收: <span id="received-count">0</span></div>
            </div>
        </div>

        <!-- 控制按钮 -->
        <div class="controls">
            <button class="btn btn-primary" onclick="connect()" id="connectBtn">连接</button>
            <button class="btn btn-danger" onclick="disconnect()" id="disconnectBtn" disabled>断开</button>
            <button class="btn btn-info" onclick="sendPing()" id="pingBtn" disabled>发送Ping</button>
            <button class="btn btn-success" onclick="sendAuth()" id="authBtn" disabled>发送认证</button>
            <button class="btn btn-warning" onclick="checkServerHealth()">检查服务器</button>
            <button class="btn btn-info" onclick="getServerStats()">获取统计</button>
            <button class="btn btn-warning" onclick="clearLog()">清空日志</button>
        </div>

        <!-- 自定义消息发送 -->
        <div class="input-group">
            <input type="text" id="customMessage" placeholder="输入自定义JSON消息，例如: {&quot;type&quot;:&quot;test&quot;,&quot;data&quot;:{}}">
            <button class="btn btn-primary" onclick="sendCustomMessage()" id="sendBtn" disabled>发送消息</button>
        </div>

        <!-- 日志显示 -->
        <div class="log-container" id="logContainer">
            <div class="log-entry log-info">[系统] WebSocket 调试工具已加载</div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value" id="uptime">0</div>
                <div class="stat-label">连接时长 (秒)</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="latency">-</div>
                <div class="stat-label">延迟 (ms)</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="reconnects">0</div>
                <div class="stat-label">重连次数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="errors">0</div>
                <div class="stat-label">错误次数</div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let connectTime = 0;
        let sentCount = 0;
        let receivedCount = 0;
        let reconnectCount = 0;
        let errorCount = 0;
        let pingTime = 0;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStatus(status, details = '') {
            const statusCard = document.getElementById('connection-status');
            const statusText = document.getElementById('status-text');
            const statusDetails = document.getElementById('status-details');
            
            statusCard.className = 'status-card';
            if (status === 'connected') {
                statusCard.classList.add('connected');
                statusText.textContent = '✅ 已连接';
            } else if (status === 'error') {
                statusCard.classList.add('error');
                statusText.textContent = '❌ 连接错误';
            } else {
                statusText.textContent = '⏳ ' + status;
            }
            
            statusDetails.textContent = details;
        }

        function updateButtons(connected) {
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
            document.getElementById('pingBtn').disabled = !connected;
            document.getElementById('authBtn').disabled = !connected;
            document.getElementById('sendBtn').disabled = !connected;
        }

        function updateStats() {
            document.getElementById('sent-count').textContent = sentCount;
            document.getElementById('received-count').textContent = receivedCount;
            document.getElementById('reconnects').textContent = reconnectCount;
            document.getElementById('errors').textContent = errorCount;
            
            if (connectTime > 0) {
                const uptime = Math.floor((Date.now() - connectTime) / 1000);
                document.getElementById('uptime').textContent = uptime;
            }
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('WebSocket已连接', 'warning');
                return;
            }

            log('正在连接 WebSocket...', 'info');
            updateStatus('连接中');

            try {
                ws = new WebSocket('ws://ceshi.huisas.com:8080');

                ws.onopen = function(event) {
                    connectTime = Date.now();
                    updateStatus('connected', `连接ID: ${Math.random().toString(36).substr(2, 9)}`);
                    updateButtons(true);
                    log('WebSocket连接成功！', 'success');
                };

                ws.onmessage = function(event) {
                    receivedCount++;
                    log(`📥 收到消息: ${event.data}`, 'receive');
                    
                    try {
                        const data = JSON.parse(event.data);
                        handleMessage(data);
                    } catch (e) {
                        log(`📥 原始消息: ${event.data}`, 'receive');
                    }
                    updateStats();
                };

                ws.onclose = function(event) {
                    updateStatus('error', `关闭代码: ${event.code}`);
                    updateButtons(false);
                    
                    let reason = getCloseReason(event.code);
                    log(`🔌 连接已关闭 (代码: ${event.code}, 原因: ${reason})`, 'error');
                    
                    if (event.reason) {
                        log(`📝 服务器消息: ${event.reason}`, 'info');
                    }
                    
                    connectTime = 0;
                    updateStats();
                };

                ws.onerror = function(error) {
                    errorCount++;
                    updateStatus('error', '连接错误');
                    updateButtons(false);
                    log(`❌ WebSocket错误: ${error}`, 'error');
                    updateStats();
                };

            } catch (error) {
                errorCount++;
                log(`❌ 连接异常: ${error.message}`, 'error');
                updateStatus('error', error.message);
                updateStats();
            }
        }

        function disconnect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.close(1000, '用户主动断开');
                log('🔌 主动断开连接', 'info');
            } else {
                log('⚠️ WebSocket未连接', 'warning');
            }
        }

        function sendPing() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket未连接', 'error');
                return;
            }

            pingTime = Date.now();
            const message = {
                type: 'ping',
                data: {
                    timestamp: pingTime,
                    client_time: new Date().toISOString()
                }
            };

            ws.send(JSON.stringify(message));
            sentCount++;
            log(`📤 发送Ping: ${JSON.stringify(message)}`, 'send');
            updateStats();
        }

        function sendAuth() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket未连接', 'error');
                return;
            }

            const message = {
                type: 'auth',
                data: {
                    merchant_id: 'debug_merchant_' + Date.now(),
                    token: 'debug_token_' + Math.random().toString(36).substr(2, 15),
                    app_key: 'payment_websocket_2024'
                }
            };

            ws.send(JSON.stringify(message));
            sentCount++;
            log(`📤 发送认证: ${JSON.stringify(message)}`, 'send');
            updateStats();
        }

        function sendCustomMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket未连接', 'error');
                return;
            }

            const input = document.getElementById('customMessage');
            const messageText = input.value.trim();
            
            if (!messageText) {
                log('❌ 请输入消息内容', 'error');
                return;
            }

            try {
                JSON.parse(messageText); // 验证JSON格式
                ws.send(messageText);
                sentCount++;
                log(`📤 发送自定义消息: ${messageText}`, 'send');
                input.value = '';
                updateStats();
            } catch (e) {
                log(`❌ 消息格式错误: ${e.message}`, 'error');
            }
        }

        function handleMessage(data) {
            switch (data.type) {
                case 'welcome':
                    log('🎉 收到欢迎消息', 'success');
                    break;
                case 'auth_result':
                    if (data.data.success) {
                        log('✅ 认证成功', 'success');
                    } else {
                        log(`❌ 认证失败: ${data.data.message}`, 'error');
                    }
                    break;
                case 'pong':
                    if (pingTime > 0) {
                        const latency = Date.now() - pingTime;
                        document.getElementById('latency').textContent = latency;
                        log(`🏓 收到Pong响应，延迟: ${latency}ms`, 'success');
                        pingTime = 0;
                    }
                    break;
                case 'heartbeat':
                    log('💓 收到服务器心跳', 'info');
                    // 自动回复心跳
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        const response = {
                            type: 'heartbeat_response',
                            data: {
                                timestamp: Date.now()
                            }
                        };
                        ws.send(JSON.stringify(response));
                        log('💓 已回复心跳响应', 'send');
                    }
                    break;
                default:
                    log(`📨 收到消息类型: ${data.type}`, 'info');
                    log(`📨 消息内容: ${JSON.stringify(data.data)}`, 'info');
            }
        }

        function getCloseReason(code) {
            const reasons = {
                1000: '正常关闭',
                1001: '端点离开',
                1002: '协议错误',
                1003: '不支持的数据类型',
                1006: '异常关闭 (网络问题或服务器重启)',
                1007: '数据格式错误',
                1008: '违反策略',
                1009: '消息过大',
                1010: '扩展协商失败',
                1011: '服务器内部错误'
            };
            return reasons[code] || '未知原因';
        }

        function checkServerHealth() {
            log('🔍 检查服务器健康状态...', 'info');
            
            fetch('http://ceshi.huisas.com:8080/health')
                .then(response => response.json())
                .then(data => {
                    log('✅ 服务器健康检查通过', 'success');
                    log(`📊 服务器信息: ${JSON.stringify(data)}`, 'info');
                    document.getElementById('server-status').textContent = '✅ 服务正常';
                })
                .catch(error => {
                    log(`❌ 服务器健康检查失败: ${error.message}`, 'error');
                    document.getElementById('server-status').textContent = '❌ 服务异常';
                });
        }

        function getServerStats() {
            log('📊 获取服务器统计信息...', 'info');
            
            fetch('http://ceshi.huisas.com:8080/stats')
                .then(response => response.json())
                .then(data => {
                    log('✅ 统计信息获取成功', 'success');
                    log(`📊 统计数据: ${JSON.stringify(data, null, 2)}`, 'info');
                })
                .catch(error => {
                    log(`❌ 统计信息获取失败: ${error.message}`, 'error');
                });
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            log('🧹 日志已清空', 'info');
        }

        // 定时更新统计信息
        setInterval(updateStats, 1000);

        // 页面加载时检查服务器状态
        window.addEventListener('load', function() {
            log('🚀 WebSocket调试工具已加载', 'info');
            checkServerHealth();
        });
    </script>
</body>
</html>
