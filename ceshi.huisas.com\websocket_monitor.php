<?php
/**
 * WebSocket服务监控脚本
 * 用于监控Swoole WebSocket服务状态，自动重启异常退出的服务
 */

// 配置
$config = [
    'pid_file' => __DIR__ . '/logs/swoole_websocket.pid',
    'log_file' => __DIR__ . '/logs/websocket_monitor.log',
    'server_script' => __DIR__ . '/swoole_websocket_server.php',
    'check_interval' => 30, // 检查间隔（秒）
    'max_restart_attempts' => 5, // 最大重启尝试次数
    'restart_cooldown' => 300, // 重启冷却时间（秒）
];

// 创建日志目录
if (!is_dir(dirname($config['log_file']))) {
    mkdir(dirname($config['log_file']), 0755, true);
}

/**
 * 记录日志
 */
function writeLog($message, $level = 'INFO') {
    global $config;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [{$level}] {$message}\n";
    file_put_contents($config['log_file'], $logMessage, FILE_APPEND | LOCK_EX);
    echo $logMessage;
}

/**
 * 检查WebSocket服务是否运行
 */
function isWebSocketRunning() {
    global $config;
    
    // 检查PID文件
    if (!file_exists($config['pid_file'])) {
        return false;
    }
    
    $pid = trim(file_get_contents($config['pid_file']));
    if (empty($pid) || !is_numeric($pid)) {
        return false;
    }
    
    // 检查进程是否存在
    if (!posix_kill($pid, 0)) {
        // 进程不存在，删除过期的PID文件
        unlink($config['pid_file']);
        return false;
    }
    
    // 检查端口是否监听
    $connection = @fsockopen('127.0.0.1', 8080, $errno, $errstr, 3);
    if (!$connection) {
        return false;
    }
    fclose($connection);
    
    return true;
}

/**
 * 启动WebSocket服务
 */
function startWebSocketService() {
    global $config;
    
    writeLog('正在启动WebSocket服务...', 'INFO');
    
    // 切换到服务器脚本目录
    $oldDir = getcwd();
    chdir(dirname($config['server_script']));
    
    // 启动服务
    $command = "php " . basename($config['server_script']) . " start > /dev/null 2>&1 &";
    exec($command, $output, $returnCode);
    
    // 恢复目录
    chdir($oldDir);
    
    if ($returnCode === 0) {
        writeLog('WebSocket服务启动命令执行成功', 'INFO');
        
        // 等待服务启动
        sleep(3);
        
        if (isWebSocketRunning()) {
            writeLog('WebSocket服务启动成功', 'SUCCESS');
            return true;
        } else {
            writeLog('WebSocket服务启动失败：服务未正常运行', 'ERROR');
            return false;
        }
    } else {
        writeLog("WebSocket服务启动失败：命令执行错误 (返回码: {$returnCode})", 'ERROR');
        return false;
    }
}

/**
 * 停止WebSocket服务
 */
function stopWebSocketService() {
    global $config;
    
    if (!file_exists($config['pid_file'])) {
        return true;
    }
    
    $pid = trim(file_get_contents($config['pid_file']));
    if (empty($pid) || !is_numeric($pid)) {
        return true;
    }
    
    writeLog("正在停止WebSocket服务 (PID: {$pid})...", 'INFO');
    
    if (posix_kill($pid, SIGTERM)) {
        // 等待进程退出
        $attempts = 0;
        while ($attempts < 10 && posix_kill($pid, 0)) {
            sleep(1);
            $attempts++;
        }
        
        if (!posix_kill($pid, 0)) {
            unlink($config['pid_file']);
            writeLog('WebSocket服务已停止', 'INFO');
            return true;
        } else {
            // 强制杀死进程
            posix_kill($pid, SIGKILL);
            unlink($config['pid_file']);
            writeLog('WebSocket服务已强制停止', 'WARNING');
            return true;
        }
    }
    
    return false;
}

/**
 * 主监控循环
 */
function monitorLoop() {
    global $config;
    
    $restartAttempts = 0;
    $lastRestartTime = 0;
    
    writeLog('WebSocket监控服务启动', 'INFO');
    writeLog("检查间隔: {$config['check_interval']}秒", 'INFO');
    writeLog("最大重启尝试: {$config['max_restart_attempts']}次", 'INFO');
    
    while (true) {
        $currentTime = time();
        
        if (!isWebSocketRunning()) {
            writeLog('检测到WebSocket服务未运行', 'WARNING');
            
            // 检查重启冷却时间
            if ($currentTime - $lastRestartTime < $config['restart_cooldown']) {
                $remainingTime = $config['restart_cooldown'] - ($currentTime - $lastRestartTime);
                writeLog("重启冷却中，剩余 {$remainingTime} 秒", 'INFO');
                sleep($config['check_interval']);
                continue;
            }
            
            // 检查重启尝试次数
            if ($restartAttempts >= $config['max_restart_attempts']) {
                writeLog('达到最大重启尝试次数，停止自动重启', 'ERROR');
                writeLog('请手动检查服务状态并重启监控', 'ERROR');
                break;
            }
            
            $restartAttempts++;
            $lastRestartTime = $currentTime;
            
            writeLog("尝试重启服务 (第 {$restartAttempts} 次)", 'INFO');
            
            // 先停止可能存在的异常进程
            stopWebSocketService();
            sleep(2);
            
            // 启动服务
            if (startWebSocketService()) {
                writeLog('服务重启成功', 'SUCCESS');
                $restartAttempts = 0; // 重置重启计数
            } else {
                writeLog('服务重启失败', 'ERROR');
            }
        } else {
            // 服务正常运行
            if ($restartAttempts > 0) {
                writeLog('WebSocket服务运行正常', 'INFO');
                $restartAttempts = 0; // 重置重启计数
            }
        }
        
        sleep($config['check_interval']);
    }
}

// 处理信号
pcntl_signal(SIGTERM, function() {
    writeLog('收到终止信号，监控服务退出', 'INFO');
    exit(0);
});

pcntl_signal(SIGINT, function() {
    writeLog('收到中断信号，监控服务退出', 'INFO');
    exit(0);
});

// 检查命令行参数
$action = isset($argv[1]) ? $argv[1] : 'monitor';

switch ($action) {
    case 'start':
        if (startWebSocketService()) {
            writeLog('WebSocket服务启动成功', 'SUCCESS');
        } else {
            writeLog('WebSocket服务启动失败', 'ERROR');
            exit(1);
        }
        break;
        
    case 'stop':
        if (stopWebSocketService()) {
            writeLog('WebSocket服务停止成功', 'SUCCESS');
        } else {
            writeLog('WebSocket服务停止失败', 'ERROR');
            exit(1);
        }
        break;
        
    case 'restart':
        writeLog('重启WebSocket服务...', 'INFO');
        stopWebSocketService();
        sleep(2);
        if (startWebSocketService()) {
            writeLog('WebSocket服务重启成功', 'SUCCESS');
        } else {
            writeLog('WebSocket服务重启失败', 'ERROR');
            exit(1);
        }
        break;
        
    case 'status':
        if (isWebSocketRunning()) {
            writeLog('WebSocket服务正在运行', 'SUCCESS');
        } else {
            writeLog('WebSocket服务未运行', 'WARNING');
            exit(1);
        }
        break;
        
    case 'monitor':
    default:
        monitorLoop();
        break;
}
