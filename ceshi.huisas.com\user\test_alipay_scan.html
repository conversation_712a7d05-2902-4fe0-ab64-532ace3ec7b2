<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝反扫支付测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1677ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 15px;
            background-color: #1677ff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0958d9;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        .info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0958d9;
        }
        .note {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
            color: #d48806;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 支付宝反扫支付测试</h1>
        
        <div class="note">
            <strong>测试说明：</strong><br>
            1. 打开支付宝APP，点击"付钱"<br>
            2. 复制显示的付款码（18位数字）<br>
            3. 粘贴到下方输入框进行测试
        </div>

        <form id="scanPayForm">
            <div class="form-group">
                <label for="amount">收款金额 (元):</label>
                <input type="number" id="amount" name="amount" value="0.01" step="0.01" min="0.01" required>
            </div>

            <div class="form-group">
                <label for="auth_code">支付宝付款码:</label>
                <input type="text" id="auth_code" name="auth_code" placeholder="请输入18位支付宝付款码" maxlength="18" required>
                <small style="color: #666;">示例: 288888888888888888</small>
            </div>

            <div class="form-group">
                <label for="subject">商品描述:</label>
                <input type="text" id="subject" name="subject" value="测试商品" required>
            </div>

            <button type="submit">🚀 发起反扫支付</button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        document.getElementById('scanPayForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            // 显示加载状态
            resultDiv.className = 'result info';
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '⏳ 正在处理支付请求...';
            
            // 发送请求到直接调用版API
            fetch('http://ceshi.huisas.com/user/scan_pay_direct.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ 反扫支付成功！</h3>
                        <p><strong>订单号:</strong> ${data.trade_no || '未返回'}</p>
                        <p><strong>支付金额:</strong> ¥${data.money || formData.get('amount')}</p>
                        <p><strong>支付方式:</strong> ${data.pay_type}</p>
                        <p><strong>支付插件:</strong> ${data.plugin}</p>
                        <p><strong>支付通道:</strong> ${data.channel}</p>
                        <p><strong>支付时间:</strong> ${new Date().toLocaleString()}</p>
                        ${data.plugin_result ? `
                            <h4>🔍 插件返回信息:</h4>
                            <pre style="background:#f5f5f5;padding:10px;border-radius:5px;font-size:12px;">${JSON.stringify(data.plugin_result, null, 2)}</pre>
                        ` : ''}
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>❌ 反扫支付失败</h3>
                        <p><strong>错误信息:</strong> ${data.msg || '未知错误'}</p>
                        <p><strong>错误代码:</strong> ${data.code}</p>
                    `;
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ 请求失败</h3>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p>请检查网络连接或联系技术支持</p>
                `;
            });
        });

        // 付款码输入格式化
        document.getElementById('auth_code').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // 只保留数字
            if (value.length > 18) {
                value = value.substring(0, 18);
            }
            e.target.value = value;
        });
    </script>
</body>
</html>
