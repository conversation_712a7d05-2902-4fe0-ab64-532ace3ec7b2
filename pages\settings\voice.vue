<template>
  <view class="voice-settings">
    <!-- 导航栏 -->
    <view class="navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="nav-content">
        <view class="nav-left" @click="goBack">
          <text class="nav-icon">‹</text>
        </view>
        <view class="nav-title">语音播报设置</view>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 设置内容 -->
    <view class="settings-content">
      <!-- WebSocket连接状态 -->
      <view class="setting-group">
        <view class="group-title">
          <text class="title-icon">🌐</text>
          <text class="title-text">WebSocket连接状态</text>
        </view>

        <view class="status-card" :class="{ 'connected': wsConnected, 'connecting': wsConnecting }">
          <view class="status-info">
            <text class="status-icon">{{ wsConnected ? '🟢' : (wsConnecting ? '🟡' : '🔴') }}</text>
            <view class="status-details">
              <text class="status-text">{{ getStatusText() }}</text>
              <text class="status-detail">{{ getStatusDetail() }}</text>
            </view>
          </view>
          <view class="status-actions">
            <button @click="toggleConnection" class="status-btn" :class="{ 'connected': wsConnected }">
              {{ wsConnected ? '断开' : '连接' }}
            </button>
          </view>
        </view>
      </view>

      <!-- 基础设置 -->
      <view class="setting-group">
        <view class="group-title">
          <text class="title-icon">🔊</text>
          <text class="title-text">基础设置</text>
        </view>

        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-label">语音播报</text>
            <text class="setting-desc">开启后收到新订单时自动语音提醒</text>
          </view>
          <switch
            :checked="voiceEnabled"
            @change="toggleVoice"
            color="#5145F7"
          />
        </view>
        
        <view class="setting-item" v-if="voiceEnabled">
          <view class="setting-left">
            <text class="setting-label">播报音量</text>
            <text class="setting-desc">当前音量：{{ Math.round(volume * 100) }}%</text>
          </view>
          <view class="setting-right">
            <slider 
              :value="volume * 100" 
              @change="setVolume"
              min="0" 
              max="100"
              show-value
              activeColor="#5145F7"
              backgroundColor="#e5e5e5"
              block-color="#5145F7"
              block-size="20"
            />
          </view>
        </view>
      </view>
      
      <!-- 高级设置 -->
      <view class="setting-group" v-if="voiceEnabled">
        <view class="group-title">
          <text class="title-icon">⚙️</text>
          <text class="title-text">高级设置</text>
        </view>
        
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-label">播报模式</text>
            <text class="setting-desc">{{ currentTemplate.description }}</text>
          </view>
          <picker 
            :value="templateIndex" 
            :range="templateNames"
            @change="setTemplate"
          >
            <view class="picker-text">
              {{ currentTemplate.name }}
              <text class="picker-arrow">›</text>
            </view>
          </picker>
        </view>
        
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-label">大额提醒</text>
            <text class="setting-desc">1000元以上订单特殊提醒</text>
          </view>
          <switch 
            :checked="largeAmountAlert" 
            @change="toggleLargeAlert"
            color="#5145F7"
          />
        </view>
      </view>
      
      <!-- 测试功能 -->
      <view class="setting-group">
        <view class="group-title">
          <text class="title-icon">🧪</text>
          <text class="title-text">测试功能</text>
        </view>

        <view class="test-buttons">
          <button @click="testConnection" class="test-btn primary">
            🔗 测试连接
          </button>
          <button @click="testVoice" class="test-btn success" :disabled="!voiceEnabled">
            🎵 测试语音
          </button>
          <button @click="simulatePayment" class="test-btn warning">
            💰 模拟支付
          </button>
          <button @click="clearLogs" class="test-btn danger">
            🗑️ 清空日志
          </button>
        </view>
      </view>

      <!-- 今日统计 -->
      <view class="setting-group">
        <view class="group-title">
          <text class="title-icon">📊</text>
          <text class="title-text">今日统计</text>
        </view>

        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-value">{{ todayStats.count }}</text>
            <text class="stat-label">支付笔数</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">¥{{ todayStats.amount }}</text>
            <text class="stat-label">支付金额</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ totalMessages }}</text>
            <text class="stat-label">消息总数</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ paymentNotifications }}</text>
            <text class="stat-label">支付通知</text>
          </view>
        </view>
      </view>

      <!-- 连接日志 -->
      <view class="setting-group">
        <view class="group-title">
          <text class="title-icon">📝</text>
          <text class="title-text">连接日志</text>
        </view>

        <scroll-view class="log-content" scroll-y>
          <text v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
            [{{ formatTime(log.timestamp) }}] {{ log.message }}
          </text>
        </scroll-view>
      </view>
      
      <!-- 监听状态 -->
      <view class="setting-group">
        <view class="group-title">
          <text class="title-icon">📊</text>
          <text class="title-text">监听状态</text>
        </view>
        
        <view class="status-section">
          <view class="status-item">
            <text class="status-label">当前状态</text>
            <text class="status-value" :class="{ active: voiceStatus.isMonitoring }">
              {{ voiceStatus.isMonitoring ? '监听中' : '未监听' }}
            </text>
          </view>
          
          <view class="status-item" v-if="voiceStatus.currentPage">
            <text class="status-label">监听页面</text>
            <text class="status-value">{{ getPageName(voiceStatus.currentPage) }}</text>
          </view>
          
          <view class="status-item" v-if="voiceStatus.currentStrategy">
            <text class="status-label">监听频率</text>
            <text class="status-value">{{ voiceStatus.currentStrategy.interval / 1000 }}秒/次</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import voiceManager from '@/utils/voiceManager.js'
import AudioPlayer from '@/utils/audioPlayer.js'
import websocketManager from '@/utils/websocketManager.js'
import { websocketMixin } from '@/mixins/websocketMixin.js'

export default {
  mixins: [websocketMixin],

  data() {
    return {
      statusBarHeight: 20,

      // WebSocket状态
      wsConnected: false,
      wsConnecting: false,
      socketTask: null,
      heartbeatTimer: null,

      // 认证信息
      authInfo: {
        merchant_id: '1000',
        staff_id: '',
        token: 'test_token_1234567890'
      },

      // 语音设置
      voiceEnabled: true,
      voiceVolume: 1.0,
      volume: 0.8,
      largeAmountAlert: true,
      templateIndex: 0,
      templates: [],
      isTesting: false,

      // 音频播放器
      audioPlayer: null,

      // 统计数据
      todayStats: {
        count: 0,
        amount: '0.00'
      },
      totalMessages: 0,
      paymentNotifications: 0,

      // 语音状态
      voiceStatus: {
        isMonitoring: false,
        currentPage: '',
        currentStrategy: null
      },

      // 日志
      logs: []
    }
  },
  
  computed: {
    templateNames() {
      return this.templates.map(t => t.name)
    },

    currentTemplate() {
      return this.templates[this.templateIndex] || this.templates[0] || { name: '标准模式', description: '简洁明了的播报内容' }
    },
    
    previewText() {
      if (!this.currentTemplate) return ''

      // 生成预览文本
      const sampleOrder = {
        money: '88.88',
        typename: '微信支付',
        trade_no: 'TEST123456'
      }

      try {
        // 使用简单的模板替换
        return this.currentTemplate.template.replace(/\{money\}/g, sampleOrder.money)
      } catch (error) {
        return this.currentTemplate.template
      }
    },

    // WebSocket状态文本
    websocketStatusText() {
      if (this.wsConnected) {
        return '已连接，实时监听中'
      } else if (this.wsConnecting) {
        return '连接中...'
      } else {
        return '未连接'
      }
    },

    // 今日统计文本
    todayStatsText() {
      return `${this.todayStats.count}笔 / ¥${this.todayStats.amount}`
    }
  },
  
  created() {
    // 初始化音频播放器
    this.audioPlayer = new AudioPlayer()
  },

  onLoad() {
    console.log('🔊 语音设置页面加载')
    this.initSystemInfo()
    this.loadSettings()
    this.loadTemplates()
    this.setupWebSocketListeners()
    this.updateVoiceStatus()
    this.addLog('📱 语音设置页面已加载', 'info')

    // 🔧 暂时禁用WebSocket管理器，使用页面级别的统一连接
    // if (this.voiceEnabled) {
    //   this.addLog('🔊 语音已开启，启用WebSocket管理器', 'info')
    //   websocketManager.enable()
    // }
    this.addLog('🔊 使用页面级别的统一WebSocket连接', 'info')
  },

  onShow() {
    // 页面显示时更新状态
    this.updateVoiceStatus()
    this.addLog('👁️ 页面显示', 'info')

    // 更新WebSocket状态显示
    this.updateWebSocketStatusFromManager()
  },

  onHide() {
    // 页面隐藏时保持WebSocket连接
    console.log('📱 语音设置页面隐藏，WebSocket管理器继续运行')
    this.addLog('👁️ 页面隐藏，WebSocket保持后台运行', 'info')
  },

  onUnload() {
    // 页面卸载时移除事件监听器，但保持WebSocket连接
    console.log('🚪 语音设置页面卸载，移除事件监听器')
    this.removeWebSocketListeners()
    this.addLog('🚪 页面卸载，移除监听器但保持连接', 'info')
  },
  
  methods: {
    // 初始化系统信息
    initSystemInfo() {
      const systemInfo = uni.getSystemInfoSync()
      this.statusBarHeight = systemInfo.statusBarHeight || 20
    },
    
    // 加载设置
    loadSettings() {
      const voiceStatus = voiceManager.getStatus()

      this.voiceEnabled = voiceStatus.enabled
      this.volume = voiceStatus.volume
      this.voiceVolume = voiceStatus.volume
      this.largeAmountAlert = true // 保持兼容

      console.log('🔊 当前语音设置:', voiceStatus)
    },

    // 加载模板
    loadTemplates() {
      this.templates = [
        { id: 'simple', name: '简洁模式', description: '收款X元', template: '收款{money}元' },
        { id: 'detailed', name: '详细模式', description: '收到XX付款X元', template: '收到{type}付款{money}元' },
        { id: 'custom', name: '自定义模式', description: '恭喜收款X元成功', template: '恭喜收款{money}元成功' }
      ]

      // 设置当前选中的模板
      const currentTemplate = voiceManager.templateMode || 'simple'
      this.templateIndex = this.templates.findIndex(t => t.id === currentTemplate)
      if (this.templateIndex === -1) this.templateIndex = 0
    },

    // 更新语音状态
    updateVoiceStatus() {
      this.loadSettings()
      this.updateWebSocketStatus()
    },

    // 设置WebSocket事件监听器
    setupWebSocketListeners() {
      // 连接事件
      websocketManager.on('connect', () => {
        this.wsConnected = true
        this.wsConnecting = false
        this.addLog('✅ WebSocket管理器连接成功', 'success')
      })

      // 断开事件
      websocketManager.on('disconnect', () => {
        this.wsConnected = false
        this.wsConnecting = false
        this.addLog('🔌 WebSocket管理器连接断开', 'warning')
      })

      // 消息事件
      websocketManager.on('message', (data) => {
        this.handleWebSocketMessage(data)
      })

      // 错误事件
      websocketManager.on('error', (error) => {
        this.addLog(`❌ WebSocket管理器错误: ${JSON.stringify(error)}`, 'error')
      })
    },

    // 移除WebSocket事件监听器
    removeWebSocketListeners() {
      // 这里可以移除特定的监听器，但为了简化，我们保持监听器
      // websocketManager.off('connect', this.onWebSocketConnect)
      // websocketManager.off('disconnect', this.onWebSocketDisconnect)
      // websocketManager.off('message', this.onWebSocketMessage)
      // websocketManager.off('error', this.onWebSocketError)
    },

    // 从管理器更新WebSocket状态
    updateWebSocketStatusFromManager() {
      const status = websocketManager.getStatus()
      this.wsConnected = status.isConnected
      this.wsConnecting = status.isConnecting

      console.log('🌐 从WebSocket管理器更新状态:', status)
    },

    // 更新WebSocket状态
    updateWebSocketStatus() {
      this.updateWebSocketStatusFromManager()
    },

    // 获取状态文本
    getStatusText() {
      if (this.wsConnected) return 'WebSocket已连接'
      if (this.wsConnecting) return 'WebSocket连接中...'
      return 'WebSocket未连接'
    },

    // 获取状态详情
    getStatusDetail() {
      if (this.wsConnected) return '实时监听支付通知中'
      if (this.wsConnecting) return '正在建立连接...'
      return '点击连接按钮开始监听'
    },

    // 切换连接状态
    toggleConnection() {
      if (this.wsConnected) {
        websocketManager.disable()
        this.addLog('🔌 手动断开WebSocket连接', 'info')
      } else {
        websocketManager.enable()
        this.addLog('🔗 手动启用WebSocket连接', 'info')
      }
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 这些方法已被WebSocket管理器替代
    // connectWebSocket() 和 disconnectWebSocket() 已移除

    // 切换语音开关
    toggleVoice(e) {
      this.voiceEnabled = e.detail.value

      // 更新VoiceManager设置
      voiceManager.setEnabled(this.voiceEnabled)

      // 🔧 暂时禁用WebSocket管理器，使用页面级别的统一连接
      // if (this.voiceEnabled) {
      //   console.log('🔊 开启语音播报，启用WebSocket管理器')
      //   this.addLog('🔊 开启语音播报，启用WebSocket管理器', 'info')
      //   websocketManager.enable()
      // } else {
      //   console.log('🔇 关闭语音播报，禁用WebSocket管理器')
      //   this.addLog('🔇 关闭语音播报，禁用WebSocket管理器', 'info')
      //   websocketManager.disable()
      // }

      console.log(`🔊 语音播报${this.voiceEnabled ? '已开启' : '已关闭'}，使用页面级别统一连接`)
      this.addLog(`🔊 语音播报${this.voiceEnabled ? '已开启' : '已关闭'}，使用页面级别统一连接`, 'info')

      this.updateVoiceStatus()
      this.addLog(`🔊 语音播报${this.voiceEnabled ? '已开启' : '已关闭'}`, 'info')

      uni.showToast({
        title: this.voiceEnabled ? '🔊 语音播报已开启' : '🔇 语音播报已关闭',
        icon: 'none',
        duration: 2000
      })
    },
    
    // 这些方法已被WebSocket管理器替代
    // sendAuth(), subscribePaymentChannel(), sendMessage(), startHeartbeat(), handleMessage(), handleRawMessage() 已移除

    // 处理WebSocket管理器的消息
    handleWebSocketMessage(data) {
      this.addLog(`📨 WebSocket管理器消息: ${JSON.stringify(data)}`, 'info')

      // 根据消息类型处理
      switch (data.type) {
        case 'payment_notification':
          this.addLog('💰 收到支付通知消息', 'success')
          this.handlePaymentMessage(data)
          break

        case 'payment':
        case 'payment_success':
          this.addLog('💰 收到支付消息', 'success')
          this.handlePaymentMessage(data)
          break

        case 'welcome':
          this.addLog('👋 收到欢迎消息', 'success')
          break

        case 'auth_result':
          if (data.data?.success) {
            this.addLog('🔐 认证成功', 'success')
          } else {
            this.addLog(`❌ 认证失败: ${data.data?.message}`, 'error')
          }
          break

        case 'pong':
          // 心跳响应，不显示日志
          break

        case 'heartbeat':
          this.addLog('💓 收到心跳消息', 'info')
          break

        default:
          this.addLog(`📨 未知消息类型: ${data.type}`, 'warning')
      }

      this.totalMessages++
    },

    // 处理支付消息
    handlePaymentMessage(data) {
      this.addLog(`💰 处理支付消息: ${JSON.stringify(data)}`, 'info')

      // 支持多种数据结构
      let paymentData = data.data || data

      // 尝试从不同字段提取金额
      let money = paymentData.amount ||
                  paymentData.money ||
                  paymentData.extra_data?.money ||
                  paymentData.extra_data?.amount ||
                  '0.00'

      // 尝试从不同字段提取支付类型
      let type = paymentData.pay_type ||
                 paymentData.type ||
                 paymentData.typename ||
                 paymentData.extra_data?.type ||
                 paymentData.extra_data?.typename ||
                 'unknown'

      // 获取支付类型名称
      let typeName = paymentData.pay_typename ||
                     paymentData.typename ||
                     paymentData.extra_data?.typename ||
                     this.getPayTypeName(type)

      this.addLog(`💰 解析支付信息: ${money}元 (${typeName})`, 'success')

      // 更新统计
      this.paymentNotifications++
      this.todayStats.count++
      this.todayStats.amount = (parseFloat(this.todayStats.amount) + parseFloat(money)).toFixed(2)

      // 播放语音
      if (this.voiceEnabled) {
        this.playPaymentVoice({
          money: money,
          type: type,
          typename: typeName
        })
      } else {
        this.addLog('🔇 语音已关闭，跳过播放', 'info')
      }
    },

    // 获取支付类型名称
    getPayTypeName(type) {
      const typeMap = {
        'alipay': '支付宝',
        'wxpay': '微信支付',
        'qqpay': 'QQ钱包',
        'bank': '网银支付',
        'unknown': '未知支付方式'
      }
      return typeMap[type] || type || '未知支付方式'
    },

    // 设置音量
    onVolumeChange(e) {
      this.voiceVolume = e.detail.value / 100
      this.volume = this.voiceVolume
      voiceManager.setVolume(this.volume)
      this.addLog(`🔊 音量调节: ${Math.round(this.voiceVolume * 100)}%`, 'info')

      // 实时播报音量变化
      if (this.voiceEnabled) {
        this.debounceTestVolume()
      }
    },

    // 防抖测试音量
    debounceTestVolume() {
      clearTimeout(this.volumeTestTimer)
      this.volumeTestTimer = setTimeout(() => {
        this.testVoice()
      }, 500)
    },

    // 设置模板
    setTemplate(e) {
      this.templateIndex = e.detail.value
      const template = this.templates[this.templateIndex]

      if (template) {
        voiceManager.setTemplate(template.id)

        uni.showToast({
          title: `已切换到${template.name}`,
          icon: 'none',
          duration: 2000
        })
      }
    },
    
    // 切换大额提醒
    toggleLargeAlert(e) {
      this.largeAmountAlert = e.detail.value
      // 全局语音服务暂不支持大额提醒，保持兼容性

      uni.showToast({
        title: this.largeAmountAlert ? '大额提醒已开启' : '大额提醒已关闭',
        icon: 'none',
        duration: 2000
      })
    },

    // 测试连接
    testConnection() {
      this.addLog('🧪 测试WebSocket连接...', 'info')

      const status = websocketManager.getStatus()
      this.addLog(`📊 WebSocket状态: ${JSON.stringify(status)}`, 'info')

      if (status.isConnected) {
        this.addLog('✅ WebSocket已连接，发送测试消息', 'success')
        websocketManager.sendMessage({
          type: 'ping',
          timestamp: Date.now()
        })
      } else {
        this.addLog('🔗 WebSocket未连接，尝试启用', 'info')
        websocketManager.enable()
      }
    },

    // 测试语音
    testVoice() {
      this.addLog('🎵 测试语音播报...', 'info')

      if (!this.voiceEnabled) {
        this.addLog('❌ 语音已关闭', 'warning')
        return
      }

      // 先测试单个音频文件
      this.addLog('🔊 测试单个音频文件...', 'info')
      const testAudio = uni.createInnerAudioContext()
      testAudio.src = '/static/music/_shoukuan.mp3'

      testAudio.onCanplay(() => {
        this.addLog('✅ 音频文件可以播放', 'success')
        testAudio.play()
      })

      testAudio.onError((error) => {
        this.addLog(`❌ 音频文件播放失败: ${JSON.stringify(error)}`, 'error')
        testAudio.destroy()
      })

      testAudio.onEnded(() => {
        this.addLog('🎵 单个音频测试完成，开始完整语音测试', 'info')
        testAudio.destroy()

        // 测试完整语音播报
        const testData = {
          money: '88.88',
          type: 'alipay'
        }

        this.playPaymentVoice(testData)
      })
    },

    // 模拟支付通知
    simulatePayment() {
      this.addLog('🧪 模拟支付通知...', 'info')

      // 方法1: 调用后端测试接口
      uni.request({
        url: 'http://ceshi.huisas.com/test_websocket_notify.php',
        method: 'GET',
        success: (res) => {
          this.addLog('✅ 后端测试接口调用成功', 'success')
        },
        fail: (err) => {
          this.addLog(`❌ 后端测试接口调用失败: ${err.errMsg}`, 'error')
        }
      })

      // 方法2: 本地模拟支付数据
      const paymentData = {
        type: 'payment',
        money: '0.01',
        typename: '支付宝'
      }

      // 直接触发支付处理
      setTimeout(() => {
        this.addLog('💰 本地模拟支付通知', 'info')
        this.handlePaymentMessage(paymentData)
      }, 1000)
    },

    // 播放支付语音
    playPaymentVoice(data) {
      if (!this.voiceEnabled) {
        this.addLog('🔇 语音已关闭，跳过播放', 'info')
        return
      }

      this.addLog(`🎵 播放语音: ${data.money}元 (${data.type || 'alipay'})`, 'info')

      try {
        // 异步播放语音
        this.audioPlayer.playPaymentNotification(data.money, data.type || 'alipay')
          .then(() => {
            this.addLog('✅ 语音播放完成', 'success')
          })
          .catch((error) => {
            this.addLog(`❌ 语音播放失败: ${error.message || error}`, 'error')
          })

        this.addLog('🎵 语音播放已启动', 'info')
      } catch (error) {
        this.addLog(`❌ 语音播放启动失败: ${error.message}`, 'error')
      }
    },

    // 添加日志
    addLog(message, type = 'info') {
      this.logs.unshift({
        message,
        type,
        timestamp: Date.now()
      })

      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs.pop()
      }
    },

    // 清空日志
    clearLogs() {
      this.logs = []
      this.addLog('📝 日志已清空', 'info')
    },

    // 格式化时间
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString()
    },
    
    // 获取页面名称
    getPageName(pagePath) {
      const pageNames = {
        'pages/index/index': '首页',
        'pages/scan/index': '收款码',
        'pages/dynamic-code/index': '动态收款码',
        'pages/pay/mini-payment': '反扫收银台',
        'pages/bill/index': '账单记录'
      }
      return pageNames[pagePath] || '未知页面'
    },








  }
}
</script>

<style scoped>
/* 导航栏 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 100%);
}

.nav-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}

.nav-left, .nav-right {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  font-size: 48rpx;
  color: #fff;
  font-weight: 300;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

/* 设置内容 */
.settings-content {
  padding-top: 108rpx;
  padding-bottom: 40rpx;
}

.setting-group {
  margin: 32rpx 24rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.group-title {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-bottom: 2rpx solid #f0f0f0;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f8f8f8;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-left {
  flex: 1;
}

.setting-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.setting-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.setting-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
  width: 300rpx;
  margin-left: 24rpx;
}

.picker-text {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #5145F7;
  padding: 16rpx 0;
}

.picker-arrow {
  font-size: 32rpx;
  color: #ccc;
}

/* 预览区域 */
.preview-section {
  padding: 32rpx;
}

.preview-text {
  background: #f8f9ff;
  border: 2rpx solid #e5e7ff;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 26rpx;
  color: #5145F7;
  line-height: 1.5;
  margin-bottom: 24rpx;
  text-align: center;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 小测试按钮（在设置项中） */
.setting-right .test-btn {
  width: 120rpx;
  height: 60rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
}

.test-btn:disabled {
  background: #ccc;
  color: #999;
}

/* WebSocket状态指示器 */
.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 40rpx;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #ccc;
  animation: none;
}

.status-indicator.connected .status-dot {
  background: #52c41a;
  animation: pulse 2s infinite;
}

.status-indicator.connecting .status-dot {
  background: #faad14;
  animation: spin 1s linear infinite;
}

.status-indicator.disconnected .status-dot {
  background: #ff4d4f;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 状态区域 */
.status-section {
  padding: 32rpx;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f8f8f8;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 26rpx;
  color: #666;
}

.status-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.status-value.active {
  color: #52c41a;
}

/* WebSocket状态卡片 */
.status-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: white;
  border-radius: 16rpx;
  margin: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  border-left: 8rpx solid #ddd;
}

.status-card.connected {
  border-left-color: #34C759;
}

.status-card.connecting {
  border-left-color: #FF9500;
}

.status-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.status-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.status-details {
  flex: 1;
}

.status-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.status-detail {
  font-size: 24rpx;
  color: #666;
}

.status-actions {
  margin-left: 24rpx;
}

.status-btn {
  background: #007AFF;
  color: white;
  border: none;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.status-btn.connected {
  background: #FF3B30;
}

/* 测试按钮组 */
.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 32rpx;
}

.test-btn.primary { background: #007AFF; }
.test-btn.success { background: #34C759; }
.test-btn.warning { background: #FF9500; }
.test-btn.danger { background: #FF3B30; }

.test-buttons .test-btn {
  flex: 1;
  min-width: 160rpx;
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  color: white;
  font-size: 26rpx;
  font-weight: 500;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding: 32rpx;
}

.stat-item {
  background: #f8f9ff;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #5145F7;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 日志内容 */
.log-content {
  background: #000;
  border-radius: 12rpx;
  padding: 24rpx;
  height: 400rpx;
  font-family: 'Courier New', monospace;
  margin: 32rpx;
}

.log-item {
  display: block;
  font-size: 24rpx;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.log-item.success { color: #34C759; }
.log-item.error { color: #FF3B30; }
.log-item.warning { color: #FF9500; }
.log-item.info { color: #00ff00; }
</style>
