<template>
	<view class="container">
		<!-- 顶部标题栏 -->
		<view class="header">
			<view class="header-left">
				<uni-icons type="left" color="#FFFFFF" size="24" @click="goBack"></uni-icons>
				<text class="header-title">对账单</text>
			</view>
			<view class="header-right">
				<uni-icons type="calendar" color="#FFFFFF" size="24"></uni-icons>
				<uni-icons type="download" color="#FFFFFF" size="24" class="margin-left"></uni-icons>
			</view>
		</view>
		
		<!-- 月份选择 -->
		<view class="month-selector">
			<text class="month-label">本月</text>
			<view class="date-picker" @click="openDatePicker">
				<text>{{ selectedMonth }}</text>
				<uni-icons type="bottom" size="16" color="#666666"></uni-icons>
			</view>
		</view>
		
		<!-- 总收入概览 -->
		<view class="income-summary">
			<view class="summary-row">
				<view class="summary-item">
					<text class="item-label">总收入(元)</text>
					<text class="item-value">¥ {{ totalIncome }}</text>
				</view>
				<view class="summary-item">
					<text class="item-label">总笔数</text>
					<text class="item-value">{{ totalCount }}</text>
				</view>
			</view>
			<view class="summary-row">
				<view class="summary-item">
					<text class="item-label">已结算(元)</text>
					<text class="item-value income">¥ {{ settledAmount }}</text>
				</view>
				<view class="summary-item">
					<text class="item-label">待结算(元)</text>
					<text class="item-value pending">¥ {{ pendingAmount }}</text>
				</view>
			</view>
		</view>
		
		<!-- 支付渠道对账 -->
		<view class="channel-section">
			<view class="section-header">
				<text class="section-title">支付渠道对账</text>
				<view class="section-actions">
					<text class="action-text">筛选</text>
					<text class="action-text margin-left">导出</text>
				</view>
			</view>
			
			<!-- 微信支付 -->
			<view class="payment-channel">
				<view class="channel-left">
					<view class="channel-icon wechat">
						<image src="/static/home/<USER>" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
					</view>
					<view class="channel-info">
						<text class="channel-name">微信支付</text>
						<text class="channel-count">{{ wechatInfo.count }}笔交易</text>
					</view>
				</view>
				<view class="channel-right">
					<text class="channel-amount">¥ {{ wechatInfo.amount }}</text>
				</view>
			</view>
			<view class="settlement-info wechat">
				<text>已结算：¥ {{ wechatInfo.settled }}</text>
				<text>待结算：¥ {{ wechatInfo.pending }}</text>
			</view>
			
			<!-- 支付宝 -->
			<view class="payment-channel">
				<view class="channel-left">
					<view class="channel-icon alipay">
						<image src="/static/home/<USER>" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
					</view>
					<view class="channel-info">
						<text class="channel-name">支付宝</text>
						<text class="channel-count">{{ alipayInfo.count }}笔交易</text>
					</view>
				</view>
				<view class="channel-right">
					<text class="channel-amount">¥ {{ alipayInfo.amount }}</text>
				</view>
			</view>
			<view class="settlement-info alipay">
				<text>已结算：¥ {{ alipayInfo.settled }}</text>
				<text>待结算：¥ {{ alipayInfo.pending }}</text>
			</view>
			
			<!-- 云闪付 -->
			<view class="payment-channel">
				<view class="channel-left">
					<view class="channel-icon cloudpay">
						<image src="/static/home/<USER>" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
					</view>
					<view class="channel-info">
						<text class="channel-name">云闪付</text>
						<text class="channel-count">{{ cloudpayInfo.count }}笔交易</text>
					</view>
				</view>
				<view class="channel-right">
					<text class="channel-amount">¥ {{ cloudpayInfo.amount }}</text>
				</view>
			</view>
			<view class="settlement-info cloudpay">
				<text>已结算：¥ {{ cloudpayInfo.settled }}</text>
				<text>待结算：¥ {{ cloudpayInfo.pending }}</text>
			</view>
		</view>
		
		<!-- 日对账单 -->
		<view class="daily-section">
			<text class="daily-title">日对账单</text>
			
			<view v-for="(item, index) in dailyList" :key="index" class="daily-item">
				<view class="daily-left">
					<view class="daily-date">
						<text class="date-month">{{ item.month }}</text>
						<text class="date-day">{{ item.day }}</text>
					</view>
					<view class="daily-info">
						<text class="daily-weekday">{{ item.weekday }}</text>
						<text class="daily-count">{{ item.count }}笔交易</text>
					</view>
				</view>
				<view class="daily-right">
					<text class="daily-amount">¥ {{ item.amount }}</text>
					<text class="daily-status" :class="{settled: item.status === '已结算', pending: item.status === '待结算'}">{{ item.status }}</text>
				</view>
			</view>
			
			<view class="view-all">
				<text>查看全部</text>
			</view>
		</view>
		
		<!-- 日期选择器弹窗 -->
		<uni-datetime-picker 
			ref="datePicker"
			type="date" 
			:value="dateValue" 
			format="yyyy-MM"
			:start="startDate"
			:end="endDate"
			return-type="string"
			@change="onDateChange" />
	</view>
</template>

<script>
export default {
	data() {
		return {
			dateValue: '',
			selectedMonth: '2025年4月',
			startDate: '2024-01',
			endDate: '2025-12',
			totalIncome: '42,58.90',
			totalCount: 152,
			settledAmount: '38,62.00',
			pendingAmount: '4,06.90',
			
			// 支付渠道信息
			wechatInfo: {
				count: 48,
				amount: '26,392.72',
				settled: '24,280.00',
				pending: '2,112.72'
			},
			alipayInfo: {
				count: 36,
				amount: '12,583.40',
				settled: '11,382.00',
				pending: '1,201.40'
			},
			cloudpayInfo: {
				count: 23,
				amount: '3,592.78',
				settled: '2,600.00',
				pending: '992.78'
			},
			
			// 日对账单列表
			dailyList: [
				{
					month: '4月',
					day: '14',
					weekday: '今天',
					count: 23,
					amount: '2,586.50',
					status: '待结算'
				},
				{
					month: '4月',
					day: '13',
					weekday: '昨天',
					count: 32,
					amount: '3,280.20',
					status: '已结算'
				},
				{
					month: '4月',
					day: '12',
					weekday: '周五',
					count: 28,
					amount: '2,835.60',
					status: '已结算'
				},
				{
					month: '4月',
					day: '11',
					weekday: '周四',
					count: 26,
					amount: '2,745.80',
					status: '已结算'
				}
			]
		}
	},
	onLoad() {
		// 设置当前日期为默认值
		const now = new Date();
		this.dateValue = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		openDatePicker() {
			// 打开日期选择器
			this.$refs.datePicker.show();
		},
		onDateChange(e) {
			// 日期选择器值变化
			if (e) {
				const date = new Date(e);
				const year = date.getFullYear();
				const month = date.getMonth() + 1;
				this.selectedMonth = `${year}年${month}月`;
				this.loadData(year, month);
			}
		},
		loadData(year, month) {
			// 这里可以根据选择的年月加载对应的数据
			console.log(`加载${year}年${month}月的数据`);
			// 模拟数据加载，实际项目中应该从服务器获取
			// 在此只是演示，数据不变
		}
	}
}
</script>

<style>
page {
	font-family: 'Segoe UI', sans-serif;
	background-color: #f5f5f5;
}

.container {
	width: 100%;
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 顶部标题栏 */
.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #5145F7;
	color: white;
	padding: 20rpx 32rpx;
	box-sizing: border-box;
}

.header-left {
	display: flex;
	align-items: center;
}

.header-title {
	font-size: 34rpx;
	font-weight: 500;
	margin-left: 16rpx;
}

.header-right {
	display: flex;
	align-items: center;
}

.margin-left {
	margin-left: 32rpx;
}

/* 月份选择器 */
.month-selector {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 32rpx;
	background-color: #FFFFFF;
	border-bottom: 1px solid #EEEEEE;
}

.month-label {
	font-size: 28rpx;
	color: #333333;
}

.date-picker {
	display: flex;
	align-items: center;
	font-size: 28rpx;
	color: #333333;
}

/* 总收入概览 */
.income-summary {
	padding: 24rpx 32rpx;
	background-color: #FFFFFF;
	margin-bottom: 20rpx;
}

.summary-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.summary-row:last-child {
	margin-bottom: 0;
}

.summary-item {
	width: 48%;
}

.item-label {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 12rpx;
	display: block;
}

.item-value {
	font-size: 34rpx;
	color: #333333;
	font-weight: 600;
}

.item-value.income {
	color: #333333;
}

.item-value.pending {
	color: #333333;
}

/* 支付渠道对账 */
.channel-section {
	background-color: #FFFFFF;
	margin-bottom: 20rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 32rpx;
	border-bottom: 1px solid #EEEEEE;
}

.section-title {
	font-size: 30rpx;
	color: #333333;
	font-weight: 500;
}

.section-actions {
	display: flex;
	align-items: center;
}

.action-text {
	font-size: 26rpx;
	color: #5145F7;
}

.payment-channel {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 32rpx;
}

.channel-left {
	display: flex;
	align-items: center;
}

.channel-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.channel-icon.wechat {
	background-color: #E4FFEF;
}

.channel-icon.alipay {
	background-color: #E9EFFF;
}

.channel-icon.cloudpay {
	background-color: #FFE4E4;
}

.channel-info {
	display: flex;
	flex-direction: column;
}

.channel-name {
	font-size: 30rpx;
	color: #333333;
	margin-bottom: 8rpx;
}

.channel-count {
	font-size: 24rpx;
	color: #999999;
}

.channel-amount {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
}

.settlement-info {
	display: flex;
	justify-content: space-between;
	padding: 0 32rpx 24rpx 130rpx;
	font-size: 24rpx;
	color: #999999;
	border-bottom: 1px solid #EEEEEE;
}

.settlement-info:last-child {
	border-bottom: none;
}

/* 日对账单 */
.daily-section {
	background-color: #FFFFFF;
}

.daily-title {
	font-size: 30rpx;
	color: #333333;
	font-weight: 500;
	padding: 24rpx 32rpx;
	border-bottom: 1px solid #EEEEEE;
	display: block;
}

.daily-item {
	display: flex;
	justify-content: space-between;
	padding: 24rpx 32rpx;
	border-bottom: 1px solid #EEEEEE;
}

.daily-left {
	display: flex;
	align-items: center;
}

.daily-date {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 24rpx;
}

.date-month {
	font-size: 26rpx;
	color: #666666;
}

.date-day {
	font-size: 36rpx;
	color: #5145F7;
	font-weight: bold;
}

.daily-info {
	display: flex;
	flex-direction: column;
}

.daily-weekday {
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 8rpx;
}

.daily-count {
	font-size: 24rpx;
	color: #999999;
}

.daily-right {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.daily-amount {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.daily-status {
	font-size: 24rpx;
}

.daily-status.settled {
	color: #4CD964;
}

.daily-status.pending {
	color: #999999;
}

.view-all {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 24rpx 0;
	font-size: 28rpx;
	color: #5145F7;
	border-top: 1px solid #EEEEEE;
}
</style> 