# WebSocket统一频道语音播报方案

## 🎯 **方案概述**

采用**统一频道 + 商户ID过滤**的架构，简化WebSocket管理，提高系统效率。

### 📋 **核心设计**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   商户A前端     │    │                 │    │   商户B前端     │
│  (ID: 1001)    │◄──►│  payment_channel │◄──►│  (ID: 1002)    │
│                 │    │   (统一频道)     │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                        ▲                        │
        │                        │                        │
        ▼                        │                        ▼
   自动过滤1001的消息        后端推送消息           自动过滤1002的消息
                              (包含merchant_id)
```

## 🔧 **技术实现**

### 1. **后端WebSocket通知** (`websocket_notify.php`)

```php
// 统一频道方案
function notifyPaymentSuccess($order_info) {
    $channel = 'payment_channel';  // 所有商户共用一个频道
    $event = 'payment_success';
    
    $data = [
        'type' => 'payment_success',
        'merchant_id' => $order_info['uid'],  // 关键：商户ID
        'trade_no' => $order_info['trade_no'],
        'amount' => $order_info['money'],
        'pay_type' => $order_info['type'],
        'timestamp' => time()
    ];
    
    return sendPaymentNotification($channel, $event, $data);
}
```

### 2. **前端消息过滤** (`paymentWebSocket.js`)

```javascript
// 消息处理 - 添加商户ID过滤
handleMessage(message) {
    // 检查是否是当前商户的消息
    if (message.merchant_id && !this.isCurrentMerchant(message.merchant_id)) {
        console.log('🔒 消息不属于当前商户，已过滤:', message.merchant_id)
        return
    }
    
    // 处理消息...
}

// 商户ID检查
isCurrentMerchant(merchantId) {
    const currentMerchantId = uni.getStorageSync('merchant_id') || 
                             uni.getStorageSync('uid')
    return String(currentMerchantId) === String(merchantId)
}
```

### 3. **WebSocket连接** (前端)

```javascript
// 连接配置 - Swoole WebSocket标准协议
const wsUrl = `ws://ceshi.huisas.com:8080`

// 订阅统一频道
const subscribeMessage = {
    type: 'subscribe',
    data: {
        channel: 'payment_channel'  // 所有商户订阅同一个频道
    }
}
```

## 🎵 **语音播报流程**

### 1. **支付完成触发**
```
支付成功 → processOrder() → notifyPaymentSuccess() → WebSocket推送
```

### 2. **消息格式**
```json
{
    "type": "payment_success",
    "merchant_id": "1001",
    "trade_no": "TEST_123456",
    "amount": "88.88",
    "pay_type": "alipay",
    "timestamp": 1702540800
}
```

### 3. **前端处理**
```
收到消息 → 检查merchant_id → 播放语音 → 显示通知
```

## 🧪 **测试方案**

### 1. **后端服务测试**
```bash
# 访问后端诊断页面
http://ceshi.huisas.com/test_websocket_backend_simple.php
```

### 2. **前端连接测试**
```bash
# 访问前端连接测试
http://ceshi.huisas.com/test_websocket_frontend_simple.html
```

### 3. **支付通知测试**
```bash
# 访问支付通知测试（支持多商户）
http://ceshi.huisas.com/test_payment_notification_simple.php
```

### 4. **UniApp语音测试**
```bash
# 在UniApp中访问
pages/test/websocket-voice-simple
```

## ✅ **方案优势**

### 1. **简化管理**
- ✅ 只需要一个 `payment_channel` 频道
- ✅ 不需要为每个商户创建单独频道
- ✅ 减少服务器资源消耗

### 2. **前端简单**
- ✅ 所有客户端订阅同一个频道
- ✅ 自动过滤不相关消息
- ✅ 代码逻辑清晰

### 3. **易于扩展**
- ✅ 添加新商户无需修改频道配置
- ✅ 支持多种消息类型
- ✅ 便于添加新功能

### 4. **安全性**
- ✅ 前端只处理自己商户的消息
- ✅ 商户数据自然隔离
- ✅ 无需复杂的权限控制

## 🔍 **测试检查清单**

### 后端检查
- [ ] WebSocket服务端口8080和8081正常监听
- [ ] `websocket_notify.php` 使用统一频道
- [ ] 消息包含正确的 `merchant_id`

### 前端检查
- [ ] WebSocket连接URL格式正确
- [ ] 成功订阅 `payment_channel` 频道
- [ ] 商户ID过滤逻辑正常工作
- [ ] 语音播报功能正常

### 多商户测试
- [ ] 不同商户ID的消息正确过滤
- [ ] 只播放当前商户的支付语音
- [ ] 多个商户同时在线互不干扰

## 🚀 **部署步骤**

### 1. **启动WebSocket服务**
```bash
cd /path/to/payment_websocket_v2/service
php start.php start -d
```

### 2. **检查服务状态**
```bash
php start.php status
netstat -tlnp | grep :8080
netstat -tlnp | grep :8081
```

### 3. **测试连接**
```bash
# 1. 后端服务诊断
curl http://ceshi.huisas.com/test_websocket_backend_simple.php

# 2. 前端连接测试
打开: http://ceshi.huisas.com/test_websocket_frontend_simple.html

# 3. 支付通知测试
打开: http://ceshi.huisas.com/test_payment_notification_simple.php
```

### 4. **UniApp测试**
```bash
# 在UniApp中测试
pages/test/websocket-voice-simple
```

## 📝 **注意事项**

1. **商户ID存储** - 前端需要正确存储当前商户ID
2. **消息格式** - 后端推送的消息必须包含 `merchant_id`
3. **错误处理** - 连接失败时的重连机制
4. **性能优化** - 大量消息时的过滤效率

## 🎯 **下一步优化**

1. **消息缓存** - 离线时的消息缓存机制
2. **连接池** - 多个WebSocket连接的管理
3. **监控告警** - WebSocket服务状态监控
4. **负载均衡** - 多个WebSocket服务器的负载均衡

---

**📅 更新时间**: 2024-12-17
**🔧 版本**: v2.0 - 统一频道方案
