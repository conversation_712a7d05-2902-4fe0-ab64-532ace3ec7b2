<?php
/**
 * 员工管理API接口
 * 文件位置: epay_release_99009/user/staff.php
 */

// 设置CORS头部
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

include("../includes/common.php");

// 检查登录状态和获取商户ID
if($islogin2 != 1) {
    // 如果没有登录，尝试从POST参数获取uid（用于员工管理）
    $posted_uid = intval($_POST['uid'] ?? 0);
    if($posted_uid > 0) {
        // 使用前端传递的商户ID
        $uid = $posted_uid;
        $islogin2 = 1; // 临时设置登录状态用于员工管理
    } else {
        // 测试版本：强制设置登录状态和用户ID
        $islogin2 = 1;
        $uid = 99009; // 使用你的测试商户ID
    }
}

$act = isset($_GET['act']) ? $_GET['act'] : '';

switch($act) {
    
    // 获取员工列表 - 借鉴staff_test.php的成功实现
    case 'getStaffList':
        try {
            $sql = "SELECT * FROM pre_staff WHERE uid = :uid AND status = 1 ORDER BY id ASC";
            $staffList = $DB->getAll($sql, [':uid' => $uid]);

            $result = [];
            foreach($staffList as $staff) {
                $result[] = [
                    'id' => intval($staff['id']),
                    'name' => $staff['name'],
                    'role' => $staff['role'],
                    'color' => $staff['avatar_color'],
                    'phone' => $staff['phone'],
                    'email' => $staff['email'],
                    'addtime' => $staff['addtime']
                ];
            }

            exit(json_encode(['code' => 0, 'data' => $result, 'debug' => ['uid' => $uid, 'count' => count($staffList)]]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '获取员工列表失败: ' . $e->getMessage()]));
        }
        break;
    
    // 添加员工 - 借鉴staff_test.php的成功实现
    case 'addStaff':
        try {
            // 调试信息：记录接收到的数据
            $debug_info = [
                'uid' => $uid,
                'post_data' => $_POST,
                'islogin2' => $islogin2
            ];

            $name = trim($_POST['name']);
            $role = trim($_POST['role']);
            $phone = trim($_POST['phone']);
            $email = trim($_POST['email']);
            $avatar_color = trim($_POST['avatar_color']) ?: 'blue';

            if(empty($name)) {
                exit(json_encode(['code' => -1, 'msg' => '员工姓名不能为空', 'debug' => $debug_info]));
            }

            // 检查同名员工 - 使用固定表名 pre_staff
            $exists = $DB->getRow("SELECT id FROM pre_staff WHERE uid = :uid AND name = :name",
                [':uid' => $uid, ':name' => $name]);
            if($exists) {
                exit(json_encode(['code' => -1, 'msg' => '员工姓名已存在']));
            }

            $data = [
                'uid' => $uid,
                'name' => $name,
                'role' => $role ?: '收银员',
                'phone' => $phone,
                'email' => $email,
                'avatar_color' => $avatar_color,
                'status' => 1,
                'addtime' => date('Y-m-d H:i:s')
            ];

            $staff_id = $DB->insert('staff', $data);
            if($staff_id) {
                // 记录操作日志（可选，如果表不存在会跳过）
                try {
                    $DB->insert('staff_log', [
                        'uid' => $uid,
                        'staff_id' => $staff_id,
                        'action' => 'add_staff',
                        'content' => json_encode(['name' => $name, 'role' => $role]),
                        'ip' => $_SERVER['REMOTE_ADDR'],
                        'addtime' => date('Y-m-d H:i:s')
                    ]);
                } catch (Exception $logError) {
                    // 日志记录失败不影响主流程
                }

                exit(json_encode(['code' => 0, 'msg' => '添加员工成功', 'staff_id' => $staff_id, 'debug' => array_merge($debug_info, ['insert_data' => $data])]));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '添加员工失败，数据库插入失败', 'debug' => array_merge($debug_info, ['data' => $data, 'error' => $DB->error()])]));
            }
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '添加员工失败: ' . $e->getMessage()]));
        }
        break;
    
    // 编辑员工
    case 'editStaff':
        try {
            $staff_id = intval($_POST['staff_id']);
            $name = trim($_POST['name']);
            $role = trim($_POST['role']);
            $account = trim($_POST['account']);
            $password = trim($_POST['password']);
            $phone = trim($_POST['phone']);
            $email = trim($_POST['email']);
            $avatar_color = trim($_POST['avatar_color']);

            if(empty($name)) {
                exit(json_encode(['code' => -1, 'msg' => '员工姓名不能为空']));
            }

            if(empty($account)) {
                exit(json_encode(['code' => -1, 'msg' => '登录账号不能为空']));
            }

            if(empty($password)) {
                exit(json_encode(['code' => -1, 'msg' => '登录密码不能为空']));
            }
            
            // 检查员工是否存在且属于当前商户
            $staff = $DB->getRow("SELECT * FROM pre_staff WHERE id = :id AND uid = :uid",
                [':id' => $staff_id, ':uid' => $uid]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 检查同名员工（排除自己）
            $exists = $DB->getRow("SELECT id FROM pre_staff WHERE uid = :uid AND name = :name AND id != :id",
                [':uid' => $uid, ':name' => $name, ':id' => $staff_id]);
            if($exists) {
                exit(json_encode(['code' => -1, 'msg' => '员工姓名已存在']));
            }

            // 检查账号是否已存在（排除自己）
            $accountExists = $DB->getRow("SELECT id FROM pre_staff WHERE uid = :uid AND account = :account AND id != :id",
                [':uid' => $uid, ':account' => $account, ':id' => $staff_id]);
            if($accountExists) {
                exit(json_encode(['code' => -1, 'msg' => '登录账号已存在']));
            }
            
            $data = [
                'name' => $name,
                'role' => $role ?: '收银员',
                'account' => $account,
                'password' => md5($password), // 密码加密存储
                'phone' => $phone,
                'email' => $email,
                'avatar_color' => $avatar_color ?: 'blue',
                'updatetime' => date('Y-m-d H:i:s')
            ];
            
            $result = $DB->update('staff', $data, ['id' => $staff_id, 'uid' => $uid]);
            if($result !== false) {
                // 记录操作日志
                try {
                    $DB->insert('staff_log', [
                        'uid' => $uid,
                        'staff_id' => $staff_id,
                        'action' => 'edit_staff',
                        'content' => json_encode(['old' => $staff, 'new' => $data]),
                        'ip' => $_SERVER['REMOTE_ADDR'],
                        'addtime' => date('Y-m-d H:i:s')
                    ]);
                } catch (Exception $logError) {
                    // 日志记录失败不影响主流程
                }
                
                exit(json_encode(['code' => 0, 'msg' => '编辑员工成功']));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '编辑员工失败']));
            }
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '编辑员工失败: ' . $e->getMessage()]));
        }
        break;
    
    // 删除员工
    case 'deleteStaff':
        try {
            $staff_id = intval($_POST['staff_id']);
            
            // 检查员工是否存在且属于当前商户
            $staff = $DB->getRow("SELECT * FROM pre_staff WHERE id = :id AND uid = :uid",
                [':id' => $staff_id, ':uid' => $uid]);
            if(!$staff) {
                exit(json_encode(['code' => -1, 'msg' => '员工不存在']));
            }

            // 检查是否有绑定的收款码
            $qrcode_count = $DB->getColumn("SELECT COUNT(*) FROM pre_qrcode_config WHERE staff_id = :staff_id",
                [':staff_id' => $staff_id]);
            if($qrcode_count > 0) {
                exit(json_encode(['code' => -1, 'msg' => '该员工已绑定收款码，无法删除']));
            }

            // 软删除（设置状态为0）
            $result = $DB->update('staff', ['status' => 0, 'updatetime' => date('Y-m-d H:i:s')],
                ['id' => $staff_id, 'uid' => $uid]);

            if($result !== false) {
                // 记录操作日志
                try {
                    $DB->insert('staff_log', [
                        'uid' => $uid,
                        'staff_id' => $staff_id,
                        'action' => 'delete_staff',
                        'content' => json_encode(['staff' => $staff]),
                        'ip' => $_SERVER['REMOTE_ADDR'],
                        'addtime' => date('Y-m-d H:i:s')
                    ]);
                } catch (Exception $logError) {
                    // 日志记录失败不影响主流程
                }
                
                exit(json_encode(['code' => 0, 'msg' => '删除员工成功']));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '删除员工失败']));
            }
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '删除员工失败: ' . $e->getMessage()]));
        }
        break;
    
    // 保存收款码配置（包含员工绑定）
    case 'saveQRConfig':
        try {
            $staff_id = intval($_POST['staff_id']);
            $name = trim($_POST['name']);
            $qr_style = trim($_POST['qr_style']) ?: 'native';
            $amount = floatval($_POST['amount']);
            $description = trim($_POST['description']);
            
            if(empty($name)) {
                exit(json_encode(['code' => -1, 'msg' => '收款码名称不能为空']));
            }
            
            // 如果指定了员工，检查员工是否存在
            if($staff_id > 0) {
                $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND uid = :uid AND status = 1",
                    [':id' => $staff_id, ':uid' => $uid]);
                if(!$staff) {
                    exit(json_encode(['code' => -1, 'msg' => '指定的员工不存在']));
                }
            }
            
            // 生成收款码URL
            $qr_url = "http://ceshi.huisas.com/user/pay.php?uid={$uid}";
            if($amount > 0) {
                $qr_url .= "&amount={$amount}";
            }
            if($staff_id > 0) {
                $qr_url .= "&staff_id={$staff_id}";
            }
            
            $data = [
                'uid' => $uid,
                'staff_id' => $staff_id > 0 ? $staff_id : null,
                'name' => $name,
                'qr_style' => $qr_style,
                'amount' => $amount > 0 ? $amount : null,
                'description' => $description,
                'qr_url' => $qr_url,
                'status' => 1,
                'addtime' => date('Y-m-d H:i:s')
            ];
            
            $config_id = $DB->insert($dbconfig['dbqz'].'_qrcode_config', $data);
            if($config_id) {
                exit(json_encode(['code' => 0, 'msg' => '保存成功', 'config_id' => $config_id, 'qr_url' => $qr_url]));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '保存失败']));
            }
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '保存失败: ' . $e->getMessage()]));
        }
        break;

    // 获取收款码配置列表
    case 'getQRConfigList':
        try {
            $sql = "SELECT qc.*, s.name as staff_name, s.role as staff_role
                    FROM {$dbconfig['dbqz']}_qrcode_config qc
                    LEFT JOIN {$dbconfig['dbqz']}_staff s ON qc.staff_id = s.id
                    WHERE qc.uid = :uid AND qc.status = 1
                    ORDER BY qc.id DESC";
            $configList = $DB->getAll($sql, [':uid' => $uid]);

            $result = [];
            foreach($configList as $config) {
                $result[] = [
                    'id' => intval($config['id']),
                    'name' => $config['name'],
                    'staff_id' => intval($config['staff_id']),
                    'staff_name' => $config['staff_name'],
                    'staff_role' => $config['staff_role'],
                    'qr_style' => $config['qr_style'],
                    'amount' => floatval($config['amount']),
                    'description' => $config['description'],
                    'qr_url' => $config['qr_url'],
                    'addtime' => $config['addtime']
                ];
            }

            exit(json_encode(['code' => 0, 'data' => $result]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '获取配置列表失败: ' . $e->getMessage()]));
        }
        break;

    // 员工登录
    case 'staffLogin':
        try {
            $account = trim($_POST['account']);
            $password = trim($_POST['password']);
            $uid = intval($_POST['uid']); // 商户ID

            if(empty($account)) {
                exit(json_encode(['code' => -1, 'msg' => '请输入登录账号']));
            }

            if(empty($password)) {
                exit(json_encode(['code' => -1, 'msg' => '请输入登录密码']));
            }

            if(empty($uid)) {
                exit(json_encode(['code' => -1, 'msg' => '商户ID不能为空']));
            }

            // 验证员工登录
            $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE uid = :uid AND account = :account AND password = :password AND status = 1",
                [':uid' => $uid, ':account' => $account, ':password' => md5($password)]);

            if($staff) {
                // 记录登录日志
                $DB->insert($dbconfig['dbqz'].'_staff_log', [
                    'uid' => $uid,
                    'staff_id' => $staff['id'],
                    'action' => 'login',
                    'content' => json_encode(['account' => $account]),
                    'ip' => $_SERVER['REMOTE_ADDR'],
                    'addtime' => date('Y-m-d H:i:s')
                ]);

                // 返回员工信息（不包含密码）
                $result = [
                    'id' => intval($staff['id']),
                    'name' => $staff['name'],
                    'role' => $staff['role'],
                    'account' => $staff['account'],
                    'avatar_color' => $staff['avatar_color'],
                    'phone' => $staff['phone'],
                    'email' => $staff['email']
                ];

                exit(json_encode(['code' => 0, 'msg' => '登录成功', 'data' => $result]));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '账号或密码错误']));
            }
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '登录失败: ' . $e->getMessage()]));
        }
        break;

    // 生成收款码URL
    case 'generateQRUrl':
        try {
            $staff_id = intval($_POST['staff_id']);

            // 如果指定了员工，检查员工是否存在
            if($staff_id > 0) {
                $staff = $DB->getRow("SELECT * FROM {$dbconfig['dbqz']}_staff WHERE id = :id AND uid = :uid AND status = 1",
                    [':id' => $staff_id, ':uid' => $uid]);
                if(!$staff) {
                    exit(json_encode(['code' => -1, 'msg' => '指定的员工不存在']));
                }
            }

            // 生成加密的商户ID
            $merchant = authcode($uid, 'ENCODE', SYS_KEY);

            // 生成收款码URL
            $qr_url = "http://ceshi.huisas.com/paypage/?merchant=" . urlencode($merchant);
            if($staff_id > 0) {
                $qr_url .= "&staff_id=" . $staff_id;
            }

            exit(json_encode(['code' => 0, 'data' => ['qr_url' => $qr_url]]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '生成收款码URL失败: ' . $e->getMessage()]));
        }
        break;

    default:
        exit(json_encode(['code' => -1, 'msg' => '无效的操作']));
        break;
}
?>
