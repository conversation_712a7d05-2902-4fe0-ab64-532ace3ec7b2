<?php
/**
 * 员工管理API接口 - 测试版本（绕过登录检查）
 * 文件位置: epay_release_99009/user/staff_test.php
 */

// 设置CORS头部
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

include("../includes/common.php");

// 测试版本：强制设置登录状态和用户ID
$islogin2 = 1;
$uid = 99009; // 使用你的测试商户ID

$act = isset($_GET['act']) ? $_GET['act'] : '';

switch($act) {
    
    // 获取员工列表
    case 'getStaffList':
        try {
            $sql = "SELECT * FROM pre_staff WHERE uid = :uid AND status = 1 ORDER BY id ASC";
            $staffList = $DB->getAll($sql, [':uid' => $uid]);
            
            $result = [];
            foreach($staffList as $staff) {
                $result[] = [
                    'id' => intval($staff['id']),
                    'name' => $staff['name'],
                    'role' => $staff['role'],
                    'color' => $staff['avatar_color'],
                    'phone' => $staff['phone'],
                    'email' => $staff['email'],
                    'addtime' => $staff['addtime']
                ];
            }
            
            exit(json_encode(['code' => 0, 'data' => $result, 'debug' => ['uid' => $uid, 'count' => count($staffList)]]));
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '获取员工列表失败: ' . $e->getMessage()]));
        }
        break;
    
    // 添加员工
    case 'addStaff':
        try {
            $name = trim($_POST['name']);
            $role = trim($_POST['role']);
            $phone = trim($_POST['phone']);
            $email = trim($_POST['email']);
            $avatar_color = trim($_POST['avatar_color']) ?: 'blue';

            if(empty($name)) {
                exit(json_encode(['code' => -1, 'msg' => '员工姓名不能为空']));
            }

            // 检查同名员工
            $exists = $DB->getRow("SELECT id FROM pre_staff WHERE uid = :uid AND name = :name",
                [':uid' => $uid, ':name' => $name]);
            if($exists) {
                exit(json_encode(['code' => -1, 'msg' => '员工姓名已存在']));
            }

            $data = [
                'uid' => $uid,
                'name' => $name,
                'role' => $role ?: '收银员',
                'phone' => $phone,
                'email' => $email,
                'avatar_color' => $avatar_color,
                'status' => 1,
                'addtime' => date('Y-m-d H:i:s')
            ];
            
            $staff_id = $DB->insert('staff', $data);
            if($staff_id) {
                // 记录操作日志（可选，如果表不存在会跳过）
                try {
                    $DB->insert('staff_log', [
                        'uid' => $uid,
                        'staff_id' => $staff_id,
                        'action' => 'add_staff',
                        'content' => json_encode(['name' => $name, 'role' => $role]),
                        'ip' => $_SERVER['REMOTE_ADDR'],
                        'addtime' => date('Y-m-d H:i:s')
                    ]);
                } catch (Exception $logError) {
                    // 日志记录失败不影响主流程
                }

                exit(json_encode(['code' => 0, 'msg' => '添加员工成功', 'staff_id' => $staff_id, 'debug' => $data]));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '添加员工失败，数据库插入失败', 'debug' => ['data' => $data, 'error' => $DB->errorInfo()]]));
            }
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '添加员工失败: ' . $e->getMessage()]));
        }
        break;

    default:
        exit(json_encode(['code' => -1, 'msg' => '无效的操作']));
        break;
}
?>
