<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swoole WebSocket 监控测试</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fa fa-wifi"></i> Swoole WebSocket 服务测试</h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- 服务状态检查 -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>服务状态检查</h5>
                                    </div>
                                    <div class="card-body">
                                        <button class="btn btn-primary" onclick="checkHealth()">
                                            <i class="fa fa-heartbeat"></i> 健康检查
                                        </button>
                                        <button class="btn btn-info" onclick="getStats()">
                                            <i class="fa fa-bar-chart"></i> 获取统计
                                        </button>
                                        <button class="btn btn-success" onclick="testPaymentAPI()">
                                            <i class="fa fa-credit-card"></i> 测试支付API
                                        </button>
                                        <div id="api-results" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>WebSocket连接测试</h5>
                                    </div>
                                    <div class="card-body">
                                        <button class="btn btn-primary" onclick="testWebSocket()">
                                            <i class="fa fa-plug"></i> 测试WebSocket连接
                                        </button>
                                        <button class="btn btn-warning" onclick="disconnectWebSocket()">
                                            <i class="fa fa-unlink"></i> 断开连接
                                        </button>
                                        <div id="websocket-results" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 实时日志 -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>实时测试日志</h5>
                                        <button class="btn btn-sm btn-secondary float-right" onclick="clearLogs()">
                                            <i class="fa fa-trash"></i> 清空日志
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div id="test-logs" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; font-family: monospace; font-size: 12px;">
                                            <div class="text-muted">等待测试...</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let logContainer = document.getElementById('test-logs');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'info': '#17a2b8',
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107'
            };
            
            const logEntry = `<div style="color: ${colors[type]}; margin-bottom: 5px;">
                [${timestamp}] ${message}
            </div>`;
            
            logContainer.innerHTML += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLogs() {
            logContainer.innerHTML = '<div class="text-muted">日志已清空...</div>';
        }

        // 健康检查
        function checkHealth() {
            log('🔍 开始健康检查...', 'info');
            
            fetch('http://ceshi.huisas.com:8080/health')
                .then(response => response.json())
                .then(data => {
                    log('✅ 健康检查成功: ' + JSON.stringify(data), 'success');
                    document.getElementById('api-results').innerHTML = 
                        '<div class="alert alert-success">健康检查通过</div>';
                })
                .catch(error => {
                    log('❌ 健康检查失败: ' + error.message, 'error');
                    document.getElementById('api-results').innerHTML = 
                        '<div class="alert alert-danger">健康检查失败: ' + error.message + '</div>';
                });
        }

        // 获取统计信息
        function getStats() {
            log('📊 获取统计信息...', 'info');
            
            fetch('http://ceshi.huisas.com:8080/stats')
                .then(response => response.json())
                .then(data => {
                    log('✅ 统计信息获取成功', 'success');
                    document.getElementById('api-results').innerHTML = 
                        '<div class="alert alert-info"><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                })
                .catch(error => {
                    log('❌ 统计信息获取失败: ' + error.message, 'error');
                    document.getElementById('api-results').innerHTML = 
                        '<div class="alert alert-danger">统计信息获取失败: ' + error.message + '</div>';
                });
        }

        // 测试支付API
        function testPaymentAPI() {
            log('💳 测试支付通知API...', 'info');
            
            const testData = {
                merchant_id: 'test_merchant',
                order_id: 'TEST_' + Date.now(),
                amount: '88.88',
                status: 'success'
            };

            fetch('http://ceshi.huisas.com:8080/payment/notify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            })
                .then(response => response.json())
                .then(data => {
                    log('✅ 支付API测试成功: ' + JSON.stringify(data), 'success');
                    document.getElementById('api-results').innerHTML = 
                        '<div class="alert alert-success">支付API测试成功</div>';
                })
                .catch(error => {
                    log('❌ 支付API测试失败: ' + error.message, 'error');
                    document.getElementById('api-results').innerHTML = 
                        '<div class="alert alert-danger">支付API测试失败: ' + error.message + '</div>';
                });
        }

        // 测试WebSocket连接
        function testWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('⚠️ WebSocket已连接，请先断开', 'warning');
                return;
            }

            log('🔌 开始WebSocket连接测试...', 'info');
            
            try {
                ws = new WebSocket('ws://ceshi.huisas.com:8080');
                
                ws.onopen = function() {
                    log('✅ WebSocket连接成功', 'success');
                    document.getElementById('websocket-results').innerHTML = 
                        '<div class="alert alert-success">WebSocket连接成功</div>';
                    
                    // 发送认证消息
                    const authMessage = {
                        type: 'auth',
                        data: {
                            merchant_id: 'test_merchant',
                            token: 'test_token_' + Date.now()
                        }
                    };
                    ws.send(JSON.stringify(authMessage));
                    log('📤 发送认证消息', 'info');
                };

                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        log('📥 收到消息: ' + data.type + ' - ' + JSON.stringify(data.data), 'success');
                    } catch (e) {
                        log('📥 收到原始消息: ' + event.data, 'info');
                    }
                };

                ws.onerror = function(error) {
                    log('❌ WebSocket错误: ' + error, 'error');
                    document.getElementById('websocket-results').innerHTML = 
                        '<div class="alert alert-danger">WebSocket连接错误</div>';
                };

                ws.onclose = function(event) {
                    log('🔌 WebSocket连接已关闭 (代码: ' + event.code + ')', 'warning');
                    document.getElementById('websocket-results').innerHTML = 
                        '<div class="alert alert-warning">WebSocket连接已关闭</div>';
                };

            } catch (error) {
                log('❌ WebSocket连接异常: ' + error.message, 'error');
                document.getElementById('websocket-results').innerHTML = 
                    '<div class="alert alert-danger">WebSocket连接异常: ' + error.message + '</div>';
            }
        }

        // 断开WebSocket连接
        function disconnectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.close();
                log('🔌 主动断开WebSocket连接', 'info');
            } else {
                log('⚠️ WebSocket未连接', 'warning');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Swoole WebSocket 监控测试页面已加载', 'info');
            log('📍 WebSocket地址: ws://ceshi.huisas.com:8080', 'info');
            log('📍 HTTP API地址: http://ceshi.huisas.com:8080', 'info');
        });
    </script>
</body>
</html>
