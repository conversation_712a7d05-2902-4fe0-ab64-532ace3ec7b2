<template>
	<view class="container">
		<!-- 顶部标题栏 -->
		<view class="header">
			<view class="back-button" @click="goBack">
				<image src="/static/back.png" mode="aspectFit" class="icon-back"></image>
			</view>
			<text class="header-title">选择日期范围</text>
			<view class="header-right"></view>
		</view>
		
		<!-- 日期选择区域 -->
		<view class="date-selection">
			<view class="date-range-display">
				<view class="date-item">
					<text class="date-label">开始日期</text>
					<view class="date-value" @click="openStartDatePicker">
						<text>{{ startDateFormatted || '请选择' }}</text>
						<image src="/static/date.png" mode="aspectFit" class="icon-calendar"></image>
					</view>
				</view>
				
				<view class="date-separator">
					<text>至</text>
				</view>
				
				<view class="date-item">
					<text class="date-label">结束日期</text>
					<view class="date-value" @click="openEndDatePicker">
						<text>{{ endDateFormatted || '请选择' }}</text>
						<image src="/static/date.png" mode="aspectFit" class="icon-calendar"></image>
					</view>
				</view>
			</view>
			
			<!-- 快捷日期选择 -->
			<view class="quick-dates">
				<text class="quick-dates-title">快捷选择</text>
				<view class="quick-date-buttons">
					<view class="quick-date-btn" @click="selectLastWeek">
						<text>最近一周</text>
					</view>
					<view class="quick-date-btn" @click="selectLastMonth">
						<text>最近一月</text>
					</view>
					<view class="quick-date-btn" @click="selectLastThreeMonths">
						<text>最近三月</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 确认按钮 -->
		<view class="confirm-button" :class="{ 'confirm-active': isDateRangeValid }" @click="confirmDateRange">
			<text>确认</text>
		</view>
		
		<!-- 日期选择弹窗 -->
		<view class="date-picker-popup" v-if="showDatePicker">
			<view class="date-picker-mask" @click="cancelDatePicker"></view>
			<view class="date-picker-container">
				<view class="date-picker-header">
					<view class="date-picker-action" @click="cancelDatePicker">取消</view>
					<view class="date-picker-title">{{ currentPickerType === 'start' ? '选择开始日期' : '选择结束日期' }}</view>
					<view class="date-picker-action confirm" @click="confirmDatePicker">确定</view>
				</view>
				<picker-view 
					class="date-picker-view" 
					:value="datePickerValue" 
					@change="onDatePickerChange"
				>
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in years" :key="'year-'+index">{{ item }}年</view>
					</picker-view-column>
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in months" :key="'month-'+index">{{ item }}月</view>
					</picker-view-column>
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in days" :key="'day-'+index">{{ item }}日</view>
					</picker-view-column>
				</picker-view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			const currentDate = new Date();
			const currentYear = currentDate.getFullYear();
			const currentMonth = currentDate.getMonth() + 1;
			const currentDay = currentDate.getDate();
			
			return {
				startDate: '',
				endDate: '',
				showDatePicker: false,
				currentPickerType: 'start', // 'start' or 'end'
				datePickerValue: [5, currentMonth-1, currentDay-1], // 默认选中当前日期
				years: Array.from({length: 10}, (_, i) => currentYear - 5 + i), // 前后5年
				months: Array.from({length: 12}, (_, i) => i + 1),
				days: Array.from({length: 31}, (_, i) => i + 1),
				tempSelectedDate: '',
				isSubmitting: false
			}
		},
		computed: {
			startDateFormatted() {
				return this.formatDisplayDate(this.startDate);
			},
			endDateFormatted() {
				return this.formatDisplayDate(this.endDate);
			},
			isDateRangeValid() {
				return this.startDate && this.endDate;
			}
		},
		methods: {
			goBack() {
				uni.navigateBack();
			},
			
			openStartDatePicker() {
				this.currentPickerType = 'start';
				this.initDatePicker(this.startDate);
				this.showDatePicker = true;
			},
			
			openEndDatePicker() {
				this.currentPickerType = 'end';
				this.initDatePicker(this.endDate);
				this.showDatePicker = true;
			},
			
			initDatePicker(dateStr) {
				let date;
				if (dateStr) {
					date = new Date(dateStr);
				} else {
					date = new Date();
				}
				
				const yearIndex = this.years.findIndex(y => y === date.getFullYear());
				const monthIndex = date.getMonth(); // 0-11
				const dayIndex = date.getDate() - 1; // 0-30
				
				this.datePickerValue = [
					yearIndex >= 0 ? yearIndex : 5,
					monthIndex,
					dayIndex
				];
				
				this.updateTempSelectedDate();
			},
			
			onDatePickerChange(e) {
				this.datePickerValue = e.detail.value;
				this.updateTempSelectedDate();
			},
			
			updateTempSelectedDate() {
				const year = this.years[this.datePickerValue[0]];
				const month = this.months[this.datePickerValue[1]];
				const day = this.days[this.datePickerValue[2]];
				
				// 检查日期是否有效（例如：避免2月30日）
				const daysInMonth = new Date(year, month, 0).getDate();
				const validDay = Math.min(day, daysInMonth);
				
				// 如果日期无效，更新日期选择器的值
				if (day !== validDay) {
					this.datePickerValue[2] = validDay - 1;
				}
				
				// 格式化为YYYY-MM-DD
				this.tempSelectedDate = `${year}-${month.toString().padStart(2, '0')}-${validDay.toString().padStart(2, '0')}`;
			},
			
			cancelDatePicker() {
				this.showDatePicker = false;
				this.tempSelectedDate = '';
			},
			
			confirmDatePicker() {
				if (this.currentPickerType === 'start') {
					this.startDate = this.tempSelectedDate;
					
					// 如果结束日期早于开始日期，则清空结束日期
					if (this.endDate && this.endDate < this.startDate) {
						this.endDate = '';
					}
				} else {
					this.endDate = this.tempSelectedDate;
					
					// 如果开始日期晚于结束日期，则更新开始日期为结束日期
					if (this.startDate && this.startDate > this.endDate) {
						this.startDate = this.endDate;
					}
				}
				
				this.showDatePicker = false;
				this.tempSelectedDate = '';
			},
			
			formatDisplayDate(dateStr) {
				if (!dateStr) return '';
				
				const date = new Date(dateStr);
				const year = date.getFullYear();
				const month = date.getMonth() + 1;
				const day = date.getDate();
				
				return `${year}年${month}月${day}日`;
			},
			
			selectLastWeek() {
				const now = new Date();
				const endDate = this.formatDateToString(now);
				
				const weekAgo = new Date();
				weekAgo.setDate(weekAgo.getDate() - 7);
				const startDate = this.formatDateToString(weekAgo);
				
				this.startDate = startDate;
				this.endDate = endDate;
			},
			
			selectLastMonth() {
				const now = new Date();
				const endDate = this.formatDateToString(now);
				
				const monthAgo = new Date();
				monthAgo.setMonth(monthAgo.getMonth() - 1);
				const startDate = this.formatDateToString(monthAgo);
				
				this.startDate = startDate;
				this.endDate = endDate;
			},
			
			selectLastThreeMonths() {
				const now = new Date();
				const endDate = this.formatDateToString(now);
				
				const threeMonthsAgo = new Date();
				threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
				const startDate = this.formatDateToString(threeMonthsAgo);
				
				this.startDate = startDate;
				this.endDate = endDate;
			},
			
			formatDateToString(date) {
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			getCurrentDate() {
				return this.formatDateToString(new Date());
			},
			
			confirmDateRange() {
				if (!this.isDateRangeValid || this.isSubmitting) {
					return;
				}
				
				this.isSubmitting = true;
				
				// 返回上一页并传递日期范围
				uni.redirectTo({
					url: `/pages/bill/index?startDate=${this.startDate}&endDate=${this.endDate}`,
					complete: () => {
						this.isSubmitting = false;
					}
				});
			}
		}
	}
</script>

<style>
	page {
		background-color: #f5f5f5;
		font-family: 'Segoe UI', sans-serif;
	}
	
	.container {
		width: 100%;
		min-height: 100vh;
	}
	
	/* 顶部标题栏 */
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #5145F7;
		color: white;
		padding: 32rpx;
		position: relative;
	}
	
	.back-button {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.icon-back {
		width: 36rpx;
		height: 36rpx;
	}
	
	.header-title {
		font-size: 36rpx;
		font-weight: 500;
		text-align: center;
		flex: 1;
	}
	
	.header-right {
		width: 60rpx;
	}
	
	/* 日期选择区域 */
	.date-selection {
		margin: 30rpx;
		background-color: white;
		border-radius: 16rpx;
		padding: 30rpx;
	}
	
	.date-range-display {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 40rpx;
	}
	
	.date-item {
		flex: 1;
		display: flex;
		flex-direction: column;
	}
	
	.date-label {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 16rpx;
	}
	
	.date-value {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1px solid #eaeaea;
	}
	
	.date-value text {
		font-size: 30rpx;
		color: #333;
	}
	
	.icon-calendar {
		width: 32rpx;
		height: 32rpx;
	}
	
	.date-separator {
		margin: 0 20rpx;
		padding-top: 40rpx;
		color: #666;
	}
	
	/* 快捷日期选择 */
	.quick-dates {
		margin-top: 40rpx;
	}
	
	.quick-dates-title {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 20rpx;
	}
	
	.quick-date-buttons {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}
	
	.quick-date-btn {
		flex: 0 0 calc(33% - 20rpx);
		height: 80rpx;
		background-color: #f5f5f5;
		border-radius: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.quick-date-btn text {
		font-size: 28rpx;
		color: #333;
	}
	
	/* 确认按钮 */
	.confirm-button {
		margin: 80rpx 30rpx;
		height: 100rpx;
		background-color: #dddddd;
		border-radius: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.confirm-button text {
		font-size: 32rpx;
		color: #999;
	}
	
	.confirm-active {
		background-color: #2b5bdd;
	}
	
	.confirm-active text {
		color: white;
	}
	
	/* 日期选择器弹窗 */
	.date-picker-popup {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 999;
	}
	
	.date-picker-mask {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
	}
	
	.date-picker-container {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: white;
		border-radius: 24rpx 24rpx 0 0;
		overflow: hidden;
	}
	
	.date-picker-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 30rpx;
		border-bottom: 1px solid #eaeaea;
	}
	
	.date-picker-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}
	
	.date-picker-action {
		font-size: 30rpx;
		color: #666;
	}
	
	.date-picker-action.confirm {
		color: #2b5bdd;
	}
	
	.date-picker-view {
		width: 100%;
		height: 480rpx;
	}
	
	.picker-item {
		line-height: 80rpx;
		text-align: center;
		font-size: 32rpx;
		color: #333;
	}
</style> 