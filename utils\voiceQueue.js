/**
 * 语音播报任务队列管理器
 * 防止多个语音同时播放，确保按顺序播报
 */
class VoiceQueue {
    constructor() {
        this.queue = [];           // 任务队列
        this.isProcessing = false; // 是否正在处理任务
        this.maxConcurrent = 1;    // 最大并发数（语音播报应该是1）
        this.currentTasks = 0;     // 当前正在执行的任务数
    }

    /**
     * 添加语音播报任务到队列
     * @param {Function} task 异步任务函数
     * @param {Object} options 任务选项
     * @returns {Promise} 任务执行结果
     */
    add(task, options = {}) {
        return new Promise((resolve, reject) => {
            const taskWrapper = {
                id: this.generateTaskId(),
                task: task,
                resolve: resolve,
                reject: reject,
                priority: options.priority || 0,
                timeout: options.timeout || 30000,
                createdAt: Date.now(),
                ...options
            };

            // 根据优先级插入队列
            this.insertByPriority(taskWrapper);
            
            console.log(`语音任务已加入队列 [${taskWrapper.id}], 队列长度: ${this.queue.length}`);
            
            // 开始处理队列
            this.processQueue();
        });
    }

    /**
     * 根据优先级插入任务
     * @param {Object} taskWrapper 任务包装对象
     */
    insertByPriority(taskWrapper) {
        let inserted = false;
        
        for (let i = 0; i < this.queue.length; i++) {
            if (taskWrapper.priority > this.queue[i].priority) {
                this.queue.splice(i, 0, taskWrapper);
                inserted = true;
                break;
            }
        }
        
        if (!inserted) {
            this.queue.push(taskWrapper);
        }
    }

    /**
     * 处理队列中的任务
     */
    async processQueue() {
        if (this.isProcessing || this.currentTasks >= this.maxConcurrent) {
            return;
        }

        if (this.queue.length === 0) {
            return;
        }

        this.isProcessing = true;
        
        while (this.queue.length > 0 && this.currentTasks < this.maxConcurrent) {
            const taskWrapper = this.queue.shift();
            this.executeTask(taskWrapper);
        }
        
        this.isProcessing = false;
    }

    /**
     * 执行单个任务
     * @param {Object} taskWrapper 任务包装对象
     */
    async executeTask(taskWrapper) {
        this.currentTasks++;
        
        console.log(`开始执行语音任务 [${taskWrapper.id}]`);
        
        try {
            // 设置超时
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error(`任务超时 [${taskWrapper.id}]`));
                }, taskWrapper.timeout);
            });

            // 执行任务
            const result = await Promise.race([
                taskWrapper.task(),
                timeoutPromise
            ]);

            console.log(`语音任务执行成功 [${taskWrapper.id}]`);
            taskWrapper.resolve(result);
            
        } catch (error) {
            console.error(`语音任务执行失败 [${taskWrapper.id}]:`, error);
            taskWrapper.reject(error);
        } finally {
            this.currentTasks--;
            
            // 继续处理队列中的下一个任务
            setTimeout(() => {
                this.processQueue();
            }, 100);
        }
    }

    /**
     * 清空队列
     */
    clear() {
        console.log(`清空语音队列，取消 ${this.queue.length} 个待执行任务`);
        
        // 拒绝所有待执行的任务
        this.queue.forEach(taskWrapper => {
            taskWrapper.reject(new Error('队列已清空'));
        });
        
        this.queue = [];
    }

    /**
     * 获取队列状态
     */
    getStatus() {
        return {
            queueLength: this.queue.length,
            isProcessing: this.isProcessing,
            currentTasks: this.currentTasks,
            maxConcurrent: this.maxConcurrent
        };
    }

    /**
     * 生成任务ID
     */
    generateTaskId() {
        return `voice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 设置最大并发数
     * @param {number} max 最大并发数
     */
    setMaxConcurrent(max) {
        this.maxConcurrent = Math.max(1, max);
        console.log(`设置最大并发数: ${this.maxConcurrent}`);
    }

    /**
     * 移除指定任务
     * @param {string} taskId 任务ID
     */
    removeTask(taskId) {
        const index = this.queue.findIndex(task => task.id === taskId);
        if (index !== -1) {
            const taskWrapper = this.queue.splice(index, 1)[0];
            taskWrapper.reject(new Error('任务已被移除'));
            console.log(`移除语音任务 [${taskId}]`);
            return true;
        }
        return false;
    }

    /**
     * 获取队列中的任务列表
     */
    getQueueTasks() {
        return this.queue.map(task => ({
            id: task.id,
            priority: task.priority,
            createdAt: task.createdAt,
            description: task.description || '语音播报任务'
        }));
    }
}

// 创建单例实例
const voiceQueue = new VoiceQueue();

export default voiceQueue;
