<!-- components/custom-navbar.vue -->
<template>
  <view class="custom-navbar">
    <uni-nav-bar
      :title="title"
      :left-text="showBack ? '返回' : ''"
      :right-text="rightText"
      :left-icon="showBack ? 'left' : ''"
      :right-icon="rightIcon"
      :color="color"
      :background-color="backgroundColor"
      :status-bar="true"
      :fixed="true"
      :shadow="shadow"
      @clickLeft="onClickLeft"
      @clickRight="onClickRight"
      @clickTitle="onClickTitle"
    >
      <template v-if="$slots.default" #default>
        <slot></slot>
      </template>
      <template v-if="$slots.left" #left>
        <slot name="left"></slot>
      </template>
      <template v-if="$slots.right" #right>
        <slot name="right"></slot>
      </template>
    </uni-nav-bar>
  </view>
</template>

<script>
export default {
  name: 'CustomNavbar',
  props: {
    title: {
      type: String,
      default: ''
    },
    showBack: {
      type: Boolean,
      default: true
    },
    rightText: {
      type: String,
      default: ''
    },
    rightIcon: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: '#ffffff'
    },
    backgroundColor: {
      type: String,
      default: '#5145F7'
    },
    shadow: {
      type: Boolean,
      default: false
    },
    // 可以在这里添加更多的props来自定义导航栏
  },
  methods: {
    onClickLeft() {
      if (this.showBack) {
        // 判断是否有上一页
        const pages = getCurrentPages();
        if (pages.length > 1) {
          uni.navigateBack();
        } else {
          // 如果没有上一页，可以自定义行为
          this.$emit('clickLeft');
        }
      } else {
        this.$emit('clickLeft');
      }
    },
    onClickRight() {
      this.$emit('clickRight');
    },
    onClickTitle() {
      this.$emit('clickTitle');
    }
  }
}
</script>

<style>
.custom-navbar {
  width: 100%;
}

/* 让标题区域填充更多空间 */
.uni-navbar__header-container {
  padding: 0 !important;
  justify-content: center !important;
}

/* 自定义标题区样式 */
.uni-navbar__header-container-inner {
  justify-content: center !important;
  flex: 1 !important;
}
</style> 