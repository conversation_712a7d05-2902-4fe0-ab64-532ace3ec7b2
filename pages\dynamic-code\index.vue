<template>
  <view class="cashier-container">
    <!-- 主页面内容 -->
    <view class="main-content">
      <view class="header-section">
        <text class="main-title">收银台系统</text>
        <text class="main-subtitle">专为移动收银设备优化的动态收款码</text>
      </view>
      
      <button class="start-cashier-btn" @click="openCashier">
        <text class="btn-icon">💰</text>
        <text class="btn-text">开始收款</text>
      </button>
      
      <view class="tips-section">
        <view class="hint-title">💡 操作提示</view>
        <text class="tip-line">• 按 Enter 键快速开始收款</text>
        <text class="tip-line">• 上下键切换支付方式</text>
        <text class="tip-line">• 回车键生成二维码</text>
        <text class="tip-line">• ESC键关闭弹窗</text>
      </view>
    </view>
    
    <!-- 收银弹窗 -->
    <view class="cashier-modal" v-if="showCashier" @click="handleOverlayClick">
      <!-- 收银输入界面 -->
      <view class="cashier-container-inner" v-if="!qrCodeData" @click.stop>
        <view class="cashier-header">
          <text class="cashier-title">动态收款码</text>
          <view class="close-btn" @click="closeCashier">
            <text class="close-icon">✕</text>
          </view>
        </view>
        
        <view class="cashier-content">
          <!-- 商品名称 -->
          <view class="product-section">
            <text class="section-label">商品名称</text>
            <input
              class="product-input"
              v-model="orderForm.name"
              placeholder="在线支付"
              maxlength="50"
              @focus="handleInputFocus"
            />
          </view>
          
          <!-- 金额输入 -->
          <view class="amount-section">
            <text class="section-label">付款金额</text>
            <view class="amount-display">
              <!-- #ifdef H5 -->
              <input
                class="amount-input"
                v-model="orderForm.money"
                placeholder="0.00"
                type="number"
                step="0.01"
                @focus="handleInputFocus"
                @blur="handleInputBlur"
                @input="handleAmountInput"
                ref="amountInput"
                id="amountInputH5"
                :focus="shouldFocusAmount"
                autocomplete="off"
                autocorrect="off"
                autocapitalize="off"
                spellcheck="false"
              />
              <!-- #endif -->
              <!-- #ifndef H5 -->
              <input
                class="amount-input"
                v-model="orderForm.money"
                placeholder="0.00"
                type="digit"
                @focus="handleInputFocus"
                @blur="handleInputBlur"
                @input="handleAmountInput"
                ref="amountInput"
                :focus="shouldFocusAmount"
                cursor-spacing="10"
                adjust-position
                :auto-focus="shouldFocusAmount"
              />
              <!-- #endif -->
              <text class="amount-unit">元</text>
            </view>
          </view>
          
          <!-- 支付方式选择 -->
          <view class="payment-section">
            <text class="section-label">选择支付方式</text>
            <view class="payment-options">
              <view
                class="payment-option"
                v-for="(type, index) in paymentTypes"
                :key="index"
                @click="selectPaymentType(index)"
                :class="{
                  active: selectedIndex === index,
                  loading: isGenerating && selectedIndex === index
                }"
              >
                <text class="payment-name">{{ type.showname }}</text>
                <view class="loading-indicator" v-if="isGenerating && selectedIndex === index">
                  <text class="loading-text">生成中...</text>
                </view>
              </view>
            </view>
            
            <view class="keyboard-hint">
              <text class="hint-title">键盘操作</text>
              <text class="hint-desc">↑↓ 切换支付方式 | Enter 生成二维码 | Esc 关闭</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 二维码显示界面 -->
      <view class="qrcode-container" v-if="qrCodeData" @click.stop>
        <view class="cashier-header">
          <text class="qrcode-title">{{ qrCodeData.name || '在线支付' }}</text>
          <view class="close-btn" @click="closeCashier">
            <text class="close-icon">✕</text>
          </view>
        </view>
        
        <view class="qrcode-content">
          <!-- 金额显示 -->
          <view class="amount-display-large">
            <text class="amount-symbol">¥</text>
            <text class="amount-value">{{ qrCodeData.money || orderForm.money }}</text>
          </view>
          
          <!-- 二维码 -->
          <view class="qrcode-wrapper">
            <image
              v-if="qrCodeData.qrcode_image && !qrCodeLoading"
              :src="qrCodeData.qrcode_image"
              class="qrcode-image"
              mode="aspectFit"
              @error="handleQRCodeError"
              @load="handleQRCodeLoad"
              :style="{ opacity: imageLoaded ? 1 : 0.3 }"
            />
            <view v-else-if="!qrCodeData.qrcode_url && !qrCodeLoading" class="qrcode-placeholder">
              <text class="placeholder-icon">📱</text>
              <text class="placeholder-text">等待生成</text>
            </view>

            <!-- 优化的加载状态 -->
            <view class="qrcode-loading" v-if="qrCodeLoading">
              <view class="loading-spinner"></view>
              <text class="loading-text">{{ loadingText }}</text>
              <view class="loading-progress">
                <view class="progress-bar" :style="{ width: loadingProgress + '%' }"></view>
              </view>
            </view>

            <!-- 错误状态 -->
            <view class="qrcode-error" v-if="qrCodeError">
              <text class="error-icon">⚠️</text>
              <text class="error-text">{{ qrCodeError }}</text>
              <button class="retry-btn" @click="retryGenerate">重试</button>
            </view>
          </view>
          
          <!-- 支付提示 -->
          <text class="payment-tip">请使用{{ selectedType?.showname || paymentTypes[selectedIndex]?.showname }}扫码支付</text>

          <!-- #ifdef H5 -->
          <!-- 焦点状态提示 -->
          <view v-if="!isInputFocused" class="focus-hint">
            <text class="focus-hint-text">💡 按 Enter 键返回输入金额</text>
          </view>
          <!-- #endif -->
          
          <!-- 操作按钮 -->
          <view class="action-buttons">
            <button class="action-btn secondary" @click="regenerateCode">
              重新生成
            </button>
            <button class="action-btn primary" @click="refreshCode">
              刷新二维码
            </button>
          </view>
          
          <!-- 过期提示 -->
          <text class="expire-tip">二维码有效期：30分钟</text>
        </view>
      </view>
    </view>


  </view>
</template>

<script>
import { checkLoginStatus } from '@/utils/auth.js'
import config from '@/config/index.js'
import { request } from '@/utils/request.js'
import { websocketMixin } from '@/mixins/websocketMixin.js'

export default {
  // 🔧 添加WebSocket Mixin，确保动态码页面也能播报
  mixins: [websocketMixin],
  components: {
  },
  data() {
    return {
      // 收银台状态
      showCashier: false,
      shouldFocusAmount: false,

      // 订单表单
      orderForm: {
        name: '在线支付',
        money: ''
      },
      
      // 支付方式
      paymentTypes: [
        { id: 1, name: 'alipay', showname: '支付宝' },
        { id: 2, name: 'wxpay', showname: '微信支付' },
        { id: 3, name: 'qqpay', showname: 'QQ钱包' }
      ],
      selectedType: null,
      selectedIndex: 0, // 当前选中的支付方式索引
      
      // 二维码数据
      qrCodeData: null,
      qrCodeLoading: false,
      isGenerating: false,
      imageLoaded: false,
      qrCodeError: null,
      loadingText: '正在生成二维码...',
      loadingProgress: 0,
      loadingTimer: null
    }
  },

  onLoad() {
    this.checkAuth()
    this.addKeyboardListeners()
  },

  onUnload() {
    this.removeKeyboardListeners()
  },

  onShow() {
    console.log('💰 动态收款码页面显示');
    // 页面显示时重新添加键盘监听
    this.addKeyboardListeners();
  },

  onHide() {
    console.log('💰 动态收款码页面隐藏');
    // 页面隐藏时移除键盘监听
    this.removeKeyboardListeners();
  },

  onUnload() {
    console.log('💰 动态收款码页面卸载');
    this.removeKeyboardListeners();
  },

  methods: {
    // 检查登录状态
    async checkAuth() {
      const isLoggedIn = await checkLoginStatus()
      if (!isLoggedIn) {
        uni.reLaunch({
          url: '/pages/login/index'
        })
        return
      }
    },

    // 打开收银台
    openCashier() {
      this.showCashier = true
      this.resetForm()

      // 设置焦点标志
      this.shouldFocusAmount = true

      // 延迟设置焦点
      this.$nextTick(() => {
        setTimeout(() => {
          this.setAmountInputFocus()
        }, 200)
      })
    },

    // 通过键盘打开收银台并聚焦
    openCashierWithFocus() {
      this.showCashier = true
      this.resetForm()

      // 延迟聚焦到金额输入框
      this.$nextTick(() => {
        // 延迟确保DOM完全渲染
        setTimeout(() => {
          this.setAmountInputFocus()
        }, 300)
      })
    },

    // 设置金额输入框焦点
    setAmountInputFocus() {
      console.log('开始设置焦点...')

      // 统一使用uni-app的focus属性方式
      // 先重置焦点状态，然后重新设置
      this.shouldFocusAmount = false
      this.$nextTick(() => {
        this.shouldFocusAmount = true
        console.log('✅ 通过focus属性设置焦点')
      })
    },

    // 关闭收银台
    closeCashier() {
      this.showCashier = false
      this.shouldFocusAmount = false  // 关闭时重置焦点标志
      this.resetForm()
    },

    // 处理遮罩层点击
    handleOverlayClick(event) {
      // 只有点击遮罩层本身时才关闭弹窗
      if (event.target === event.currentTarget) {
        this.closeCashier()
      }
    },

    // 重置表单
    resetForm() {
      this.qrCodeData = null
      this.selectedType = null
      this.orderForm.money = ''
      this.orderForm.name = '在线支付'
      this.selectedIndex = 0
      this.isGenerating = false
      this.qrCodeLoading = false
      this.imageLoaded = false
      this.qrCodeError = null
      this.loadingProgress = 0
      this.clearLoadingTimer()
      // 不重置焦点标志，让用户可以继续输入
      // this.shouldFocusAmount = false
    },

    // 添加键盘事件监听
    addKeyboardListeners() {
      // #ifdef H5
      document.addEventListener('keydown', this.handleKeyDown)
      // #endif
    },

    // 移除键盘事件监听
    removeKeyboardListeners() {
      // #ifdef H5
      document.removeEventListener('keydown', this.handleKeyDown)
      // #endif
    },

    // 处理键盘事件
    handleKeyDown(event) {
      // 如果收银台未显示，只处理Enter键打开收银台
      if (!this.showCashier) {
        if (event.key === 'Enter') {
          event.preventDefault()
          this.openCashierWithFocus()
        }
        return
      }

      // 如果已显示二维码，不处理键盘事件
      if (this.qrCodeData) return

      switch(event.key) {
        case 'ArrowUp':
          event.preventDefault()
          this.selectPreviousPayment()
          break
        case 'ArrowDown':
          event.preventDefault()
          this.selectNextPayment()
          break
        case 'Enter':
          event.preventDefault()
          this.generateCurrentPayment()
          break
        case 'Escape':
          event.preventDefault()
          this.closeCashier()
          break
      }
    },

    // 选择上一个支付方式
    selectPreviousPayment() {
      this.selectedIndex = this.selectedIndex > 0 ? this.selectedIndex - 1 : this.paymentTypes.length - 1
    },

    // 选择下一个支付方式
    selectNextPayment() {
      this.selectedIndex = this.selectedIndex < this.paymentTypes.length - 1 ? this.selectedIndex + 1 : 0
    },

    // 选择支付方式
    selectPaymentType(index) {
      this.selectedIndex = index
      // 自动生成二维码
      this.generateCurrentPayment()
    },

    // 生成当前选中的支付方式二维码
    generateCurrentPayment() {
      const paymentType = this.paymentTypes[this.selectedIndex]
      this.generateQRCode(paymentType)
    },



    // 处理输入焦点
    handleInputFocus(event) {
      console.log('输入框获得焦点:', event)
      this.isInputFocused = true
    },

    // 处理输入失焦
    handleInputBlur(event) {
      console.log('输入框失去焦点:', event)
      this.isInputFocused = false
    },

    // 处理金额输入
    handleAmountInput(event) {
      // 限制只能输入数字和小数点
      let value = event.detail.value
      value = value.replace(/[^\d.]/g, '')

      // 限制小数点后两位
      const parts = value.split('.')
      if (parts.length > 2) {
        value = parts[0] + '.' + parts[1]
      }
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2)
      }

      this.orderForm.money = value
    },

    // 重新生成（返回输入界面）
    regenerateCode() {
      this.qrCodeData = null
      this.selectedType = null
      this.qrCodeLoading = false
      this.isGenerating = false
    },

    // 生成二维码 - 优化版本
    async generateQRCode(paymentType) {
      if (!this.orderForm.name.trim()) {
        uni.showToast({
          title: '请填写商品名称',
          icon: 'none'
        })
        return
      }

      if (!this.orderForm.money || parseFloat(this.orderForm.money) <= 0) {
        uni.showToast({
          title: '请输入正确的金额',
          icon: 'none'
        })
        return
      }

      try {
        this.isGenerating = true
        this.qrCodeLoading = true
        this.selectedType = paymentType
        this.qrCodeError = null
        this.imageLoaded = false

        // 启动进度动画
        this.startLoadingProgress()

        // 优化：使用Promise.race实现超时控制
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 8000) // 8秒超时
        })

        const requestPromise = request({
          url: '/user/ajax_dtm.php?act=testpay2',
          method: 'POST',
          data: {
            money: this.orderForm.money,
            typeid: paymentType.id,
            name: this.orderForm.name
          }
        })

        const response = await Promise.race([requestPromise, timeoutPromise])

        if (response && response.code === 0) {
          this.loadingText = '正在获取支付链接...'

          // testpay2 返回的是 submit2.php 的URL，我们直接使用这个URL作为支付链接
          if (response.url) {
            // 构建完整的支付URL - 使用配置文件中的服务器地址
            const submitUrl = config.serverUrl + '/' + response.url.replace('../', '')
            const tradeNo = response.url.match(/trade_no=(\d+)/)?.[1] || ''

            // 生成二维码图片
            const qrCodeImageUrl = await this.generateQRCodeImage(submitUrl)

            this.qrCodeData = {
              qrcode_url: submitUrl,
              qrcode_image: qrCodeImageUrl,
              payment_url: submitUrl,
              trade_no: tradeNo,
              type: 'submit_redirect',
              name: this.orderForm.name,
              amount: this.orderForm.amount,
              expire_time: Date.now() + 1800000 // 30分钟过期
            }

            this.loadingText = '二维码生成成功，正在加载...'

            uni.showToast({
              title: '二维码生成成功',
              icon: 'success',
              duration: 1500
            })

            // ⚡ 二维码生成成功后立即检查新订单
            this.triggerImmediateCheck()
          } else {
            // 降级到备用方案
            this.loadingText = '正在尝试备用方案...'
            await this.generateFallbackQRCode(paymentType)
          }
        } else {
          // 降级到备用方案
          this.loadingText = '正在尝试备用方案...'
          await this.generateFallbackQRCode(paymentType)
        }
      } catch (error) {
        console.error('❌ 生成二维码失败:', error)

        // 自动降级到备用方案
        this.loadingText = '正在尝试备用方案...'
        await this.generateFallbackQRCode(paymentType)
      } finally {
        this.isGenerating = false
        this.qrCodeLoading = false
        this.clearLoadingTimer()
      }
    },

    // 备用二维码生成方案
    async generateFallbackQRCode(paymentType) {
      try {
        console.log('🔄 使用备用二维码生成方案...')

        // 首先获取加密的merchant参数
        const merchantResponse = await this.request('/user/staff.php', {
          act: 'generateQRUrl',
          staff_id: 0  // 0表示商户收款码
        }, 'POST')

        if (merchantResponse.code !== 0) {
          throw new Error(merchantResponse.msg || '获取商户参数失败')
        }

        // 从返回的URL中提取merchant参数
        const qrUrl = merchantResponse.data.qr_url
        const urlParams = new URLSearchParams(qrUrl.split('?')[1])
        const encryptedMerchant = urlParams.get('merchant')

        // 生成本地支付URL
        const trade_no = 'DTM' + Date.now() + Math.floor(Math.random() * 1000)
        const payment_url = `http://ceshi.huisas.com/paypage/?merchant=${encodeURIComponent(encryptedMerchant)}&order_id=${trade_no}&money=${this.orderForm.money}&name=${encodeURIComponent(this.orderForm.name)}&type=${paymentType.id}`

        // 使用草料二维码最新API（国内服务，更稳定）
        const qrcode_apis = [
          `https://api.2dcode.biz/v1/create-qr-code?data=${encodeURIComponent(payment_url)}&size=300x300&format=png`,
          `https://api.2dcode.biz/v1/create-qr-code?data=${encodeURIComponent(payment_url)}&size=256x256`,
          `https://api.2dcode.biz/v1/create-qr-code?data=${encodeURIComponent(payment_url)}&size=200x200&error_correction=M`
        ]

        this.qrCodeData = {
          trade_no: trade_no,
          qrcode_url: payment_url,
          qrcode_image: qrcode_apis[0], // 使用第一个API生成的二维码图片
          payment_url: payment_url,
          money: this.orderForm.money,
          name: this.orderForm.name,
          expire_time: Date.now() + 1800000 // 30分钟过期
        }

        // 预加载图片
        this.preloadQRCodeImage(qrcode_apis[0])

        uni.showToast({
          title: '二维码生成成功',
          icon: 'success',
          duration: 1500
        })

        // ⚡ 备用方案成功后也立即检查新订单
        this.triggerImmediateCheck()

      } catch (error) {
        console.error('❌ 备用方案也失败了:', error)
        uni.showToast({
          title: '生成失败，请重试',
          icon: 'none'
        })
      }
    },

    // 生成二维码图片 - 使用草料二维码最新API
    async generateQRCodeImage(url) {
      try {
        // 使用草料二维码最新API（国内服务，更稳定）
        const qrApiUrl = `https://api.2dcode.biz/v1/create-qr-code?data=${encodeURIComponent(url)}&size=300x300&format=png`
        return qrApiUrl
      } catch (error) {
        console.error('生成二维码图片失败:', error)
        // 备用方案：使用不同尺寸的草料API
        return `https://api.2dcode.biz/v1/create-qr-code?data=${encodeURIComponent(url)}&size=256x256`
      }
    },

    // 预加载二维码图片
    async preloadQRCodeImage(imageUrl) {
      return new Promise((resolve, reject) => {
        try {
          // 在uni-app中使用uni.getImageInfo来预加载图片
          uni.getImageInfo({
            src: imageUrl,
            success: (res) => {
              console.log('✅ 二维码图片预加载成功', res)
              this.imageLoaded = true
              resolve()
            },
            fail: (err) => {
              console.log('⚠️ 二维码图片预加载失败', err)
              this.qrCodeError = '图片加载失败'
              this.imageLoaded = true // 仍然允许显示，让image组件自己处理错误
              resolve() // 不reject，避免阻塞流程
            }
          })

          // 设置超时
          setTimeout(() => {
            if (!this.imageLoaded) {
              console.log('⚠️ 图片加载超时')
              this.imageLoaded = true // 允许显示，即使可能还在加载
              resolve()
            }
          }, 3000)
        } catch (error) {
          console.log('⚠️ 预加载功能异常，跳过', error)
          this.imageLoaded = true
          resolve()
        }
      })
    },

    // 启动加载进度动画
    startLoadingProgress() {
      this.loadingProgress = 0
      this.loadingText = '正在生成二维码...'

      this.loadingTimer = setInterval(() => {
        if (this.loadingProgress < 90) {
          this.loadingProgress += Math.random() * 15

          if (this.loadingProgress > 30 && this.loadingProgress < 60) {
            this.loadingText = '正在处理订单...'
          } else if (this.loadingProgress > 60) {
            this.loadingText = '正在生成二维码...'
          }
        }
      }, 200)
    },

    // 清除加载计时器
    clearLoadingTimer() {
      if (this.loadingTimer) {
        clearInterval(this.loadingTimer)
        this.loadingTimer = null
      }
      this.loadingProgress = 100
    },

    // 重试生成
    retryGenerate() {
      this.qrCodeError = null
      if (this.selectedType) {
        this.generateQRCode(this.selectedType)
      }
    },

    // 刷新二维码
    refreshCode() {
      if (this.selectedType) {
        this.generateQRCode(this.selectedType)
      }
    },

    // 处理二维码加载完成
    handleQRCodeLoad() {
      console.log('✅ 二维码图片加载完成')
      this.qrCodeLoading = false
      this.imageLoaded = true
      this.qrCodeError = null
    },

    // 处理二维码加载错误
    handleQRCodeError() {
      console.log('❌ 二维码图片加载失败')
      this.qrCodeLoading = false
      this.imageLoaded = false
      this.qrCodeError = '二维码加载失败，请重试'
    }
  }
}
</script>

<style lang="scss" scoped>
/* 主容器 */
.cashier-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

/* 主页面内容 */
.main-content {
  width: 100%;
  max-width: 600rpx;
  text-align: center;
}

.header-section {
  margin-bottom: 80rpx;
}

.main-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #fff;
  margin-bottom: 16rpx;
  display: block;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.main-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  line-height: 1.5;
}

.start-cashier-btn {
  width: 100%;
  height: 120rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border-radius: 24rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 60rpx;
  box-shadow: 0 8rpx 32rpx rgba(238, 90, 36, 0.4);
  transition: all 0.3s ease;
}

.start-cashier-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(238, 90, 36, 0.4);
}

.btn-icon {
  font-size: 36rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

.tips-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 32rpx;
  backdrop-filter: blur(10rpx);
}

.hint-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 24rpx;
  display: block;
}

.tip-line {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 8rpx;
  display: block;
}

/* 收银弹窗 */
.cashier-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4rpx);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 0;
}

/* 收银容器 */
.cashier-container-inner {
  background: #fff;
  border-radius: 0 0 32rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: slideDown 0.3s ease-out;
  width: 100%;
  max-width: 750rpx;
}

.qrcode-container {
  background: #fff;
  border-radius: 0 0 32rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: slideDown 0.3s ease-out;
  width: 100%;
  max-width: 750rpx;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 收银头部 */
.cashier-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 100%);
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.1);
}

.cashier-title, .qrcode-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.close-icon {
  font-size: 28rpx;
  color: #fff;
  font-weight: bold;
}

/* 收银内容区域 */
.cashier-content {
  padding: 32rpx;
  background: #fff;
}

/* 商品名称区域 */
.product-section {
  margin-bottom: 32rpx;
}

.section-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
  display: block;
}

.product-input {
  width: 100%;
  height: 80rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.product-input:focus {
  border-color: #5145F7;
  background: #fff;
}

/* 金额区域 */
.amount-section {
  margin-bottom: 32rpx;
}

.amount-display {
  position: relative;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.amount-display:focus-within {
  border-color: #5145F7;
  background: #fff;
}

.amount-input {
  width: 100%;
  height: 120rpx;
  padding: 0 80rpx 0 20rpx;
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  background: transparent;
  border: none;
  box-sizing: border-box;
  text-align: left;
}

.amount-unit {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

/* 支付方式区域 */
.payment-section {
  margin-bottom: 32rpx;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.payment-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  position: relative;
}

.payment-option.active {
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 100%);
  border-color: #5145F7;
  color: #fff;
}

.payment-option.loading {
  opacity: 0.7;
  pointer-events: none;
}

.payment-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.payment-option.active .payment-name {
  color: #fff;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.loading-indicator .loading-text {
  font-size: 24rpx;
  color: #fff;
}

.qrcode-loading .loading-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 0;
}

.keyboard-hint {
  background: rgba(81, 69, 247, 0.1);
  border-radius: 12rpx;
  padding: 16rpx;
  text-align: center;
}

.hint-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #5145F7;
  margin-bottom: 8rpx;
  display: block;
}

.hint-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
  display: block;
}

/* 二维码内容区域 */
.qrcode-content {
  padding: 40rpx;
  text-align: center;
  background: #fff;
}

.amount-display-large {
  margin-bottom: 40rpx;
}

.amount-symbol {
  font-size: 48rpx;
  color: #5145F7;
  font-weight: 600;
}

.amount-value {
  font-size: 72rpx;
  color: #333;
  font-weight: 700;
  margin-left: 8rpx;
}

.qrcode-wrapper {
  position: relative;
  width: 400rpx;
  height: 400rpx;
  margin: 0 auto 32rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-image {
  width: 360rpx;
  height: 360rpx;
  border-radius: 12rpx;
}

.qrcode-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.placeholder-icon {
  font-size: 80rpx;
  color: #ccc;
}

.qrcode-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  backdrop-filter: blur(4rpx);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #5145F7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-progress {
  width: 200rpx;
  height: 6rpx;
  background: #f0f0f0;
  border-radius: 3rpx;
  overflow: hidden;
  margin-top: 20rpx;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #5145F7, #6366F1);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.qrcode-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  padding: 20rpx;
}

.error-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.error-text {
  font-size: 26rpx;
  color: #ff4757;
  text-align: center;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.retry-btn {
  background: #5145F7;
  color: #fff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  cursor: pointer;
}

.retry-btn:active {
  background: #4338CA;
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

.payment-tip {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  display: block;
}

.focus-hint {
  text-align: center;
  margin: 20rpx 0;
  padding: 16rpx 32rpx;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 40rpx;
  border: 2rpx solid rgba(64, 158, 255, 0.2);
}

.focus-hint-text {
  color: #409eff;
  font-size: 24rpx;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

.action-btn.primary {
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 100%);
  color: #fff;
}

.action-btn:active {
  transform: translateY(2rpx);
}

.expire-tip {
  font-size: 24rpx;
  color: #999;
  display: block;
}
</style>
