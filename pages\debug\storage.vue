<template>
  <view class="container">
    <view class="header">
      <text class="title">🔍 存储调试工具</text>
      <text class="subtitle">检查uni-app本地存储状态</text>
    </view>
    
    <view class="section">
      <button @click="checkStorage" class="btn-primary">检查存储状态</button>
      <button @click="clearStorage" class="btn-danger">清空存储</button>
    </view>
    
    <view class="section" v-if="storageData">
      <text class="section-title">存储数据：</text>
      <view class="storage-item" v-for="(value, key) in storageData" :key="key">
        <text class="key">{{ key }}:</text>
        <text class="value" :class="value ? 'has-value' : 'no-value'">
          {{ value || '未设置' }}
        </text>
      </view>
    </view>
    
    <view class="section" v-if="allKeys.length > 0">
      <text class="section-title">所有存储键：</text>
      <text class="keys-list">{{ allKeys.join(', ') }}</text>
    </view>
    
    <view class="section">
      <button @click="testLogin" class="btn-secondary">测试登录状态</button>
      <button @click="goToLogin" class="btn-secondary">前往登录</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      storageData: null,
      allKeys: []
    }
  },
  
  onLoad() {
    this.checkStorage();
  },
  
  methods: {
    checkStorage() {
      console.log('开始检查存储...');
      
      // 检查关键存储项
      const storageData = {
        user_token: uni.getStorageSync('user_token'),
        user_uid: uni.getStorageSync('user_uid'),
        user_key: uni.getStorageSync('user_key'),
        user_info: uni.getStorageSync('user_info'),
        csrf_token: uni.getStorageSync('csrf_token'),
        session_id: uni.getStorageSync('session_id'),
        merchantId: uni.getStorageSync('merchantId'),
        merchantKey: uni.getStorageSync('merchantKey'),
        remembered_username: uni.getStorageSync('remembered_username')
      };
      
      this.storageData = storageData;
      
      // 获取所有存储键
      try {
        const info = uni.getStorageInfoSync();
        this.allKeys = info.keys || [];
        console.log('所有存储键:', this.allKeys);
      } catch (e) {
        console.error('获取存储信息失败:', e);
        this.allKeys = [];
      }
      
      // 输出到控制台
      console.log('存储数据:', storageData);
      
      // 显示结果
      const hasLoginData = storageData.user_token && storageData.user_uid;
      uni.showToast({
        title: hasLoginData ? '找到登录数据' : '缺少登录数据',
        icon: hasLoginData ? 'success' : 'none'
      });
    },
    
    clearStorage() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有存储数据吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              uni.clearStorageSync();
              this.storageData = null;
              this.allKeys = [];
              uni.showToast({
                title: '存储已清空',
                icon: 'success'
              });
              this.checkStorage();
            } catch (e) {
              console.error('清空存储失败:', e);
              uni.showToast({
                title: '清空失败',
                icon: 'error'
              });
            }
          }
        }
      });
    },
    
    testLogin() {
      const token = uni.getStorageSync('user_token');
      const uid = uni.getStorageSync('user_uid');
      
      if (token && uid) {
        uni.showModal({
          title: '登录状态',
          content: `已登录\nUID: ${uid}\nToken: ${token.substring(0, 20)}...`,
          showCancel: false
        });
      } else {
        uni.showModal({
          title: '登录状态',
          content: '未登录或登录已过期',
          showCancel: false
        });
      }
    },
    
    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/index'
      });
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 14px;
  color: #666;
  display: block;
}

.section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15px;
}

.storage-item {
  display: flex;
  margin-bottom: 10px;
  align-items: flex-start;
}

.key {
  font-weight: bold;
  color: #666;
  width: 120px;
  flex-shrink: 0;
}

.value {
  flex: 1;
  word-break: break-all;
}

.has-value {
  color: #27ae60;
}

.no-value {
  color: #e74c3c;
}

.keys-list {
  font-size: 12px;
  color: #666;
  word-break: break-all;
  line-height: 1.5;
}

.btn-primary {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  margin: 5px;
  font-size: 16px;
}

.btn-secondary {
  background-color: #34c759;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  margin: 5px;
  font-size: 16px;
}

.btn-danger {
  background-color: #ff3b30;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  margin: 5px;
  font-size: 16px;
}
</style>
