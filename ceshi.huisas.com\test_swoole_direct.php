<?php
/**
 * 直接测试Swoole WebSocket服务器
 */

header('Content-Type: application/json; charset=utf-8');

$action = $_GET['action'] ?? 'notify';

switch ($action) {
    case 'notify':
        testPaymentNotify();
        break;
    case 'health':
        testHealthCheck();
        break;
    default:
        echo json_encode(['error' => 'Invalid action'], JSON_UNESCAPED_UNICODE);
}

/**
 * 测试支付通知
 */
function testPaymentNotify() {
    $url = 'http://ceshi.huisas.com:8080/payment/notify';
    
    $testData = [
        'merchant_id' => '1000',
        'order_id' => 'SWOOLE_TEST_' . time(),
        'amount' => '66.66',
        'status' => 'success',
        'extra_data' => [
            'voice_text' => '支付宝收款66.66元',
            'type' => 'alipay',
            'typename' => '支付宝'
        ]
    ];
    
    echo "🚀 发送测试数据到Swoole服务器...\n";
    echo "URL: {$url}\n";
    echo "数据: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($testData),
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_CONNECTTIMEOUT => 5,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'User-Agent: Swoole-Test-Client/1.0'
        ]
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $result = [
        'test_type' => 'payment_notify',
        'url' => $url,
        'request_data' => $testData,
        'http_code' => $httpCode,
        'response' => $response,
        'curl_error' => $error,
        'success' => $httpCode >= 200 && $httpCode < 300 && empty($error),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if ($result['success']) {
        echo "✅ 请求成功！\n";
        echo "HTTP状态码: {$httpCode}\n";
        echo "服务器响应: {$response}\n";
        echo "\n🎵 请检查语音播报页面是否收到通知！\n";
    } else {
        echo "❌ 请求失败！\n";
        echo "HTTP状态码: {$httpCode}\n";
        echo "错误信息: {$error}\n";
        echo "服务器响应: {$response}\n";
    }
    
    echo "\n" . json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}

/**
 * 测试健康检查
 */
function testHealthCheck() {
    $url = 'http://ceshi.huisas.com:8080/health';
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 5
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $result = [
        'test_type' => 'health_check',
        'url' => $url,
        'http_code' => $httpCode,
        'response' => $response,
        'curl_error' => $error,
        'success' => $httpCode >= 200 && $httpCode < 300 && empty($error),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
