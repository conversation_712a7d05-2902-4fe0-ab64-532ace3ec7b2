<template>
	<view class="navbar-spacer" :style="spacerStyle"></view>
</template>

<script>
export default {
	name: 'NavbarSpacer',
	data() {
		return {
			statusBarHeight: 20,
			navbarHeight: 44
		}
	},
	computed: {
		spacerStyle() {
			return {
				height: (this.statusBarHeight + this.navbarHeight) + 'px'
			}
		}
	},
	mounted() {
		this.initSystemInfo();
	},
	methods: {
		initSystemInfo() {
			try {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 20;
				
				// 根据平台调整导航栏高度
				// #ifdef H5
				this.statusBarHeight = 0; // H5没有状态栏
				// #endif
				
				// #ifdef MP-WEIXIN
				this.navbarHeight = 44; // 微信小程序标准高度
				// #endif
				
				// #ifdef MP-ALIPAY
				this.navbarHeight = 44; // 支付宝小程序标准高度
				// #endif
				
				// #ifdef APP-PLUS
				this.navbarHeight = 44; // App标准高度
				// #endif
				
			} catch (error) {
				console.warn('获取系统信息失败，使用默认值:', error);
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.navbar-spacer {
	width: 100%;
	flex-shrink: 0;
	background: transparent;
}
</style>
