<?php
/**
 * 测试防重复通知机制
 * 模拟支付网关多次回调同一个订单
 */

require_once 'includes/common.php';
require_once 'includes/lib/Payment.php';

// 设置测试订单号
$test_trade_no = '2025072718301496998'; // 使用日志中的真实订单号

echo "<h2>🧪 测试防重复通知机制</h2>";

// 1. 查询订单当前状态
$order = $DB->find('order', '*', ['trade_no' => $test_trade_no]);
if (!$order) {
    echo "<p>❌ 订单不存在: {$test_trade_no}</p>";
    exit;
}

echo "<h3>📋 订单当前状态</h3>";
echo "<ul>";
echo "<li>订单号: {$order['trade_no']}</li>";
echo "<li>支付状态: " . ($order['status'] == 1 ? '✅ 已支付' : '❌ 未支付') . "</li>";
echo "<li>通知状态: " . ($order['notify'] == 1 ? '✅ 已通知' : '❌ 未通知') . "</li>";
echo "<li>商户ID: {$order['uid']}</li>";
echo "<li>金额: {$order['money']} 元</li>";
echo "</ul>";

// 2. 重置通知状态（模拟第一次通知）
echo "<h3>🔄 重置通知状态</h3>";
$resetResult = $DB->update('order', ['notify' => 0], ['trade_no' => $test_trade_no]);
echo "<p>重置结果: " . ($resetResult ? '✅ 成功' : '❌ 失败') . "</p>";

// 3. 模拟第一次回调（应该成功通知）
echo "<h3>📞 第一次回调（应该成功通知）</h3>";
$result1 = \lib\Payment::processOrder(
    true, // isnotify
    $order,
    $order['api_trade_no'],
    $order['buyer']
);
echo "<p>第一次回调结果: " . ($result1 ? '✅ 成功' : '❌ 失败') . "</p>";

// 检查通知状态是否已更新
$order_after_first = $DB->find('order', '*', ['trade_no' => $test_trade_no]);
echo "<p>通知状态: " . ($order_after_first['notify'] == 1 ? '✅ 已更新为已通知' : '❌ 未更新') . "</p>";

// 4. 模拟第二次回调（应该跳过通知）
echo "<h3>📞 第二次回调（应该跳过通知）</h3>";
$result2 = \lib\Payment::processOrder(
    true, // isnotify
    $order_after_first,
    $order['api_trade_no'],
    $order['buyer']
);
echo "<p>第二次回调结果: " . ($result2 ? '✅ 成功（跳过重复通知）' : '❌ 失败') . "</p>";

// 5. 模拟第三次回调（应该跳过通知）
echo "<h3>📞 第三次回调（应该跳过通知）</h3>";
$result3 = \lib\Payment::processOrder(
    true, // isnotify
    $order_after_first,
    $order['api_trade_no'],
    $order['buyer']
);
echo "<p>第三次回调结果: " . ($result3 ? '✅ 成功（跳过重复通知）' : '❌ 失败') . "</p>";

// 6. 查看最新日志
echo "<h3>📋 最新日志记录</h3>";
$log_file = 'includes/logs/payment_debug_' . date('Y-m-d') . '.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $log_lines = explode("\n", $log_content);
    $recent_logs = array_slice($log_lines, -10); // 最后10行
    
    echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;'>";
    foreach ($recent_logs as $line) {
        if (strpos($line, $test_trade_no) !== false) {
            echo "<div style='color: #007cba;'>" . htmlspecialchars($line) . "</div>";
        }
    }
    echo "</div>";
} else {
    echo "<p>❌ 日志文件不存在</p>";
}

echo "<h3>✅ 测试完成</h3>";
echo "<p>如果防重复通知机制工作正常，第二次和第三次回调应该跳过WebSocket通知。</p>";
?>
