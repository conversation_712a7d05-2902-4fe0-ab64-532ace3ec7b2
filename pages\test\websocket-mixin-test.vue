<template>
  <view class="container">
    <view class="header">
      <text class="title">WebSocket Mixin 测试</text>
    </view>
    
    <view class="status-section">
      <view class="status-item">
        <text class="label">连接状态:</text>
        <text :class="['status', wsConnected ? 'connected' : 'disconnected']">
          {{ wsConnected ? '已连接' : '未连接' }}
        </text>
      </view>
      
      <view class="status-item">
        <text class="label">连接中:</text>
        <text :class="['status', wsConnecting ? 'connecting' : '']">
          {{ wsConnecting ? '是' : '否' }}
        </text>
      </view>
      
      <view class="status-item">
        <text class="label">语音播报:</text>
        <text :class="['status', voiceEnabled ? 'enabled' : 'disabled']">
          {{ voiceEnabled ? '已开启' : '已关闭' }}
        </text>
      </view>
    </view>
    
    <view class="button-section">
      <button @click="testConnection" class="test-btn">测试连接</button>
      <button @click="testPayment" class="test-btn">模拟支付</button>
      <button @click="toggleVoice" class="test-btn">
        {{ voiceEnabled ? '关闭' : '开启' }}语音
      </button>
    </view>
    
    <view class="log-section">
      <text class="log-title">日志信息:</text>
      <scroll-view class="log-container" scroll-y>
        <view v-for="(log, index) in logs" :key="index" class="log-item">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-message">{{ log.message }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import { websocketMixin } from '@/mixins/websocketMixin.js'

export default {
  mixins: [websocketMixin],
  
  data() {
    return {
      logs: []
    }
  },
  
  onLoad() {
    this.addLog('页面加载完成')
  },
  
  onShow() {
    this.addLog('页面显示，初始化WebSocket')
  },
  
  methods: {
    addLog(message) {
      const now = new Date()
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
      
      this.logs.unshift({
        time,
        message
      })
      
      // 只保留最近50条日志
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
      
      console.log(`[${time}] ${message}`)
    },
    
    testConnection() {
      this.addLog('手动测试连接...')
      this.initWebSocket()
    },
    
    testPayment() {
      this.addLog('模拟支付通知...')
      
      const mockPaymentData = {
        amount: '0.01',
        money: '0.01',
        typename: '测试支付',
        order_id: 'TEST_' + Date.now(),
        extra_data: {
          money: '0.01',
          typename: '支付宝'
        }
      }
      
      this.handlePaymentNotification(mockPaymentData)
    },
    
    toggleVoice() {
      this.voiceEnabled = !this.voiceEnabled
      uni.setStorageSync('voice_enabled', this.voiceEnabled)
      this.addLog(`语音播报已${this.voiceEnabled ? '开启' : '关闭'}`)
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.status-section {
  background: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}

.status-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.status {
  font-size: 28rpx;
  font-weight: bold;
}

.connected {
  color: #4CAF50;
}

.disconnected {
  color: #f44336;
}

.connecting {
  color: #ff9800;
}

.enabled {
  color: #4CAF50;
}

.disabled {
  color: #999;
}

.button-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.test-btn {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.log-section {
  background: white;
  border-radius: 10rpx;
  padding: 20rpx;
  height: 600rpx;
}

.log-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.log-container {
  height: 500rpx;
}

.log-item {
  display: flex;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
  font-size: 24rpx;
}

.log-time {
  color: #999;
  margin-right: 20rpx;
  min-width: 120rpx;
}

.log-message {
  color: #333;
  flex: 1;
}
</style>
