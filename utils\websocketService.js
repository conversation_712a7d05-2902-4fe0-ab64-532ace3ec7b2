/**
 * WebSocket服务管理器 (Swoole WebSocket版本)
 * 用于支付通知的实时通信
 *
 * 特性：
 * 1. 🚀 连接到Swoole WebSocket服务器 (端口8080)
 * 2. 🔄 基于开源 websocket.js 的稳定重连机制
 * 3. 🎯 标准WebSocket协议，无需特殊路径
 * 4. 💓 内置心跳检测
 * 5. 🎵 支付语音播报功能
 */

import { paymentWebSocket } from './paymentWebSocket.js'

class WebSocketService {
    constructor() {
        this.paymentService = paymentWebSocket;
        this.isInitialized = false;
        
        // 配置
        this.config = {
            host: 'ceshi.huisas.com',
            port: 8080,
            app_key: 'payment_websocket_2024',
            reconnectInterval: 3000,
            heartbeatInterval: 30000
        };
        
        // 回调函数 (兼容旧版本API)
        this.onMessageCallback = null;
        this.onConnectCallback = null;
        this.onDisconnectCallback = null;
        this.onErrorCallback = null;
    }

    /**
     * 连接WebSocket (兼容旧版本API)
     * @param {Object} callbacks 回调函数对象
     */
    connect(callbacks = {}) {
        if (this.isInitialized) {
            console.log('WebSocket服务已初始化');
            return;
        }

        // 设置回调函数
        this.onMessageCallback = callbacks.onMessage || null;
        this.onConnectCallback = callbacks.onConnect || null;
        this.onDisconnectCallback = callbacks.onDisconnect || null;
        this.onErrorCallback = callbacks.onError || null;

        // 绑定事件监听器
        this.bindEventListeners();

        // 初始化支付WebSocket服务
        this.paymentService.init(this.config);
        this.isInitialized = true;

        console.log('🚀 WebSocket服务初始化完成');
    }

    /**
     * 绑定事件监听器
     */
    bindEventListeners() {
        // 连接成功事件
        this.paymentService.on('connect', (data) => {
            console.log('✅ WebSocket连接成功:', data);
            if (this.onConnectCallback) {
                this.onConnectCallback(data);
            }
        });

        // 连接断开事件
        this.paymentService.on('disconnect', (data) => {
            console.log('❌ WebSocket连接断开:', data);
            if (this.onDisconnectCallback) {
                this.onDisconnectCallback(data);
            }
        });

        // 支付通知事件
        this.paymentService.on('payment', (data) => {
            console.log('💰 收到支付通知:', data);
            
            // 兼容旧版本的消息格式
            const compatMessage = {
                event: data.type === 'success' ? 'payment_success' : 'payment_failed',
                data: data.data,
                timestamp: data.timestamp
            };
            
            if (this.onMessageCallback) {
                this.onMessageCallback(compatMessage);
            }
        });

        // 通用消息事件
        this.paymentService.on('message', (message) => {
            console.log('📨 收到WebSocket消息:', message);
            if (this.onMessageCallback) {
                this.onMessageCallback(message);
            }
        });

        // 错误事件
        this.paymentService.on('error', (error) => {
            console.error('❌ WebSocket错误:', error);
            if (this.onErrorCallback) {
                this.onErrorCallback(error);
            }
        });
    }

    /**
     * 发送消息 (兼容旧版本API)
     * @param {Object} message 要发送的消息
     */
    send(message) {
        if (this.paymentService) {
            this.paymentService.send(message);
        } else {
            console.warn('WebSocket服务未初始化');
        }
    }

    /**
     * 获取连接状态 (兼容旧版本API)
     */
    get isConnected() {
        return this.paymentService ? this.paymentService.getStatus().connected : false;
    }

    /**
     * 获取连接中状态 (兼容旧版本API)
     */
    get isConnecting() {
        return this.paymentService ? this.paymentService.getStatus().connecting : false;
    }

    /**
     * 关闭连接 (兼容旧版本API)
     */
    close() {
        if (this.paymentService) {
            this.paymentService.close();
        }
        this.isInitialized = false;
        console.log('🛑 WebSocket服务已关闭');
    }

    /**
     * 断开连接 (兼容旧版本API)
     */
    disconnect() {
        this.close();
    }

    /**
     * 重新连接 (兼容旧版本API)
     */
    reconnect() {
        if (this.paymentService) {
            this.paymentService.reconnect();
        }
    }

    /**
     * 获取服务状态
     */
    getStatus() {
        if (this.paymentService) {
            return this.paymentService.getStatus();
        }
        return {
            connected: false,
            connecting: false,
            initialized: this.isInitialized
        };
    }

    /**
     * 订阅支付通知频道 (兼容旧版本API)
     */
    subscribeToPaymentChannel() {
        // 新版本中，连接成功后自动订阅所有支付通知
        // 这里保留方法以兼容旧代码
        console.log('📺 支付通知频道已自动订阅');

        if (this.paymentService) {
            this.paymentService.send({
                type: 'subscribe',
                data: {
                    channel: 'payment_notifications'
                }
            });
        }
    }

    /**
     * 订阅指定频道 (通用订阅方法)
     * @param {string} channel 频道名称
     */
    subscribe(channel) {
        console.log('📺 订阅频道:', channel);

        if (this.paymentService) {
            this.paymentService.send({
                type: 'subscribe',
                data: {
                    channel: channel
                }
            });
        } else {
            console.warn('WebSocket服务未初始化，无法订阅频道');
        }
    }

    /**
     * 发送心跳 (兼容旧版本API)
     */
    sendHeartbeat() {
        // 新版本中心跳由底层自动处理
        console.log('💓 心跳由底层自动处理');
    }

    /**
     * 启动心跳 (兼容旧版本API)
     */
    startHeartbeat() {
        // 新版本中心跳由底层自动处理
        console.log('💓 心跳已自动启动');
    }

    /**
     * 停止心跳 (兼容旧版本API)
     */
    stopHeartbeat() {
        // 新版本中心跳由底层自动处理
        console.log('💓 心跳由底层自动管理');
    }

    /**
     * 处理连接丢失 (兼容旧版本API)
     */
    handleConnectionLost() {
        console.log('🔄 连接丢失，自动重连由底层处理');
    }

    /**
     * 处理错误 (兼容旧版本API)
     */
    handleError(type, error) {
        console.error(`WebSocket ${type}:`, error);
        if (this.onErrorCallback) {
            this.onErrorCallback({ type, error });
        }
    }

    /**
     * 安排重连 (兼容旧版本API)
     */
    scheduleReconnect() {
        console.log('🔄 重连由底层自动处理');
    }

    /**
     * 停止重连 (兼容旧版本API)
     */
    stopReconnect() {
        console.log('🛑 重连控制由底层管理');
    }
}

// 创建全局实例
const webSocketService = new WebSocketService();

export default WebSocketService;
export { webSocketService };
