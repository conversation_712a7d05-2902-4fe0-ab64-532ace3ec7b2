<?php
/**
 * 调试easypay配置信息
 */
include("../includes/common.php");

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: text/html; charset=utf-8');

echo "<h2>🔍 Easypay配置调试</h2>";

try {
    // 使用测试商户
    $test_uid = 1000;
    $pay_type = 'alipay';
    $amount = 0.01;
    
    echo "<h3>1. 商户信息</h3>";
    $userrow = $DB->getRow("SELECT * FROM pay_user WHERE uid='$test_uid' LIMIT 1");
    if($userrow) {
        echo "<p>✅ 商户存在: {$userrow['username']}, 状态: {$userrow['status']}</p>";
        echo "<p>商户组ID: {$userrow['gid']}</p>";
    } else {
        echo "<p>❌ 商户不存在</p>";
        exit();
    }
    
    echo "<h3>2. 获取支付通道</h3>";
    $submitData = \lib\Channel::submit($pay_type, $test_uid, 2, $amount);
    if($submitData) {
        echo "<p>✅ 获取通道成功</p>";
        echo "<p>插件: {$submitData['plugin']}</p>";
        echo "<p>通道ID: {$submitData['channel']}</p>";
        echo "<p>类型ID: {$submitData['typeid']}</p>";
    } else {
        echo "<p>❌ 没有可用的支付通道</p>";
        exit();
    }
    
    echo "<h3>3. 通道配置详情</h3>";
    $channel = $DB->getRow("SELECT * FROM pay_channel WHERE id='{$submitData['channel']}' LIMIT 1");
    if($channel) {
        echo "<p>✅ 通道配置存在</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>字段</th><th>值</th></tr>";
        foreach($channel as $key => $value) {
            if(in_array($key, ['appkey', 'appsecret'])) {
                // 敏感信息只显示前后几位
                $display_value = strlen($value) > 10 ? substr($value, 0, 10) . '...' . substr($value, -10) : $value;
            } else {
                $display_value = $value;
            }
            echo "<tr><td>{$key}</td><td>{$display_value}</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ 通道配置不存在</p>";
        exit();
    }
    
    echo "<h3>4. 检查easypay插件文件</h3>";
    $plugin_file = PLUGIN_ROOT . $submitData['plugin'] . '/' . $submitData['plugin'] . '_plugin.php';
    if(file_exists($plugin_file)) {
        echo "<p>✅ 插件文件存在: {$plugin_file}</p>";
    } else {
        echo "<p>❌ 插件文件不存在: {$plugin_file}</p>";
        exit();
    }
    
    $client_file = PLUGIN_ROOT . $submitData['plugin'] . '/inc/EasypayClient.php';
    if(file_exists($client_file)) {
        echo "<p>✅ 客户端文件存在: {$client_file}</p>";
    } else {
        echo "<p>❌ 客户端文件不存在: {$client_file}</p>";
        exit();
    }
    
    echo "<h3>5. 测试EasypayClient初始化</h3>";
    try {
        // 设置必要的常量
        if(!defined('IN_PLUGIN')) define('IN_PLUGIN', true);
        define('PAY_ROOT', PLUGIN_ROOT . $submitData['plugin'] . '/');
        define('TRADE_NO', 'TEST123456');
        
        // 加载客户端类
        require_once($client_file);
        
        // 尝试初始化客户端
        $client = new EasypayClient(
            $channel['appid'],
            $channel['reqtype'],
            $channel['appkey'],
            $channel['appsecret'],
            $channel['appswitch']==1
        );
        
        echo "<p>✅ EasypayClient初始化成功</p>";
        echo "<p>网关地址: " . ($channel['appswitch']==1 ? '测试环境' : '生产环境') . "</p>";
        
    } catch(Exception $e) {
        echo "<p>❌ EasypayClient初始化失败: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>6. 检查配置完整性</h3>";
    $required_fields = ['appid', 'appkey', 'appsecret', 'reqtype'];
    $missing_fields = [];
    
    foreach($required_fields as $field) {
        if(empty($channel[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if(empty($missing_fields)) {
        echo "<p>✅ 所有必需字段都已配置</p>";
    } else {
        echo "<p>❌ 缺少必需字段: " . implode(', ', $missing_fields) . "</p>";
    }
    
    echo "<h3>7. 配置建议</h3>";
    echo "<ul>";
    echo "<li>appid (机构号/商户号): " . ($channel['appid'] ? '已配置' : '❌ 未配置') . "</li>";
    echo "<li>appkey (易生公钥): " . ($channel['appkey'] ? '已配置' : '❌ 未配置') . "</li>";
    echo "<li>appsecret (商户私钥): " . ($channel['appsecret'] ? '已配置' : '❌ 未配置') . "</li>";
    echo "<li>reqtype (接入模式): " . ($channel['reqtype'] == 1 ? '商户模式' : ($channel['reqtype'] == 2 ? '机构模式' : '❌ 未设置')) . "</li>";
    echo "<li>appswitch (环境): " . ($channel['appswitch'] == 1 ? '测试环境' : '生产环境') . "</li>";
    echo "</ul>";
    
} catch(Exception $e) {
    echo "<p>❌ 调试过程中出错: " . $e->getMessage() . "</p>";
}
?>
