<template>
	<view class="container">
		<!-- 优雅现代导航栏 -->
		<view class="elegant-navbar">
			<!-- 状态栏占位 -->
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

			<!-- 导航栏主体 -->
			<view class="navbar-main">
				<!-- 左侧：标题区域 -->
				<view class="navbar-title-section">
					<text class="page-title">数据报表</text>
					<text class="page-subtitle">经营数据分析</text>
				</view>

				<!-- 右侧：日期选择器 -->
				<view class="navbar-actions">
					<view class="date-picker-btn" @click="showDatePicker">
						<view class="date-icon-wrapper">
							<text class="date-icon">📅</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 导航栏占位 - 自适应安全区域 -->
		<view class="navbar-placeholder" :style="{ height: navbarPlaceholderHeight + 'px' }"></view>
		
		<!-- 当前查询时间范围显示 -->
		<view class="time-range-display">
			<text class="time-range-label">查询时间</text>
			<text class="time-range-value">{{selectedMonth}}</text>
		</view>
		
		<!-- 数据摘要卡片 -->
		<view class="summary-card">
			<view class="data-grid">
				<view class="data-item">
					<text class="data-value">¥ {{formatNumber(totalIncome)}}</text>
					<text class="data-label">总收入</text>
					<text class="data-change" :class="incomeChange >= 0 ? 'up' : 'down'">
						{{formatPercentage(incomeChange)}}
					</text>
				</view>
				<view class="data-item">
					<text class="data-value">{{totalOrders}}</text>
					<text class="data-label">订单数</text>
					<text class="data-change" :class="orderChange >= 0 ? 'up' : 'down'">
						{{formatPercentage(orderChange)}}
					</text>
				</view>
				<view class="data-item">
					<text class="data-value">¥ {{formatNumber(averageOrder)}}</text>
					<text class="data-label">客单价</text>
					<text class="data-change" :class="avgChange >= 0 ? 'up' : 'down'">
						{{formatPercentage(avgChange)}}
					</text>
				</view>
			</view>
		</view>
		
		<!-- 收入趋势卡片 -->
		<view class="card">
			<view class="card-header">
				<text class="section-title">收入趋势</text>
				<view class="card-actions">
					<text class="toggle-btn" :class="{ active: trendChartType === 'line' }" @click="setTrendChartType('line')">折线图</text>
					<text class="toggle-btn" :class="{ active: trendChartType === 'column' }" @click="setTrendChartType('column')">柱状图</text>
				</view>
			</view>
			
			<view class="chart-container">
				<qiun-data-charts :type="trendChartType" :opts="trendOpts" :chartData="trendData" @getIndex="onTrendChartClick" />
			</view>
		</view>
		
		<!-- 支付方式分析卡片 -->
		<view class="card">
			<view class="card-header">
				<text class="section-title">支付方式分析</text>
				<view class="card-actions">
					<text class="toggle-btn" :class="{ active: paymentChartType === 'pie' }" @click="setPaymentChartType('pie')">饼图</text>
					<text class="toggle-btn" :class="{ active: paymentChartType === 'ring' }" @click="setPaymentChartType('ring')">环形图</text>
				</view>
			</view>
			
			<view class="chart-container">
				<qiun-data-charts :type="paymentChartType" :opts="paymentOpts" :chartData="paymentData" />
			</view>
			
			<view class="payment-total">
				<text>总交易额: ¥ {{formatNumber(totalIncome)}}</text>
			</view>
			
			<!-- 支付方式图例 -->
			<view class="payment-legend">
				<view class="legend-item" v-for="(item, index) in paymentMethods" :key="index">
					<view class="legend-color" :style="{ backgroundColor: paymentColors[index] }"></view>
					<text class="legend-name">{{item.name}}</text>
					<text class="legend-percent">{{item.percentage}}%</text>
					<text class="legend-amount">¥ {{formatNumber(item.amount)}}</text>
				</view>
			</view>
		</view>
		
		<!-- 热销商品分析 - 暂时隐藏，待开发 -->
		<!--
		<view class="card">
			<view class="card-header">
				<text class="section-title">热销商品</text>
			</view>

			<view class="chart-container">
				<qiun-data-charts type="bar" :opts="productOpts" :chartData="productData" />
			</view>
		</view>
		-->
		
		<!-- 收入时段分布 -->
		<view class="card">
			<view class="card-header">
				<text class="section-title">收入时段分布</text>
			</view>
			
			<view class="chart-container">
				<qiun-data-charts type="area" :opts="timeOpts" :chartData="timeData" />
			</view>
			<view class="insight">
				<view class="insight-icon">💡</view>
				<text class="insight-text">{{timeInsight}}</text>
			</view>
		</view>
		
		<!-- 客户忠诚度分析 -->
		<view class="card">
			<view class="card-header">
				<text class="section-title">客户忠诚度分析</text>
				<text class="section-subtitle">基于用户标识(openid/appid)的深度分析</text>
			</view>

			<view class="chart-container">
				<qiun-data-charts type="column" :opts="loyaltyOpts" :chartData="loyaltyData" />
			</view>

			<!-- 基础统计 -->
			<view class="customer-stats">
				<view class="customer-stat-item">
					<text class="customer-stat-value">{{newCustomers}}</text>
					<text class="customer-stat-label">新客户</text>
				</view>
				<view class="customer-stat-item">
					<text class="customer-stat-value">{{returningCustomers}}</text>
					<text class="customer-stat-label">回头客</text>
				</view>
				<view class="customer-stat-item">
					<text class="customer-stat-value">{{loyaltyRate}}%</text>
					<text class="customer-stat-label">留存率</text>
				</view>
			</view>

			<!-- 详细分析数据 -->
			<view v-if="loyaltyAnalysis" class="loyalty-analysis">
				<!-- 关键指标 -->
				<view class="analysis-section">
					<text class="analysis-title">📊 关键指标</text>
					<view class="metrics-grid">
						<view class="metric-item">
							<text class="metric-value">{{loyaltyAnalysis.summary.totalCustomers}}</text>
							<text class="metric-label">总客户数</text>
						</view>
						<view class="metric-item">
							<text class="metric-value">¥{{loyaltyAnalysis.summary.avgCustomerValue}}</text>
							<text class="metric-label">客均价值</text>
						</view>
						<view class="metric-item">
							<text class="metric-value">{{loyaltyAnalysis.distribution.vip}}</text>
							<text class="metric-label">VIP客户</text>
						</view>
						<view class="metric-item">
							<text class="metric-value">{{loyaltyAnalysis.distribution.highRisk}}</text>
							<text class="metric-label">流失风险</text>
						</view>
					</view>
				</view>

				<!-- 客户分布 -->
				<view class="analysis-section">
					<text class="analysis-title">👥 客户分布</text>
					<view class="distribution-list">
						<view class="distribution-item">
							<view class="distribution-label">
								<text class="label-text">新客户</text>
								<text class="label-count">{{loyaltyAnalysis.distribution.new}}</text>
							</view>
							<view class="distribution-bar">
								<view class="bar-fill new-customer" :style="{width: (loyaltyAnalysis.distribution.new / loyaltyAnalysis.summary.totalCustomers * 100) + '%'}"></view>
							</view>
						</view>
						<view class="distribution-item">
							<view class="distribution-label">
								<text class="label-text">回头客</text>
								<text class="label-count">{{loyaltyAnalysis.distribution.returning}}</text>
							</view>
							<view class="distribution-bar">
								<view class="bar-fill returning-customer" :style="{width: (loyaltyAnalysis.distribution.returning / loyaltyAnalysis.summary.totalCustomers * 100) + '%'}"></view>
							</view>
						</view>
						<view class="distribution-item">
							<view class="distribution-label">
								<text class="label-text">忠诚客户</text>
								<text class="label-count">{{loyaltyAnalysis.distribution.loyal}}</text>
							</view>
							<view class="distribution-bar">
								<view class="bar-fill loyal-customer" :style="{width: (loyaltyAnalysis.distribution.loyal / loyaltyAnalysis.summary.totalCustomers * 100) + '%'}"></view>
							</view>
						</view>
						<view class="distribution-item">
							<view class="distribution-label">
								<text class="label-text">VIP客户</text>
								<text class="label-count">{{loyaltyAnalysis.distribution.vip}}</text>
							</view>
							<view class="distribution-bar">
								<view class="bar-fill vip-customer" :style="{width: (loyaltyAnalysis.distribution.vip / loyaltyAnalysis.summary.totalCustomers * 100) + '%'}"></view>
							</view>
						</view>
					</view>
				</view>

				<!-- 高价值客户 -->
				<view v-if="loyaltyAnalysis.topCustomers && loyaltyAnalysis.topCustomers.length > 0" class="analysis-section">
					<text class="analysis-title">💎 高价值客户 (前5名)</text>
					<view class="top-customers">
						<view v-for="(customer, index) in loyaltyAnalysis.topCustomers.slice(0, 5)" :key="index" class="customer-item">
							<view class="customer-rank">{{index + 1}}</view>
							<view class="customer-info">
								<text class="customer-id">{{customer.buyerId}}</text>
								<text class="customer-stats">{{customer.orderCount}}单 · ¥{{customer.totalAmount}}</text>
							</view>
							<view class="customer-type" :class="customer.customerType">{{getCustomerTypeLabel(customer.customerType)}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 数据导出按钮 -->
		<view class="action-buttons">
			<view class="action-btn export-btn" @click="exportData">
				<text class="action-btn-text">导出报表数据</text>
			</view>
			<view class="action-btn diamond-btn" @click="goToDiamondChart">
				<text class="action-btn-text">多维数据分析</text>
			</view>
		</view>
	</view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar.vue'
import {
	getReportStatistics,
	getMerchantBasicInfo,
	getPaymentMethodStats,
	getDailyIncomeStats,
	getHourlyStats,
	getCustomerStats,
	getCustomerLoyaltyAnalysis,
	exportReportData
} from '@/api/report'

export default {
	components: {
		CustomNavbar
	},
	data() {
		return {
			// 系统信息
			statusBarHeight: 20,
			// 加载状态
			loading: false,
			// 总体数据
			totalIncome: 0,
			totalOrders: 0,
			averageOrder: 0,
			incomeChange: 0,
			orderChange: 0,
			avgChange: 0,
			// 当前查询时间范围
			currentStartTime: '',
			currentEndTime: '',
			// 客户忠诚度详细数据
			loyaltyAnalysis: null,
			
			// 图表类型切换
			trendChartType: 'line',
			paymentChartType: 'pie',
			
			// 收入趋势图表数据
			trendData: {},
			trendOpts: {
				color: ["#5145F7", "#91CB74"],
				padding: [15, 10, 15, 10],
				enableScroll: false,
				enableMarkLine: false,
				dataLabel: true,
				dataPointShape: true,
				dataPointShapeType: 'solid',
				legend: {
					show: true,
					position: 'top',
					lineHeight: 25
				},
				xAxis: {
					boundaryGap: true,
					disableGrid: true,
					axisLine: true,
					fontSize: 10
				},
				yAxis: {
					gridType: "dash",
					dashLength: 2,
					data: [{
						min: 0
					}]
				},
				extra: {
					line: {
						type: "curve",
						width: 2,
						activeType: "hollow"
					},
					column: {
						width: 25,
						seriesGap: 5,
						categoryGap: 10,
						barBorderRadius: [3, 3, 0, 0],
						activeBgColor: "#000000",
						activeBgOpacity: 0.08
					},
					tooltip: {
						showBox: true,
						showArrow: true,
						showCategory: true,
						borderWidth: 0,
						borderRadius: 5,
						borderColor: "#000000",
						borderOpacity: 0.7,
						bgColor: "#000000",
						bgOpacity: 0.7,
						gridType: "solid",
						dashLength: 4,
						gridColor: "#CCCCCC",
						fontColor: "#FFFFFF",
						horizentalLine: false,
						xAxisLabel: false,
						yAxisLabel: false,
						labelBgColor: "#FFFFFF",
						labelBgOpacity: 0.7,
						labelFontColor: "#666666"
					}
				}
			},
			
			// 支付方式饼图数据
			paymentData: {},
			paymentOpts: {
				color: ["#4CD964", "#1890FF", "#9013FE", "#FF6B22"],
				padding: [15, 5, 15, 5],
				dataLabel: true,
				enableScroll: false,
				legend: {
					show: false
				},
				extra: {
					pie: {
						activeOpacity: 1,
						activeRadius: 10,
						offsetAngle: 0,
						labelWidth: 15,
						border: false,
						borderWidth: 3,
						borderColor: "#FFFFFF"
					},
					ring: {
						ringWidth: 30,
						activeOpacity: 1,
						activeRadius: 10,
						offsetAngle: 0,
						labelWidth: 15,
						border: false,
						borderWidth: 3,
						borderColor: "#FFFFFF"
					}
				}
			},
			
			// 热销商品数据
			productData: {},
			productOpts: {
				color: ["#1890FF"],
				padding: [15, 15, 15, 15],
				enableScroll: false,
				legend: {
					show: false
				},
				xAxis: {
					boundaryGap: "justify",
					disableGrid: true,
					min: 0,
					axisLine: false
				},
				yAxis: {},
				extra: {
					bar: {
						type: "group",
						width: 30,
						meterBorde: 1,
						meterFillColor: "#FFFFFF",
						activeBgColor: "#000000",
						activeBgOpacity: 0.08,
						barBorderRadius: [0, 3, 3, 0]
					}
				}
			},
			
			// 收入时段分布数据
			timeData: {},
			timeOpts: {
				color: ["#5145F7"],
				padding: [15, 10, 15, 10],
				enableScroll: false,
				legend: {
					show: false
				},
				xAxis: {
					disableGrid: true
				},
				yAxis: {
					gridType: "dash",
					dashLength: 2,
					data: [{
						min: 0
					}]
				},
				extra: {
					area: {
						type: "curve",
						opacity: 0.2,
						addLine: true,
						width: 2,
						gradient: true,
						activeType: "hollow"
					}
				}
			},
			
			// 客户忠诚度数据
			loyaltyData: {},
			loyaltyOpts: {
				color: ["#5145F7", "#FF6B22", "#4CD964"],
				padding: [15, 15, 0, 5],
				enableScroll: false,
				legend: {
					show: true,
					position: 'top',
					lineHeight: 25
				},
				xAxis: {
					disableGrid: true
				},
				yAxis: {
					data: [{
						min: 0
					}]
				},
				extra: {
					column: {
						type: "group",
						width: 30,
						activeBgColor: "#000000",
						activeBgOpacity: 0.08
					}
				}
			},
			
			// 客户数据
			newCustomers: 87,
			returningCustomers: 65,
			loyaltyRate: 42.8,
			
			// 支付方式数据
			paymentColors: ["#4CD964", "#1890FF", "#9013FE", "#FF6B22"],
			paymentMethods: [
				{ name: "微信支付", percentage: 30, amount: 12770.67 },
				{ name: "支付宝", percentage: 40, amount: 17027.56 },
				{ name: "云闪付", percentage: 20, amount: 8513.78 },
				{ name: "其他", percentage: 10, amount: 4256.89 }
			],
			
			// 时段洞察
			timeInsight: "您的高峰销售时段集中在11:00-14:00和17:00-20:00，建议在这些时段增加人手以提高服务效率。",

			// 当前显示的时间范围
			selectedMonth: '2025年6月'
		};
	},

	computed: {
		// 导航栏占位高度
		navbarPlaceholderHeight() {
			// 状态栏高度 + 导航栏高度(44px) - 与首页保持一致
			return this.statusBarHeight + 44;
		}
	},

	onReady() {
		// 初始化系统信息
		this.initSystemInfo();
		// 初始化时间范围（默认本月）
		this.initTimeRange();
		// 加载报表数据
		this.loadReportData();
	},

	onShow() {
		// 🔧 修复：页面显示时强制刷新图表，解决折线图不显示问题
		this.$nextTick(() => {
			// 强制触发图表重新渲染
			if (this.trendData && Object.keys(this.trendData).length > 0) {
				const tempData = JSON.parse(JSON.stringify(this.trendData));
				this.trendData = {};
				this.$nextTick(() => {
					this.trendData = tempData;
				});
			}
		});
	},
	methods: {
		// 初始化系统信息
		initSystemInfo() {
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight || 20;

			// 特殊处理iPhone 14 Pro Max等设备
			const model = systemInfo.model || '';
			const platform = systemInfo.platform || '';

			console.log('设备信息:', {
				model,
				platform,
				statusBarHeight: this.statusBarHeight,
				safeAreaInsets: systemInfo.safeAreaInsets
			});

			// iPhone 14 Pro Max的状态栏通常是47px
			if (model.includes('iPhone') && this.statusBarHeight > 40) {
				console.log('检测到iPhone Pro Max系列设备，状态栏高度:', this.statusBarHeight);
			}

			// 设置CSS变量，供样式使用
			const app = document.documentElement || document.body;
			if (app && app.style) {
				app.style.setProperty('--status-bar-height', this.statusBarHeight + 'px');
			}
		},

		// 初始化时间范围
		initTimeRange() {
			const now = new Date();
			const year = now.getFullYear();
			const month = now.getMonth() + 1;

			// 本月第一天
			this.currentStartTime = `${year}-${month.toString().padStart(2, '0')}-01`;
			// 今天
			this.currentEndTime = `${year}-${month.toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`;

			// 更新选择器显示
			this.selectedMonth = `${year}年${month}月`;
		},

		// 加载报表数据
		async loadReportData() {
			if (this.loading) return;

			this.loading = true;
			uni.showLoading({ title: '加载中...' });

			try {
				// 并行加载所有数据
				await Promise.all([
					this.loadBasicStats(),
					this.loadTrendData(),
					this.loadPaymentData(),
					this.loadTimeData(),
					this.loadCustomerData()
				]);

				console.log('📊 报表数据加载完成');
			} catch (error) {
				console.error('❌ 报表数据加载失败:', error);
				uni.showToast({
					title: '数据加载失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
				uni.hideLoading();
			}
		},

		// 加载基础统计数据
		async loadBasicStats() {
			try {
				const params = {
					starttime: this.currentStartTime,
					endtime: this.currentEndTime
				};

				const result = await getReportStatistics(params);
				if (result.code === 0) {
					const data = result.data;
					this.totalIncome = parseFloat(data.successMoney || 0);
					this.totalOrders = parseInt(data.successCount || 0);
					this.averageOrder = this.totalOrders > 0 ? (this.totalIncome / this.totalOrders) : 0;

					// 计算环比变化（这里简化处理，实际应该对比上期数据）
					this.incomeChange = Math.random() * 20 - 10; // 模拟数据
					this.orderChange = Math.random() * 15 - 5;
					this.avgChange = Math.random() * 10 - 5;

					console.log('📈 基础统计数据:', {
						totalIncome: this.totalIncome,
						totalOrders: this.totalOrders,
						averageOrder: this.averageOrder
					});
				}
			} catch (error) {
				console.error('❌ 加载基础统计失败:', error);
			}
		},

		// 获取客户类型标签
		getCustomerTypeLabel(type) {
			const labels = {
				'new': '新客户',
				'returning': '回头客',
				'loyal': '忠诚客户',
				'vip': 'VIP客户'
			};
			return labels[type] || '未知';
		},

		formatNumber(number) {
			return number.toLocaleString('zh', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
		},

		// 🎯 格式化百分比 - 保留2位小数
		formatPercentage(value) {
			if (value === null || value === undefined || isNaN(value)) {
				return '0.00%';
			}
			const formatted = parseFloat(value).toFixed(2);
			return `${parseFloat(formatted) >= 0 ? '+' : ''}${formatted}%`;
		},
		
		// 加载趋势数据
		async loadTrendData() {
			try {
				const result = await getDailyIncomeStats({
					starttime: this.currentStartTime,
					endtime: this.currentEndTime
				});

				if (result.code === 0) {
					const dailyData = result.data || [];
					const categories = dailyData.map(item => {
						const date = new Date(item.date);
						return `${date.getMonth() + 1}/${date.getDate()}`;
					});
					const amounts = dailyData.map(item => parseFloat(item.amount));

					// 生成对比数据（上期数据，这里简化处理）
					const lastPeriodAmounts = amounts.map(amount => amount * (0.8 + Math.random() * 0.4));

					const chartData = {
						categories,
						series: [
							{
								name: "本期收入",
								data: amounts
							},
							{
								name: "上期收入",
								data: lastPeriodAmounts
							}
						]
					};

					this.trendData = JSON.parse(JSON.stringify(chartData));
					console.log('📈 趋势数据加载完成:', this.trendData);
				}
			} catch (error) {
				console.error('❌ 加载趋势数据失败:', error);
			}
		},

		// 加载支付方式数据
		async loadPaymentData() {
			try {
				const result = await getPaymentMethodStats({
					starttime: this.currentStartTime,
					endtime: this.currentEndTime
				});

				if (result.code === 0) {
					const paymentData = result.data || [];
					const totalAmount = paymentData.reduce((sum, item) => sum + item.amount, 0);

					// 更新支付方式统计
					this.paymentMethods = paymentData.map(item => ({
						name: item.name,
						amount: item.amount,
						percentage: totalAmount > 0 ? ((item.amount / totalAmount) * 100).toFixed(1) : 0
					}));

					// 生成图表数据
					const chartData = {
						series: [{
							data: this.paymentMethods.map(item => ({
								name: item.name,
								value: parseFloat(item.percentage)
							}))
						}]
					};

					this.paymentData = JSON.parse(JSON.stringify(chartData));
					console.log('💳 支付方式数据加载完成:', this.paymentData);
				}
			} catch (error) {
				console.error('❌ 加载支付方式数据失败:', error);
			}
		},

		showDatePicker() {
			// 显示月份选择器
			const currentDate = new Date();
			const currentYear = currentDate.getFullYear();
			const currentMonth = currentDate.getMonth() + 1;

			// 生成快速选择选项
			const quickOptions = [
				`${currentYear}年${currentMonth}月 (本月)`,
				`${currentMonth === 1 ? currentYear - 1 : currentYear}年${currentMonth === 1 ? 12 : currentMonth - 1}月 (上月)`,
				`${currentYear - 1}年${currentMonth}月 (去年同期)`,
				`${currentYear}年全年`,
				'自定义选择...'
			];

			uni.showActionSheet({
				itemList: quickOptions,
				success: (res) => {
					if (res.tapIndex === 0) {
						// 本月
						this.selectMonth(currentYear, currentMonth);
					} else if (res.tapIndex === 1) {
						// 上月
						const lastMonth = currentMonth === 1 ? 12 : currentMonth - 1;
						const lastYear = currentMonth === 1 ? currentYear - 1 : currentYear;
						this.selectMonth(lastYear, lastMonth);
					} else if (res.tapIndex === 2) {
						// 去年同期
						this.selectMonth(currentYear - 1, currentMonth);
					} else if (res.tapIndex === 3) {
						// 本年
						this.selectYear(currentYear);
					} else if (res.tapIndex === 4) {
						// 自定义选择
						this.showCustomDatePicker();
					}
				}
			});
		},

		showCustomDatePicker() {
			// 显示自定义年月选择
			const currentDate = new Date();
			const currentYear = currentDate.getFullYear();

			// 生成年份选项 (最近5年)
			const yearOptions = [];
			for (let i = currentYear - 2; i <= currentYear + 2; i++) {
				yearOptions.push(`${i}年`);
			}

			uni.showActionSheet({
				itemList: yearOptions,
				success: (res) => {
					const selectedYear = currentYear - 2 + res.tapIndex;
					// 选择月份
					const monthOptions = [
						'1月', '2月', '3月', '4月', '5月', '6月',
						'7月', '8月', '9月', '10月', '11月', '12月'
					];

					uni.showActionSheet({
						itemList: monthOptions,
						success: (monthRes) => {
							const selectedMonth = monthRes.tapIndex + 1;
							this.selectMonth(selectedYear, selectedMonth);
						}
					});
				}
			});
		},



		selectMonth(year, month) {
			// 更新时间范围
			this.currentStartTime = `${year}-${month.toString().padStart(2, '0')}-01`;

			// 计算月末日期
			const lastDay = new Date(year, month, 0).getDate();
			this.currentEndTime = `${year}-${month.toString().padStart(2, '0')}-${lastDay.toString().padStart(2, '0')}`;

			// 更新显示
			this.selectedMonth = `${year}年${month}月`;

			console.log('📅 选择时间范围:', {
				year,
				month,
				start: this.currentStartTime,
				end: this.currentEndTime
			});

			// 重新加载数据
			this.loadReportData();

			uni.showToast({
				title: `已切换到${year}年${month}月`,
				icon: 'success'
			});
		},

		selectYear(year) {
			// 选择整年数据
			this.currentStartTime = `${year}-01-01`;
			this.currentEndTime = `${year}-12-31`;
			this.selectedMonth = `${year}年全年`;

			console.log('📅 选择年度范围:', {
				year,
				start: this.currentStartTime,
				end: this.currentEndTime
			});

			// 重新加载数据
			this.loadReportData();

			uni.showToast({
				title: `已切换到${year}年全年`,
				icon: 'success'
			});
		},
		
		// 加载时段数据
		async loadTimeData() {
			try {
				const result = await getHourlyStats({
					starttime: this.currentStartTime,
					endtime: this.currentEndTime
				});

				if (result.code === 0) {
					const hourlyData = result.data || [];
					const categories = hourlyData.map(item => item.hourLabel);
					const amounts = hourlyData.map(item => parseFloat(item.amount));

					const chartData = {
						categories,
						series: [{
							name: "收入金额",
							data: amounts
						}]
					};

					this.timeData = JSON.parse(JSON.stringify(chartData));

					// 生成时段洞察
					const maxHour = hourlyData.reduce((max, item) =>
						parseFloat(item.amount) > parseFloat(max.amount) ? item : max, hourlyData[0] || {});
					this.timeInsight = `您的高峰销售时段是${maxHour.hourLabel || '未知'}，建议在此时段加强服务。`;

					console.log('⏰ 时段数据加载完成:', this.timeData);
				}
			} catch (error) {
				console.error('❌ 加载时段数据失败:', error);
			}
		},

		// 加载客户数据
		async loadCustomerData() {
			try {
				// 并行加载基础客户统计和详细忠诚度分析
				const [basicResult, loyaltyResult] = await Promise.all([
					getCustomerStats({
						starttime: this.currentStartTime,
						endtime: this.currentEndTime
					}),
					getCustomerLoyaltyAnalysis({
						starttime: this.currentStartTime,
						endtime: this.currentEndTime
					})
				]);

				// 处理基础统计数据
				if (basicResult.code === 0) {
					const data = basicResult.data;
					this.newCustomers = data.newCustomers || 0;
					this.returningCustomers = data.returningCustomers || 0;
					this.loyaltyRate = data.loyaltyRate || 0;

					// 生成客户忠诚度图表数据
					const chartData = {
						categories: ["新客户", "回头客", "忠诚客户", "VIP客户"],
						series: [{
							name: "客户数量",
							data: [
								data.newCustomers || 0,
								data.returningCustomers || 0,
								data.loyalCustomers || 0,
								data.highValueCustomers || 0
							]
						}]
					};

					this.loyaltyData = JSON.parse(JSON.stringify(chartData));
				}

				// 处理详细忠诚度分析数据
				if (loyaltyResult.code === 0) {
					this.loyaltyAnalysis = loyaltyResult.data;
					console.log('🎯 客户忠诚度详细分析:', this.loyaltyAnalysis);

					// 更新忠诚度相关指标
					if (this.loyaltyAnalysis.summary) {
						this.loyaltyRate = parseFloat(this.loyaltyAnalysis.summary.retentionRate);
					}
				}

				console.log('👥 客户数据加载完成:', {
					basic: this.loyaltyData,
					analysis: this.loyaltyAnalysis
				});
			} catch (error) {
				console.error('❌ 加载客户数据失败:', error);
			}
		},

		shareReport() {
			// 实现报表分享功能
			uni.showToast({
				title: '分享报表功能',
				icon: 'none'
			});
		},

		async exportData() {
			try {
				uni.showLoading({ title: '导出中...' });
				const result = await exportReportData({
					starttime: this.currentStartTime,
					endtime: this.currentEndTime
				});

				if (result.code === 0) {
					uni.showToast({
						title: '导出成功',
						icon: 'success'
					});
				} else {
					uni.showToast({
						title: result.msg || '导出失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('❌ 导出失败:', error);
				uni.showToast({
					title: '导出失败',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},
		
		goToDiamondChart() {
			uni.navigateTo({
				url: '/pages/report/diamond'
			});
		},
		
		getTrendData() {
			// 模拟收入趋势数据
			setTimeout(() => {
				let res;
				if (this.trendChartType === 'column') {
					res = {
						categories: ["1日", "5日", "10日", "15日", "20日", "25日", "30日"],
						series: [
							{
								name: "本月收入",
								data: [3800, 4200, 5600, 6900, 4800, 3500, 6300]
							},
							{
								name: "上月收入",
								data: [3200, 3600, 4800, 5900, 4200, 3300, 5800]
							}
						]
					};
				} else {
					res = {
						categories: ["1日", "5日", "10日", "15日", "20日", "25日", "30日"],
						series: [
							{
								name: "本月收入",
								data: [3800, 4200, 5600, 6900, 4800, 3500, 6300]
							},
							{
								name: "上月收入",
								data: [3200, 3600, 4800, 5900, 4200, 3300, 5800]
							}
						]
					};
				}
				this.trendData = JSON.parse(JSON.stringify(res));
			}, 500);
		},
		
		getPaymentData() {
			// 模拟支付方式分析数据
			setTimeout(() => {
				let res = {
					series: [{
						data: this.paymentMethods.map(item => {
							return {
								name: item.name,
								value: item.percentage
							};
						})
					}]
				};
				this.paymentData = JSON.parse(JSON.stringify(res));
			}, 500);
		},
		
		// 热销商品数据 - 暂时注释，待开发
		/*
		getProductData() {
			// 模拟热销商品数据
			setTimeout(() => {
				let res = {
					categories: ["黑咖啡", "拿铁", "卡布奇诺", "冰美式", "抹茶拿铁"],
					series: [{
						name: "销售量",
						data: [235, 187, 165, 134, 122]
					}]
				};
				this.productData = JSON.parse(JSON.stringify(res));
			}, 500);
		},
		*/
		
		getTimeData() {
			// 模拟收入时段分布数据
			setTimeout(() => {
				let res = {
					categories: ["8:00", "10:00", "12:00", "14:00", "16:00", "18:00", "20:00", "22:00"],
					series: [{
						name: "收入",
						data: [2300, 5600, 7800, 4500, 3900, 6700, 8100, 3200]
					}]
				};
				this.timeData = JSON.parse(JSON.stringify(res));
			}, 500);
		},
		
		getLoyaltyData() {
			// 模拟客户忠诚度数据
			setTimeout(() => {
				let res = {
					categories: ["客户类型"],
					series: [
						{
							name: "新客户",
							data: [this.newCustomers]
						},
						{
							name: "回头客",
							data: [this.returningCustomers]
						}
					]
				};
				this.loyaltyData = JSON.parse(JSON.stringify(res));
			}, 500);
		},
		

		
		// 切换图表类型时刷新图表
		setTrendChartType(type) {
			if (this.trendChartType === type) return;

			// 设置新的图表类型
			this.trendChartType = type;

			// 🔧 修复：强制刷新图表数据
			this.$nextTick(() => {
				this.loadTrendData();
			});
		},
		
		setPaymentChartType(type) {
			if (this.paymentChartType === type) return;

			// 设置新的图表类型
			this.paymentChartType = type;

			// 🔧 修复：强制刷新图表数据
			this.$nextTick(() => {
				this.loadPaymentData();
			});
		},
		
		onTrendChartClick(e) {
			console.log('图表点击', e);
			const {
				type,
				series,
				categories,
				index
			} = e;
			
			// 点击时显示数据
			uni.showToast({
				title: `${categories[index]}：${series[0].data[index]}`,
				icon: 'none'
			});
		}
	}
};
</script>

<style lang="scss" scoped>
page {
  font-family: 'Segoe UI', sans-serif;
  background-color: #f5f5f5;
}

.container {
  width: 100%;
  position: relative;
  min-height: 100vh;
  padding-bottom: 32rpx;
  background-color: #f5f5f5;
}

/* 优雅现代导航栏样式 */
.elegant-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 50%, #8B5CF6 100%);
  box-shadow: 0 4rpx 20rpx rgba(81, 69, 247, 0.15);
}

.status-bar {
  width: 100%;
}

.navbar-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 32rpx;
  height: 88rpx; /* 固定高度，与首页保持一致 */
}

.navbar-title-section {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.page-title {
  font-size: 36rpx;
  color: #fff;
  font-weight: 600;
  margin-bottom: 4rpx;
  letter-spacing: 0.5rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.date-picker-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.date-picker-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}

.date-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.date-icon {
  font-size: 32rpx;
  line-height: 1;
}

/* 导航栏占位 - 自适应各种设备 */
.navbar-placeholder {
  width: 100%;
  /* 针对iPhone 14 Pro Max等设备的特殊处理 */
  height: calc(var(--status-bar-height, 47px) + 44px);
  /* 使用安全区域，兼容刘海屏和动态岛 */
  height: calc(env(safe-area-inset-top, 47px) + 44px);
  /* 最小高度保证，适配iPhone 14 Pro Max */
  min-height: 91px;
  flex-shrink: 0;
}

/* 针对不同平台的适配 */
/* #ifdef H5 */
.navbar-placeholder {
  /* H5环境，针对iPhone 14 Pro Max的动态岛 */
  height: calc(env(safe-area-inset-top, 47px) + 44px);
  min-height: 91px;
}
/* #endif */

/* #ifdef MP */
.navbar-placeholder {
  height: calc(var(--status-bar-height, 47px) + 44px);
  min-height: 91px;
}
/* #endif */

/* #ifdef APP-PLUS */
.navbar-placeholder {
  height: calc(var(--status-bar-height, 47px) + 44px);
  min-height: 91px;
}
/* #endif */

.icon-calendar, .icon-share {
	width: 40rpx;
	height: 40rpx;
}

/* 时间范围显示 */
.time-range-display {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 32rpx;
	background-color: #fff;
	margin-bottom: 16rpx;
}

.time-range-label {
	font-size: 30rpx;
	color: #666;
}

.time-range-value {
	font-size: 30rpx;
	color: #5145F7;
	font-weight: 500;
	padding: 8rpx 16rpx;
	background: linear-gradient(135deg, #5145F7 0%, #7B68EE 100%);
	color: white;
	border-radius: 20rpx;
}

.icon-arrow {
	width: 24rpx;
	height: 24rpx;
}

/* 数据摘要卡片 */
.summary-card {
	margin: 24rpx;
	background-color: white;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

.data-grid {
	display: flex;
	width: 100%;
}

.data-item {
	flex: 1;
	padding: 24rpx 0;
	text-align: center;
	position: relative;
}

.data-item:not(:last-child):after {
	content: '';
	position: absolute;
	right: 0;
	top: 20%;
	height: 60%;
	width: 1rpx;
	background-color: #f0f0f0;
}

.data-value {
	font-size: 34rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.data-label {
	font-size: 26rpx;
	color: #999;
	display: block;
	margin-bottom: 8rpx;
}

.data-change {
	font-size: 22rpx;
	display: block;
}

.data-change.up {
	color: #4CD964;
}

.data-change.down {
	color: #FF3B30;
}

/* 卡片通用样式 */
.card {
	margin: 24rpx;
	padding: 24rpx 12rpx;
	background-color: white;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	width: auto;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
	padding: 0 12rpx;
}

.card-actions {
	display: flex;
	gap: 16rpx;
}

.toggle-btn {
	font-size: 24rpx;
	color: #999;
	padding: 6rpx 16rpx;
	border-radius: 100rpx;
	background-color: #f5f5f5;
}

.toggle-btn.active {
	color: white;
	background-color: #5145F7;
}

.section-title {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
}

/* 图表容器 */
.chart-container {
	height: 500rpx;
	width: 100%;
	box-sizing: border-box;
	overflow: hidden;
	padding: 0;
	margin: 0;
}

/* 支付方式分析卡片 */
.payment-total {
	text-align: center;
	font-size: 28rpx;
	color: #666;
	margin: 16rpx 0;
}

.payment-legend {
	margin-top: 24rpx;
}

.legend-item {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.legend-color {
	width: 24rpx;
	height: 24rpx;
	border-radius: 6rpx;
	margin-right: 12rpx;
}

.legend-name {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.legend-percent {
	font-size: 28rpx;
	color: #666;
	margin-right: 24rpx;
}

.legend-amount {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

/* 洞察卡片 */
.insight {
	display: flex;
	align-items: flex-start;
	background-color: rgba(81, 69, 247, 0.05);
	padding: 16rpx;
	border-radius: 8rpx;
	margin-top: 16rpx;
}

.insight-icon {
	font-size: 32rpx;
	margin-right: 12rpx;
}

.insight-text {
	font-size: 26rpx;
	color: #666;
	flex: 1;
	line-height: 1.5;
}

/* 客户统计 */
.customer-stats {
	display: flex;
	margin-top: 24rpx;
	border-top: 1rpx solid #f0f0f0;
	padding-top: 24rpx;
}

.customer-stat-item {
	flex: 1;
	text-align: center;
}

.customer-stat-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.customer-stat-label {
	font-size: 26rpx;
	color: #999;
}

/* 客户忠诚度详细分析样式 */
.loyalty-analysis {
	margin-top: 32rpx;
}

.analysis-section {
	margin-bottom: 40rpx;
}

.analysis-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
	display: block;
}

.metrics-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 24rpx;
}

.metric-item {
	background: #f8f9fa;
	padding: 24rpx;
	border-radius: 12rpx;
	text-align: center;
}

.metric-value {
	font-size: 36rpx;
	font-weight: 600;
	color: #5145F7;
	display: block;
	margin-bottom: 8rpx;
}

.metric-label {
	font-size: 24rpx;
	color: #666;
}

.distribution-list {
	space-y: 20rpx;
}

.distribution-item {
	margin-bottom: 20rpx;
}

.distribution-label {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.label-text {
	font-size: 28rpx;
	color: #333;
}

.label-count {
	font-size: 28rpx;
	font-weight: 600;
	color: #5145F7;
}

.distribution-bar {
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
}

.bar-fill {
	height: 100%;
	border-radius: 4rpx;
	transition: width 0.3s ease;
}

.new-customer {
	background: #FF6B22;
}

.returning-customer {
	background: #1890FF;
}

.loyal-customer {
	background: #4CD964;
}

.vip-customer {
	background: #9013FE;
}

.top-customers {
	space-y: 16rpx;
}

.customer-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
}

.customer-rank {
	width: 48rpx;
	height: 48rpx;
	background: #5145F7;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: 600;
	margin-right: 20rpx;
}

.customer-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.customer-id {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 4rpx;
}

.customer-stats {
	font-size: 24rpx;
	color: #666;
}

.customer-type {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	color: white;
	font-weight: 500;
}

.customer-type.new {
	background: #FF6B22;
}

.customer-type.returning {
	background: #1890FF;
}

.customer-type.loyal {
	background: #4CD964;
}

.customer-type.vip {
	background: #9013FE;
}

/* 导出按钮 */
.action-buttons {
	margin: 24rpx;
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	border-radius: 8rpx;
	text-align: center;
	padding: 24rpx 0;
}

.export-btn {
	background-color: #5145F7;
}

.diamond-btn {
	background-color: #1890FF;
}

.action-btn-text {
	color: white;
	font-size: 30rpx;
	font-weight: 500;
}
</style> 