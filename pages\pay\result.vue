<template>
  <view class="container">
    <custom-navbar title="支付结果" :shadow="true"></custom-navbar>
    
    <view class="result-content">
      <!-- 成功状态 -->
      <template v-if="status === 'success'">
        <view class="icon-wrapper success">
          <text class="iconfont icon-success">✓</text>
        </view>
        <view class="result-title">支付成功</view>
        <view class="result-desc">您的订单已支付成功</view>
        
        <!-- 订单信息卡片 - 直接显示 -->
        <view class="order-info">
          <view class="info-item">
            <text class="label">订单金额</text>
            <text class="value">¥{{ orderInfo?.amount || '--' }}</text>
          </view>
          <view class="info-item" v-if="orderInfo?.payMethod">
            <text class="label">支付方式</text>
            <text class="value">{{ orderInfo?.payMethod || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="label">商户名称</text>
            <text class="value">{{ orderInfo?.merchantName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="label">订单编号</text>
            <text class="value">{{ orderInfo?.orderId || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="label">支付时间</text>
            <text class="value">{{ orderInfo?.payTime || '--' }}</text>
          </view>
        </view>
      </template>
      
      <!-- 失败状态 -->
      <template v-else-if="status === 'fail'">
        <view class="icon-wrapper fail">
          <text class="iconfont icon-fail">✕</text>
        </view>
        <view class="result-title">支付失败</view>
        <view class="result-desc">{{ failReason || '支付未完成，请重新支付' }}</view>
      </template>
      
      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="btn secondary" @tap="goBack">
          {{ status === 'success' ? '关闭' : '重新支付' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      orderId: '',
      status: 'success', // 'success' 或 'fail'
      failReason: '',
      orderInfo: null
    }
  },
  onLoad(options) {
    console.log('支付结果页面参数:', options)

    // 解析URL参数
    if (options.orderId) {
      this.orderId = options.orderId;
    }

    if (options.status) {
      this.status = options.status;
    }

    if (options.reason || options.message) {
      this.failReason = decodeURIComponent(options.reason || options.message);
    }

    // 处理付款码支付的参数
    if (options.amount || options.payMethod || options.tradeNo) {
      this.orderInfo = {
        amount: options.amount || '--',
        merchantName: '商户收银台',
        orderId: options.tradeNo || '--',
        payTime: new Date().toLocaleString(),
        payMethod: options.payMethod || '--'
      }
    }

    // 如果有订单ID且状态为成功，加载订单详情
    if (this.orderId && this.status === 'success' && !this.orderInfo) {
      this.loadOrderDetail();
    }
  },
  methods: {
    // 加载订单详情
    loadOrderDetail() {
      uni.showLoading({
        title: '加载中...'
      });
      
      // 直接导入订单API
      const orderApi = require('@/api/order');
      const getOrder = orderApi.getOrder || orderApi.default?.getOrder;

      // 使用新版API获取订单详情
      getOrder(this.orderId)
        .then(res => {
          if (res.code === 0) {
            // 映射新版API的字段到当前组件的数据结构
            this.orderInfo = {
              amount: res.money || '--',
              merchantName: res.name || '--', // 商品名称作为商户名称
              orderId: res.trade_no || this.orderId,
              payTime: res.endtime || '--'
            };
          } else {
            uni.showToast({
              title: res.msg || '获取订单详情失败',
              icon: 'none'
            });
          }
        })
        .catch(() => {
          uni.showToast({
            title: '网络异常，请重试',
            icon: 'none'
          });
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    
    // 返回或重新支付
    goBack() {
      if (this.status === 'success') {
        // 支付成功，返回收银台页面
        uni.redirectTo({
          url: '/pages/pay/mini-payment'
        });
      } else {
        // 如果支付失败，返回上一页（支付页面）
        uni.navigateBack();
      }
    }
  }
}
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.result-content {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon-wrapper {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
  margin-top: 60rpx;
}

.icon-wrapper.success {
  background-color: #52c41a;
}

.icon-wrapper.fail {
  background-color: #f5222d;
}

.iconfont {
  font-size: 80rpx;
  color: #fff;
}

.result-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.result-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
}

.order-info {
  width: 100%;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 60rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  color: #666;
  font-size: 28rpx;
}

.value {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

.action-buttons {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.btn {
  width: 100%;
  padding: 20rpx 0;
  border-radius: 10rpx;
  font-size: 32rpx;
}

.btn.primary {
  background-color: #5145F7;
  color: #fff;
}

.btn.secondary {
  background-color: #fff;
  color: #333;
  border: 1px solid #ddd;
}
</style> 