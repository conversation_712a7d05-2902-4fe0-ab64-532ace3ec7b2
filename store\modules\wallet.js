// store/modules/wallet.js - 钱包模块状态管理
import api from '@/api';

// 初始状态
const state = {
  // 账户余额
  balance: '0.00',
  // 转账手续费率
  transferRate: '0',
  // 余额加载状态
  balanceLoading: false,
  // 银行卡列表
  bankCards: []
};

// 修改状态的方法
const mutations = {
  // 设置余额
  SET_BALANCE(state, { balance, transferRate }) {
    state.balance = balance;
    state.transferRate = transferRate;
  },
  // 设置余额加载状态
  SET_BALANCE_LOADING(state, loading) {
    state.balanceLoading = loading;
  }
};

// 异步操作
const actions = {
  // 获取余额
  async getBalance({ commit }) {
    try {
      commit('SET_BALANCE_LOADING', true);
      
      const response = await api.pay.getBalance();
      
      if (response.code === 0) {
        commit('SET_BALANCE', {
          balance: response.available_money || '0.00',
          transferRate: response.transfer_rate || '0'
        });
        return response;
      }
      
      return Promise.reject(new Error(response.msg || '获取余额失败'));
    } catch (error) {
      return Promise.reject(error);
    } finally {
      commit('SET_BALANCE_LOADING', false);
    }
  }
};

// 获取状态的方法
const getters = {
  balance: state => state.balance,
  transferRate: state => state.transferRate,
  balanceLoading: state => state.balanceLoading,
  
  // 格式化余额（千分位显示）
  formattedBalance: state => {
    return parseFloat(state.balance).toLocaleString('zh-CN', { 
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}; 