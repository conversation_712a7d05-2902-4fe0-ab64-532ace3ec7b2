module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es6: true
  },
  extends: [
    'plugin:vue/essential',
    '@vue/standard'
  ],
  parserOptions: {
    parser: 'babel-eslint',
    ecmaVersion: 2020,
    sourceType: 'module'
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'indent': ['error', 2],
    'quotes': ['error', 'single'],
    'semi': ['error', 'never'],
    'comma-dangle': ['error', 'never'],
    'space-before-function-paren': ['error', 'always'],
    'no-unused-vars': ['warn', { 'args': 'none' }],
    'vue/multi-word-component-names': 'off',
    'vue/no-v-model-argument': 'off',
    'vue/require-default-prop': 'off',
    'vue/no-multiple-template-root': 'off',
    'vue/no-v-for-template-key': 'off',
    'vue/no-v-html': 'off',
    'vue/valid-v-model': 'off',
    'vue/no-mutating-props': 'off',
    'vue/no-side-effects-in-computed-properties': 'off',
    'vue/no-unused-components': 'warn',
    'vue/no-unused-vars': 'warn',
    'vue/require-v-for-key': 'warn',
    'vue/valid-template-root': 'warn',
    'vue/valid-v-bind': 'warn',
    'vue/valid-v-on': 'warn',
    'vue/valid-v-slot': 'warn'
  },
  globals: {
    uni: true,
    wx: true,
    getApp: true,
    getCurrentPages: true
  }
} 