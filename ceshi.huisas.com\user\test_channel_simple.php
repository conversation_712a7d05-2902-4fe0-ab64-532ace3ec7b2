<?php
/**
 * 简单的支付通道配置测试（无需登录）
 */
include("../includes/common.php");

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: text/html; charset=utf-8');

if($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 使用您的商户ID（从截图看到是mike11，UID应该是1000）
$test_uid = 1000;

// 获取商户信息
$userrow = $DB->getRow("SELECT * FROM pre_user WHERE uid='$test_uid' LIMIT 1");
if(!$userrow) {
    echo "<h2>错误</h2>";
    echo "<p>商户ID $test_uid 不存在</p>";
    exit();
}

echo "<h2>商户支付通道配置检查</h2>";
echo "<p>商户ID: {$userrow['uid']}</p>";
echo "<p>商户账号: {$userrow['user']}</p>";
echo "<p>商户组: {$userrow['gid']}</p>";
echo "<p>商户状态: " . ($userrow['status'] == 1 ? '正常' : '禁用') . "</p>";

// 检查支付方式
echo "<h3>支付方式配置:</h3>";
$types = $DB->getAll("SELECT * FROM pre_type ORDER BY id");
foreach($types as $type) {
    echo "<p>ID: {$type['id']}, 名称: {$type['name']}, 显示名: {$type['showname']}, 状态: " . ($type['status'] == 1 ? '启用' : '禁用') . "</p>";
}

// 检查支付通道
echo "<h3>支付通道配置:</h3>";
$channels = $DB->getAll("SELECT * FROM pre_channel WHERE status=1 ORDER BY id");
if(empty($channels)) {
    echo "<p style='color:red;'>没有启用的支付通道！</p>";
} else {
    foreach($channels as $channel) {
        echo "<p>通道ID: {$channel['id']}, 名称: {$channel['name']}, 插件: {$channel['plugin']}, 支付方式: {$channel['type']}</p>";
    }
}

// 测试各支付方式的通道获取
echo "<h3>支付方式通道测试:</h3>";
$test_types = ['alipay', 'wxpay', 'qqpay', 'bank'];
foreach($test_types as $pay_type) {
    try {
        $submitData = \lib\Channel::submit($pay_type, $userrow['uid'], $userrow['gid'], 1.00);
        if($submitData) {
            echo "<p style='color:green;'>{$pay_type}: 可用 - 通道ID: {$submitData['channel']}, 插件: {$submitData['plugin']}</p>";
        } else {
            echo "<p style='color:red;'>{$pay_type}: 不可用 - 无可用通道</p>";
        }
    } catch(Exception $e) {
        echo "<p style='color:red;'>{$pay_type}: 错误 - {$e->getMessage()}</p>";
    }
}

// 检查商户组配置
echo "<h3>商户组配置:</h3>";
if($userrow['gid'] > 0) {
    $groupinfo = $DB->getColumn("SELECT info FROM pre_group WHERE gid='{$userrow['gid']}' LIMIT 1");
    if($groupinfo) {
        $info = json_decode($groupinfo, true);
        echo "<pre>" . print_r($info, true) . "</pre>";
    } else {
        echo "<p>商户组无特殊配置</p>";
    }
} else {
    echo "<p>使用默认商户组</p>";
}

// 检查插件配置
echo "<h3>插件配置:</h3>";
$plugins = $DB->getAll("SELECT * FROM pre_plugin ORDER BY name");
foreach($plugins as $plugin) {
    echo "<p>插件: {$plugin['name']}, 显示名: {$plugin['showname']}, 支持类型: {$plugin['types']}</p>";
}
?>
