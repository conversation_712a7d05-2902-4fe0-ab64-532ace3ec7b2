<?php
/**
 * WebSocket服务监控页面
 * 管理后台 - WebSocket服务状态监控
 */

include("../includes/common.php");
$title='WebSocket服务监控';
include './head.php';

// 引入WebSocket通知模块
require_once '../includes/websocket_notify_workerman.php';

// 处理AJAX请求
if(isset($_GET['act'])){
    switch($_GET['act']){
        case 'status':
            // 获取服务状态
            $notifier = new WebSocketNotifyWorkerman();
            $isRunning = $notifier->checkServiceStatus();
            $stats = $notifier->getServiceStats();
            
            $result = [
                'status' => $isRunning ? 'running' : 'stopped',
                'stats' => $stats,
                'timestamp' => time()
            ];
            
            header('Content-Type: application/json');
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            exit;
            
        case 'test':
            // 发送测试通知
            $notifier = new WebSocketNotifyWorkerman();
            $result = $notifier->sendTestNotification();
            
            header('Content-Type: application/json');
            echo json_encode(['success' => $result], JSON_UNESCAPED_UNICODE);
            exit;
            
        case 'restart':
            // 重启服务
            if(!checkPermission('admin')){
                echo json_encode(['success' => false, 'message' => '权限不足']);
                exit;
            }
            
            $command = 'cd /www/wwwroot/ceshi.huisas.com/websocket_workerman && php basic_server.php restart > /dev/null 2>&1 &';
            exec($command);
            
            sleep(2); // 等待服务重启
            
            $notifier = new WebSocketNotifyWorkerman();
            $isRunning = $notifier->checkServiceStatus();
            
            header('Content-Type: application/json');
            echo json_encode(['success' => $isRunning], JSON_UNESCAPED_UNICODE);
            exit;
    }
}

// 获取初始状态
$notifier = new WebSocketNotifyWorkerman();
$isRunning = $notifier->checkServiceStatus();
$stats = $notifier->getServiceStats();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fa fa-wifi"></i> WebSocket服务监控
                        <span id="service-status" class="badge <?php echo $isRunning ? 'badge-success' : 'badge-danger'; ?>">
                            <?php echo $isRunning ? '运行中' : '已停止'; ?>
                        </span>
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- 服务状态概览 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="current-connections"><?php echo $stats['current_connections'] ?? 0; ?></h4>
                                            <p class="mb-0">当前连接数</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="total-requests"><?php echo $stats['total_requests'] ?? 0; ?></h4>
                                            <p class="mb-0">总请求数</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-exchange fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="payment-notifications"><?php echo $stats['payment_notifications'] ?? 0; ?></h4>
                                            <p class="mb-0">支付通知数</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-bell fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="uptime"><?php echo $stats['uptime_formatted'] ?? '00:00:00'; ?></h4>
                                            <p class="mb-0">运行时间</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-clock-o fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 服务控制 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>服务控制</h5>
                                </div>
                                <div class="card-body">
                                    <button type="button" class="btn btn-primary" onclick="refreshStatus()">
                                        <i class="fa fa-refresh"></i> 刷新状态
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="sendTestNotification()">
                                        <i class="fa fa-paper-plane"></i> 发送测试通知
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="runDiagnostics()">
                                        <i class="fa fa-stethoscope"></i> 运行诊断
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="restartService()">
                                        <i class="fa fa-refresh"></i> 重启服务
                                    </button>
                                    <a href="http://ceshi.huisas.com:8081/" target="_blank" class="btn btn-info">
                                        <i class="fa fa-external-link"></i> API状态
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 详细统计信息 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>服务信息</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <td>WebSocket地址</td>
                                            <td>ws://ceshi.huisas.com:8081</td>
                                        </tr>
                                        <tr>
                                            <td>HTTP API地址</td>
                                            <td>http://ceshi.huisas.com:8081</td>
                                        </tr>
                                        <tr>
                                            <td>框架</td>
                                            <td>Workerman</td>
                                        </tr>
                                        <tr>
                                            <td>PHP版本</td>
                                            <td><?php echo PHP_VERSION; ?></td>
                                        </tr>
                                        <tr>
                                            <td>内存使用</td>
                                            <td id="memory-usage"><?php echo formatBytes($stats['memory_usage'] ?? 0); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>实时日志</h5>
                                </div>
                                <div class="card-body">
                                    <div id="log-container" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                                        <div class="text-muted">正在加载日志...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- WebSocket连接测试 -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>WebSocket连接测试</h5>
                                </div>
                                <div class="card-body">
                                    <button type="button" class="btn btn-primary" onclick="testWebSocketConnection()">
                                        <i class="fa fa-plug"></i> 测试WebSocket连接
                                    </button>
                                    <div id="websocket-test-result" class="mt-3"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 自动刷新状态
let autoRefreshInterval;

$(document).ready(function() {
    // 启动自动刷新
    autoRefreshInterval = setInterval(refreshStatus, 10000); // 每10秒刷新一次
    
    // 初始化日志显示
    loadRecentLogs();
});

// 刷新服务状态
function refreshStatus() {
    $.get('?act=status', function(data) {
        if (data.status === 'running') {
            $('#service-status').removeClass('badge-danger').addClass('badge-success').text('运行中');
        } else {
            $('#service-status').removeClass('badge-success').addClass('badge-danger').text('已停止');
        }
        
        if (data.stats) {
            $('#current-connections').text(data.stats.current_connections || 0);
            $('#total-requests').text(data.stats.total_requests || 0);
            $('#payment-notifications').text(data.stats.payment_notifications || 0);
            $('#uptime').text(data.stats.uptime_formatted || '00:00:00');
            $('#memory-usage').text(formatBytes(data.stats.memory_usage || 0));
        }
    }).fail(function() {
        $('#service-status').removeClass('badge-success').addClass('badge-danger').text('连接失败');
    });
}

// 发送测试通知
function sendTestNotification() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 发送中...';
    btn.disabled = true;
    
    $.get('?act=test', function(data) {
        if (data.success) {
            layer.msg('测试通知发送成功！', {icon: 1});
        } else {
            layer.msg('测试通知发送失败！', {icon: 2});
        }
    }).fail(function() {
        layer.msg('请求失败！', {icon: 2});
    }).always(function() {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 重启服务
function restartService() {
    layer.confirm('确定要重启WebSocket服务吗？', {icon: 3, title: '确认重启'}, function(index) {
        layer.close(index);
        
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 重启中...';
        btn.disabled = true;
        
        $.get('?act=restart', function(data) {
            if (data.success) {
                layer.msg('服务重启成功！', {icon: 1});
                setTimeout(refreshStatus, 1000);
            } else {
                layer.msg('服务重启失败！', {icon: 2});
            }
        }).fail(function() {
            layer.msg('重启请求失败！', {icon: 2});
        }).always(function() {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    });
}

// 运行系统诊断
function runDiagnostics() {
    const resultDiv = $('#websocket-test-result');
    resultDiv.html('<div class="alert alert-info"><i class="fa fa-spinner fa-spin"></i> 正在运行系统诊断...</div>');

    $.get('../check_websocket_service_status.php', function(data) {
        let html = '<div class="alert alert-' + (data.overall.status === 'healthy' ? 'success' : (data.overall.status === 'warning' ? 'warning' : 'danger')) + '">';
        html += '<h5><i class="fa fa-stethoscope"></i> 诊断结果 (' + data.overall.success_rate + '% 通过)</h5>';
        html += '</div>';

        // 显示各项测试结果
        for (const [key, test] of Object.entries(data.tests)) {
            const statusClass = test.status === 'success' ? 'success' : 'danger';
            const icon = test.status === 'success' ? 'check' : 'times';

            html += '<div class="alert alert-' + statusClass + '">';
            html += '<i class="fa fa-' + icon + '"></i> <strong>' + test.name + '</strong>: ' + test.message;
            html += '</div>';
        }

        // 显示建议
        if (data.recommendations && data.recommendations.length > 0) {
            html += '<div class="alert alert-info">';
            html += '<h6><i class="fa fa-lightbulb-o"></i> 建议:</h6>';
            html += '<ul>';
            data.recommendations.forEach(function(rec) {
                html += '<li>' + rec + '</li>';
            });
            html += '</ul>';
            html += '</div>';
        }

        resultDiv.html(html);
    }).fail(function() {
        resultDiv.html('<div class="alert alert-danger"><i class="fa fa-times"></i> 诊断请求失败！</div>');
    });
}

// 测试WebSocket连接
function testWebSocketConnection() {
    const resultDiv = $('#websocket-test-result');
    resultDiv.html('<div class="alert alert-info"><i class="fa fa-spinner fa-spin"></i> 正在测试WebSocket连接...</div>');

    try {
        const ws = new WebSocket('ws://ceshi.huisas.com:8081/app/payment_websocket_2024');
        let connected = false;
        let timeout;

        // 设置连接超时
        timeout = setTimeout(function() {
            if (!connected) {
                ws.close();
                resultDiv.html('<div class="alert alert-danger"><i class="fa fa-times"></i> WebSocket连接超时！请检查服务是否正常运行。</div>');
            }
        }, 10000);

        ws.onopen = function() {
            connected = true;
            clearTimeout(timeout);
            resultDiv.html('<div class="alert alert-success"><i class="fa fa-check"></i> WebSocket连接成功！</div>');

            // 订阅测试频道
            ws.send(JSON.stringify({
                event: 'pusher:subscribe',
                data: {
                    channel: 'payment_notifications'
                }
            }));
        };

        ws.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                resultDiv.append('<div class="alert alert-info">收到消息: ' + JSON.stringify(data) + '</div>');
            } catch (e) {
                resultDiv.append('<div class="alert alert-info">收到原始消息: ' + event.data + '</div>');
            }
        };

        ws.onerror = function(error) {
            clearTimeout(timeout);
            resultDiv.html('<div class="alert alert-danger"><i class="fa fa-times"></i> WebSocket连接错误，请运行诊断检查详细问题</div>');
        };

        ws.onclose = function(event) {
            clearTimeout(timeout);
            if (connected) {
                resultDiv.append('<div class="alert alert-warning">WebSocket连接已关闭 (代码: ' + event.code + ')</div>');
            } else {
                resultDiv.html('<div class="alert alert-danger"><i class="fa fa-times"></i> WebSocket连接失败！请点击"运行诊断"查看详细问题。</div>');
            }
        };

        // 10秒后自动关闭连接
        setTimeout(function() {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        }, 10000);

    } catch (error) {
        resultDiv.html('<div class="alert alert-danger"><i class="fa fa-times"></i> WebSocket测试异常: ' + error.message + '</div>');
    }
}

// 加载最近的日志
function loadRecentLogs() {
    // 这里可以通过AJAX加载日志文件内容
    // 暂时显示示例日志
    const logs = [
        '[' + new Date().toLocaleString() + '] [WebSocket] 服务器启动成功',
        '[' + new Date().toLocaleString() + '] [HTTP-API] HTTP API服务器启动成功',
        '[' + new Date().toLocaleString() + '] [WebSocket] 等待客户端连接...'
    ];
    
    $('#log-container').html(logs.join('<br>'));
}

// 格式化字节数
function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 页面卸载时清理定时器
$(window).on('beforeunload', function() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
});
</script>

<?php
function formatBytes($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $unitIndex = 0;
    
    while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
        $bytes /= 1024;
        $unitIndex++;
    }
    
    return round($bytes, 2) . ' ' . $units[$unitIndex];
}

include './foot.php';
?>
