<template>
  <view class="container">
    <custom-navbar title="付款码测试" :shadow="true"></custom-navbar>
    
    <view class="test-section">
      <view class="section-title">付款码支付测试</view>
      
      <!-- 测试表单 -->
      <view class="form-section">
        <view class="form-item">
          <text class="label">收款金额</text>
          <input 
            class="input" 
            type="digit" 
            v-model="testData.amount" 
            placeholder="请输入金额"
          />
        </view>
        
        <view class="form-item">
          <text class="label">付款码</text>
          <input 
            class="input" 
            type="text" 
            v-model="testData.auth_code" 
            placeholder="请输入18位付款码"
            maxlength="18"
          />
        </view>
        
        <view class="form-item">
          <text class="label">商品名称</text>
          <input 
            class="input" 
            type="text" 
            v-model="testData.product_name" 
            placeholder="商品名称"
          />
        </view>
      </view>
      
      <!-- 快捷测试按钮 -->
      <view class="quick-test">
        <view class="quick-title">快捷测试</view>
        <view class="quick-buttons">
          <button class="quick-btn alipay" @tap="setTestData('alipay')">
            支付宝测试码
          </button>
          <button class="quick-btn wxpay" @tap="setTestData('wxpay')">
            微信测试码
          </button>
          <button class="quick-btn bank" @tap="setTestData('bank')">
            银联测试码
          </button>
        </view>
      </view>
      
      <!-- 测试按钮 -->
      <view class="action-section">
        <button class="test-button" @tap="testScanPay" :disabled="isLoading">
          <text v-if="!isLoading">测试付款码支付</text>
          <text v-else>测试中...</text>
        </button>
      </view>
      
      <!-- 测试结果 -->
      <view class="result-section" v-if="testResult">
        <view class="result-title">测试结果</view>
        <view class="result-content">
          <text class="result-text">{{ testResult }}</text>
        </view>
      </view>
      
      <!-- 响应数据 -->
      <view class="response-section" v-if="responseData">
        <view class="response-title">响应数据</view>
        <view class="response-content">
          <text class="response-text">{{ JSON.stringify(responseData, null, 2) }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { post } from '@/utils/request'
import { checkLoginStatus } from '@/utils/auth'

export default {
  data() {
    return {
      testData: {
        amount: '0.01',
        auth_code: '',
        product_name: '测试商品'
      },
      isLoading: false,
      testResult: '',
      responseData: null
    }
  },
  
  async onLoad() {
    // 检查登录状态
    const isLoggedIn = await checkLoginStatus()
    if (!isLoggedIn) {
      uni.reLaunch({
        url: '/pages/login/index'
      })
      return
    }
  },
  
  methods: {
    // 设置测试数据
    setTestData(type) {
      const testCodes = {
        alipay: '280060801797506151', // 支付宝测试码
        wxpay: '134567890123456789', // 微信测试码
        bank: '621234567890123456'    // 银联测试码
      }
      
      this.testData.auth_code = testCodes[type] || ''
      this.testData.amount = '0.01'
      this.testData.product_name = `${type}测试商品`
    },
    
    // 测试付款码支付
    async testScanPay() {
      if (!this.testData.amount || !this.testData.auth_code) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
        return
      }
      
      this.isLoading = true
      this.testResult = ''
      this.responseData = null
      
      try {
        // 生成订单号
        const outTradeNo = 'TEST' + Date.now() + Math.floor(Math.random() * 1000)
        
        // 构建请求参数
        const params = {
          amount: this.testData.amount,
          auth_code: this.testData.auth_code,
          out_trade_no: outTradeNo,
          product_name: this.testData.product_name
        }
        
        console.log('测试请求参数:', params)
        
        // 调用付款码支付接口
        const response = await post('/user/ajax2.php?act=scan_pay', params, {
          loading: false
        })
        
        console.log('测试响应:', response)
        this.responseData = response
        
        if (response.code === 0) {
          this.testResult = '✅ 测试成功！订单创建成功，返回了submit_url'
          
          if (response.submit_url) {
            // 显示跳转URL
            uni.showModal({
              title: '测试成功',
              content: `订单创建成功！\n跳转URL: ${response.submit_url}`,
              showCancel: true,
              cancelText: '关闭',
              confirmText: '打开链接',
              success: (res) => {
                if (res.confirm) {
                  // 在webview中打开链接
                  uni.navigateTo({
                    url: `/pages/webview/index?url=${encodeURIComponent(response.submit_url)}&title=付款码支付测试`
                  })
                }
              }
            })
          }
        } else {
          this.testResult = `❌ 测试失败：${response.msg || '未知错误'}`
        }
        
      } catch (error) {
        console.error('测试失败:', error)
        this.testResult = `❌ 测试失败：${error.message || '网络错误'}`
        this.responseData = error
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.form-section {
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 20rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.input {
  width: 100%;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.quick-test {
  margin-bottom: 30rpx;
}

.quick-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.quick-buttons {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.quick-btn {
  flex: 1;
  padding: 15rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #fff;
  border: none;
  min-width: 120rpx;
}

.quick-btn.alipay {
  background-color: #1677ff;
}

.quick-btn.wxpay {
  background-color: #07c160;
}

.quick-btn.bank {
  background-color: #ff6b35;
}

.action-section {
  margin-bottom: 30rpx;
}

.test-button {
  width: 100%;
  background: linear-gradient(135deg, #5145F7 0%, #7B68EE 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  padding: 25rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.test-button:disabled {
  background: #d9d9d9;
}

.result-section, .response-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.result-title, .response-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.result-content, .response-content {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 15rpx;
}

.result-text, .response-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  word-break: break-all;
}
</style>
