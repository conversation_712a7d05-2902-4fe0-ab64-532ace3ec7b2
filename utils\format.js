// utils/format.js - 格式化工具

/**
 * 格式化金额
 * @param {Number|String} amount - 金额
 * @param {Number} decimals - 小数位数
 * @param {String} decimalSeparator - 小数点分隔符
 * @param {String} thousandsSeparator - 千位分隔符
 * @returns {String} 格式化后的金额
 */
export function formatAmount(amount, decimals = 2, decimalSeparator = '.', thousandsSeparator = ',') {
  if (amount === undefined || amount === null || amount === '') {
    return '0.00';
  }
  
  // 转换为数字
  const num = parseFloat(amount);
  if (isNaN(num)) {
    return '0.00';
  }
  
  // 格式化为指定小数位
  const fixed = num.toFixed(decimals);
  
  // 分割整数和小数部分
  const parts = fixed.split('.');
  
  // 添加千位分隔符
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
  
  // 合并整数和小数部分
  return parts.join(decimalSeparator);
}

/**
 * 格式化日期时间
 * @param {Date|String|Number} date - 日期对象、日期字符串或时间戳
 * @param {String} format - 格式化模板
 * @returns {String} 格式化后的日期时间
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';
  
  // 转换为日期对象
  let dateObj;
  if (date instanceof Date) {
    dateObj = date;
  } else if (typeof date === 'string') {
    // 兼容处理日期字符串中的'-'
    dateObj = new Date(date.replace(/-/g, '/'));
  } else {
    dateObj = new Date(date);
  }
  
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');
  
  return format
    .replace(/YYYY/g, year)
    .replace(/YY/g, String(year).slice(-2))
    .replace(/MM/g, month)
    .replace(/DD/g, day)
    .replace(/HH/g, hours)
    .replace(/mm/g, minutes)
    .replace(/ss/g, seconds);
}

/**
 * 格式化银行卡号（隐藏中间位）
 * @param {String} cardNo - 银行卡号
 * @returns {String} 格式化后的银行卡号
 */
export function formatBankCard(cardNo) {
  if (!cardNo) return '';
  
  const str = String(cardNo).trim();
  if (str.length < 8) return str;
  
  return str.substring(0, 4) + ' **** **** ' + str.substring(str.length - 4);
}

/**
 * 格式化手机号（隐藏中间位）
 * @param {String} phone - 手机号
 * @returns {String} 格式化后的手机号
 */
export function formatPhone(phone) {
  if (!phone) return '';
  
  const str = String(phone).trim();
  if (str.length < 7) return str;
  
  return str.substring(0, 3) + '****' + str.substring(str.length - 4);
} 