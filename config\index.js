// config/index.js - 全局配置文件

const config = {
  // 开发环境
  development: {
    // API接口基础URL - 开发环境使用相对路径，通过代理解决跨域
    baseUrl: '',

    // 服务器完整地址 - 用于生成二维码等需要完整URL的场景
    serverUrl: 'http://ceshi.huisas.com',

    // 默认请求头
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
      'X-Requested-With': 'XMLHttpRequest'
    },

    // API超时时间（毫秒）
    timeout: 15000,

    // 支付回调
    notifyUrl: '/notify_url.php',
    returnUrl: '/return_url.php',

    // 商户默认配置（为了开发测试方便）
    merchant: {
      id: '1000', // 修正为实际登录账号的uid
      key: '35e8b3afe6a0a5325nnqC3ae+0k8b8 5f4b21a152e6b5c01d0c0dea6e0c379' // 从数据库复制的真实key
    }
  },

  // 生产环境
  production: {
    // API接口基础URL - 生产环境使用相对路径
    baseUrl: '',

    // 服务器完整地址 - 用于生成二维码等需要完整URL的场景
    serverUrl: 'http://ceshi.huisas.com',

    // 默认请求头
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
      'X-Requested-With': 'XMLHttpRequest'
    },

    // API超时时间（毫秒）
    timeout: 10000,

    // 支付回调
    notifyUrl: '/notify_url.php',
    returnUrl: '/return_url.php',

    // 商户默认配置
    merchant: {
      id: '',
      key: ''
    }
  }
};

// 根据当前环境判断使用哪个配置
let currentConfig = config.development;

// 在生产构建时切换配置
if (process.env.NODE_ENV === 'production') {
  currentConfig = config.production;
}

export default currentConfig; 