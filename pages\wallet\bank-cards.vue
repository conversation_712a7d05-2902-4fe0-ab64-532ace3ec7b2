<template>
	<view class="container">
		<!-- 顶部标题栏 -->
		<view class="header">
			<view class="header-left" @click="goBack">
				<image src="/static/back.png" mode="aspectFit" class="icon-back"></image>
				<text class="header-title">银行卡管理</text>
			</view>
			<view class="header-right">
				<view class="header-btn" @click="navigateToDuizhangList">对账单</view>
				<image src="/static/mine/qianbao/more.png" mode="aspectFit" class="icon-more"></image>
			</view>
		</view>
		
		<!-- 银行卡列表 -->
		<view class="card-list" v-if="bankCards.length > 0">
			<view v-for="(card, index) in bankCards" :key="index" class="bank-card" :class="{ 'default-card': card.isDefault }">
				<view class="card-top">
					<view class="bank-info">
						<image :src="card.bankIcon" mode="aspectFit" class="bank-icon"></image>
						<view class="bank-details">
							<text class="bank-name">{{card.bankName}}</text>
							<text class="card-type">{{card.cardType}}</text>
						</view>
					</view>
					<view class="default-tag" v-if="card.isDefault">默认</view>
				</view>
				
				<view class="card-number">
					<text>**** **** **** {{card.lastFourDigits}}</text>
				</view>
				
				<view class="card-bottom">
					<text class="owner-name">{{card.holderName}}</text>
					<view class="card-actions">
						<text class="action-btn" @click="setDefault(index)">{{card.isDefault ? '已设为默认' : '设为默认'}}</text>
						<text class="action-btn account" @click="navigateToDuizhang">对账</text>
						<text class="action-btn delete" @click="deleteCard(index)">删除</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 无银行卡状态 -->
		<view class="empty-state" v-else>
			<image src="/static/mine/qianbao/empty-card.png" mode="aspectFit" class="empty-icon"></image>
			<text class="empty-text">您还没有添加银行卡</text>
			<text class="empty-subtext">添加银行卡后可以进行提现操作</text>
		</view>
		
		<!-- 添加银行卡表单 -->
		<view class="add-card-form" v-if="showAddCardForm">
			<view class="form-header">
				<text class="form-title">添加银行卡</text>
				<text class="close-btn" @click="showAddCardForm = false">×</text>
			</view>
			
			<view class="form-group">
				<text class="label">持卡人</text>
				<input type="text" v-model="newCard.holderName" placeholder="请输入持卡人姓名" class="card-input" />
			</view>
			
			<view class="form-group">
				<text class="label">银行卡号</text>
				<input type="number" v-model="newCard.cardNumber" placeholder="请输入银行卡号" class="card-input" maxlength="19" @input="formatCardNumber" />
			</view>
			
			<view class="form-group">
				<text class="label">发卡银行</text>
				<view class="bank-selector" @click="toggleBankListSelector">
					<text v-if="newCard.bankName">{{newCard.bankName}}</text>
					<text v-else class="placeholder">请选择发卡银行</text>
					<text class="arrow-down">▼</text>
				</view>
			</view>
			
			<view class="form-group">
				<text class="label">卡片类型</text>
				<view class="type-selector">
					<view 
						class="type-option" 
						:class="{ 'selected': newCard.cardType === '储蓄卡' }"
						@click="newCard.cardType = '储蓄卡'"
					>
						<text>储蓄卡</text>
					</view>
					<view 
						class="type-option" 
						:class="{ 'selected': newCard.cardType === '信用卡' }"
						@click="newCard.cardType = '信用卡'"
					>
						<text>信用卡</text>
					</view>
				</view>
			</view>
			
			<view class="form-group" v-if="newCard.cardType === '信用卡'">
				<text class="label">安全码 (CVV)</text>
				<input type="number" v-model="newCard.cvv" placeholder="卡片背面3位数字" class="card-input" maxlength="3" />
			</view>
			
			<view class="form-group" v-if="newCard.cardType === '信用卡'">
				<text class="label">有效期</text>
				<view class="expiry-input">
					<input type="number" v-model="newCard.expiryMonth" placeholder="月" class="month-input" maxlength="2" />
					<text class="expiry-separator">/</text>
					<input type="number" v-model="newCard.expiryYear" placeholder="年" class="year-input" maxlength="2" />
				</view>
			</view>
			
			<view class="form-group">
				<text class="label">设为默认卡</text>
				<switch :checked="newCard.isDefault" @change="newCard.isDefault = !newCard.isDefault" color="#5145F7" />
			</view>
			
			<view class="confirm-area">
				<button class="confirm-add-btn" :disabled="!canAddCard" @click="addNewCard">确认添加</button>
				<text class="hint-text">请确保填写的信息准确无误</text>
			</view>
		</view>
		
		<!-- 银行列表选择器 -->
		<view class="bank-list-selector" v-if="showBankSelector">
			<view class="form-header">
				<text class="form-title">选择银行</text>
				<text class="close-btn" @click="showBankSelector = false">×</text>
			</view>
			
			<view class="search-bar">
				<image src="/static/mine/qianbao/search.png" mode="aspectFit" class="search-icon"></image>
				<input type="text" v-model="bankSearchKey" placeholder="搜索银行" class="search-input" />
			</view>
			
			<scroll-view scroll-y class="bank-list">
				<view 
					class="bank-item" 
					v-for="(bank, index) in filteredBanks" 
					:key="index"
					@click="selectBank(bank)"
				>
					<image :src="bank.icon" mode="aspectFit" class="bank-icon"></image>
					<text class="bank-name">{{bank.name}}</text>
				</view>
			</scroll-view>
		</view>
		
		<!-- 添加银行卡开发中提示 -->
		<view class="developing-tip" v-if="showDevelopingTip" @click="showDevelopingTip = false">
			<view class="developing-tip-content">
				添加银行卡功能正在开发中
			</view>
		</view>
		
		<!-- 提现表单 (初始隐藏) -->
		<view class="withdraw-form" v-if="showWithdrawForm">
			<view class="form-header">
				<text class="form-title">提现</text>
				<text class="close-btn" @click="showWithdrawForm = false">×</text>
			</view>
			
			<!-- 提现渠道选择 -->
			<view class="withdraw-channels">
				<view class="channel-tab" 
					:class="{ 'active': withdrawChannel === 'bank' }" 
					@click="withdrawChannel = 'bank'">
					<text>银行卡</text>
				</view>
				<view class="channel-tab" 
					:class="{ 'active': withdrawChannel === 'alipay' }" 
					@click="withdrawChannel = 'alipay'">
					<text>支付宝</text>
				</view>
			</view>
			
			<view class="form-group">
				<text class="label">提现金额</text>
				<view class="amount-input">
					<text class="currency">¥</text>
					<input type="digit" v-model="withdrawAmount" placeholder="请输入提现金额" />
				</view>
				<text class="balance-hint">可提现余额: ¥5,833.30</text>
			</view>
			
			<!-- 银行卡提现选择器 -->
			<view class="form-group" v-if="withdrawChannel === 'bank'">
				<text class="label">提现到</text>
				<view class="bank-selector" @click="toggleBankSelector">
					<view class="selected-bank" v-if="selectedBankIndex !== null">
						<image :src="bankCards[selectedBankIndex].bankIcon" mode="aspectFit" class="mini-bank-icon"></image>
						<text>{{bankCards[selectedBankIndex].bankName}} ({{bankCards[selectedBankIndex].lastFourDigits}})</text>
					</view>
					<text v-else>请选择银行卡</text>
					<text class="arrow-down">▼</text>
				</view>
			</view>
			
			<!-- 支付宝提现信息 -->
			<view class="form-group" v-if="withdrawChannel === 'alipay'">
				<text class="label">提现到支付宝</text>
				
				<view class="alipay-account" v-if="alipayAccount">
					<view class="alipay-account-info">
						<image src="/static/home/<USER>" mode="aspectFit" class="mini-alipay-icon"></image>
						<view class="alipay-details">
							<text class="alipay-name">{{alipayAccount.name}}</text>
							<text class="alipay-number">{{alipayAccount.account}}</text>
						</view>
					</view>
					<text class="alipay-change" @click="showBindAlipay = true">更换</text>
				</view>
				
				<view class="bind-alipay" v-else @click="showBindAlipay = true">
					<text class="bind-alipay-text">+ 绑定支付宝账号</text>
				</view>
			</view>
			
			<button class="confirm-btn" :disabled="!canWithdraw" @click="processWithdraw">确认提现</button>
			<text class="withdraw-hint">预计1-2个工作日到账，具体以到账时间为准</text>
		</view>
		
		<!-- 绑定支付宝表单 -->
		<view class="bind-alipay-form" v-if="showBindAlipay">
			<view class="form-header">
				<text class="form-title">绑定支付宝</text>
				<text class="close-btn" @click="showBindAlipay = false">×</text>
			</view>
			
			<view class="form-group">
				<text class="label">支付宝账号</text>
				<input type="text" v-model="newAlipayAccount" placeholder="请输入支付宝账号" class="alipay-input" />
			</view>
			
			<view class="form-group">
				<text class="label">真实姓名</text>
				<input type="text" v-model="newAlipayName" placeholder="请输入真实姓名" class="alipay-input" />
			</view>
			
			<button class="confirm-btn" :disabled="!canBindAlipay" @click="bindAlipay">确认绑定</button>
			<text class="withdraw-hint">请确保支付宝账号信息准确无误</text>
		</view>
		
		<!-- 添加银行卡按钮 -->
		<view class="add-card-btn" @click="navigateToAddCard">
			<text class="add-icon">+</text>
			<text>添加银行卡</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			bankCards: [
				{
					bankName: '中国工商银行',
					bankIcon: '/static/mine/qianbao/bank-icbc.png',
					cardType: '储蓄卡',
					lastFourDigits: '8352',
					holderName: '张三',
					isDefault: true
				},
				{
					bankName: '招商银行',
					bankIcon: '/static/mine/qianbao/bank-cmb.png',
					cardType: '储蓄卡',
					lastFourDigits: '2671',
					holderName: '张三',
					isDefault: false
				}
			],
			showWithdrawForm: false,
			withdrawAmount: '',
			selectedBankIndex: 0,
			// 新增支付宝相关数据
			withdrawChannel: 'bank', // 'bank' 或 'alipay'
			alipayAccount: null, // {name: '张三', account: '135****7890'}
			showBindAlipay: false,
			newAlipayAccount: '',
			newAlipayName: '',
			showDevelopingTip: false,
			// 添加银行卡相关数据
			showAddCardForm: false,
			showBankSelector: false,
			bankSearchKey: '',
			newCard: {
				holderName: '',
				cardNumber: '',
				bankName: '',
				bankIcon: '',
				cardType: '储蓄卡',
				cvv: '',
				expiryMonth: '',
				expiryYear: '',
				isDefault: false
			},
			// 银行列表
			bankList: [
				{ name: '中国工商银行', icon: '/static/mine/qianbao/bank-icbc.png' },
				{ name: '中国农业银行', icon: '/static/mine/qianbao/bank-abc.png' },
				{ name: '中国银行', icon: '/static/mine/qianbao/bank-boc.png' },
				{ name: '中国建设银行', icon: '/static/mine/qianbao/bank-ccb.png' },
				{ name: '招商银行', icon: '/static/mine/qianbao/bank-cmb.png' },
				{ name: '交通银行', icon: '/static/mine/qianbao/bank-bocom.png' },
				{ name: '中信银行', icon: '/static/mine/qianbao/bank-citic.png' },
				{ name: '浦发银行', icon: '/static/mine/qianbao/bank-spdb.png' },
				{ name: '民生银行', icon: '/static/mine/qianbao/bank-cmbc.png' },
				{ name: '广发银行', icon: '/static/mine/qianbao/bank-cgb.png' },
				{ name: '兴业银行', icon: '/static/mine/qianbao/bank-cib.png' },
				{ name: '华夏银行', icon: '/static/mine/qianbao/bank-hxb.png' },
				{ name: '光大银行', icon: '/static/mine/qianbao/bank-ceb.png' },
				{ name: '邮储银行', icon: '/static/mine/qianbao/bank-psbc.png' }
			]
		}
	},
	computed: {
		canWithdraw() {
			const amount = parseFloat(this.withdrawAmount);
			if (isNaN(amount) || amount <= 0) return false;
			
			if (this.withdrawChannel === 'bank') {
				return this.selectedBankIndex !== null;
			} else if (this.withdrawChannel === 'alipay') {
				return this.alipayAccount !== null;
			}
			
			return false;
		},
		canBindAlipay() {
			return this.newAlipayAccount.trim() !== '' && this.newAlipayName.trim() !== '';
		},
		// 添加银行卡相关计算属性
		canAddCard() {
			const card = this.newCard;
			// 基本验证：持卡人、卡号和银行不能为空
			const baseValid = card.holderName.trim() !== '' && 
				card.cardNumber.trim().length > 10 && 
				card.bankName !== '';
				
			// 信用卡需要额外验证
			if (card.cardType === '信用卡') {
				return baseValid && 
					card.cvv.length > 0 && 
					card.expiryMonth.length > 0 && 
					card.expiryYear.length > 0;
			}
			
			return baseValid;
		},
		filteredBanks() {
			if (!this.bankSearchKey) return this.bankList;
			
			return this.bankList.filter(bank => 
				bank.name.includes(this.bankSearchKey)
			);
		}
	},
	onLoad(options) {
		// 如果是从提现按钮跳转过来的，显示提现表单
		if(options.action === 'withdraw') {
			this.showWithdrawForm = true;
			// 如果有指定渠道
			if(options.channel === 'alipay') {
				this.withdrawChannel = 'alipay';
			}
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		setDefault(index) {
			this.bankCards.forEach((card, i) => {
				card.isDefault = i === index;
			});
		},
		deleteCard(index) {
			uni.showModal({
				title: '删除银行卡',
				content: '确定要删除该银行卡吗？',
				success: (res) => {
					if(res.confirm) {
						this.bankCards.splice(index, 1);
						// 如果删除的是默认卡，则设置第一张卡为默认
						if(this.bankCards.length > 0 && !this.bankCards.some(card => card.isDefault)) {
							this.bankCards[0].isDefault = true;
						}
						// 调整selectedBankIndex
						if(this.selectedBankIndex === index) {
							this.selectedBankIndex = this.bankCards.length > 0 ? 0 : null;
						} else if(this.selectedBankIndex > index) {
							this.selectedBankIndex--;
						}
					}
				}
			});
		},
		toggleBankSelector() {
			// 在实际项目中，这里应该显示银行卡选择器
			// 简化起见，这里仅切换选中的银行卡
			if(this.bankCards.length > 0) {
				this.selectedBankIndex = (this.selectedBankIndex + 1) % this.bankCards.length;
			}
		},
		processWithdraw() {
			const amount = parseFloat(this.withdrawAmount);
			if(isNaN(amount) || amount <= 0) {
				uni.showToast({
					title: '请输入有效金额',
					icon: 'none'
				});
				return;
			}
			
			// 检查提现方式
			if (this.withdrawChannel === 'alipay' && !this.alipayAccount) {
				uni.showToast({
					title: '请先绑定支付宝账号',
					icon: 'none'
				});
				return;
			}
			
			uni.showLoading({
				title: '处理中'
			});
			
			// 模拟提现处理
			setTimeout(() => {
				uni.hideLoading();
				let message = this.withdrawChannel === 'bank' ? 
					'提现申请已提交，将转入您的银行卡' : 
					'提现申请已提交，将转入您的支付宝';
				
				uni.showToast({
					title: message,
					icon: 'success',
					duration: 2000
				});
				
				this.showWithdrawForm = false;
				this.withdrawAmount = '';
			}, 1500);
		},
		bindAlipay() {
			if (!this.canBindAlipay) {
				uni.showToast({
					title: '请填写完整的支付宝信息',
					icon: 'none'
				});
				return;
			}
			
			uni.showLoading({
				title: '绑定中'
			});
			
			// 模拟绑定处理
			setTimeout(() => {
				uni.hideLoading();
				
				// 隐藏支付宝账号部分数字
				let accountDisplay = this.newAlipayAccount;
				if (accountDisplay.length > 7) {
					accountDisplay = accountDisplay.substring(0, 3) + '****' + 
						accountDisplay.substring(accountDisplay.length - 4);
				}
				
				this.alipayAccount = {
					name: this.newAlipayName,
					account: accountDisplay
				};
				
				this.showBindAlipay = false;
				this.newAlipayAccount = '';
				this.newAlipayName = '';
				
				uni.showToast({
					title: '支付宝账号绑定成功',
					icon: 'success'
				});
			}, 1000);
		},
		navigateToAddCard() {
			// 打开添加银行卡表单
			this.resetNewCardForm();
			this.showAddCardForm = true;
		},
		// 重置添加银行卡表单
		resetNewCardForm() {
			this.newCard = {
				holderName: '',
				cardNumber: '',
				bankName: '',
				bankIcon: '',
				cardType: '储蓄卡',
				cvv: '',
				expiryMonth: '',
				expiryYear: '',
				isDefault: false
			};
		},
		// 格式化银行卡号输入
		formatCardNumber(e) {
			// 移除非数字字符
			let value = e.detail.value.replace(/\D/g, '');
			// 限制长度为19位数字
			if (value.length > 19) {
				value = value.slice(0, 19);
			}
			this.newCard.cardNumber = value;
		},
		// 打开银行选择器
		toggleBankListSelector() {
			this.bankSearchKey = '';
			this.showBankSelector = true;
		},
		// 选择银行
		selectBank(bank) {
			this.newCard.bankName = bank.name;
			this.newCard.bankIcon = bank.icon;
			this.showBankSelector = false;
		},
		// 添加新银行卡
		addNewCard() {
			if (!this.canAddCard) {
				uni.showToast({
					title: '请完善银行卡信息',
					icon: 'none'
				});
				return;
			}
			
			uni.showLoading({
				title: '添加中'
			});
			
			// 模拟添加处理
			setTimeout(() => {
				// 获取卡号后四位
				const cardNumberLength = this.newCard.cardNumber.length;
				const lastFourDigits = this.newCard.cardNumber.substring(cardNumberLength - 4);
				
				// 如果新卡设为默认，则取消其他卡的默认状态
				if (this.newCard.isDefault) {
					this.bankCards.forEach(card => {
						card.isDefault = false;
					});
				}
				
				// 添加新卡到卡列表
				this.bankCards.push({
					bankName: this.newCard.bankName,
					bankIcon: this.newCard.bankIcon,
					cardType: this.newCard.cardType,
					lastFourDigits: lastFourDigits,
					holderName: this.newCard.holderName,
					isDefault: this.newCard.isDefault
				});
				
				uni.hideLoading();
				uni.showToast({
					title: '银行卡添加成功',
					icon: 'success'
				});
				
				this.showAddCardForm = false;
			}, 1500);
		},
		navigateToDuizhang() {
			// 跳转到对账单页面
			uni.navigateTo({
				url: '/pages/duizhang/index'
			});
		},
		navigateToDuizhangList() {
			// 跳转到对账单页面
			uni.navigateTo({
				url: '/pages/duizhang/index'
			});
		}
	}
}
</script>

<style>
page {
	font-family: 'Segoe UI', sans-serif;
	background-color: #f5f5f5;
}

.container {
	width: 100%;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 顶部标题栏 */
.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #5145F7;
	color: white;
	padding: 32rpx;
	position: relative;
}

.header-left {
	display: flex;
	align-items: center;
}

.icon-back {
	width: 48rpx;
	height: 48rpx;
	margin-right: 16rpx;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

.header-right {
	display: flex;
	align-items: center;
}

.header-btn {
	font-size: 26rpx;
	color: white;
	margin-right: 24rpx;
}

.icon-more {
	width: 48rpx;
	height: 48rpx;
}

/* 银行卡列表 */
.card-list {
	padding: 32rpx;
}

.bank-card {
	background-color: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.default-card {
	border: 2rpx solid #5145F7;
}

.card-top {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.bank-info {
	display: flex;
	align-items: center;
}

.bank-icon {
	width: 64rpx;
	height: 64rpx;
	margin-right: 16rpx;
}

.bank-details {
	display: flex;
	flex-direction: column;
}

.bank-name {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 4rpx;
}

.card-type {
	font-size: 24rpx;
	color: #666;
}

.default-tag {
	background-color: #5145F7;
	color: white;
	font-size: 22rpx;
	padding: 6rpx 16rpx;
	border-radius: 32rpx;
}

.card-number {
	font-size: 32rpx;
	padding: 24rpx 0;
}

.card-bottom {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.owner-name {
	font-size: 28rpx;
	color: #666;
}

.card-actions {
	display: flex;
}

.action-btn {
	font-size: 26rpx;
	color: #5145F7;
	margin-left: 24rpx;
}

.action-btn.account {
	color: #5145F7;
}

.action-btn.delete {
	color: #FF3B30;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 32rpx;
}

.empty-icon {
	width: 180rpx;
	height: 180rpx;
	margin-bottom: 32rpx;
}

.empty-text {
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 12rpx;
}

.empty-subtext {
	font-size: 28rpx;
	color: #999;
}

/* 添加银行卡按钮 */
.add-card-btn {
	position: fixed;
	bottom: 32rpx;
	left: 32rpx;
	right: 32rpx;
	background-color: #5145F7;
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
}

.add-icon {
	font-size: 40rpx;
	margin-right: 8rpx;
}

/* 提现表单 */
.withdraw-form,
.bind-alipay-form {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: white;
	border-radius: 32rpx 32rpx 0 0;
	padding: 32rpx;
	z-index: 10;
	box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.form-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
}

.form-title {
	font-size: 36rpx;
	font-weight: bold;
}

.close-btn {
	font-size: 44rpx;
	color: #999;
}

/* 提现渠道选择器 */
.withdraw-channels {
	display: flex;
	background-color: #f5f5f5;
	border-radius: 8rpx;
	margin-bottom: 32rpx;
	overflow: hidden;
}

.channel-tab {
	flex: 1;
	text-align: center;
	padding: 20rpx 0;
	font-size: 28rpx;
	color: #666;
}

.channel-tab.active {
	background-color: #5145F7;
	color: white;
}

.form-group {
	margin-bottom: 32rpx;
}

.label {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 12rpx;
	display: block;
}

.amount-input {
	display: flex;
	align-items: center;
	border-bottom: 1px solid #e5e5e5;
	padding-bottom: 16rpx;
}

.currency {
	font-size: 40rpx;
	font-weight: bold;
	margin-right: 8rpx;
}

.amount-input input {
	flex: 1;
	font-size: 40rpx;
	height: 80rpx;
}

.balance-hint {
	font-size: 24rpx;
	color: #999;
	margin-top: 8rpx;
}

.bank-selector {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #f5f5f5;
	padding: 24rpx;
	border-radius: 8rpx;
}

.selected-bank {
	display: flex;
	align-items: center;
}

.mini-bank-icon,
.mini-alipay-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 12rpx;
}

.arrow-down {
	font-size: 24rpx;
	color: #999;
}

/* 支付宝账号信息 */
.alipay-account {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #f5f5f5;
	padding: 24rpx;
	border-radius: 8rpx;
}

.alipay-account-info {
	display: flex;
	align-items: center;
}

.alipay-details {
	display: flex;
	flex-direction: column;
}

.alipay-name {
	font-size: 28rpx;
	font-weight: bold;
}

.alipay-number {
	font-size: 24rpx;
	color: #666;
}

.alipay-change {
	color: #5145F7;
	font-size: 26rpx;
}

.bind-alipay {
	background-color: #f5f5f5;
	padding: 24rpx;
	border-radius: 8rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.bind-alipay-text {
	color: #5145F7;
	font-size: 28rpx;
}

.alipay-input {
	background-color: #f5f5f5;
	padding: 24rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	margin-bottom: 8rpx;
}

.confirm-btn {
	background-color: #5145F7;
	color: white;
	border-radius: 44rpx;
	height: 88rpx;
	line-height: 88rpx;
	font-size: 32rpx;
	margin-top: 48rpx;
}

.confirm-btn[disabled] {
	background-color: #cccccc;
	color: #ffffff;
}

.withdraw-hint {
	font-size: 24rpx;
	color: #999;
	text-align: center;
	margin-top: 16rpx;
}

/* 添加银行卡开发中提示 */
.developing-tip {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 100;
}

.developing-tip-content {
	background-color: #3a3a3a;
	color: white;
	padding: 24rpx 32rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
}

/* 添加银行卡表单 */
.add-card-form {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: white;
	z-index: 20;
	padding: 32rpx;
	display: flex;
	flex-direction: column;
}

.card-input {
	background-color: #f5f5f5;
	padding: 24rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
}

.type-selector {
	display: flex;
	gap: 24rpx;
}

.type-option {
	flex: 1;
	background-color: #f5f5f5;
	padding: 20rpx 0;
	border-radius: 8rpx;
	text-align: center;
	font-size: 28rpx;
	color: #666;
}

.type-option.selected {
	background-color: #5145F7;
	color: white;
}

.expiry-input {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.month-input, .year-input {
	background-color: #f5f5f5;
	padding: 24rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	flex: 1;
	text-align: center;
}

.expiry-separator {
	font-size: 32rpx;
	font-weight: bold;
}

.placeholder {
	color: #999;
}

.hint-text {
	font-size: 24rpx;
	color: #999;
	text-align: center;
	margin-top: 16rpx;
}

/* 银行列表选择器 */
.bank-list-selector {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: white;
	z-index: 30;
	padding: 32rpx;
	display: flex;
	flex-direction: column;
}

.search-bar {
	display: flex;
	align-items: center;
	background-color: #f5f5f5;
	padding: 16rpx 24rpx;
	border-radius: 8rpx;
	margin-bottom: 24rpx;
}

.search-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 16rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
}

.bank-list {
	flex: 1;
	margin: 0 -16rpx;
}

.bank-item {
	display: flex;
	align-items: center;
	padding: 24rpx 16rpx;
	border-bottom: 1px solid #f5f5f5;
}

.bank-item .bank-icon {
	width: 48rpx;
	height: 48rpx;
	margin-right: 24rpx;
}

.bank-item .bank-name {
	font-size: 28rpx;
}

/* 确认添加区域 */
.confirm-area {
	margin-top: 60rpx;
	text-align: center;
}

.confirm-add-btn {
	background-color: #5145F7;
	color: white;
	width: 200rpx;
	height: 80rpx;
	line-height: 80rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	display: inline-block;
	padding: 0;
	margin: 0 auto 16rpx;
}

.confirm-add-btn[disabled] {
	background-color: #cccccc;
}
</style> 