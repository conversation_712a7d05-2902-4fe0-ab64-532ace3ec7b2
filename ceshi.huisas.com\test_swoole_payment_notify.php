<?php
/**
 * Swoole WebSocket 支付通知测试页面
 * 用于测试向Swoole WebSocket服务器发送支付通知
 */

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: text/html; charset=utf-8');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    $action = $_POST['action'];
    
    if ($action === 'send_payment_notify') {
        $merchantId = $_POST['merchant_id'] ?? '1000';
        $orderId = $_POST['order_id'] ?? 'TEST_' . time();
        $amount = $_POST['amount'] ?? '100.00';
        $staffId = $_POST['staff_id'] ?? '';
        
        // 构造支付通知数据
        $paymentData = [
            'merchant_id' => $merchantId,
            'order_id' => $orderId,
            'amount' => $amount,
            'status' => 'success',
            'extra_data' => [
                'pay_type' => 'alipay',
                'pay_time' => date('Y-m-d H:i:s')
            ]
        ];
        
        if (!empty($staffId)) {
            $paymentData['staff_id'] = $staffId;
        }
        
        // 发送到Swoole WebSocket服务器
        $result = sendPaymentNotifyToSwoole($paymentData);
        
        echo json_encode([
            'success' => $result['success'],
            'message' => $result['message'],
            'data' => $paymentData
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    echo json_encode(['success' => false, 'message' => '未知操作'], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 发送支付通知到Swoole WebSocket服务器
 */
function sendPaymentNotifyToSwoole($data) {
    $url = 'http://127.0.0.1:8080/payment/notify';
    
    $postData = json_encode($data);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($postData)
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return [
            'success' => false,
            'message' => "CURL错误: $error"
        ];
    }
    
    if ($httpCode !== 200) {
        return [
            'success' => false,
            'message' => "HTTP错误: $httpCode, 响应: $response"
        ];
    }
    
    $result = json_decode($response, true);
    if ($result && isset($result['success'])) {
        return $result;
    }
    
    return [
        'success' => false,
        'message' => "响应解析失败: $response"
    ];
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Swoole WebSocket 支付通知测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        .form-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-input:focus {
            border-color: #667eea;
            outline: none;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .result-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }
        .result-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .result-content {
            background: white;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            border-left: 4px solid #667eea;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .info-title {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
        }
        .info-text {
            color: #1565c0;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">🚀 Swoole WebSocket 支付通知测试</div>
            <div class="subtitle">测试向Swoole WebSocket服务器发送支付通知</div>
        </div>

        <div class="info-box">
            <div class="info-title">📋 使用说明</div>
            <div class="info-text">
                1. 确保Swoole WebSocket服务器已启动 (端口8080)<br>
                2. 在前端打开测试页面并连接WebSocket<br>
                3. 填写下方表单并发送支付通知<br>
                4. 观察前端是否收到支付通知消息
            </div>
        </div>

        <div class="form-section">
            <form id="paymentForm">
                <div class="form-group">
                    <label class="form-label">商户ID</label>
                    <input type="text" id="merchant_id" class="form-input" value="1000" placeholder="请输入商户ID">
                </div>
                
                <div class="form-group">
                    <label class="form-label">订单号</label>
                    <input type="text" id="order_id" class="form-input" value="" placeholder="留空自动生成">
                </div>
                
                <div class="form-group">
                    <label class="form-label">支付金额</label>
                    <input type="number" id="amount" class="form-input" value="100.00" step="0.01" placeholder="请输入支付金额">
                </div>
                
                <div class="form-group">
                    <label class="form-label">员工ID (可选)</label>
                    <input type="text" id="staff_id" class="form-input" value="" placeholder="员工ID，可选">
                </div>
                
                <button type="submit" class="btn" id="sendBtn">发送支付通知</button>
            </form>
        </div>

        <div class="result-section" id="resultSection">
            <div class="result-title">📤 发送结果</div>
            <div class="result-content" id="resultContent"></div>
        </div>
    </div>

    <script>
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            sendPaymentNotify();
        });

        function sendPaymentNotify() {
            const btn = document.getElementById('sendBtn');
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');
            
            // 禁用按钮
            btn.disabled = true;
            btn.textContent = '发送中...';
            
            // 获取表单数据
            const formData = new FormData();
            formData.append('action', 'send_payment_notify');
            formData.append('merchant_id', document.getElementById('merchant_id').value);
            formData.append('order_id', document.getElementById('order_id').value);
            formData.append('amount', document.getElementById('amount').value);
            formData.append('staff_id', document.getElementById('staff_id').value);
            
            // 发送请求
            fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // 显示结果
                resultSection.style.display = 'block';
                resultContent.className = 'result-content ' + (data.success ? 'success' : 'error');
                resultContent.textContent = JSON.stringify(data, null, 2);
                
                // 恢复按钮
                btn.disabled = false;
                btn.textContent = '发送支付通知';
                
                // 滚动到结果区域
                resultSection.scrollIntoView({ behavior: 'smooth' });
            })
            .catch(error => {
                // 显示错误
                resultSection.style.display = 'block';
                resultContent.className = 'result-content error';
                resultContent.textContent = '请求失败: ' + error.message;
                
                // 恢复按钮
                btn.disabled = false;
                btn.textContent = '发送支付通知';
            });
        }
        
        // 页面加载时生成随机订单号
        window.addEventListener('load', function() {
            if (!document.getElementById('order_id').value) {
                document.getElementById('order_id').value = 'TEST_' + Date.now();
            }
        });
    </script>
</body>
</html>
