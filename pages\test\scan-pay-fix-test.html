<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>反扫支付修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>反扫支付修复测试</h1>
        <p>测试修复后的反扫支付功能，检查submit_url是否使用绝对路径</p>
        
        <form id="scanPayForm">
            <div class="form-group">
                <label for="amount">支付金额：</label>
                <input type="number" id="amount" step="0.01" min="0.01" value="0.01" required>
            </div>
            
            <div class="form-group">
                <label for="authCode">付款码：</label>
                <input type="text" id="authCode" placeholder="请输入18位付款码" maxlength="18" value="281711908879118544" required>
                <small>测试用付款码：281711908879118544（支付宝格式）</small>
            </div>
            
            <div class="form-group">
                <label for="productName">商品名称：</label>
                <input type="text" id="productName" value="测试商品" required>
            </div>
            
            <button type="submit">测试反扫支付</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        const API_BASE = 'http://ceshi.huisas.com';
        
        document.getElementById('scanPayForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const amount = document.getElementById('amount').value;
            const authCode = document.getElementById('authCode').value;
            const productName = document.getElementById('productName').value;
            const resultDiv = document.getElementById('result');
            
            // 验证付款码格式
            if (!/^\d{18}$/.test(authCode)) {
                showResult('付款码格式不正确，必须是18位数字', 'error');
                return;
            }
            
            try {
                showResult('正在测试反扫支付...', 'info');
                
                // 生成订单号
                const outTradeNo = 'SCAN' + Date.now() + Math.floor(Math.random() * 1000);
                
                // 构建请求参数
                const params = new FormData();
                params.append('amount', amount);
                params.append('auth_code', authCode);
                params.append('out_trade_no', outTradeNo);
                params.append('product_name', productName);
                
                // 调用反扫支付接口
                const response = await fetch(`${API_BASE}/user/ajax2.php?act=scan_pay`, {
                    method: 'POST',
                    body: params
                });
                
                const result = await response.text();
                console.log('原始响应:', result);
                
                try {
                    const jsonResult = JSON.parse(result);
                    console.log('解析后的响应:', jsonResult);
                    
                    if (jsonResult.code === 0) {
                        showResult(`✅ 反扫支付接口调用成功！
                        
响应数据：
${JSON.stringify(jsonResult, null, 2)}

关键信息：
- 订单创建成功
- submit_url: ${jsonResult.submit_url}
- 是否为绝对路径: ${jsonResult.submit_url.startsWith('http') ? '是' : '否'}

下一步：
1. 前端会跳转到webview页面
2. webview加载submit_url进行支付处理
3. submit2.php会识别auth_code参数并调用反扫支付`, 'success');
                        
                        // 模拟前端跳转逻辑
                        const webviewUrl = `/pages/webview/index?url=${encodeURIComponent(jsonResult.submit_url)}&title=付款码支付`;
                        console.log('前端跳转URL:', webviewUrl);
                        
                        // 显示跳转信息
                        setTimeout(() => {
                            const currentResult = document.getElementById('result').innerHTML;
                            document.getElementById('result').innerHTML = currentResult + `

🔗 前端跳转信息：
- webview页面URL: ${webviewUrl}
- 编码后的submit_url: ${encodeURIComponent(jsonResult.submit_url)}
- 解码验证: ${decodeURIComponent(encodeURIComponent(jsonResult.submit_url))}`;
                        }, 1000);
                        
                    } else {
                        showResult(`❌ 反扫支付失败：
                        
错误信息：${jsonResult.msg}
错误代码：${jsonResult.code}

完整响应：
${JSON.stringify(jsonResult, null, 2)}`, 'error');
                    }
                } catch (parseError) {
                    showResult(`❌ 响应解析失败：
                    
原始响应：
${result}

解析错误：
${parseError.message}`, 'error');
                }
                
            } catch (error) {
                showResult(`❌ 请求失败：${error.message}`, 'error');
            }
        });
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }
    </script>
</body>
</html>
