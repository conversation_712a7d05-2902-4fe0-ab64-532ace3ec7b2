<?php
// 调试版本的ajax2.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 添加跨域支持
$allowed_origins = [
    'http://localhost:8080',
    'http://127.0.0.1:8080',
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://localhost:5173',
    'http://127.0.0.1:5173',
    'http://ceshi.huisas.com',
    'https://ceshi.huisas.com',
    'null'
];

$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
if (in_array($origin, $allowed_origins) || $origin === '') {
    if ($origin) {
        header("Access-Control-Allow-Origin: " . $origin);
    } else {
        header("Access-Control-Allow-Origin: *");
    }
    header("Access-Control-Allow-Credentials: true");
    header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, X-Requested-With, Authorization, X-CSRF-Token");
}

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 调试信息收集
$debug_info = [
    'timestamp' => date('Y-m-d H:i:s'),
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'request_uri' => $_SERVER['REQUEST_URI'],
    'post_data' => $_POST,
    'get_data' => $_GET,
    'headers' => getallheaders(),
    'user_token_sources' => [
        'cookie' => isset($_COOKIE["user_token"]) ? substr($_COOKIE["user_token"], 0, 20) . '...' : 'null',
        'post' => isset($_POST["user_token"]) ? substr($_POST["user_token"], 0, 20) . '...' : 'null',
        'get' => isset($_GET["user_token"]) ? substr($_GET["user_token"], 0, 20) . '...' : 'null'
    ]
];

// 包含必要文件
try {
    include("../includes/common.php");
    $debug_info['common_included'] = true;
} catch (Exception $e) {
    $debug_info['common_error'] = $e->getMessage();
}

try {
    include("../includes/member.php");
    $debug_info['member_included'] = true;
} catch (Exception $e) {
    $debug_info['member_error'] = $e->getMessage();
}

// 检查变量状态
$debug_info['variables'] = [
    'islogin2_defined' => isset($islogin2),
    'islogin2_value' => isset($islogin2) ? $islogin2 : 'undefined',
    'uid_defined' => isset($uid),
    'uid_value' => isset($uid) ? $uid : 'undefined',
    'userrow_defined' => isset($userrow),
    'userrow_uid' => isset($userrow) && isset($userrow['uid']) ? $userrow['uid'] : 'undefined'
];

// 检查user_token处理
$user_token = '';
if(isset($_COOKIE["user_token"])) {
    $user_token = $_COOKIE["user_token"];
    $debug_info['token_source'] = 'cookie';
} elseif(isset($_POST["user_token"])) {
    $user_token = $_POST["user_token"];
    $debug_info['token_source'] = 'post';
} elseif(isset($_GET["user_token"])) {
    $user_token = $_GET["user_token"];
    $debug_info['token_source'] = 'get';
}

$debug_info['user_token_found'] = !empty($user_token);
$debug_info['user_token_length'] = strlen($user_token);

if (!empty($user_token)) {
    // 尝试解码token
    try {
        if (function_exists('authcode') && defined('SYS_KEY')) {
            $token = authcode(daddslashes($user_token), 'DECODE', SYS_KEY);
            $debug_info['token_decoded'] = true;
            $debug_info['decoded_token'] = $token;
            
            $parts = explode("\t", $token);
            $debug_info['token_parts'] = [
                'count' => count($parts),
                'uid' => isset($parts[0]) ? $parts[0] : 'missing',
                'sid' => isset($parts[1]) ? substr($parts[1], 0, 10) . '...' : 'missing',
                'expiretime' => isset($parts[2]) ? $parts[2] : 'missing',
                'current_time' => time(),
                'is_expired' => isset($parts[2]) ? ($parts[2] < time()) : 'unknown'
            ];
        } else {
            $debug_info['token_decode_error'] = 'authcode function or SYS_KEY not available';
        }
    } catch (Exception $e) {
        $debug_info['token_decode_error'] = $e->getMessage();
    }
}

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');

// 检查act参数
$act = isset($_GET['act']) ? $_GET['act'] : null;
$debug_info['act'] = $act;

// 如果是调试请求，返回调试信息
if (isset($_GET['debug']) || isset($_POST['debug'])) {
    echo json_encode([
        'code' => -999,
        'msg' => 'Debug Info',
        'debug' => $debug_info
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

// 即使不是调试请求，也在响应中包含调试信息
$include_debug = true;

// 正常的登录检查
if (isset($islogin2) && $islogin2 == 1) {
    $debug_info['login_check'] = 'passed';
} else {
    $debug_info['login_check'] = 'failed';
    $debug_info['login_check_details'] = [
        'islogin2_isset' => isset($islogin2),
        'islogin2_value' => isset($islogin2) ? $islogin2 : 'not_set',
        'islogin2_type' => isset($islogin2) ? gettype($islogin2) : 'undefined'
    ];
    echo json_encode([
        'code' => -3,
        'msg' => 'No Login',
        'debug' => $debug_info
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

// 如果登录检查通过，执行相应操作
switch($act) {
    case 'getcount':
        echo json_encode([
            'code' => 0,
            'msg' => 'Login check passed',
            'debug' => $debug_info,
            'orders' => 0,
            'orders_today' => 0,
            'settle_money' => '0.00'
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        break;
    
    default:
        echo json_encode([
            'code' => -1,
            'msg' => 'Invalid action',
            'debug' => $debug_info
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        break;
}
?>
