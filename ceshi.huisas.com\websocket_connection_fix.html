<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 WebSocket连接修复工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .status-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #007bff;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .status-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .control-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        
        .console {
            background: #1e1e1e;
            color: #00ff00;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
            border: 2px solid #333;
        }
        
        .console-line {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .console-line.error { color: #ff6b6b; }
        .console-line.success { color: #51cf66; }
        .console-line.warning { color: #ffd43b; }
        .console-line.info { color: #74c0fc; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .diagnostic-section {
            background: #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .diagnostic-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .diagnostic-item:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-indicator.online { background: #28a745; }
        .status-indicator.offline { background: #dc3545; }
        .status-indicator.pending { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 WebSocket连接修复工具</h1>
            <p>专门解决前端连接延迟和语音播报问题</p>
        </div>
        
        <div class="content">
            <!-- 状态面板 -->
            <div class="status-panel">
                <div class="status-card" id="connection-status">
                    <h3>🔗 连接状态</h3>
                    <p id="connection-text">检测中...</p>
                </div>
                
                <div class="status-card" id="auth-status">
                    <h3>🔐 认证状态</h3>
                    <p id="auth-text">等待连接...</p>
                </div>
                
                <div class="status-card" id="subscription-status">
                    <h3>📺 频道订阅</h3>
                    <p id="subscription-text">等待认证...</p>
                </div>
                
                <div class="status-card" id="server-status">
                    <h3>🌐 服务器状态</h3>
                    <p id="server-text">检测中...</p>
                </div>
            </div>
            
            <!-- 控制面板 -->
            <div class="control-panel">
                <h3>🎮 连接控制</h3>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="connectWebSocket()">
                        🔌 连接WebSocket
                    </button>
                    <button class="btn btn-warning" onclick="reconnectWebSocket()">
                        🔄 重新连接
                    </button>
                    <button class="btn btn-success" onclick="testNotification()">
                        🧪 测试通知
                    </button>
                    <button class="btn btn-info" onclick="checkServerStatus()">
                        📊 检查服务器
                    </button>
                    <button class="btn btn-danger" onclick="disconnectWebSocket()">
                        ❌ 断开连接
                    </button>
                    <button class="btn btn-secondary" onclick="clearConsole()">
                        🗑️ 清空日志
                    </button>
                </div>
                
                <div>
                    <label for="merchant-id">商户ID:</label>
                    <input type="text" id="merchant-id" value="1000" style="margin: 0 10px; padding: 8px; border-radius: 5px; border: 1px solid #ccc;">
                    
                    <label for="staff-id">员工ID (可选):</label>
                    <input type="text" id="staff-id" placeholder="留空表示商户主账号" style="margin: 0 10px; padding: 8px; border-radius: 5px; border: 1px solid #ccc;">
                </div>
            </div>
            
            <!-- 进度条 -->
            <div>
                <h4>🚀 连接进度</h4>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <p id="progress-text">等待开始...</p>
            </div>
            
            <!-- 控制台 -->
            <div>
                <h3>📟 实时日志</h3>
                <div class="console" id="console">
                    <div class="console-line info">🚀 WebSocket连接修复工具已就绪</div>
                    <div class="console-line info">💡 点击"连接WebSocket"开始诊断和修复连接问题</div>
                    <div class="console-line warning">⚠️ 如果遇到连接延迟，此工具将自动诊断并提供解决方案</div>
                </div>
            </div>
            
            <!-- 诊断信息 -->
            <div class="diagnostic-section">
                <h3>🔍 连接诊断</h3>
                <div class="diagnostic-item">
                    <span>WebSocket服务器</span>
                    <span><span class="status-indicator pending" id="server-indicator"></span><span id="server-diagnostic">检测中...</span></span>
                </div>
                <div class="diagnostic-item">
                    <span>网络连接</span>
                    <span><span class="status-indicator pending" id="network-indicator"></span><span id="network-diagnostic">检测中...</span></span>
                </div>
                <div class="diagnostic-item">
                    <span>认证流程</span>
                    <span><span class="status-indicator pending" id="auth-indicator"></span><span id="auth-diagnostic">等待连接...</span></span>
                </div>
                <div class="diagnostic-item">
                    <span>频道订阅</span>
                    <span><span class="status-indicator pending" id="channel-indicator"></span><span id="channel-diagnostic">等待认证...</span></span>
                </div>
                <div class="diagnostic-item">
                    <span>消息接收</span>
                    <span><span class="status-indicator pending" id="message-indicator"></span><span id="message-diagnostic">等待订阅...</span></span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // WebSocket连接修复工具
        class WebSocketConnectionFixer {
            constructor() {
                this.ws = null;
                this.isConnected = false;
                this.isAuthenticated = false;
                this.isSubscribed = false;
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                this.connectionSteps = [
                    '建立WebSocket连接',
                    '发送认证信息',
                    '等待认证确认',
                    '订阅支付频道',
                    '确认订阅成功',
                    '连接完全就绪'
                ];
                this.currentStep = 0;
                
                this.init();
            }
            
            init() {
                this.log('🔧 WebSocket连接修复工具初始化完成', 'info');
                this.checkServerStatus();
            }
            
            log(message, type = 'info') {
                const console = document.getElementById('console');
                const timestamp = new Date().toLocaleTimeString();
                const line = document.createElement('div');
                line.className = `console-line ${type}`;
                line.textContent = `[${timestamp}] ${message}`;
                console.appendChild(line);
                console.scrollTop = console.scrollHeight;
            }
            
            updateProgress(step, total = 6) {
                const percentage = (step / total) * 100;
                document.getElementById('progress-fill').style.width = `${percentage}%`;
                document.getElementById('progress-text').textContent = 
                    step < total ? `步骤 ${step}/${total}: ${this.connectionSteps[step - 1] || '处理中...'}` : '✅ 连接完成！';
            }
            
            updateStatus(elementId, text, type = 'info') {
                const element = document.getElementById(elementId);
                element.textContent = text;
                element.parentElement.className = `status-card ${type}`;
            }
            
            updateDiagnostic(indicatorId, diagnosticId, status, text) {
                document.getElementById(indicatorId).className = `status-indicator ${status}`;
                document.getElementById(diagnosticId).textContent = text;
            }
        }
        
        // 全局实例
        const fixer = new WebSocketConnectionFixer();
        
            // 连接WebSocket
            connectWebSocket() {
                if (this.isConnected) {
                    this.log('⚠️ WebSocket已连接，请先断开', 'warning');
                    return;
                }

                this.currentStep = 1;
                this.updateProgress(1);
                this.log('🔌 正在连接WebSocket服务器...', 'info');
                this.updateStatus('connection-status', '连接中...', 'warning');
                this.updateDiagnostic('network-indicator', 'network-diagnostic', 'pending', '连接中...');

                const wsUrl = 'ws://ceshi.huisas.com:8080';

                try {
                    this.ws = new WebSocket(wsUrl);

                    this.ws.onopen = () => {
                        this.isConnected = true;
                        this.currentStep = 2;
                        this.updateProgress(2);
                        this.log('✅ WebSocket连接成功！', 'success');
                        this.updateStatus('connection-status', '已连接', 'success');
                        this.updateDiagnostic('network-indicator', 'network-diagnostic', 'online', '连接正常');
                        this.updateDiagnostic('server-indicator', 'server-diagnostic', 'online', '服务器在线');

                        // 自动开始认证
                        setTimeout(() => this.authenticate(), 1000);
                    };

                    this.ws.onmessage = (event) => {
                        this.handleMessage(JSON.parse(event.data));
                    };

                    this.ws.onclose = (event) => {
                        this.isConnected = false;
                        this.isAuthenticated = false;
                        this.isSubscribed = false;
                        this.log(`❌ WebSocket连接关闭: ${event.code} ${event.reason}`, 'error');
                        this.updateStatus('connection-status', '已断开', 'error');
                        this.updateDiagnostic('network-indicator', 'network-diagnostic', 'offline', '连接断开');

                        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
                            this.scheduleReconnect();
                        }
                    };

                    this.ws.onerror = (error) => {
                        this.log('❌ WebSocket连接错误', 'error');
                        this.updateStatus('connection-status', '连接错误', 'error');
                        this.updateDiagnostic('network-indicator', 'network-diagnostic', 'offline', '连接失败');
                    };

                } catch (error) {
                    this.log(`❌ 连接失败: ${error.message}`, 'error');
                    this.updateStatus('connection-status', '连接失败', 'error');
                }
            }

            // 认证
            authenticate() {
                if (!this.isConnected) {
                    this.log('❌ 请先连接WebSocket', 'error');
                    return;
                }

                this.currentStep = 3;
                this.updateProgress(3);
                this.log('🔐 正在发送认证信息...', 'info');
                this.updateStatus('auth-status', '认证中...', 'warning');
                this.updateDiagnostic('auth-indicator', 'auth-diagnostic', 'pending', '认证中...');

                const merchantId = document.getElementById('merchant-id').value || '1000';
                const staffId = document.getElementById('staff-id').value || null;

                const authMessage = {
                    type: 'auth',
                    data: {
                        merchant_id: merchantId,
                        staff_id: staffId,
                        token: 'websocket_fix_tool_' + Date.now() // 临时token
                    }
                };

                this.ws.send(JSON.stringify(authMessage));
                this.log(`📤 发送认证: 商户${merchantId}${staffId ? ', 员工' + staffId : ''}`, 'info');
            }

            // 订阅频道
            subscribeChannels() {
                if (!this.isAuthenticated) {
                    this.log('❌ 请先完成认证', 'error');
                    return;
                }

                this.currentStep = 4;
                this.updateProgress(4);
                this.log('📺 正在订阅支付频道...', 'info');
                this.updateStatus('subscription-status', '订阅中...', 'warning');
                this.updateDiagnostic('channel-indicator', 'channel-diagnostic', 'pending', '订阅中...');

                const merchantId = document.getElementById('merchant-id').value || '1000';
                const channel = `merchant_${merchantId}_payment`;

                const subscribeMessage = {
                    type: 'subscribe',
                    data: {
                        channel: channel
                    }
                };

                this.ws.send(JSON.stringify(subscribeMessage));
                this.log(`📤 订阅频道: ${channel}`, 'info');
            }

            // 处理消息
            handleMessage(message) {
                this.log(`📨 收到消息: ${message.type}`, 'info');

                switch (message.type) {
                    case 'welcome':
                        this.log('👋 收到欢迎消息', 'success');
                        break;

                    case 'auth_result':
                        if (message.data.success) {
                            this.isAuthenticated = true;
                            this.currentStep = 4;
                            this.updateProgress(4);
                            this.log('✅ 认证成功！', 'success');
                            this.updateStatus('auth-status', '已认证', 'success');
                            this.updateDiagnostic('auth-indicator', 'auth-diagnostic', 'online', '认证成功');

                            // 自动订阅频道
                            setTimeout(() => this.subscribeChannels(), 500);
                        } else {
                            this.log(`❌ 认证失败: ${message.data.message}`, 'error');
                            this.updateStatus('auth-status', '认证失败', 'error');
                            this.updateDiagnostic('auth-indicator', 'auth-diagnostic', 'offline', '认证失败');
                        }
                        break;

                    case 'subscription_succeeded':
                        this.isSubscribed = true;
                        this.currentStep = 6;
                        this.updateProgress(6);
                        this.log(`✅ 频道订阅成功: ${message.data.channel}`, 'success');
                        this.updateStatus('subscription-status', '已订阅', 'success');
                        this.updateDiagnostic('channel-indicator', 'channel-diagnostic', 'online', '订阅成功');
                        this.updateDiagnostic('message-indicator', 'message-diagnostic', 'online', '准备接收');

                        this.log('🎉 WebSocket连接完全就绪！可以接收支付通知了', 'success');
                        break;

                    case 'payment_notification':
                        this.log(`💰 收到支付通知: ¥${message.data.amount}`, 'success');
                        this.updateDiagnostic('message-indicator', 'message-diagnostic', 'online', '消息正常');
                        break;

                    case 'pong':
                        this.log('💓 心跳正常', 'info');
                        break;

                    default:
                        this.log(`📋 其他消息: ${JSON.stringify(message)}`, 'info');
                }
            }

            // 计划重连
            scheduleReconnect() {
                this.reconnectAttempts++;
                const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 10000);

                this.log(`🔄 ${delay/1000}秒后尝试第${this.reconnectAttempts}次重连...`, 'warning');

                setTimeout(() => {
                    this.connectWebSocket();
                }, delay);
            }
        }

        // 全局实例
        const fixer = new WebSocketConnectionFixer();

        // 控制函数
        function connectWebSocket() {
            fixer.connectWebSocket();
        }

        function reconnectWebSocket() {
            if (fixer.ws) {
                fixer.ws.close();
            }
            fixer.reconnectAttempts = 0;
            setTimeout(() => fixer.connectWebSocket(), 1000);
        }

        function testNotification() {
            fixer.log('🧪 发送测试通知到服务器...', 'info');

            fetch('test_websocket_notify.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=force_broadcast'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    fixer.log(`✅ 测试通知发送成功`, 'success');
                    fixer.log(`📊 广播到 ${data.data.server_response?.broadcast_count || 0} 个连接`, 'info');
                } else {
                    fixer.log(`❌ 测试通知失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                fixer.log(`❌ 测试请求失败: ${error.message}`, 'error');
            });
        }

        function checkServerStatus() {
            fixer.log('📊 检查服务器状态...', 'info');

            fetch('test_websocket_notify.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=check_websocket_status'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const status = data.data;
                    fixer.log(`🌐 服务器运行: ${status.service_running ? '✅ 正常' : '❌ 异常'}`, status.service_running ? 'success' : 'error');
                    fixer.log(`💚 健康状态: ${status.health_status ? '✅ 健康' : '❌ 异常'}`, status.health_status ? 'success' : 'error');

                    if (status.stats_data) {
                        fixer.log(`📊 当前连接: ${status.stats_data.current_connections || 0}个`, 'info');
                        fixer.log(`📈 总消息数: ${status.stats_data.total_messages || 0}条`, 'info');
                    }

                    fixer.updateStatus('server-status',
                        status.service_running && status.health_status ? '服务正常' : '服务异常',
                        status.service_running && status.health_status ? 'success' : 'error'
                    );
                } else {
                    fixer.log(`❌ 状态检查失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                fixer.log(`❌ 状态检查请求失败: ${error.message}`, 'error');
                fixer.updateStatus('server-status', '检查失败', 'error');
            });
        }

        function disconnectWebSocket() {
            if (fixer.ws) {
                fixer.ws.close(1000, '用户主动断开');
                fixer.log('❌ 已断开WebSocket连接', 'warning');
            } else {
                fixer.log('⚠️ 没有活动的WebSocket连接', 'warning');
            }
        }

        function clearConsole() {
            document.getElementById('console').innerHTML = '';
            fixer.log('🗑️ 日志已清空', 'info');
        }
    </script>
</body>
</html>
