# POS打印功能开发方案

## 🎯 多打印方式统一管理方案

### 📱 不同平台打印策略

#### H5/Web端
- **主要方式**: 网络热敏打印机
- **备用方式**: 云打印服务
- **兜底方案**: 浏览器打印
- **自动发现**: 局域网扫描网络打印机、云打印服务可用性检测

#### APP端
- **主要方式**: 蓝牙打印机
- **备用方式**: 网络打印机
- **第三选择**: 云打印服务
- **自动发现**: 蓝牙设备扫描、局域网打印机发现、USB打印机检测

#### 小程序端
- **主要方式**: 云打印服务
- **备用方式**: 网络打印机（受限）
- **自动发现**: 云打印服务配置检测

#### POS机端
- **主要方式**: POS机内置热敏打印机
- **备用方式**: 蓝牙打印机
- **第三选择**: 网络打印机
- **自动发现**: 内置打印机检测、蓝牙设备扫描、局域网扫描

## 🔍 智能打印机发现系统

### 统一发现管理器

```javascript
class UnifiedPrinterManager {
  constructor() {
    this.printers = new Map()           // 已发现的打印机
    this.activePrinter = null           // 当前活动打印机
    this.discoveryManager = new DiscoveryManager()
    this.connectionManager = new ConnectionManager()
  }
  
  // 自动发现所有可用打印机
  async discoverPrinters() {
    console.log('🔍 开始自动发现打印机...')
    
    const discoveries = await Promise.allSettled([
      this.discoverNetworkPrinters(),
      this.discoverBluetoothPrinters(),
      this.discoverUSBPrinters(),
      this.discoverPOSNativePrinters(),
      this.discoverCloudPrinters()
    ])
    
    const allPrinters = this.mergeDiscoveryResults(discoveries)
    return this.rankPrintersByPriority(allPrinters)
  }
  
  // 智能选择最佳打印机
  async selectBestPrinter() {
    const printers = await this.discoverPrinters()
    
    // 按平台和场景选择最佳打印机
    const platform = this.getCurrentPlatform()
    const bestPrinter = this.getBestPrinterForPlatform(platform, printers)
    
    if (bestPrinter) {
      await this.setActivePrinter(bestPrinter)
      return bestPrinter
    }
    
    throw new Error('未发现可用的打印机')
  }
  
  // 统一打印接口
  async print(receiptData, options = {}) {
    if (!this.activePrinter) {
      await this.selectBestPrinter()
    }
    
    const adapter = this.getAdapterForPrinter(this.activePrinter)
    return await adapter.print(receiptData, options)
  }
}
```

### 发现流程
1. 并行启动所有扫描器
2. 收集发现的打印机
3. 测试连接可用性
4. 按优先级排序
5. 推荐最佳打印机

## 🖨️ 网络打印机自动发现

### 局域网扫描方法
- **mDNS/Bonjour服务发现** (推荐)
- **SNMP协议扫描**
- **IP段端口扫描**
- **UPnP设备发现**

### 网络打印机扫描器实现

```javascript
class NetworkPrinterScanner {
  async scan() {
    const printers = []
    
    // 1. mDNS扫描（推荐）
    const mdnsPrinters = await this.scanMDNS()
    printers.push(...mdnsPrinters)
    
    // 2. IP段扫描
    const ipPrinters = await this.scanIPRange()
    printers.push(...ipPrinters)
    
    // 3. 去重和验证
    return this.validatePrinters(printers)
  }
  
  async scanMDNS() {
    // 扫描 _printer._tcp, _ipp._tcp 等服务
    return await mdns.discover(['_printer._tcp', '_ipp._tcp'])
  }
  
  async scanIPRange() {
    const localIP = await this.getLocalIP()
    const subnet = this.getSubnet(localIP)
    
    // 扫描常用打印机端口：9100, 515, 631
    const ports = [9100, 515, 631]
    return await this.scanSubnetPorts(subnet, ports)
  }
}
```

### 支持的网络打印机品牌

| 品牌 | 型号 | 端口 | 协议 |
|------|------|------|------|
| **佳博(Gprinter)** | GP-3120TU, GP-58130IVC, GP-U80300I | 9100 | RAW, LPR |
| **汉印(HPRT)** | D35, N31, N41 | 9100, 515 | RAW, LPR, IPP |
| **芯烨(XINYE)** | XP-365B, XP-470B, XP-Q200 | 9100 | RAW |

## 📱 蓝牙打印机自动发现

### APP端蓝牙扫描

```javascript
class BluetoothPrinterScanner {
  async scan() {
    // 检查蓝牙权限
    await this.checkBluetoothPermission()
    
    // 开始扫描
    const devices = await this.scanBluetoothDevices()
    
    // 过滤打印机设备
    const printers = this.filterPrinterDevices(devices)
    
    // 测试连接
    return await this.testPrinterConnections(printers)
  }
  
  filterPrinterDevices(devices) {
    const printerKeywords = [
      'printer', 'print', 'thermal', 'receipt',
      'gprinter', 'hprt', 'xinye', 'pos'
    ]
    
    return devices.filter(device => {
      const name = device.name.toLowerCase()
      return printerKeywords.some(keyword => name.includes(keyword))
    })
  }
}
```

### 支持的蓝牙打印机型号
- 佳博 GP-2120TU
- 汉印 D35
- 芯烨 XP-P323B
- 启锐 QR-380A

## 🏗️ 打印机适配器架构

### 适配器模式设计
为不同类型的打印机提供统一接口：
- `NetworkPrinterAdapter` - 网络打印机适配器
- `BluetoothPrinterAdapter` - 蓝牙打印机适配器
- `USBPrinterAdapter` - USB打印机适配器
- `POSNativePrinterAdapter` - POS机内置打印机适配器
- `CloudPrintAdapter` - 云打印适配器

### 网络打印机适配器

```javascript
class NetworkPrinterAdapter extends BasePrinterAdapter {
  constructor(printer) {
    super(printer)
    this.type = 'network'
  }
  
  async connect() {
    try {
      // 测试TCP连接
      await this.testTCPConnection(this.printer.ip, this.printer.port)
      this.connected = true
      return true
    } catch (error) {
      this.connected = false
      throw new Error(`网络打印机连接失败: ${error.message}`)
    }
  }
  
  async print(receiptData) {
    if (!this.connected) {
      await this.connect()
    }
    
    // 生成ESC/POS指令
    const escposData = this.generateESCPOS(receiptData)
    
    // 发送到网络打印机
    return await this.sendToNetworkPrinter(escposData)
  }
}
```

### 蓝牙打印机适配器

```javascript
class BluetoothPrinterAdapter extends BasePrinterAdapter {
  constructor(printer) {
    super(printer)
    this.type = 'bluetooth'
  }
  
  async connect() {
    // #ifdef APP-PLUS
    try {
      await this.bluetoothConnect(this.printer.address)
      this.connected = true
      return true
    } catch (error) {
      throw new Error(`蓝牙打印机连接失败: ${error.message}`)
    }
    // #endif
  }
  
  async print(receiptData) {
    const escposData = this.generateESCPOS(receiptData)
    return await this.sendToBluetoothPrinter(escposData)
  }
}
```

### POS机内置打印机适配器

```javascript
class POSNativePrinterAdapter extends BasePrinterAdapter {
  constructor() {
    super({ name: 'POS内置打印机', type: 'pos_native' })
  }
  
  async connect() {
    // #ifdef APP-PLUS
    try {
      this.nativePlugin = uni.requireNativePlugin('pos-printer')
      await this.nativePlugin.init()
      this.connected = true
      return true
    } catch (error) {
      throw new Error(`POS打印机初始化失败: ${error.message}`)
    }
    // #endif
  }
  
  async print(receiptData) {
    // 直接调用POS机原生打印API
    return await this.nativePlugin.print({
      content: this.formatForPOS(receiptData)
    })
  }
}
```

## 📁 UniApp项目结构

```
src/
├── components/
│   └── printer/
│       ├── PrinterSetup.vue          # 打印机设置
│       ├── ReceiptPreview.vue        # 小票预览
│       ├── PrintHistory.vue          # 打印历史
│       └── PrinterDiscovery.vue      # 打印机发现
├── utils/
│   └── printer/
│       ├── printerManager.js         # 打印管理器
│       ├── receiptGenerator.js       # 小票生成器
│       ├── posAdapter.js             # POS机适配器
│       ├── networkAdapter.js         # 网络适配器
│       ├── bluetoothAdapter.js       # 蓝牙适配器
│       └── templateEngine.js         # 模板引擎
├── api/
│   └── printer.js                    # 打印相关API
├── pages/
│   └── settings/
│       └── printer/
│           ├── index.vue             # 打印设置
│           ├── discovery.vue         # 打印机发现
│           └── template.vue          # 模板管理
├── nativeplugins/                    # 原生插件
│   └── pos-printer/
└── mixins/
    └── printerMixin.js               # 打印功能混入

## 🔧 集成到现有WebSocket系统

### 修改WebSocket支付通知处理

```javascript
// 修改 mixins/websocketMixin.js
import PrinterManager from '@/utils/printer/printerManager.js'

export const websocketMixin = {
  data() {
    return {
      // 现有数据...
      printerManager: null
    }
  },

  async onLoad() {
    // 现有初始化...

    // 初始化打印管理器
    try {
      this.printerManager = new PrinterManager()
      await this.printerManager.initialize()
    } catch (error) {
      console.warn('⚠️ 打印机初始化失败，将跳过自动打印:', error)
    }
  },

  methods: {
    async handlePaymentNotification(paymentData) {
      try {
        // 现有的语音播报逻辑...
        await this.playPaymentVoice(amount)

        // 新增：自动打印小票
        if (this.shouldAutoPrint() && this.printerManager) {
          await this.printPaymentReceipt(paymentData)
        }

      } catch (error) {
        console.error('❌ 处理支付通知失败:', error)
      }
    },

    async printPaymentReceipt(paymentData) {
      try {
        // 获取完整订单数据
        const orderData = await this.getOrderPrintData(paymentData.orderId)

        // 执行打印
        await this.printerManager.printReceipt(orderData)

        console.log('✅ 小票打印成功')

        // 显示成功提示
        uni.showToast({
          title: '小票打印成功',
          icon: 'success'
        })

      } catch (error) {
        console.error('❌ 小票打印失败:', error)

        // 显示失败提示
        uni.showToast({
          title: '小票打印失败',
          icon: 'none'
        })
      }
    },

    async getOrderPrintData(orderId) {
      const response = await this.$http.get(`/api/orders/${orderId}/print-data`)
      return response.data
    },

    shouldAutoPrint() {
      const settings = uni.getStorageSync('print_settings')
      return settings && settings.autoPrint !== false
    }
  }
}
```

## 🎨 小票模板设计

### 标准小票模板结构

```javascript
const RECEIPT_TEMPLATE = {
  // 小票头部
  header: {
    merchantName: '商户名称',
    merchantPhone: '联系电话',
    merchantAddress: '商户地址',
    logo: '商户LOGO（可选）'
  },

  // 订单信息
  order: {
    orderNo: '订单号',
    payTime: '支付时间',
    payMethod: '支付方式',
    amount: '支付金额',
    discountAmount: '优惠金额（可选）',
    actualAmount: '实付金额'
  },

  // 商品明细（可选）
  items: [
    {
      name: '商品名称',
      quantity: '数量',
      price: '单价',
      total: '小计'
    }
  ],

  // 小票尾部
  footer: {
    thankYou: '感谢您的惠顾',
    contact: '如有问题请联系商户',
    qrCode: '商户二维码（可选）',
    timestamp: '打印时间'
  }
}
```

### 小票样式配置

```javascript
const RECEIPT_STYLES = {
  // 纸张设置
  paper: {
    width: '58mm', // 58mm/80mm
    margin: '2mm',
    lineHeight: '24px'
  },

  // 字体设置
  fonts: {
    title: { size: '16px', weight: 'bold', align: 'center' },
    content: { size: '12px', weight: 'normal', align: 'left' },
    amount: { size: '14px', weight: 'bold', align: 'right' }
  },

  // 分割线
  divider: {
    style: 'dashed',
    length: '32'
  }
}
```

## 🗄️ 后端数据库设计

### 打印模板表
```sql
CREATE TABLE print_templates (
  id INT PRIMARY KEY AUTO_INCREMENT,
  merchant_id INT NOT NULL,
  name VARCHAR(100) NOT NULL,
  type ENUM('receipt', 'kitchen', 'label') DEFAULT 'receipt',
  template_data JSON NOT NULL,
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 打印机配置表
```sql
CREATE TABLE printer_configs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  merchant_id INT NOT NULL,
  name VARCHAR(100) NOT NULL,
  type ENUM('pos_native', 'network', 'usb', 'bluetooth') NOT NULL,
  config_data JSON NOT NULL,
  is_enabled BOOLEAN DEFAULT TRUE,
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 打印历史表
```sql
CREATE TABLE print_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  merchant_id INT NOT NULL,
  order_id VARCHAR(50),
  printer_id INT,
  template_id INT,
  print_type ENUM('auto', 'manual', 'reprint') DEFAULT 'auto',
  status ENUM('success', 'failed', 'pending') NOT NULL,
  error_message TEXT,
  print_data JSON,
  printed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_merchant_order (merchant_id, order_id),
  INDEX idx_printed_at (printed_at)
);
```

### 打印设置表
```sql
CREATE TABLE print_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  merchant_id INT NOT NULL UNIQUE,
  auto_print BOOLEAN DEFAULT TRUE,
  print_copies INT DEFAULT 1,
  print_delay INT DEFAULT 0,
  settings_data JSON,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🔌 后端API设计

### 核心API接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/orders/{orderId}/print-data` | GET | 获取订单的完整打印数据 |
| `/api/print-templates` | GET/POST/PUT/DELETE | 小票模板的CRUD操作 |
| `/api/printer-configs` | GET/POST/PUT/DELETE | 打印机配置管理 |
| `/api/print-logs` | GET/POST | 打印历史记录和日志 |
| `/api/orders/{orderId}/reprint` | POST | 重新打印指定订单的小票 |
| `/api/print-settings` | GET/PUT | 商户打印设置管理 |

### 获取打印数据API响应示例

```javascript
{
  "code": 0,
  "data": {
    "order": {
      "orderId": "ORDER_20241201_001",
      "amount": "88.88",
      "payMethod": "支付宝",
      "payTime": "2024-12-01 14:30:25"
    },
    "merchant": {
      "name": "测试商户",
      "phone": "************",
      "address": "测试地址123号"
    },
    "template": {
      "id": 1,
      "name": "默认模板",
      "template_data": { /* 模板配置 */ }
    },
    "settings": {
      "autoPrint": true,
      "copies": 1,
      "delay": 0
    }
  }
}
```

## 🎨 打印机管理界面设计

### 自动发现界面组件

```vue
<!-- components/printer/PrinterDiscovery.vue -->
<template>
  <view class="printer-discovery">
    <view class="discovery-header">
      <text class="title">🔍 自动发现打印机</text>
      <button class="scan-btn" @click="startDiscovery" :disabled="isScanning">
        {{ isScanning ? '扫描中...' : '开始扫描' }}
      </button>
    </view>

    <!-- 扫描进度 -->
    <view class="scan-progress" v-if="isScanning">
      <view class="progress-item" v-for="scanner in scanners" :key="scanner.type">
        <text class="scanner-name">{{ scanner.name }}</text>
        <view class="status" :class="scanner.status">
          {{ getStatusText(scanner.status) }}
        </view>
      </view>
    </view>

    <!-- 发现的打印机列表 -->
    <view class="printer-list">
      <view class="section-title">发现的打印机 ({{ discoveredPrinters.length }})</view>

      <view class="printer-item"
            v-for="printer in discoveredPrinters"
            :key="printer.id"
            @click="selectPrinter(printer)">
        <view class="printer-info">
          <text class="printer-icon">{{ getPrinterIcon(printer.type) }}</text>
          <view class="printer-details">
            <text class="printer-name">{{ printer.name }}</text>
            <text class="printer-type">{{ printer.type }} - {{ printer.connection }}</text>
          </view>
        </view>

        <view class="printer-status">
          <text class="status-text" :class="printer.status">
            {{ printer.status }}
          </text>
          <button class="test-btn" @click.stop="testPrinter(printer)">
            测试
          </button>
        </view>
      </view>
    </view>

    <!-- 推荐配置 -->
    <view class="recommendations" v-if="recommendedPrinter">
      <view class="recommendation-header">
        <text class="title">💡 推荐配置</text>
      </view>
      <view class="recommended-printer">
        <text class="printer-name">{{ recommendedPrinter.name }}</text>
        <text class="reason">{{ recommendedPrinter.reason }}</text>
        <button class="apply-btn" @click="applyRecommendation">
          应用推荐
        </button>
      </view>
    </view>
  </view>
</template>
```

### 打印机管理主界面

```vue
<!-- pages/settings/printer/index.vue -->
<template>
  <view class="printer-management">
    <!-- 当前活动打印机 -->
    <view class="active-printer-section">
      <view class="section-header">
        <text class="title">🖨️ 当前打印机</text>
        <button class="change-btn" @click="showPrinterList">更换</button>
      </view>

      <view class="active-printer" v-if="activePrinter">
        <view class="printer-card">
          <text class="printer-icon">{{ getPrinterIcon(activePrinter.type) }}</text>
          <view class="printer-info">
            <text class="name">{{ activePrinter.name }}</text>
            <text class="type">{{ activePrinter.type }}</text>
            <text class="status" :class="activePrinter.status">
              {{ activePrinter.status }}
            </text>
          </view>
          <button class="test-print-btn" @click="testPrint">测试打印</button>
        </view>
      </view>

      <view class="no-printer" v-else>
        <text class="message">未设置打印机</text>
        <button class="setup-btn" @click="startDiscovery">自动发现</button>
      </view>
    </view>

    <!-- 打印设置 -->
    <view class="print-settings-section">
      <view class="section-header">
        <text class="title">⚙️ 打印设置</text>
      </view>

      <view class="setting-item">
        <text class="label">自动打印</text>
        <switch :checked="settings.autoPrint" @change="onAutoPrintChange" />
      </view>

      <view class="setting-item">
        <text class="label">打印份数</text>
        <picker mode="selector" :value="settings.copies - 1" :range="[1,2,3,4,5]" @change="onCopiesChange">
          <text class="value">{{ settings.copies }} 份</text>
        </picker>
      </view>

      <view class="setting-item">
        <text class="label">打印延迟</text>
        <picker mode="selector" :value="settings.delay" :range="delayOptions" @change="onDelayChange">
          <text class="value">{{ delayOptions[settings.delay] }}</text>
        </picker>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <button class="action-btn" @click="showDiscovery">
        🔍 发现打印机
      </button>
      <button class="action-btn" @click="showTemplates">
        📄 小票模板
      </button>
      <button class="action-btn" @click="showHistory">
        📋 打印历史
      </button>
    </view>
  </view>
</template>
```

## 🔧 实施优先级和时间规划

### 第一阶段：核心功能（1-2周）

#### 第1周
- ✅ 统一打印机管理器架构
- ✅ 网络打印机自动发现
- ✅ 基础ESC/POS指令封装
- ✅ 简单小票模板

#### 第2周
- ✅ 蓝牙打印机支持（APP）
- ✅ POS机内置打印机支持
- ✅ 自动打印设置
- ✅ 基础管理界面

#### 交付成果
- 可以自动发现网络和蓝牙打印机
- 支付成功后自动打印小票
- 基础的打印机管理界面
- 支持主流打印机品牌

### 第二阶段：增强功能（2-3周）

#### 功能特性
- 🎨 自定义小票模板编辑器
- ☁️ 云打印服务集成
- 📊 打印历史记录
- 🔧 多打印机管理
- 📱 打印机状态监控

#### 重点
提升用户体验和管理能力

### 第三阶段：高级功能（3-4周）

#### 功能特性
- 📈 打印统计分析
- 🛒 模板市场
- 📦 批量打印
- 🔄 打印队列管理
- 🎯 智能打印优化

#### 重点
商业化功能和高级特性

## 💡 技术优势总结

### 全平台兼容
- **H5**: 网络打印 + 云打印
- **APP**: 蓝牙 + 网络 + 云打印
- **小程序**: 云打印为主
- **POS机**: 内置 + 蓝牙 + 网络

### 智能化特性
- 自动发现打印机
- 智能推荐最佳配置
- 自动故障切换
- 性能优化建议

### 用户体验
- 零配置自动发现
- 一键测试打印
- 可视化管理界面
- 实时状态监控

### 商业价值
- 降低部署成本
- 提高运营效率
- 减少技术支持
- 增强用户满意度

## 🎯 最推荐方案

**网络热敏打印机 + ESC/POS指令**

### 理由
- ✅ 跨平台兼容性最好
- ✅ 无需安装驱动程序
- ✅ 支持远程打印
- ✅ 配置和维护简单
- ✅ 成本适中
- ✅ 打印速度快
- ✅ 小票质量好

### 部署策略
- **POS机**: 内置热敏打印 + 蓝牙打印备用
- **APP**: 蓝牙打印 + 网络打印
- **H5**: 网络打印 + 云打印
- **小程序**: 云打印服务

这个方案既照顾到了所有平台的需求，又提供了智能化的管理体验，是最全面和实用的解决方案。
```
```
