// utils/validate.js - 验证工具

/**
 * 验证手机号
 * @param {String} phone - 手机号
 * @returns {Boolean} 验证结果
 */
export function isPhone(phone) {
  return /^1[3-9]\d{9}$/.test(phone);
}

/**
 * 验证邮箱
 * @param {String} email - 邮箱
 * @returns {Boolean} 验证结果
 */
export function isEmail(email) {
  return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email);
}

/**
 * 验证身份证号
 * @param {String} idCard - 身份证号
 * @returns {Boolean} 验证结果
 */
export function isIdCard(idCard) {
  return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard);
}

/**
 * 验证银行卡号
 * @param {String} bankCard - 银行卡号
 * @returns {Boolean} 验证结果
 */
export function isBankCard(bankCard) {
  return /^\d{16,19}$/.test(bankCard);
}

/**
 * 验证URL
 * @param {String} url - URL
 * @returns {Boolean} 验证结果
 */
export function isUrl(url) {
  return /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w.-]*)*\/?$/.test(url);
}

/**
 * 验证金额
 * @param {String|Number} amount - 金额
 * @returns {Boolean} 验证结果
 */
export function isAmount(amount) {
  return /^(([1-9]\d*)|\d)(\.\d{1,2})?$/.test(amount);
}

/**
 * 验证是否为空
 * @param {*} value - 值
 * @returns {Boolean} 验证结果
 */
export function isEmpty(value) {
  return (
    value === undefined ||
    value === null ||
    value === '' ||
    (Array.isArray(value) && value.length === 0) ||
    (typeof value === 'object' && Object.keys(value).length === 0)
  );
} 