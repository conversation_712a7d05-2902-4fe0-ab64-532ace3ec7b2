// store/modules/order.js - 订单模块状态管理
import api from '@/api';
import { OrderModel } from '@/models';

// 初始状态
const state = {
  // 订单列表
  orders: [],
  // 当前订单
  currentOrder: null,
  // 分页信息
  pagination: {
    page: 1,
    limit: 10,
    hasMore: true
  },
  // 筛选条件
  filter: {
    status: null // null=全部，0=未支付，1=已支付
  }
};

// 修改状态的方法
const mutations = {
  // 设置订单列表
  SET_ORDERS(state, orders) {
    state.orders = orders.map(item => {
      if (item instanceof OrderModel) {
        return item;
      }
      return new OrderModel(item);
    });
  },
  // 添加订单到列表
  ADD_ORDERS(state, orders) {
    const newOrders = orders.map(item => {
      if (item instanceof OrderModel) {
        return item;
      }
      return new OrderModel(item);
    });
    state.orders = [...state.orders, ...newOrders];
  },
  // 设置当前订单
  SET_CURRENT_ORDER(state, order) {
    state.currentOrder = order instanceof OrderModel 
      ? order 
      : new OrderModel(order);
  },
  // 重置分页
  RESET_PAGINATION(state) {
    state.pagination = {
      page: 1,
      limit: 10,
      hasMore: true
    };
  },
  // 设置分页信息
  SET_PAGINATION(state, pagination) {
    state.pagination = { ...state.pagination, ...pagination };
  },
  // 设置筛选条件
  SET_FILTER(state, filter) {
    state.filter = { ...state.filter, ...filter };
  }
};

// 异步操作
const actions = {
  // 获取订单列表
  async getOrders({ commit, state }, { append = false } = {}) {
    try {
      if (!append) {
        commit('RESET_PAGINATION');
      }
      
      const { page, limit } = state.pagination;
      const offset = (page - 1) * limit;
      const status = state.filter.status;
      
      const response = await api.order.getOrderList(limit, offset, status);
      
      if (response.code === 0) { // 新版API成功码为0
        // 判断是否还有更多数据
        const hasMore = response.data && response.data.length === limit;
        
        // 更新分页信息
        commit('SET_PAGINATION', {
          page: page + 1,
          hasMore
        });
        
        // 更新订单列表
        if (append) {
          commit('ADD_ORDERS', response.data || []);
        } else {
          commit('SET_ORDERS', response.data || []);
        }
        
        return response.data || [];
      }
      
      return Promise.reject(new Error(response.msg || '获取订单列表失败'));
    } catch (error) {
      return Promise.reject(error);
    }
  },
  
  // 获取订单详情
  async getOrderDetail({ commit }, { outTradeNo, tradeNo }) {
    try {
      const response = await api.order.getOrder(outTradeNo, tradeNo);
      
      if (response.code === 0) { // 新版API成功码为0
        commit('SET_CURRENT_ORDER', response);
        return response;
      }
      
      return Promise.reject(new Error(response.msg || '获取订单详情失败'));
    } catch (error) {
      return Promise.reject(error);
    }
  },
  
  // 申请退款
  async refundOrder({ dispatch }, { outTradeNo, amount }) {
    try {
      const response = await api.order.refundOrder(outTradeNo, null, amount);
      
      if (response.code === 0) { // 新版API成功码为0
        // 退款成功后刷新订单详情
        await dispatch('getOrderDetail', { outTradeNo });
        return response;
      }
      
      return Promise.reject(new Error(response.msg || '申请退款失败'));
    } catch (error) {
      return Promise.reject(error);
    }
  },
  
  // 筛选订单
  async filterOrders({ commit, dispatch }, filter) {
    commit('SET_FILTER', filter);
    return dispatch('getOrders');
  }
};

// 获取状态的方法
const getters = {
  orders: state => state.orders,
  currentOrder: state => state.currentOrder,
  pagination: state => state.pagination,
  filter: state => state.filter,
  
  // 订单统计
  orderStats: state => {
    return {
      total: state.orders.length,
      paid: state.orders.filter(order => order.status === OrderModel.STATUS.PAID).length,
      pending: state.orders.filter(order => order.status === OrderModel.STATUS.PENDING).length
    };
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};