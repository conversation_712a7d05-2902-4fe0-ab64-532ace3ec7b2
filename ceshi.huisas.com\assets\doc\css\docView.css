#body {
	overflow: hidden;
}

.ztree * {
	font-size: 14px;
	font-family: "Microsoft Yahei", <PERSON><PERSON><PERSON>, <PERSON><PERSON>, "Segoe UI Web Light", "Segoe UI Light", "Segoe UI Web Regular", "Segoe UI", "Segoe UI Symbol", "Helvetica Neue", Arial
}

.ztree {
	padding: 0
}

.ztree li ul {
	margin: 0;
	padding: 0;
	background-color: #fafafa;
}

.ztree li {
	line-height: 32px;
	padding-left: 1em
}

.ztree li a {
	width: 100%;
	height: 32px;
	padding-top: 0;
	padding-right: 0;
	color: #666;
	box-sizing: border-box;
	overflow: hidden;
	text-overflow: ellipsis;
}

.ztree li a::before {
	content: " ";
	width: 100%;
	position: absolute;
	left: 0;
	user-select: none;
	height: 32px;
}

.ztree li a.curSelectedNode::before {
	background: #f1f0f0;
}

.ztree li a.curSelectedNode::after {
	content: ' ';
	display: block;
	width: 4px;
	float: right;
	background-color: #5FB878;
	height: 32px;
	position: relative;
}

.ztree li a:hover::before {
	font-weight: bold;
}

.ztree li a>span {
	position: relative
}

.ztree li.level0>a {
	padding-left: 0;
}

.ztree li a:hover {
	text-decoration: none;
	color: #5FB878;
}

.ztree li a span.button.switch {
	visibility: hidden;
}

.ztree li a span.button.noline_docu {
	pointer-events: none;
}

.ztree.showIcon li a span.button.switch {
	visibility: visible
}

.ztree li a.curSelectedNode {
	border: 0;
	height: 32px;
	font-weight: bold;
	color: #5FB878;
}

.ztree li span {
	line-height: 32px;
}

.ztree li span.button {
	margin-top: -7px;
	background-image: url("../images/left_menuForOutLook.png");
	*background-image: url("../images/left_menuForOutLook.gif")
}

.ztree li span.button.switch {
	width: 20px;
	height: 16px;
	z-index: 2
}

.ztree li span.button.switch.level0 {
	width: 20px;
	height: 20px
}

.ztree li span.button.switch.level1 {
	width: 20px;
	height: 20px
}

.ztree li span.button.noline_open {
	background-position: 0 0;
}

.ztree li span.button.noline_close {
	background-position: -18px 0;
}

.ztree li a.tmpTargetNode_inner {
	border: none;
}

.ztree li a.tmpTargetNode_inner::before {
	height: 32px;
	background-color: #5FB878;
	border: 1px solid #009688;
	opacity: 1;
	filter: alpha(opacity=100);
	box-sizing: border-box;
}

span.tmpzTreeMove_arrow {
	margin: 7px 0 0 1em;
}

#editormd {
	box-sizing: border-box;
}

#leftbar .copyright {
	color: #333;
	border-top: 1px solid #dddddd;
}

#leftbar .layui-tab {
	margin-bottom: 0;
}

#content_body>h1:first-child {
	color: #393D49;
	text-align: center;
	font-size: 2rem;
	padding-top: 12px;
}

#searchForm {
	padding: 0 10px 10px 10px;
	border-bottom: 1px solid #eee;
}

.searchResultNone {
	position: fixed;
	top: 50%;
	width: 260px;
	text-align: center;
	color: #c2c2c2;
}

.searchResultNone .layui-icon {
	font-size: 48px;
}

.searchResultNone p {
	margin-top: 4px;
	font-size: 1rem;
}

/* markdown正文 */

.markdown-body {
	-ms-text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%;
	color: #333;
	overflow: hidden;
	font-family: "Microsoft YaHei", Helvetica, "Meiryo UI", "Malgun Gothic", "Segoe UI", "Trebuchet MS", "Monaco", monospace, Tahoma, STXihei, "华文细黑", STHeiti, "Helvetica Neue", "Droid Sans", "wenquanyi micro hei", FreeSans, Arimo, Arial, SimSun, "宋体", Heiti, "黑体", sans-serif;
	font-size: 16px;
	line-height: 1.6;
	word-wrap: break-word;
	padding: 1em;
}

.markdown-body * {
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.markdown-body a {
	background: transparent;
	color: #01AAED;
	text-decoration: none;
}

.markdown-body h1>a,
.markdown-body h2>a,
.markdown-body h3>a,
.markdown-body h4>a,
.markdown-body h5>a,
.markdown-body h6>a {
	color: inherit;
}

.markdown-body a:active,
.markdown-body a:hover {
	outline: 0;
	text-decoration: underline;
}

.markdown-body strong {
	font-weight: bold;
}

.markdown-body img {
	border: 0;
	max-width: 100%;
}

.markdown-body hr {
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	height: 0;
}

.markdown-body code,
.markdown-body kbd {
	font-family: inherit;
	font-size: 1em;
}

.markdown-body input {
	color: inherit;
	font: inherit;
	margin: 0;
}

.markdown-body html input[disabled] {
	cursor: default;
}

.markdown-body input {
	line-height: normal;
}

.markdown-body input[type="checkbox"] {
	padding: 0;
}

.markdown-body input {
	font: 13px/1.4 Helvetica, arial, freesans, clean, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol";
}

.markdown-body hr {
	height: 0;
	margin: 15px 0;
	overflow: hidden;
	background: transparent;
	border: 0;
	border-bottom: 1px solid #ddd;
}

.markdown-body hr:before {
	display: table;
	content: "";
}

.markdown-body hr:after {
	display: table;
	clear: both;
	content: "";
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
	position: relative;
	margin-top: 1em;
	margin-bottom: 16px;
	font-weight: bold;
}

.markdown-body h1 {
	font-size: 2.25em;
	border-bottom: 1px solid #eee;
	text-align: center;
	margin-top: 0;
}

.markdown-body h2 {
	padding-bottom: 0.3em;
	font-size: 1.75em;
	line-height: 1.225;
	border-bottom: 1px solid #eee;
}

.markdown-body h3 {
	font-size: 1.5em;
	line-height: 1.43;
}

.markdown-body h4 {
	font-size: 1.25em;
}

.markdown-body h5 {
	font-size: 1em;
}

.markdown-body h6 {
	font-size: 1em;
	color: #777;
}

.markdown-body ul,
.markdown-body ol {
	padding: 0;
	margin-top: 0;
	margin-bottom: 0;
	padding-left: 2em;
}

.markdown-body ol ol,
.markdown-body ul ol {
	list-style-type: lower-roman;
}

.markdown-body ul ul ol,
.markdown-body ul ol ol,
.markdown-body ol ul ol,
.markdown-body ol ol ol {
	list-style-type: lower-alpha;
}

.markdown-body dd {
	margin-left: 0;
}

.markdown-body .octicon {
	font: normal normal 16px octicons-anchor;
	line-height: 1;
	display: inline-block;
	text-decoration: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.markdown-body .octicon-link:before {
	content: '\f05c';
}

.markdown-body>*:first-child {
	margin-top: 0 !important;
}

.markdown-body>*:last-child {
	margin-bottom: 0 !important;
}

.markdown-body .anchor {
	position: absolute;
	top: 0;
	left: 0;
	display: block;
	padding-right: 6px;
	padding-left: 30px;
	margin-left: -30px;
}

.markdown-body .anchor:focus {
	outline: none;
}

.markdown-body h1 .octicon-link,
.markdown-body h2 .octicon-link,
.markdown-body h3 .octicon-link,
.markdown-body h4 .octicon-link,
.markdown-body h5 .octicon-link,
.markdown-body h6 .octicon-link {
	display: none;
	color: #000;
	vertical-align: middle;
}

.markdown-body h1:hover .anchor,
.markdown-body h2:hover .anchor,
.markdown-body h3:hover .anchor,
.markdown-body h4:hover .anchor,
.markdown-body h5:hover .anchor,
.markdown-body h6:hover .anchor {
	padding-left: 8px;
	margin-left: -30px;
	text-decoration: none;
}

.markdown-body h1:hover .anchor .octicon-link,
.markdown-body h2:hover .anchor .octicon-link,
.markdown-body h3:hover .anchor .octicon-link,
.markdown-body h4:hover .anchor .octicon-link,
.markdown-body h5:hover .anchor .octicon-link,
.markdown-body h6:hover .anchor .octicon-link {
	display: inline-block;
}

.markdown-body h1 .anchor {
	line-height: 1;
}

.markdown-body h2 .anchor {
	line-height: 1;
}

.markdown-body h3 .anchor {
	line-height: 1.2;
}


.markdown-body h4 .anchor {
	line-height: 1.2;
}

.markdown-body h5 .anchor {
	line-height: 1.1;
}

.markdown-body h6 .anchor {
	line-height: 1.1;
}

.markdown-body p,
.markdown-body ul,
.markdown-body ol,
.markdown-body dl {
	margin-top: 0;
	margin-bottom: 16px;
}

.markdown-body ul ul,
.markdown-body ul ol,
.markdown-body ol ol,
.markdown-body ol ul {
	margin-top: 0;
	margin-bottom: 0;
}

.markdown-body li>p {
	margin-top: 16px;
}

.markdown-body dl {
	padding: 0;
}

.markdown-body dl dt {
	padding: 0;
	margin-top: 16px;
	font-size: 1em;
	font-style: italic;
	font-weight: bold;
}

.markdown-body dl dd {
	padding: 0 16px;
	margin-bottom: 16px;
}

.markdown-body code {
	padding: 0.2em;
	margin: 0;
	border-radius: 3px;
	color: #FF5722;
}

.markdown-body code:before,
.markdown-body code:after {
	letter-spacing: -0.2em;
	content: "\00a0";
}

.syntaxhighlighter code:before,
.syntaxhighlighter code:after {
	content: "";
}

.markdown-body .highlight {
	margin-bottom: 16px;
}

.markdown-body kbd {
	display: inline-block;
	padding: 3px 5px;
	font: 11px Consolas, "Liberation Mono", Menlo, Courier, monospace;
	line-height: 10px;
	color: #555;
	vertical-align: middle;
	background-color: #fcfcfc;
	border: solid 1px #ccc;
	border-bottom-color: #bbb;
	border-radius: 3px;
	box-shadow: inset 0 -1px 0 #bbb;
}

.markdown-body .pl-c {
	color: #969896;
}

.markdown-body .pl-c1,
.markdown-body .pl-mdh,
.markdown-body .pl-mm,
.markdown-body .pl-mp,
.markdown-body .pl-mr,
.markdown-body .pl-s1 .pl-v,
.markdown-body .pl-s3,
.markdown-body .pl-sc,
.markdown-body .pl-sv {
	color: #0086b3;
}

.markdown-body .pl-e,
.markdown-body .pl-en {
	color: #795da3;
}

.markdown-body .pl-s1 .pl-s2,
.markdown-body .pl-smi,
.markdown-body .pl-smp,
.markdown-body .pl-stj,
.markdown-body .pl-vo,
.markdown-body .pl-vpf {
	color: #333;
}

.markdown-body .pl-ent {
	color: #63a35c;
}

.markdown-body .pl-k,
.markdown-body .pl-s,
.markdown-body .pl-st {
	color: #a71d5d;
}

.markdown-body .pl-pds,
.markdown-body .pl-s1,
.markdown-body .pl-s1 .pl-pse .pl-s2,
.markdown-body .pl-sr,
.markdown-body .pl-sr .pl-cce,
.markdown-body .pl-sr .pl-sra,
.markdown-body .pl-sr .pl-sre,
.markdown-body .pl-src {
	color: #df5000;
}

.markdown-body .pl-mo,
.markdown-body .pl-v {
	color: #1d3e81;
}

.markdown-body .pl-id {
	color: #b52a1d;
}

.markdown-body .pl-ii {
	background-color: #b52a1d;
	color: #f8f8f8;
}

.markdown-body .pl-sr .pl-cce {
	color: #63a35c;
	font-weight: bold;
}

.markdown-body .pl-ml {
	color: #693a17;
}

.markdown-body .pl-mh,
.markdown-body .pl-mh .pl-en,
.markdown-body .pl-ms {
	color: #1d3e81;
	font-weight: bold;
}

.markdown-body .pl-mq {
	color: #008080;
}

.markdown-body .pl-mi {
	color: #333;
	font-style: italic;
}

.markdown-body .pl-mb {
	color: #333;
	font-weight: bold;
}

.markdown-body .pl-md,
.markdown-body .pl-mdhf {
	background-color: #ffecec;
	color: #bd2c00;
}

.markdown-body .pl-mdht,
.markdown-body .pl-mi1 {
	background-color: #eaffea;
	color: #55a532;
}

.markdown-body .pl-mdr {
	color: #795da3;
	font-weight: bold;
}

.markdown-body .task-list-item {
	list-style-type: none;
}

.markdown-body .task-list-item+.task-list-item {
	margin-top: 3px;
}

.markdown-body .task-list-item input {
	float: left;
	margin: 0.3em 0 0.25em -1.6em;
	vertical-align: middle;
}

.markdown-body :checked+.radio-label {
	z-index: 1;
	position: relative;
	border-color: #4183c4;
}

.markdown-body .editormd-toc-menu ul {
	padding-left: 0;
}

.markdown-body th {
	font-weight: bold;
}

.syntaxhighlighter a,
.syntaxhighlighter div,
.syntaxhighlighter code,
.syntaxhighlighter,
.syntaxhighlighter td,
.syntaxhighlighter tr,
.syntaxhighlighter tbody,
.syntaxhighlighter thead,
.syntaxhighlighter caption,
.syntaxhighlighter textarea {
	font-family: Menlo, Monaco, Menlo, Consolas, source-code-pro, "Liberation Mono", "Ubuntu Mono", Courier, "Helvetica Neue", "Microsoft Yahei", 微软雅黑, "Lantinghei SC", STXihei, WenQuanYi, sans-serif;
	font-size: 14px !important;
	line-height: 1.625em !important;
}

.syntaxhighlighter {
	margin: 1.625em 0 !important;
	background-color: #fafafa !important;
}

.syntaxhighlighter textarea {
	height: 100%;
}

.layui-elem-quote {
	border-color: #5FB878;
	background-color: #fafafa;
}

.layui-elem-quote>p:last-child {
	margin-bottom: 0;
}

li {
	list-style: inherit;
	margin-bottom: 12px;
}

.syntaxhighlighter .gutter {
	color: #237893 !important;
}

.syntaxhighlighter .plain,
.syntaxhighlighter .plain a {
	word-break: break-all;
	color: #333 !important
}

.syntaxhighlighter .comments,
.syntaxhighlighter .comments a {
	color: gray !important;
}

.syntaxhighlighter .string,
.syntaxhighlighter .string a {
	color: #10893E !important
}

.syntaxhighlighter .keyword {
	color: #ff7800 !important
}

.syntaxhighlighter .variable {
	color: #1E9FFF !important
}

.syntaxhighlighter .functions {
	color: #FF5722 !important
}

.syntaxhighlighter .constants {
	color: #5200FF !important
}

#article-content {
	position: relative;
}

#article-content li {
	list-style: inherit;
}

#content-toc-wrap {
	width: 16em;
	position: fixed;
	right: 2em;
	z-index: 99;
	border: 1px solid #dbdbdb;
	background-color: rgba(255, 255, 255, 0.618);
	backdrop-filter: blur(4px);
}

#content-toc {
	overflow: auto;
}

#content-toc ol {
	overflow: auto;
}

#content-toc>ol {
	padding: 0.6em;
	margin: 0;
}

#content-toc li {
	line-height: 28px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	width: 100%;
	float: left;
	margin-bottom: 0;
}

#content-toc a {
	color: #333;
}

#content-toc a:hover {
	color: #5FB878;
	text-decoration: none;
}

.content-toc-title {
	margin-bottom: 0 !important;
	border-bottom: 1px solid #dbdbdb;
	padding: 0.5em;
	font-weight: bold;
	cursor: move;
}

@media screen and (max-width: 768px) {
	#content-toc-wrap {
		display: none;
	}
}