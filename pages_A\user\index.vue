<template>
  <view class="container">
    <custom-navbar title="会员中心" :shadow="true"></custom-navbar>
    
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="avatar-container">
        <image class="avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'"></image>
      </view>
      <view class="user-info">
        <view class="username">{{ userInfo.nickname || '测试用户' }}</view>
        <view class="user-level" v-if="userInfo.level">
          <text class="level-tag">{{ userInfo.level }}</text>
        </view>
      </view>
      <view class="login-btn" v-if="!isLoggedIn" @tap="handleLogin">
        登录/注册
      </view>
    </view>
    
    <!-- 会员数据统计 -->
    <view class="statistics-card">
      <view class="statistics-item" @tap="navigateTo('/pages_A/user/wallet')">
        <view class="statistics-value">{{ formatMoney(userInfo.balance || 0) }}</view>
        <view class="statistics-label">余额</view>
      </view>
      <view class="statistics-item" @tap="navigateTo('/pages_A/user/points')">
        <view class="statistics-value">{{ userInfo.points || 0 }}</view>
        <view class="statistics-label">积分</view>
      </view>
      <view class="statistics-item" @tap="navigateTo('/pages_A/user/coupons')">
        <view class="statistics-value">{{ userInfo.coupons || 0 }}</view>
        <view class="statistics-label">优惠券</view>
      </view>
    </view>
    
    <!-- 我的订单 -->
    <view class="section-card orders-card">
      <view class="section-header">
        <text class="section-title">我的订单</text>
        <view class="more-link" @tap="navigateTo('/pages_A/order/list')">
          查看全部 <text class="arrow">></text>
        </view>
      </view>
      <view class="order-icons">
        <view class="order-icon-item" @tap="navigateTo('/pages_A/order/list?status=0')">
          <view class="icon-container">
            <image src="/static/images/order-pending.png" class="order-icon"></image>
            <view class="badge" v-if="orderCounts.pending > 0">{{ orderCounts.pending }}</view>
          </view>
          <text class="icon-label">待付款</text>
        </view>
        <view class="order-icon-item" @tap="navigateTo('/pages_A/order/list?status=1')">
          <view class="icon-container">
            <image src="/static/images/order-processing.png" class="order-icon"></image>
            <view class="badge" v-if="orderCounts.processing > 0">{{ orderCounts.processing }}</view>
          </view>
          <text class="icon-label">处理中</text>
        </view>
        <view class="order-icon-item" @tap="navigateTo('/pages_A/order/list?status=3')">
          <view class="icon-container">
            <image src="/static/images/order-completed.png" class="order-icon"></image>
          </view>
          <text class="icon-label">已完成</text>
        </view>
        <view class="order-icon-item" @tap="navigateTo('/pages_A/order/list?status=4')">
          <view class="icon-container">
            <image src="/static/images/order-refund.png" class="order-icon"></image>
          </view>
          <text class="icon-label">退款/售后</text>
        </view>
      </view>
    </view>
    
    <!-- 会员服务 -->
    <view class="section-card services-card">
      <view class="section-header">
        <text class="section-title">会员服务</text>
      </view>
      <view class="services-grid">
        <view class="service-item" @tap="navigateTo('/pages_A/user/coupons')">
          <image src="/static/images/service-coupon.png" class="service-icon"></image>
          <text class="service-label">优惠券</text>
        </view>
        <view class="service-item" @tap="navigateTo('/pages_A/user/history')">
          <image src="/static/images/service-history.png" class="service-icon"></image>
          <text class="service-label">消费记录</text>
        </view>
        <view class="service-item" @tap="navigateTo('/pages_A/user/settings')">
          <image src="/static/images/service-settings.png" class="service-icon"></image>
          <text class="service-label">会员设置</text>
        </view>
        <view class="service-item" @tap="navigateTo('/pages_A/user/help')">
          <image src="/static/images/service-help.png" class="service-icon"></image>
          <text class="service-label">帮助中心</text>
        </view>
      </view>
    </view>
    
    <!-- 退出登录按钮 -->
    <view class="logout-container" v-if="isLoggedIn">
      <button class="logout-button" @tap="handleLogout">退出登录</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isLoggedIn: false,
      userInfo: {
        nickname: '',
        avatar: '',
        level: '',
        balance: 0,
        points: 0,
        coupons: 0
      },
      orderCounts: {
        pending: 0,
        processing: 0
      }
    }
  },
  onLoad() {
    // 检查登录状态
    this.checkLoginStatus();
  },
  onShow() {
    // 每次页面显示时刷新数据
    this.loadUserInfo();
    this.loadOrderCounts();
  },
  methods: {
    // 检查登录状态
    checkLoginStatus() {
      const token = uni.getStorageSync('token');
      this.isLoggedIn = !!token;
      
      // 如果已登录，加载用户信息
      if (this.isLoggedIn) {
        this.loadUserInfo();
        this.loadOrderCounts();
      }
    },
    
    // 加载用户信息
    loadUserInfo() {
      // 这里应该是从API获取用户信息
      // 为演示目的，使用模拟数据
      setTimeout(() => {
        this.userInfo = {
          nickname: '测试用户',
          avatar: '/static/images/default-avatar.png',
          level: '普通会员',
          balance: 258.66,
          points: 520,
          coupons: 3
        };
      }, 500);
      
      // 实际API调用应该类似于:
      /*
      uni.request({
        url: '/api/user/info',
        method: 'GET',
        success: (res) => {
          if (res.data.code === 0) {
            this.userInfo = res.data.data;
          }
        }
      });
      */
    },
    
    // 加载订单数量统计
    loadOrderCounts() {
      // 模拟数据
      setTimeout(() => {
        this.orderCounts = {
          pending: 2,
          processing: 1
        };
      }, 500);
    },
    
    // 格式化金额显示
    formatMoney(amount) {
      return '¥' + amount.toFixed(2);
    },
    
    // 页面导航
    navigateTo(url) {
      uni.navigateTo({
        url: url
      });
    },
    
    // 处理登录点击
    handleLogin() {
      uni.navigateTo({
        url: '/pages_A/user/login'
      });
    },
    
    // 处理退出登录
    handleLogout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除登录信息
            uni.removeStorageSync('token');
            this.isLoggedIn = false;
            this.userInfo = {
              nickname: '',
              avatar: '',
              level: '',
              balance: 0,
              points: 0,
              coupons: 0
            };
            
            uni.showToast({
              title: '已退出登录',
              icon: 'none'
            });
          }
        }
      });
    }
  }
}
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f9;
  padding-bottom: 40rpx;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #6366F1, #8b5cf6);
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.avatar-container {
  position: relative;
  margin-right: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  background-color: #fff;
}

.user-info {
  flex: 1;
}

.username {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-level {
  display: flex;
  align-items: center;
}

.level-tag {
  font-size: 24rpx;
  color: #6366F1;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.login-btn {
  color: #fff;
  font-size: 28rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
}

/* 会员数据统计 */
.statistics-card {
  background-color: #fff;
  margin-top: -20rpx;
  margin-left: 20rpx;
  margin-right: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx 0;
  display: flex;
  justify-content: space-around;
  z-index: 10;
  position: relative;
}

.statistics-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 20rpx;
}

.statistics-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}

.statistics-label {
  font-size: 24rpx;
  color: #999;
}

/* 卡片通用样式 */
.section-card {
  background-color: #fff;
  margin: 30rpx 20rpx 0;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.more-link {
  font-size: 24rpx;
  color: #999;
}

.arrow {
  font-size: 24rpx;
  margin-left: 4rpx;
}

/* 订单图标区域 */
.order-icons {
  display: flex;
  justify-content: space-between;
}

.order-icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
}

.icon-container {
  position: relative;
  margin-bottom: 10rpx;
}

.order-icon {
  width: 60rpx;
  height: 60rpx;
}

.badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background-color: #ff4d4f;
  color: #fff;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
}

.icon-label {
  font-size: 24rpx;
  color: #666;
}

/* 会员服务 */
.services-grid {
  display: flex;
  flex-wrap: wrap;
}

.service-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.service-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.service-label {
  font-size: 26rpx;
  color: #666;
}

/* 退出登录按钮 */
.logout-container {
  margin: 60rpx 50rpx;
}

.logout-button {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #f0f0f0;
  border-radius: 40rpx;
  color: #666;
  font-size: 28rpx;
}
</style> 