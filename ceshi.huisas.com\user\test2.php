<?php
include("../includes/common.php");
if($islogin2==1){}else exit("<script language='javascript'>window.location.href='./login.php';</script>");
if(isset($_GET['ok']) && isset($_GET['trade_no'])){
	$trade_no=daddslashes($_GET['trade_no']);
	$row=$DB->getRow("SELECT * FROM pre_order WHERE trade_no='{$trade_no}' AND uid='{$conf['test_pay_uid']}' limit 1");
	if(!$row)sysmsg('订单号不存在');
	if($row['status']!=1)sysmsg('订单未完成支付');
	$money = $row['money'];
}else{
	$trade_no=date("YmdHis").rand(111,999);
	$userrow = $DB->getRow("SELECT uid,gid FROM pre_user WHERE uid='{$uid}' limit 1");
	if(!$userrow)sysmsg("商户不存在");
	$paytype = \lib\Channel::getTypes($userrow['uid'], $userrow['gid']);
	$csrf_token = md5(mt_rand(0,999).time());
	$_SESSION['csrf_token'] = $csrf_token;
	$money = 1;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
	<title><?php echo $conf['sitename']?> - 动态码支付</title>
    <link href="<?php echo $cdnpublic?>twitter-bootstrap/3.4.1/css/bootstrap.min.css" rel="stylesheet"/>
	<link rel="stylesheet" href="./assets/css/captcha.css" type="text/css" />
	<style>.form-group{margin-bottom:18px} #captcha{margin: auto;margin-bottom:16px}</style>
</head>
<body>
<?php if(isset($_GET['ok'])){?>
<div style="padding:10px;">
<h3>动态码收款</h3>
<?php }?>
<form name="alipayment">
<div class="form-group"><div class="input-group">
<span class="input-group-addon"><span class="glyphicon glyphicon-barcode"></span></span>
<input class="form-control" placeholder="商户订单号" value="<?php echo $trade_no?>" name="trade_no" type="text" disabled="">
</div></div>
<div class="form-group"><div class="input-group">
<span class="input-group-addon"><span class="glyphicon glyphicon-shopping-cart"></span></span>
<input class="form-control" placeholder="请填写你的商品名称" value="在线支付" name="name" type="text" <?php echo isset($_GET['ok'])?'disabled=""':'required=""'?>>
</div></div>
<div class="form-group"><div class="input-group">
<span class="input-group-addon"><span class="glyphicon glyphicon-yen"></span></span>
<input class="form-control" placeholder="付款金额" value="<?php echo $money?>" name="money" type="text" <?php echo isset($_GET['ok'])?'disabled=""':'required=""'?>>	        
</div></div>
<center>
<?php if(isset($_GET['ok'])){?>
<div class="alert alert-success"><i class="glyphicon glyphicon-ok-circle"></i>&nbsp;订单已支付成功！</div>
<?php }else{?>
<div class="btn-group text-center" style="margin-bottom:15px;font-weight: bold;font-size:15px">生成动态码</div>
<div class="btn-group btn-group-justified" role="group" aria-label="...">
<?php foreach($paytype as $rows){?>
<div class="btn-group text-center" role="group">
<button type="button" name="type" value="<?php echo $rows['id']?>" class="btn btn-default" onclick="submitPay(this)"><img src="/assets/icon/<?php echo $rows['name']?>.ico" height="18">&nbsp;<?php echo $rows['showname']?></button>
</div>
<?php }?>
</div>
<?php }?>
</center>
</form>
<?php if(isset($_GET['ok'])){?>
</div>
<?php }?>
<script src="<?php echo $cdnpublic?>jquery/3.4.1/jquery.min.js"></script>
<script src="<?php echo $cdnpublic?>twitter-bootstrap/3.4.1/js/bootstrap.min.js"></script>
<script src="<?php echo $cdnpublic?>layer/3.1.1/layer.min.js"></script>
<script>
function submitPay(obj){
	var name=$("input[name='name']").val();
	var money=$("input[name='money']").val();
	var typeid=$(obj).val();
	if(money==''){
		layer.alert("金额不能为空");
		return false;
	}
	if(name==''){
		layer.alert("商品名称不能为空");
		return false;
	}
	var data = {money:money, typeid:typeid, name: name};
	var ii = layer.load();
	$.ajax({
		type: "POST",
		dataType: "json",
		data: data,
		url: "ajax_dtm.php?act=testpay2",
		success: function (data, textStatus) {
			layer.close(ii);
			if (data.code == 0) {
			    top.document.querySelector('#dtmxs').src = data.url;
			}else{
				layer.alert(data.msg, {icon: 2});
			}
		},
		error: function (data) {
			layer.msg('服务器错误', {icon: 2});
		}
	});
	return false;
}

</script>
</body>
</html>