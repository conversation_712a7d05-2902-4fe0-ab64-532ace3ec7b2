<?php
/**
 * 预制码处理核心文件
 * 路径: /paypage/precode.php
 * 功能: 处理预制码扫码访问，判断绑定状态并跳转
 */

// 引入公共文件
include("../includes/common.php");

// 获取预制码参数
$code = $_GET['code'] ?? '';

// 记录访问日志
error_log("预制码访问: " . $code . " - IP: " . get_client_ip() . " - Time: " . date('Y-m-d H:i:s'));

if(empty($code)) {
    exit('参数错误：预制码不能为空');
}

// 验证预制码格式（8位字母数字组合）
if(!preg_match('/^[A-Z0-9]{8}$/', $code)) {
    exit('参数错误：预制码格式不正确');
}

try {
    // 查询预制码信息
    $precode = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_precode` WHERE `code`=?", [$code]);
    
    if(!$precode) {
        // 预制码不存在，显示错误页面
        showErrorPage('预制码不存在', '该预制码不存在或已失效，请联系商户确认。');
        exit();
    }
    
    // 检查预制码状态
    if($precode['status'] == 0) {
        // 未绑定状态，跳转到绑定页面
        $bindUrl = $siteurl . "pages/precode-bind/index.html?code=" . urlencode($code);
        
        // 记录绑定页面访问
        error_log("预制码未绑定，跳转绑定页面: " . $code . " -> " . $bindUrl);
        
        header("Location: " . $bindUrl);
        exit();
        
    } elseif($precode['status'] == 1) {
        // 已绑定状态，跳转到支付页面
        
        // 构建支付页面URL
        $payUrl = $siteurl . "paypage/?uid=" . $precode['uid'];
        
        // 如果绑定了员工，添加员工参数
        if($precode['staff_id']) {
            $payUrl .= "&staff_id=" . $precode['staff_id'];
        }
        
        // 添加预制码标识参数
        $payUrl .= "&precode=" . urlencode($code);
        
        // 记录支付页面跳转
        error_log("预制码已绑定，跳转支付页面: " . $code . " -> " . $payUrl);
        
        header("Location: " . $payUrl);
        exit();
        
    } else {
        // 其他状态（如已禁用）
        showErrorPage('预制码已禁用', '该预制码已被禁用，无法使用。');
        exit();
    }
    
} catch(Exception $e) {
    // 数据库错误处理
    error_log("预制码处理异常: " . $e->getMessage());
    showErrorPage('系统错误', '系统暂时无法处理您的请求，请稍后重试。');
    exit();
}

/**
 * 显示错误页面
 * @param string $title 错误标题
 * @param string $message 错误信息
 */
function showErrorPage($title, $message) {
    global $siteurl;
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo htmlspecialchars($title); ?></title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
                color: #333;
            }
            .container {
                max-width: 400px;
                margin: 50px auto;
                background: white;
                border-radius: 12px;
                padding: 30px;
                text-align: center;
                box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            }
            .icon {
                font-size: 48px;
                color: #ff6b6b;
                margin-bottom: 20px;
            }
            .title {
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 15px;
                color: #333;
            }
            .message {
                font-size: 16px;
                line-height: 1.5;
                color: #666;
                margin-bottom: 30px;
            }
            .btn {
                display: inline-block;
                padding: 12px 24px;
                background-color: #5145F7;
                color: white;
                text-decoration: none;
                border-radius: 8px;
                font-size: 16px;
                transition: background-color 0.3s;
            }
            .btn:hover {
                background-color: #4139d4;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="icon">⚠️</div>
            <div class="title"><?php echo htmlspecialchars($title); ?></div>
            <div class="message"><?php echo htmlspecialchars($message); ?></div>
            <a href="<?php echo $siteurl; ?>" class="btn">返回首页</a>
        </div>
    </body>
    </html>
    <?php
}

/**
 * 获取客户端IP地址
 * @return string
 */
function get_client_ip() {
    $ipkeys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR');
    foreach ($ipkeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}
?>
