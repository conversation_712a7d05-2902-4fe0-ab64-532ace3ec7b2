<?php
/**
 * 员工管理API接口 - JSONP版本（绕过CORS）
 * 文件位置: epay_release_99009/user/staff_jsonp.php
 */

// 设置内容类型为JavaScript
header('Content-Type: application/javascript; charset=utf-8');

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 日志记录函数
function logDebug($message, $data = null) {
    $logFile = __DIR__ . '/staff_debug.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] JSONP - $message";
    if ($data !== null) {
        $logMessage .= " | Data: " . json_encode($data);
    }
    $logMessage .= "\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

// 获取回调函数名
$callback = isset($_GET['callback']) ? $_GET['callback'] : 'callback';

// 验证回调函数名（安全检查）
if (!preg_match('/^[a-zA-Z_$][a-zA-Z0-9_$]*$/', $callback)) {
    $callback = 'callback';
}

try {
    logDebug("JSONP API调用开始", $_REQUEST);
    
    include("../includes/common.php");
    
    // 检查数据库连接
    if (!isset($DB) || !$DB) {
        logDebug("数据库连接失败");
        $response = ['code' => -1, 'msg' => '数据库连接失败'];
        echo $callback . '(' . json_encode($response) . ');';
        exit;
    }
    
    // 获取操作类型
    $act = isset($_GET['act']) ? $_GET['act'] : (isset($_POST['act']) ? $_POST['act'] : '');
    logDebug("操作类型", $act);
    
    // 模拟登录状态（用于测试）
    if (!isset($islogin2) || $islogin2 != 1) {
        // 测试模式：允许未登录用户操作
        $islogin2 = 1;
        $uid = 1; // 使用测试用户ID
        logDebug("使用测试模式，uid=1");
    }
    
    logDebug("用户状态", ['islogin2' => $islogin2, 'uid' => $uid]);
    
    switch($act) {
        
        // 获取员工列表
        case 'getStaffList':
            try {
                logDebug("开始获取员工列表");
                
                $sql = "SELECT * FROM pre_staff WHERE uid = :uid AND status = 1 ORDER BY id ASC";
                $staffList = $DB->getAll($sql, [':uid' => $uid]);
                
                logDebug("查询结果", ['count' => count($staffList)]);
                
                $result = [];
                foreach($staffList as $staff) {
                    $result[] = [
                        'id' => intval($staff['id']),
                        'name' => $staff['name'],
                        'role' => $staff['role'],
                        'account' => $staff['account'] ?? '',
                        'color' => $staff['avatar_color'] ?? 'blue',
                        'phone' => $staff['phone'] ?? '',
                        'email' => $staff['email'] ?? '',
                        'addtime' => $staff['addtime']
                    ];
                }
                
                logDebug("返回员工列表", $result);
                $response = ['code' => 0, 'data' => $result];
                
            } catch (Exception $e) {
                logDebug("获取员工列表失败", $e->getMessage());
                $response = ['code' => -1, 'msg' => '获取员工列表失败: ' . $e->getMessage()];
            }
            break;
        
        // 添加员工
        case 'addStaff':
            try {
                logDebug("开始添加员工", $_REQUEST);
                
                $name = trim($_REQUEST['name'] ?? '');
                $role = trim($_REQUEST['role'] ?? '收银员');
                $account = trim($_REQUEST['account'] ?? '');
                $password = trim($_REQUEST['password'] ?? '');
                $phone = trim($_REQUEST['phone'] ?? '');
                $email = trim($_REQUEST['email'] ?? '');
                $avatar_color = trim($_REQUEST['avatar_color'] ?? 'blue');
                
                logDebug("解析参数", [
                    'name' => $name,
                    'role' => $role,
                    'account' => $account,
                    'phone' => $phone,
                    'email' => $email,
                    'avatar_color' => $avatar_color
                ]);
                
                // 基本验证
                if(empty($name)) {
                    logDebug("验证失败：员工姓名为空");
                    $response = ['code' => -1, 'msg' => '员工姓名不能为空'];
                    break;
                }
                
                // 如果提供了账号，则密码也必须提供
                if(!empty($account) && empty($password)) {
                    logDebug("验证失败：提供账号但密码为空");
                    $response = ['code' => -1, 'msg' => '提供登录账号时密码不能为空'];
                    break;
                }
                
                // 检查同名员工
                $exists = $DB->getRow("SELECT id FROM pre_staff WHERE uid = :uid AND name = :name", 
                    [':uid' => $uid, ':name' => $name]);
                
                if($exists) {
                    logDebug("验证失败：员工姓名已存在");
                    $response = ['code' => -1, 'msg' => '员工姓名已存在'];
                    break;
                }
                
                // 如果提供了账号，检查账号是否已存在
                if(!empty($account)) {
                    $accountExists = $DB->getRow("SELECT id FROM pre_staff WHERE uid = :uid AND account = :account", 
                        [':uid' => $uid, ':account' => $account]);
                    
                    if($accountExists) {
                        logDebug("验证失败：登录账号已存在");
                        $response = ['code' => -1, 'msg' => '登录账号已存在'];
                        break;
                    }
                }
                
                // 准备插入数据
                $data = [
                    'uid' => $uid,
                    'name' => $name,
                    'role' => $role,
                    'account' => $account,
                    'password' => !empty($password) ? md5($password) : '',
                    'phone' => $phone,
                    'email' => $email,
                    'avatar_color' => $avatar_color,
                    'status' => 1,
                    'addtime' => date('Y-m-d H:i:s')
                ];
                
                logDebug("准备插入数据", $data);
                
                // 插入员工数据
                $staff_id = $DB->insert('staff', $data);
                
                if($staff_id) {
                    logDebug("添加员工成功", ['staff_id' => $staff_id]);
                    
                    // 记录操作日志（如果表存在）
                    try {
                        $DB->insert('staff_log', [
                            'uid' => $uid,
                            'staff_id' => $staff_id,
                            'action' => 'add_staff',
                            'content' => json_encode(['name' => $name, 'role' => $role]),
                            'ip' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
                            'addtime' => date('Y-m-d H:i:s')
                        ]);
                    } catch (Exception $e) {
                        logDebug("记录日志失败（忽略）", $e->getMessage());
                    }
                    
                    $response = ['code' => 0, 'msg' => '添加员工成功', 'staff_id' => $staff_id];
                } else {
                    logDebug("插入数据库失败");
                    $response = ['code' => -1, 'msg' => '添加员工失败：数据库插入失败'];
                }
                
            } catch (Exception $e) {
                logDebug("添加员工异常", $e->getMessage());
                $response = ['code' => -1, 'msg' => '添加员工失败: ' . $e->getMessage()];
            }
            break;
        
        // 删除员工
        case 'deleteStaff':
            try {
                $staff_id = intval($_REQUEST['staff_id'] ?? 0);
                logDebug("开始删除员工", ['staff_id' => $staff_id]);
                
                if($staff_id <= 0) {
                    $response = ['code' => -1, 'msg' => '员工ID无效'];
                    break;
                }
                
                // 检查员工是否存在
                $staff = $DB->getRow("SELECT * FROM pre_staff WHERE id = :id AND uid = :uid", 
                    [':id' => $staff_id, ':uid' => $uid]);
                
                if(!$staff) {
                    $response = ['code' => -1, 'msg' => '员工不存在'];
                    break;
                }
                
                // 软删除（设置状态为0）
                $result = $DB->update('staff', ['status' => 0, 'updatetime' => date('Y-m-d H:i:s')], 
                    ['id' => $staff_id, 'uid' => $uid]);
                
                if($result !== false) {
                    logDebug("删除员工成功", ['staff_id' => $staff_id]);
                    $response = ['code' => 0, 'msg' => '删除员工成功'];
                } else {
                    $response = ['code' => -1, 'msg' => '删除员工失败'];
                }
                
            } catch (Exception $e) {
                logDebug("删除员工异常", $e->getMessage());
                $response = ['code' => -1, 'msg' => '删除员工失败: ' . $e->getMessage()];
            }
            break;
        
        default:
            logDebug("无效操作", $act);
            $response = ['code' => -1, 'msg' => '无效的操作: ' . $act];
            break;
    }
    
} catch (Exception $e) {
    logDebug("全局异常", $e->getMessage());
    $response = ['code' => -1, 'msg' => '系统错误: ' . $e->getMessage()];
}

// 输出JSONP响应
echo $callback . '(' . json_encode($response) . ');';
?>
