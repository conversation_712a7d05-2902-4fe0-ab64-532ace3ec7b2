<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音播报设置 - 增强版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 5px 0 0;
            opacity: 0.9;
        }
        
        .content {
            padding: 20px;
        }
        
        .setting-group {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .group-title {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
            color: #495057;
        }
        
        .group-title .icon {
            margin-right: 10px;
            font-size: 20px;
        }
        
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .setting-left {
            flex: 1;
        }
        
        .setting-label {
            font-size: 16px;
            font-weight: 500;
            color: #212529;
            display: block;
            margin-bottom: 5px;
        }
        
        .setting-desc {
            font-size: 14px;
            color: #6c757d;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-indicator.connected {
            background: #d4edda;
            color: #155724;
        }
        
        .status-indicator.connecting {
            background: #fff3cd;
            color: #856404;
            animation: pulse 1s infinite;
        }
        
        .status-indicator.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-indicator.reconnecting {
            background: #ffeaa7;
            color: #d63031;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #007bff;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .volume-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .volume-slider {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: #e9ecef;
            outline: none;
            -webkit-appearance: none;
        }
        
        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #007bff;
            cursor: pointer;
        }
        
        .volume-value {
            min-width: 40px;
            text-align: center;
            font-weight: 500;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #007bff;
            display: block;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .quality-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .quality-excellent { background: #28a745; }
        .quality-good { background: #17a2b8; }
        .quality-poor { background: #ffc107; }
        .quality-disconnected { background: #dc3545; }
        .quality-reconnecting { 
            background: #fd7e14; 
            animation: pulse 1s infinite;
        }
        
        .template-selector {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }
        
        .preview-text {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-style: italic;
            color: #495057;
            margin-top: 10px;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .content {
                padding: 15px;
            }
            
            .setting-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔊 语音播报设置</h1>
            <p>增强版WebSocket连接管理</p>
        </div>
        
        <div class="content">
            <!-- 连接状态 -->
            <div class="setting-group">
                <div class="group-title">
                    <span class="icon">🌐</span>
                    <span>连接状态</span>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <span class="setting-label">WebSocket连接</span>
                        <span class="setting-desc" id="connectionDesc">检查连接状态中...</span>
                    </div>
                    <div class="status-indicator" id="connectionStatus">
                        <span id="connectionText">检查中</span>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <span class="setting-label">连接质量</span>
                        <span class="setting-desc">网络延迟和稳定性</span>
                    </div>
                    <div id="qualityIndicator">
                        <span class="quality-indicator quality-disconnected"></span>
                        <span id="qualityText">未知</span>
                    </div>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-value" id="reconnectCount">0</span>
                        <span class="stat-label">重连次数</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="messageCount">0</span>
                        <span class="stat-label">消息数量</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="heartbeatFailures">0</span>
                        <span class="stat-label">心跳失败</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="pendingPings">0</span>
                        <span class="stat-label">待响应心跳</span>
                    </div>
                </div>
            </div>
            
            <!-- 基础设置 -->
            <div class="setting-group">
                <div class="group-title">
                    <span class="icon">🔊</span>
                    <span>基础设置</span>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <span class="setting-label">语音播报</span>
                        <span class="setting-desc">开启后收到支付通知时自动播报</span>
                    </div>
                    <label class="switch">
                        <input type="checkbox" id="voiceEnabled" onchange="toggleVoice(this)">
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <span class="setting-label">播报音量</span>
                        <span class="setting-desc">调节语音播报的音量大小</span>
                    </div>
                    <div class="volume-control">
                        <input type="range" class="volume-slider" id="volumeSlider" 
                               min="0" max="100" value="80" onchange="setVolume(this.value)">
                        <span class="volume-value" id="volumeValue">80%</span>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-left">
                        <span class="setting-label">播报模板</span>
                        <span class="setting-desc">选择语音播报的内容模板</span>
                    </div>
                    <select class="template-selector" id="templateSelector" onchange="setTemplate(this.value)">
                        <option value="simple">简洁模式 - 收款X元</option>
                        <option value="detailed">详细模式 - 收到XX付款X元</option>
                        <option value="welcome">欢迎模式 - 欢迎光临，收款X元</option>
                    </select>
                </div>
                
                <div class="preview-text" id="previewText">
                    预览：收款88.88元
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="setting-group">
                <div class="group-title">
                    <span class="icon">🎛️</span>
                    <span>操作控制</span>
                </div>
                
                <div class="setting-item">
                    <button class="btn success" onclick="connectWebSocket()" id="connectBtn">
                        🔗 连接WebSocket
                    </button>
                    <button class="btn danger" onclick="disconnectWebSocket()" id="disconnectBtn">
                        🔌 断开连接
                    </button>
                    <button class="btn warning" onclick="reconnectWebSocket()" id="reconnectBtn">
                        🔄 重新连接
                    </button>
                    <button class="btn" onclick="testVoice()" id="testBtn">
                        🎵 测试播报
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入增强WebSocket管理器 -->
    <script src="assets/js/enhanced_websocket_manager.js"></script>
    
    <script>
        let wsManager = null;
        let voiceSettings = {
            enabled: true,
            volume: 0.8,
            template: 'simple'
        };
        
        // 模板配置
        const templates = {
            simple: {
                text: '收款{amount}元',
                preview: '收款88.88元'
            },
            detailed: {
                text: '收到{payType}付款{amount}元',
                preview: '收到支付宝付款88.88元'
            },
            welcome: {
                text: '欢迎光临，收款{amount}元，谢谢惠顾',
                preview: '欢迎光临，收款88.88元，谢谢惠顾'
            }
        };
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
            initWebSocket();
            
            // 定期更新状态显示
            setInterval(updateStatusDisplay, 1000);
        });
        
        /**
         * 加载设置
         */
        function loadSettings() {
            // 从localStorage加载设置
            const saved = localStorage.getItem('voiceSettings');
            if (saved) {
                voiceSettings = { ...voiceSettings, ...JSON.parse(saved) };
            }
            
            // 更新UI
            document.getElementById('voiceEnabled').checked = voiceSettings.enabled;
            document.getElementById('volumeSlider').value = voiceSettings.volume * 100;
            document.getElementById('volumeValue').textContent = Math.round(voiceSettings.volume * 100) + '%';
            document.getElementById('templateSelector').value = voiceSettings.template;
            updatePreview();
        }
        
        /**
         * 保存设置
         */
        function saveSettings() {
            localStorage.setItem('voiceSettings', JSON.stringify(voiceSettings));
        }
        
        /**
         * 初始化WebSocket
         */
        function initWebSocket() {
            try {
                wsManager = new EnhancedWebSocketManager({
                    url: 'ws://160.202.244.93:8080',
                    heartbeatInterval: 15000,
                    reconnectInterval: 2000,
                    maxReconnectAttempts: 100,
                    debug: true
                });
                
                // 添加事件监听
                wsManager.on('onConnect', handleConnect);
                wsManager.on('onDisconnect', handleDisconnect);
                wsManager.on('onPaymentNotification', handlePaymentNotification);
                wsManager.on('onStatusChange', handleStatusChange);
                wsManager.on('onError', handleError);
                
            } catch (error) {
                console.error('初始化WebSocket失败:', error);
                updateConnectionStatus('disconnected', '初始化失败');
            }
        }
        
        /**
         * 处理连接成功
         */
        function handleConnect(event) {
            console.log('WebSocket连接成功');
            updateConnectionStatus('connected', '已连接，实时监听中');
        }
        
        /**
         * 处理连接断开
         */
        function handleDisconnect(event) {
            console.log('WebSocket连接断开:', event);
            updateConnectionStatus('disconnected', `连接断开: ${event.code}`);
        }
        
        /**
         * 处理支付通知
         */
        function handlePaymentNotification(data) {
            console.log('收到支付通知:', data);
            
            if (voiceSettings.enabled) {
                playVoiceNotification(data);
            }
        }
        
        /**
         * 处理状态变化
         */
        function handleStatusChange(status) {
            console.log('状态变化:', status);
            updateQualityDisplay(status.quality);
            
            if (status.status === 'connected') {
                updateConnectionStatus('connected', '已连接，实时监听中');
            } else if (status.status === 'disconnected') {
                updateConnectionStatus('disconnected', '连接断开');
            }
        }
        
        /**
         * 处理错误
         */
        function handleError(error) {
            console.error('WebSocket错误:', error);
        }
        
        /**
         * 播放语音通知
         */
        function playVoiceNotification(data) {
            try {
                const amount = parseFloat(data.money || data.amount || 0);
                const payType = data.typename || data.type || '支付';
                
                let text = templates[voiceSettings.template].text;
                text = text.replace('{amount}', amount);
                text = text.replace('{payType}', payType);
                
                if ('speechSynthesis' in window) {
                    const utterance = new SpeechSynthesisUtterance(text);
                    utterance.lang = 'zh-CN';
                    utterance.volume = voiceSettings.volume;
                    utterance.rate = 0.9;
                    utterance.pitch = 1.0;
                    
                    speechSynthesis.speak(utterance);
                    console.log('播放语音:', text);
                } else {
                    console.warn('浏览器不支持语音合成');
                }
            } catch (error) {
                console.error('播放语音失败:', error);
            }
        }
        
        /**
         * 更新连接状态显示
         */
        function updateConnectionStatus(status, description) {
            const statusElement = document.getElementById('connectionStatus');
            const textElement = document.getElementById('connectionText');
            const descElement = document.getElementById('connectionDesc');
            
            statusElement.className = 'status-indicator ' + status;
            descElement.textContent = description;
            
            switch (status) {
                case 'connected':
                    textElement.textContent = '已连接';
                    break;
                case 'connecting':
                    textElement.textContent = '连接中';
                    break;
                case 'disconnected':
                    textElement.textContent = '已断开';
                    break;
                case 'reconnecting':
                    textElement.textContent = '重连中';
                    break;
                default:
                    textElement.textContent = '未知';
            }
        }
        
        /**
         * 更新质量显示
         */
        function updateQualityDisplay(quality) {
            const indicator = document.querySelector('#qualityIndicator .quality-indicator');
            const text = document.getElementById('qualityText');
            
            // 移除所有质量类
            indicator.className = 'quality-indicator';
            indicator.classList.add('quality-' + quality);
            
            const qualityNames = {
                'excellent': '优秀',
                'good': '良好',
                'poor': '较差',
                'disconnected': '断开',
                'reconnecting': '重连中'
            };
            
            text.textContent = qualityNames[quality] || '未知';
        }
        
        /**
         * 更新状态显示
         */
        function updateStatusDisplay() {
            if (!wsManager) return;
            
            const status = wsManager.getStatus();
            
            document.getElementById('reconnectCount').textContent = status.reconnectAttempts;
            document.getElementById('messageCount').textContent = status.stats.totalMessages;
            document.getElementById('heartbeatFailures').textContent = status.stats.heartbeatFailures;
            document.getElementById('pendingPings').textContent = status.pendingPings;
        }
        
        /**
         * 连接WebSocket
         */
        function connectWebSocket() {
            if (wsManager) {
                wsManager.connect();
            } else {
                initWebSocket();
            }
        }
        
        /**
         * 断开WebSocket
         */
        function disconnectWebSocket() {
            if (wsManager) {
                wsManager.disconnect();
            }
        }
        
        /**
         * 重连WebSocket
         */
        function reconnectWebSocket() {
            if (wsManager) {
                wsManager.reconnect();
            }
        }
        
        /**
         * 切换语音开关
         */
        function toggleVoice(checkbox) {
            voiceSettings.enabled = checkbox.checked;
            saveSettings();
            
            console.log('语音播报', voiceSettings.enabled ? '开启' : '关闭');
        }
        
        /**
         * 设置音量
         */
        function setVolume(value) {
            voiceSettings.volume = value / 100;
            document.getElementById('volumeValue').textContent = value + '%';
            saveSettings();
        }
        
        /**
         * 设置模板
         */
        function setTemplate(template) {
            voiceSettings.template = template;
            updatePreview();
            saveSettings();
        }
        
        /**
         * 更新预览
         */
        function updatePreview() {
            const preview = templates[voiceSettings.template].preview;
            document.getElementById('previewText').textContent = '预览：' + preview;
        }
        
        /**
         * 测试语音
         */
        function testVoice() {
            const testData = {
                money: '88.88',
                typename: '支付宝',
                type: 'alipay'
            };
            
            playVoiceNotification(testData);
        }
    </script>
</body>
</html>
