<template>
  <view class="message-detail-page">
    <!-- 🎯 优雅现代导航栏 -->
    <view class="elegant-navbar">
      <!-- 状态栏占位 -->
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

      <!-- 导航栏主体 -->
      <view class="navbar-main">
        <!-- 左侧返回和标题 -->
        <view class="navbar-left">
          <view class="back-btn" @click="goBack">
            <text class="back-icon">←</text>
          </view>
          <view class="navbar-title">
            <text class="title-text">消息详情</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 导航栏占位 -->
    <view class="navbar-placeholder" :style="{ height: navbarPlaceholderHeight + 'px' }"></view>

    <!-- 📱 消息详情内容 -->
    <view class="detail-content">
      <!-- 加载中状态 -->
      <view v-if="isLoading" class="loading-container">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 非加载状态 -->
      <view v-else>
        <!-- 消息详情 -->
        <view v-if="messageDetail" class="message-detail">
          <!-- 消息头部 -->
          <view class="message-header">
            <view class="message-type" :class="'type-' + messageDetail.type">
              <text class="type-text">{{ getTypeText(messageDetail.type) }}</text>
            </view>
            <text class="message-time">{{ formatTime(messageDetail.time) }}</text>
          </view>

          <!-- 消息标题 -->
          <view class="message-title">
            <text>{{ messageDetail.title }}</text>
          </view>

          <!-- 消息完整内容 -->
          <view class="message-content">
            <text class="content-text">{{ messageDetail.content }}</text>
          </view>

          <!-- 特殊内容处理 -->
          <!-- 交易信息 -->
          <view v-if="messageDetail.type === 'transaction' && messageDetail.transactionInfo" class="transaction-detail">
            <view class="detail-section">
              <text class="section-title">交易详情</text>
            </view>
            <view class="transaction-item">
              <text class="item-label">交易类型:</text>
              <text class="item-value">{{ messageDetail.transactionInfo.type === 'income' ? '收入' : '支出' }}</text>
            </view>
            <view class="transaction-item">
              <text class="item-label">交易金额:</text>
              <text class="item-value amount" :class="messageDetail.transactionInfo.type">
                {{ messageDetail.transactionInfo.type === 'income' ? '+' : '-' }}{{ messageDetail.transactionInfo.amount }}
              </text>
            </view>
            <view class="transaction-item">
              <text class="item-label">交易描述:</text>
              <text class="item-value">{{ messageDetail.transactionInfo.description }}</text>
            </view>
          </view>

          <!-- 活动信息 -->
          <view v-if="messageDetail.type === 'activity' && messageDetail.activityInfo" class="activity-detail">
            <view class="detail-section">
              <text class="section-title">活动详情</text>
            </view>
            <view class="activity-card">
              <image v-if="messageDetail.activityInfo.image" :src="messageDetail.activityInfo.image" mode="aspectFill" class="activity-image" />
              <view class="activity-info">
                <text class="activity-title">{{ messageDetail.activityInfo.title }}</text>
                <text class="activity-desc">{{ messageDetail.activityInfo.description }}</text>
              </view>
            </view>
          </view>

          <!-- 银行功能 -->
          <view v-if="messageDetail.type === 'staff' && messageDetail.bankFunction" class="bank-detail">
            <view class="detail-section">
              <text class="section-title">银行功能</text>
            </view>
            <view class="bank-function-detail">
              <text class="function-text">{{ messageDetail.bankFunction }}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="action-buttons">
            <button class="back-btn" @click="goBack">返回列表</button>
          </view>
        </view>

        <!-- 错误状态 -->
        <view v-else class="error-state">
          <text class="error-text">消息不存在或已被删除</text>
          <button class="back-btn" @click="goBack">返回消息列表</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar.vue'
import { getMessageDetail } from '@/api/message.js'

export default {
  components: {
    CustomNavbar
  },
  data() {
    return {
      statusBarHeight: 0, // 状态栏高度
      messageId: '',
      messageType: '',
      messageDetail: null,
      isLoading: true
    }
  },
  computed: {
    // 📏 导航栏占位高度
    navbarPlaceholderHeight() {
      // 状态栏高度(px) + 导航栏总高度
      // 增加一些额外高度确保完全不遮挡
      return this.statusBarHeight + 80;
    }
  },
  onLoad(options) {
    console.log('📱 消息详情页面加载:', options);
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight || 0;

    this.messageId = options.id;
    this.messageType = options.type;
    this.loadMessageDetail();
  },
  methods: {
    // 🔄 加载消息详情
    async loadMessageDetail() {
      try {
        this.isLoading = true;
        console.log('📥 获取消息详情:', this.messageId);

        const response = await getMessageDetail(this.messageId);
        console.log('📥 API响应:', response);
        console.log('📥 response.data:', response?.data);
        console.log('📥 response.code:', response?.code);

        // 解析API响应数据
        if (response && response.code === 200 && response.data) {
          // 使用API返回的真实数据
          const data = response.data;
          this.messageDetail = {
            id: data.id || this.messageId,
            title: data.title || '系统消息',
            content: data.content || data.message || '暂无消息内容',
            type: data.type || this.messageType || 'system',
            time: data.time || data.created_at || new Date().toISOString(),
            isRead: data.isRead || data.is_read || false,
            // 保留其他可能的字段
            ...data
          };
          console.log('✅ 使用API数据:', this.messageDetail);
        } else if (response && response.code === 200) {
          // API成功但没有data字段，使用response本身
          this.messageDetail = {
            id: response.id || this.messageId,
            title: response.title || '系统消息',
            content: response.content || response.message || response.msg || '暂无消息内容',
            type: response.type || this.messageType || 'system',
            time: response.time || response.created_at || new Date().toISOString(),
            isRead: response.isRead || response.is_read || false
          };
          console.log('✅ 使用响应数据:', this.messageDetail);
        } else {
          // API失败，创建默认消息
          this.messageDetail = {
            id: this.messageId,
            title: '系统消息',
            content: '无法获取消息详情，请稍后重试。',
            type: this.messageType || 'system',
            time: new Date().toISOString(),
            isRead: false
          };
          console.log('⚠️ 使用默认消息:', this.messageDetail);
        }
      } catch (error) {
        console.error('❌ 获取消息详情异常:', error);
        // 异常情况下也创建默认消息
        this.messageDetail = {
          id: this.messageId,
          title: '系统消息',
          content: '网络错误，无法获取消息详情。',
          type: this.messageType || 'system',
          time: new Date().toISOString(),
          isRead: false
        };
      } finally {
        this.isLoading = false;
        console.log('🔚 最终messageDetail:', this.messageDetail);
      }
    },
    
    // 🔙 返回
    goBack() {
      uni.navigateBack();
    },
    
    // 🏷️ 获取类型文本
    getTypeText(type) {
      const typeMap = {
        'system': '系统消息',
        'transaction': '交易消息',
        'activity': '活动消息',
        'staff': '客服消息',
        'client': '客户端消息'
      };
      return typeMap[type] || '其他消息';
    },
    
    // ⏰ 格式化时间
    formatTime(time) {
      if (!time) return '';
      const date = new Date(time);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  }
}
</script>

<style scoped>
/* 🎨 页面容器 */
.message-detail-page {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 🎯 优雅现代导航栏样式 */
.elegant-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 50%, #8B5CF6 100%);
  box-shadow: 0 4rpx 20rpx rgba(81, 69, 247, 0.15);
}

.status-bar {
  width: 100%;
}

.navbar-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  min-height: 88rpx;
}

.navbar-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.back-icon {
  font-size: 36rpx;
  color: #fff;
  font-weight: 600;
}

.navbar-title {
  flex: 1;
}

.title-text {
  font-size: 36rpx;
  color: #fff;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

/* 导航栏占位 */
.navbar-placeholder {
  width: 100%;
  background: transparent;
  flex-shrink: 0;
  /* 高度通过内联样式动态设置 */
}

/* 📱 详情内容 */
.detail-content {
  flex: 1;
  height: calc(100vh - 88px);
}

/* 🔄 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  font-size: 16px;
  color: #999;
}

/* 📄 消息详情 */
.message-detail {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

/* 📋 消息头部 */
.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.message-type {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
}

.type-system {
  background-color: #e3f2fd;
  color: #1976d2;
}

.type-transaction {
  background-color: #e8f5e8;
  color: #388e3c;
}

.type-activity {
  background-color: #fff3e0;
  color: #f57c00;
}

.type-staff {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.type-client {
  background-color: #e0f2f1;
  color: #00695c;
}

.type-text {
  font-size: 12px;
  font-weight: 500;
}

.message-time {
  font-size: 13px;
  color: #999;
}

/* 📝 消息标题 */
.message-title {
  margin-bottom: 15px;
}

.message-title text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

/* 📄 消息内容 */
.message-content {
  margin-bottom: 20px;
}

.content-text {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  word-break: break-word;
}

/* 📊 详情区块 */
.detail-section {
  margin: 20px 0 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 💰 交易详情 */
.transaction-detail {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.transaction-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 14px;
  color: #666;
}

.item-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.amount.income {
  color: #388e3c;
}

.amount.expense {
  color: #d32f2f;
}

/* 🎉 活动详情 */
.activity-detail {
  margin-top: 15px;
}

.activity-card {
  display: flex;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
}

.activity-image {
  width: 100px;
  height: 80px;
  border-radius: 6px;
  margin-right: 15px;
  flex-shrink: 0;
  background-color: #eee;
}

.activity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.activity-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.activity-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

/* 🏦 银行功能详情 */
.bank-detail {
  margin-top: 15px;
}

.bank-function-detail {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
}

.function-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 🔘 操作按钮 */
.action-buttons {
  display: flex;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.mark-read-btn, .delete-btn, .back-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  font-size: 15px;
  font-weight: 500;
  border: none;
}

.mark-read-btn {
  background-color: #007AFF;
  color: #fff;
}

.delete-btn {
  background-color: #FF3B30;
  color: #fff;
}

.back-btn {
  background-color: #007AFF;
  color: #fff;
}

/* 🔄 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  padding: 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #666;
}

/* ❌ 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  padding: 20px;
}

.error-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
</style>
