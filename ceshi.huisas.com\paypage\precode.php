<?php
/**
 * 预制码处理页面
 * 处理预制码访问逻辑：未绑定跳转绑定页面，已绑定跳转支付页面
 */

// 引入公共文件
include("../includes/common.php");

// 获取预制码参数
$code = $_GET['code'] ?? '';
if(empty($code)) {
    showErrorPage('参数错误', '预制码参数不能为空');
    exit();
}

// 记录访问日志
error_log("预制码访问: " . $code . " - " . date('Y-m-d H:i:s'));

try {
    // 查询预制码信息
    $precode = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_precode` WHERE `code`=?", [$code]);
    
    if(!$precode) {
        // 预制码不存在，显示错误页面
        showErrorPage('预制码不存在', '该预制码不存在或已失效，请联系商户确认。');
        exit();
    }
    
    // 检查预制码状态
    if($precode['status'] == 0) {
        // 未绑定状态，显示绑定提示页面
        showBindPage($code);
        exit();
        
    } elseif($precode['status'] == 1) {
        // 已绑定状态，跳转到支付页面
        
        // 构建支付页面URL
        $payUrl = $siteurl . "paypage/?uid=" . $precode['uid'];
        
        // 如果绑定了员工，添加员工参数
        if($precode['staff_id']) {
            $payUrl .= "&staff_id=" . $precode['staff_id'];
        }
        
        // 添加预制码标识参数
        $payUrl .= "&precode=" . urlencode($code);
        
        // 记录支付页面跳转
        error_log("预制码已绑定，跳转支付页面: " . $code . " -> " . $payUrl);
        
        header("Location: " . $payUrl);
        exit();
        
    } else {
        // 其他状态（如已禁用）
        showErrorPage('预制码已禁用', '该预制码已被禁用，无法使用。');
        exit();
    }
    
} catch(Exception $e) {
    // 数据库错误处理
    error_log("预制码处理异常: " . $e->getMessage());
    showErrorPage('系统错误', '系统暂时无法处理您的请求，请稍后重试。');
    exit();
}

/**
 * 显示绑定页面
 */
function showBindPage($code) {
    global $siteurl;
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>预制码绑定</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 400px; margin: 50px auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
            .bind-icon { font-size: 48px; color: #28a745; margin-bottom: 20px; }
            .bind-title { font-size: 20px; color: #333; margin-bottom: 10px; }
            .bind-message { color: #666; margin-bottom: 20px; line-height: 1.5; }
            .code-display { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; font-family: monospace; font-size: 18px; font-weight: bold; color: #007bff; }
            .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
            .btn:hover { background: #0056b3; }
            .btn-secondary { background: #6c757d; }
            .btn-secondary:hover { background: #545b62; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="bind-icon">🔗</div>
            <div class="bind-title">预制码待绑定</div>
            <div class="bind-message">该预制码尚未绑定商户，请使用商户管理系统进行绑定。</div>
            <div class="code-display"><?php echo htmlspecialchars($code); ?></div>
            <div class="bind-message">
                <strong>绑定步骤：</strong><br>
                1. 打开商户管理系统<br>
                2. 进入收款码管理<br>
                3. 点击"绑定空码"<br>
                4. 输入上述预制码
            </div>
            <a href="<?php echo $siteurl; ?>" class="btn btn-secondary">返回首页</a>
        </div>
    </body>
    </html>
    <?php
}

/**
 * 显示错误页面
 */
function showErrorPage($title, $message) {
    global $siteurl;
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo htmlspecialchars($title); ?></title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 400px; margin: 50px auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
            .error-icon { font-size: 48px; color: #ff6b6b; margin-bottom: 20px; }
            .error-title { font-size: 20px; color: #333; margin-bottom: 10px; }
            .error-message { color: #666; margin-bottom: 20px; line-height: 1.5; }
            .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
            .btn:hover { background: #0056b3; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="error-icon">⚠️</div>
            <div class="error-title"><?php echo htmlspecialchars($title); ?></div>
            <div class="error-message"><?php echo htmlspecialchars($message); ?></div>
            <a href="<?php echo $siteurl; ?>" class="btn">返回首页</a>
        </div>
    </body>
    </html>
    <?php
}
?>
