/**
 * 商户隔离WebSocket客户端
 * 确保每个商户只接收自己的支付通知
 */

class MerchantIsolatedWebSocket {
    constructor(config = {}) {
        this.config = {
            wsUrl: 'ws://ceshi.huisas.com:8080',
            merchantId: null,  // 必须设置当前商户ID
            reconnectInterval: 5000,
            maxReconnectAttempts: 10,
            debug: true,
            ...config
        };
        
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.eventListeners = {};
        
        // 验证商户ID
        if (!this.config.merchantId) {
            throw new Error('商户ID是必需的，请设置 merchantId 参数');
        }
        
        this.merchantChannel = `payment_merchant_${this.config.merchantId}`;
        this.log(`初始化商户隔离WebSocket - 商户ID: ${this.config.merchantId}`);
        this.log(`专属频道: ${this.merchantChannel}`);
    }
    
    /**
     * 连接WebSocket
     */
    connect() {
        try {
            this.log('🔌 正在连接WebSocket服务器...');
            this.ws = new WebSocket(this.config.wsUrl);
            
            this.ws.onopen = () => this.handleOpen();
            this.ws.onmessage = (event) => this.handleMessage(event);
            this.ws.onerror = (error) => this.handleError(error);
            this.ws.onclose = (event) => this.handleClose(event);
            
        } catch (error) {
            this.log(`❌ WebSocket连接失败: ${error.message}`, 'error');
            this.scheduleReconnect();
        }
    }
    
    /**
     * 处理连接打开
     */
    handleOpen() {
        this.log('✅ WebSocket连接成功');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // 订阅商户专属频道
        this.subscribeToMerchantChannel();
        
        // 触发连接成功事件
        this.emit('connected');
    }
    
    /**
     * 订阅商户专属频道
     */
    subscribeToMerchantChannel() {
        const subscribeMessage = {
            event: 'pusher:subscribe',
            data: {
                channel: this.merchantChannel
            }
        };
        
        this.send(subscribeMessage);
        this.log(`📡 已订阅商户专属频道: ${this.merchantChannel}`);
    }
    
    /**
     * 处理接收到的消息
     */
    handleMessage(event) {
        try {
            const data = JSON.parse(event.data);
            this.log(`📨 收到消息: ${JSON.stringify(data)}`);
            
            // 🔒 关键：商户隔离验证
            if (this.isValidMerchantMessage(data)) {
                this.processPaymentMessage(data);
            } else {
                this.log(`🔒 消息被过滤 - 不属于当前商户 (${this.config.merchantId})`, 'warn');
            }
            
        } catch (error) {
            this.log(`❌ 消息解析失败: ${error.message}`, 'error');
        }
    }
    
    /**
     * 验证消息是否属于当前商户
     */
    isValidMerchantMessage(data) {
        // 检查频道是否匹配
        if (data.channel && data.channel !== this.merchantChannel) {
            return false;
        }
        
        // 检查消息中的商户ID
        if (data.data) {
            const messageMerchantId = data.data.merchant_id || data.data.uid;
            if (messageMerchantId && messageMerchantId != this.config.merchantId) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 处理支付消息
     */
    processPaymentMessage(data) {
        if (data.event === 'payment_success' || data.event === 'payment') {
            this.log(`💰 收到支付通知: ${JSON.stringify(data.data)}`, 'success');
            
            // 构建标准化的支付通知数据
            const paymentNotification = {
                tradeNo: data.data.trade_no,
                amount: data.data.amount || data.data.money,
                payType: data.data.pay_type || data.data.type,
                payTypeName: data.data.pay_typename || data.data.typename,
                merchantId: data.data.merchant_id || data.data.uid,
                timestamp: data.data.timestamp || Date.now(),
                voiceText: data.data.voice_text || data.data.message,
                buyer: data.data.buyer,
                apiTradeNo: data.data.api_trade_no,
                originalData: data
            };
            
            // 触发支付成功事件
            this.emit('payment_success', paymentNotification);
            
            // 播放语音提示（如果启用）
            if (this.config.enableVoice !== false) {
                this.playVoiceAlert(paymentNotification);
            }
            
            // 显示通知（如果启用）
            if (this.config.showNotifications !== false) {
                this.showNotification(paymentNotification);
            }
        }
    }
    
    /**
     * 播放语音提示
     */
    playVoiceAlert(notification) {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(
                notification.voiceText || `收款${notification.amount}元`
            );
            utterance.lang = 'zh-CN';
            utterance.rate = 1.2;
            speechSynthesis.speak(utterance);
            this.log(`🔊 播放语音: ${utterance.text}`);
        }
    }
    
    /**
     * 显示浏览器通知
     */
    showNotification(notification) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('收款通知', {
                body: `${notification.payTypeName || ''}收款 ${notification.amount} 元`,
                icon: '/favicon.ico',
                tag: notification.tradeNo
            });
        }
    }
    
    /**
     * 处理连接错误
     */
    handleError(error) {
        this.log(`❌ WebSocket错误: ${error}`, 'error');
        this.emit('error', error);
    }
    
    /**
     * 处理连接关闭
     */
    handleClose(event) {
        this.log(`🔌 WebSocket连接已关闭 (代码: ${event.code})`);
        this.isConnected = false;
        this.emit('disconnected', event);
        
        // 自动重连
        if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
            this.scheduleReconnect();
        } else {
            this.log('❌ 达到最大重连次数，停止重连', 'error');
        }
    }
    
    /**
     * 安排重连
     */
    scheduleReconnect() {
        this.reconnectAttempts++;
        this.log(`🔄 ${this.config.reconnectInterval/1000}秒后尝试重连 (第${this.reconnectAttempts}次)`);
        
        setTimeout(() => {
            if (!this.isConnected) {
                this.connect();
            }
        }, this.config.reconnectInterval);
    }
    
    /**
     * 发送消息
     */
    send(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
            return true;
        } else {
            this.log('❌ WebSocket未连接，无法发送消息', 'error');
            return false;
        }
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        this.isConnected = false;
        this.log('🔌 已主动断开WebSocket连接');
    }
    
    /**
     * 事件监听
     */
    on(event, callback) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(callback);
    }
    
    /**
     * 移除事件监听
     */
    off(event, callback) {
        if (this.eventListeners[event]) {
            const index = this.eventListeners[event].indexOf(callback);
            if (index > -1) {
                this.eventListeners[event].splice(index, 1);
            }
        }
    }
    
    /**
     * 触发事件
     */
    emit(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    this.log(`❌ 事件回调执行失败: ${error.message}`, 'error');
                }
            });
        }
    }
    
    /**
     * 日志输出
     */
    log(message, level = 'info') {
        if (!this.config.debug) return;
        
        const timestamp = new Date().toLocaleTimeString();
        const prefix = `[${timestamp}] [商户${this.config.merchantId}]`;
        
        switch (level) {
            case 'error':
                console.error(`${prefix} ${message}`);
                break;
            case 'warn':
                console.warn(`${prefix} ${message}`);
                break;
            case 'success':
                console.log(`%c${prefix} ${message}`, 'color: green');
                break;
            default:
                console.log(`${prefix} ${message}`);
        }
    }
    
    /**
     * 获取连接状态
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            merchantId: this.config.merchantId,
            merchantChannel: this.merchantChannel,
            reconnectAttempts: this.reconnectAttempts,
            wsReadyState: this.ws ? this.ws.readyState : null
        };
    }
}

// 使用示例和工具函数
window.MerchantIsolatedWebSocket = MerchantIsolatedWebSocket;

/**
 * 快速创建商户隔离WebSocket连接
 * @param {number} merchantId 商户ID
 * @param {object} options 配置选项
 * @returns {MerchantIsolatedWebSocket}
 */
window.createMerchantWebSocket = function(merchantId, options = {}) {
    const ws = new MerchantIsolatedWebSocket({
        merchantId: merchantId,
        ...options
    });
    
    // 自动连接
    ws.connect();
    
    return ws;
};

/**
 * 请求通知权限
 */
window.requestNotificationPermission = function() {
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission().then(permission => {
            console.log('通知权限:', permission);
        });
    }
};
