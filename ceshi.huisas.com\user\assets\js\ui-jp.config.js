// lazyload config

var jp_config = {
  easyPieChart:   [   './assets/vendor/jquery.easy-pie-chart/dist/jquery.easypiechart.fill.js'],
  sparkline:      [   './assets/vendor/jquery.sparkline/dist/jquery.sparkline.retina.js'],
  plot:           [   './assets/vendor/flot/jquery.flot.js',
                      './assets/vendor/flot/jquery.flot.pie.js', 
                      './assets/vendor/flot/jquery.flot.resize.js',
                      './assets/vendor/flot.tooltip/js/jquery.flot.tooltip.min.js',
                      './assets/vendor/flot.orderbars/js/jquery.flot.orderBars.js',
                      './assets/vendor/flot-spline/js/jquery.flot.spline.min.js'],
  moment:         [   './assets/vendor/moment/moment.js'],
  screenfull:     [   './assets/vendor/screenfull/dist/screenfull.min.js'],
  slimScroll:     [   './assets/vendor/slimscroll/jquery.slimscroll.min.js'],
  sortable:       [   './assets/vendor/html5sortable/jquery.sortable.js'],
  nestable:       [   './assets/vendor/nestable/jquery.nestable.js',
                      './assets/vendor/nestable/jquery.nestable.css'],
  filestyle:      [   './assets/vendor/bootstrap-filestyle/src/bootstrap-filestyle.js'],
  slider:         [   './assets/vendor/bootstrap-slider/bootstrap-slider.js',
                      './assets/vendor/bootstrap-slider/bootstrap-slider.css'],
  chosen:         [   './assets/vendor/chosen/chosen.jquery.min.js',
                      './assets/vendor/bootstrap-chosen/bootstrap-chosen.css'],
  TouchSpin:      [   './assets/vendor/bootstrap-touchspin/dist/jquery.bootstrap-touchspin.min.js',
                      './assets/vendor/bootstrap-touchspin/dist/jquery.bootstrap-touchspin.min.css'],
  wysiwyg:        [   './assets/vendor/bootstrap-wysiwyg/bootstrap-wysiwyg.js',
                      './assets/vendor/bootstrap-wysiwyg/external/jquery.hotkeys.js'],
  dataTable:      [   './assets/vendor/datatables/media/js/jquery.dataTables.min.js',
                      './assets/vendor/plugins/integration/bootstrap/3/dataTables.bootstrap.js',
                      './assets/vendor/plugins/integration/bootstrap/3/dataTables.bootstrap.css'],
  vectorMap:      [   './assets/vendor/bower-jvectormap/jquery-jvectormap-1.2.2.min.js', 
                      './assets/vendor/bower-jvectormap/jquery-jvectormap-world-mill-en.js',
                      './assets/vendor/bower-jvectormap/jquery-jvectormap-us-aea-en.js',
                      './assets/vendor/bower-jvectormap/jquery-jvectormap-1.2.2.css'],
  footable:       [   './assets/vendor/footable/dist/footable.all.min.js',
                      './assets/vendor/footable/css/footable.core.css'],
  fullcalendar:   [   './assets/vendor/moment/moment.js',
                      './assets/vendor/fullcalendar/dist/fullcalendar.min.js',
                      './assets/vendor/fullcalendar/dist/fullcalendar.css',
                      './assets/vendor/fullcalendar/dist/fullcalendar.theme.css'],
  daterangepicker:[   './assets/vendor/moment/moment.js',
                      './assets/vendor/bootstrap-daterangepicker/daterangepicker.js',
                      './assets/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css'],
  tagsinput:      [   './assets/vendor/bootstrap-tagsinput/dist/bootstrap-tagsinput.js',
                      './assets/vendor/bootstrap-tagsinput/dist/bootstrap-tagsinput.css']
                      
};
