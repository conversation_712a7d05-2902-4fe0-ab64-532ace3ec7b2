<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔗 WebSocket URL连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        
        .test-item.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .test-item.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .test-item.testing {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .console {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .url-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 WebSocket URL连接测试</h1>
        <p>测试不同的WebSocket URL，找出正确的连接方式</p>
        
        <div class="test-item" id="test1">
            <h3>测试1: 标准Swoole WebSocket (无路径)</h3>
            <input type="text" class="url-input" value="ws://ceshi.huisas.com:8080" readonly>
            <button class="btn" onclick="testConnection(1, 'ws://ceshi.huisas.com:8080')">🧪 测试连接</button>
            <div id="result1">等待测试...</div>
        </div>
        
        <div class="test-item" id="test2">
            <h3>测试2: 带路径的WebSocket (测试页面使用的)</h3>
            <input type="text" class="url-input" value="ws://ceshi.huisas.com:8080/app/payment_websocket_2024" readonly>
            <button class="btn" onclick="testConnection(2, 'ws://ceshi.huisas.com:8080/app/payment_websocket_2024')">🧪 测试连接</button>
            <div id="result2">等待测试...</div>
        </div>
        
        <div class="test-item" id="test3">
            <h3>测试3: 自定义URL</h3>
            <input type="text" class="url-input" id="custom-url" value="ws://ceshi.huisas.com:8080" placeholder="输入自定义WebSocket URL">
            <button class="btn" onclick="testCustomConnection()">🧪 测试自定义URL</button>
            <div id="result3">等待测试...</div>
        </div>
        
        <div>
            <h3>🎮 控制面板</h3>
            <button class="btn success" onclick="testAllConnections()">🚀 测试所有连接</button>
            <button class="btn danger" onclick="disconnectAll()">❌ 断开所有连接</button>
            <button class="btn" onclick="clearConsole()">🗑️ 清空日志</button>
        </div>
        
        <div class="console" id="console">
            <div>🚀 WebSocket URL连接测试工具已就绪</div>
            <div>💡 点击测试按钮开始检测正确的连接URL</div>
        </div>
        
        <div class="test-item">
            <h3>📊 测试结果总结</h3>
            <div id="summary">
                <p>🔍 <strong>目标：</strong>找出正式页面应该使用的正确WebSocket URL</p>
                <p>❓ <strong>问题：</strong>测试页面能连接，正式页面不能连接</p>
                <p>⏳ <strong>状态：</strong>等待测试结果...</p>
            </div>
        </div>
    </div>

    <script>
        const connections = {};
        let testResults = {};
        
        function log(message, type = 'info') {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.innerHTML = `[${timestamp}] ${message}`;
            
            if (type === 'error') {
                div.style.color = '#ff6b6b';
            } else if (type === 'success') {
                div.style.color = '#51cf66';
            } else if (type === 'warning') {
                div.style.color = '#ffd43b';
            }
            
            console.appendChild(div);
            console.scrollTop = console.scrollHeight;
        }
        
        function updateTestResult(testId, status, message) {
            const testElement = document.getElementById(`test${testId}`);
            const resultElement = document.getElementById(`result${testId}`);
            
            testElement.className = `test-item ${status}`;
            resultElement.innerHTML = message;
            
            testResults[testId] = { status, message };
            updateSummary();
        }
        
        function testConnection(testId, url) {
            log(`🔗 开始测试连接 ${testId}: ${url}`);
            updateTestResult(testId, 'testing', '🔄 连接中...');
            
            try {
                const ws = new WebSocket(url);
                connections[testId] = ws;
                
                const timeout = setTimeout(() => {
                    ws.close();
                    updateTestResult(testId, 'error', '❌ 连接超时 (10秒)');
                    log(`❌ 测试 ${testId} 连接超时`, 'error');
                }, 10000);
                
                ws.onopen = function() {
                    clearTimeout(timeout);
                    updateTestResult(testId, 'success', '✅ 连接成功！');
                    log(`✅ 测试 ${testId} 连接成功`, 'success');
                    
                    // 发送测试消息
                    const testMessage = {
                        type: 'ping',
                        data: {
                            test_id: testId,
                            timestamp: Date.now()
                        }
                    };
                    
                    ws.send(JSON.stringify(testMessage));
                    log(`📤 发送测试消息到连接 ${testId}`);
                };
                
                ws.onmessage = function(event) {
                    log(`📨 连接 ${testId} 收到消息: ${event.data}`, 'success');
                    
                    try {
                        const message = JSON.parse(event.data);
                        if (message.type === 'welcome') {
                            log(`👋 连接 ${testId} 收到欢迎消息`, 'success');
                            updateTestResult(testId, 'success', '✅ 连接成功并收到欢迎消息');
                        }
                    } catch (e) {
                        log(`📨 连接 ${testId} 收到非JSON消息`, 'warning');
                    }
                };
                
                ws.onclose = function(event) {
                    clearTimeout(timeout);
                    if (testResults[testId]?.status !== 'success') {
                        updateTestResult(testId, 'error', `❌ 连接关闭: ${event.code} ${event.reason}`);
                        log(`❌ 测试 ${testId} 连接关闭: ${event.code}`, 'error');
                    } else {
                        log(`🔌 测试 ${testId} 连接已关闭`, 'info');
                    }
                };
                
                ws.onerror = function(error) {
                    clearTimeout(timeout);
                    updateTestResult(testId, 'error', '❌ 连接错误');
                    log(`❌ 测试 ${testId} 连接错误`, 'error');
                };
                
            } catch (error) {
                updateTestResult(testId, 'error', `❌ 创建连接失败: ${error.message}`);
                log(`❌ 测试 ${testId} 创建连接失败: ${error.message}`, 'error');
            }
        }
        
        function testCustomConnection() {
            const url = document.getElementById('custom-url').value;
            if (!url) {
                log('❌ 请输入自定义URL', 'error');
                return;
            }
            testConnection(3, url);
        }
        
        function testAllConnections() {
            log('🚀 开始测试所有连接...', 'info');
            testConnection(1, 'ws://ceshi.huisas.com:8080');
            setTimeout(() => testConnection(2, 'ws://ceshi.huisas.com:8080/app/payment_websocket_2024'), 1000);
        }
        
        function disconnectAll() {
            log('❌ 断开所有连接...', 'warning');
            Object.keys(connections).forEach(testId => {
                if (connections[testId]) {
                    connections[testId].close();
                    delete connections[testId];
                }
            });
        }
        
        function clearConsole() {
            document.getElementById('console').innerHTML = '';
            log('🗑️ 日志已清空');
        }
        
        function updateSummary() {
            const summary = document.getElementById('summary');
            const successCount = Object.values(testResults).filter(r => r.status === 'success').length;
            const totalCount = Object.keys(testResults).length;
            
            let summaryHTML = `
                <p>🔍 <strong>目标：</strong>找出正式页面应该使用的正确WebSocket URL</p>
                <p>❓ <strong>问题：</strong>测试页面能连接，正式页面不能连接</p>
                <p>📊 <strong>测试进度：</strong>${successCount}/${totalCount} 个连接成功</p>
            `;
            
            if (successCount > 0) {
                summaryHTML += `<p>✅ <strong>建议：</strong>`;
                Object.keys(testResults).forEach(testId => {
                    if (testResults[testId].status === 'success') {
                        const urls = [
                            'ws://ceshi.huisas.com:8080',
                            'ws://ceshi.huisas.com:8080/app/payment_websocket_2024',
                            document.getElementById('custom-url').value
                        ];
                        summaryHTML += `使用测试${testId}的URL: ${urls[testId-1]} `;
                    }
                });
                summaryHTML += `</p>`;
            }
            
            summary.innerHTML = summaryHTML;
        }
        
        // 页面加载完成后自动开始测试
        window.onload = function() {
            log('🎯 建议：先测试标准Swoole连接，再测试带路径的连接');
            log('💡 如果两个都能连接，说明服务器支持多种URL格式');
        };
    </script>
</body>
</html>
