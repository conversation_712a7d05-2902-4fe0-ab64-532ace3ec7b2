<?php
/**
 * 支付回调监控页面
 * 实时监控支付流程和WebSocket通知
 */

require_once __DIR__ . '/includes/common.php';

// 获取最近的支付日志
function getRecentPaymentLogs($limit = 20) {
    global $DB;

    $logs = [];

    // 获取最近的订单 - 修复查询
    try {
        $sql = "SELECT o.*, t.showname as type_name
                FROM pre_order o
                LEFT JOIN pre_type t ON o.type = t.id
                WHERE o.addtime >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                ORDER BY o.addtime DESC
                LIMIT $limit";

        $orders = $DB->query($sql);

        if ($orders) {
            while ($order = $orders->fetch(PDO::FETCH_ASSOC)) {
                $logs[] = [
                    'type' => 'order',
                    'time' => $order['addtime'],
                    'trade_no' => $order['trade_no'],
                    'out_trade_no' => $order['out_trade_no'],
                    'money' => $order['money'],
                    'status' => $order['status'],
                    'pay_type' => $order['type'],
                    'type_name' => $order['type_name'] ?: '未知类型',
                    'uid' => $order['uid']
                ];
            }
        }
    } catch (Exception $e) {
        error_log("订单查询错误: " . $e->getMessage());
        $logs[] = [
            'type' => 'error',
            'time' => date('Y-m-d H:i:s'),
            'message' => '订单查询失败: ' . $e->getMessage()
        ];
    }

    return $logs;
}

// 获取WebSocket日志
function getWebSocketLogs($limit = 50) {
    $logFile = __DIR__ . '/websocket_notify.log';
    $logs = [];

    if (file_exists($logFile)) {
        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $lines = array_reverse(array_slice($lines, -$limit));

        foreach ($lines as $line) {
            if (preg_match('/\[(.*?)\] \[(.*?)\] (.*)/', $line, $matches)) {
                $logs[] = [
                    'time' => $matches[1],
                    'level' => $matches[2],
                    'message' => $matches[3]
                ];
            }
        }
    }

    return $logs;
}

// 获取支付日志
function getPaymentProcessLogs($limit = 50) {
    $logFile = __DIR__ . '/payment_process.log';
    $logs = [];

    if (file_exists($logFile)) {
        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $lines = array_reverse(array_slice($lines, -$limit));

        foreach ($lines as $line) {
            $logs[] = ['message' => $line];
        }
    }

    return $logs;
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>💰 支付回调监控</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: linear-gradient(135deg, #007AFF, #5856D6);
            color: white;
            padding: 20px;
            border-radius: 16px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .status-card.success { background: linear-gradient(135deg, #34C759, #30D158); }
        .status-card.warning { background: linear-gradient(135deg, #FF9500, #FF9F0A); }
        .status-card.error { background: linear-gradient(135deg, #FF3B30, #FF453A); }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e5e5e7;
            border-radius: 12px;
            padding: 15px;
            background: #fafafa;
        }
        .log-entry {
            margin: 8px 0;
            padding: 12px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
        }
        .log-info { background: #e3f2fd; border-left: 4px solid #2196f3; }
        .log-success { background: #e8f5e8; border-left: 4px solid #4caf50; }
        .log-warning { background: #fff3e0; border-left: 4px solid #ff9800; }
        .log-error { background: #ffebee; border-left: 4px solid #f44336; }
        .refresh-btn {
            background: linear-gradient(135deg, #007AFF, #5856D6);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(0,122,255,0.3);
        }
        .refresh-btn:hover { transform: translateY(-2px); }
        .order-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .order-table th, .order-table td { padding: 12px; text-align: left; border-bottom: 1px solid #e5e5e7; }
        .order-table th { background: #f8f9fa; font-weight: 600; }
        .status-badge { padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 500; }
        .status-0 { background: #fff3cd; color: #856404; }
        .status-1 { background: #d4edda; color: #155724; }
        .status-2 { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💰 支付回调监控</h1>
            <p>实时监控支付流程和WebSocket通知状态</p>
        </div>

        <!-- 状态概览 -->
        <div class="status-grid">
            <div class="status-card success">
                <h3>🔌 WebSocket服务</h3>
                <p id="websocket-status">检查中...</p>
            </div>
            <div class="status-card">
                <h3>📢 通知函数</h3>
                <p><?php echo function_exists('notifyPaymentSuccess') ? '✅ 已加载' : '❌ 未加载'; ?></p>
            </div>
            <div class="status-card">
                <h3>🔊 语音函数</h3>
                <p><?php echo function_exists('notifyVoiceAlert') ? '✅ 已加载' : '❌ 未加载'; ?></p>
            </div>
            <div class="status-card warning">
                <h3>📋 最近订单</h3>
                <p><?php echo count(getRecentPaymentLogs()); ?> 条记录</p>
            </div>
        </div>

        <!-- 最近订单 -->
        <div class="card">
            <h2>📋 最近订单 (1小时内)</h2>
            <button class="refresh-btn" onclick="location.reload()">🔄 刷新数据</button>

            <table class="order-table">
                <thead>
                    <tr>
                        <th>⏰ 时间</th>
                        <th>🔢 订单号</th>
                        <th>👤 商户ID</th>
                        <th>💰 金额</th>
                        <th>💳 支付方式</th>
                        <th>📊 状态</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $orders = getRecentPaymentLogs();
                    if (empty($orders)):
                    ?>
                        <tr><td colspan="6" style="text-align: center; color: #666; padding: 30px;">
                            🔍 暂无最近订单，请发起测试支付
                        </td></tr>
                    <?php else: ?>
                        <?php foreach ($orders as $order): ?>
                        <tr>
                            <td><?php echo $order['time']; ?></td>
                            <td><code><?php echo $order['trade_no']; ?></code></td>
                            <td><strong><?php echo $order['uid'] ?? 'N/A'; ?></strong></td>
                            <td><strong>¥<?php echo $order['money']; ?></strong></td>
                            <td><?php echo $order['type_name'] ?? $order['pay_type']; ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $order['status']; ?>">
                                    <?php
                                    switch($order['status']) {
                                        case 0: echo '⏳ 未支付'; break;
                                        case 1: echo '✅ 已支付'; break;
                                        case 2: echo '🔄 已退款'; break;
                                        default: echo '❓ 未知';
                                    }
                                    ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- WebSocket日志 -->
        <div class="card">
            <h2>🔔 WebSocket通知日志</h2>
            <div class="log-container">
                <?php
                $wsLogs = getWebSocketLogs();
                if (empty($wsLogs)):
                ?>
                    <div class="log-entry log-info">
                        📝 暂无WebSocket日志，等待支付回调触发...
                    </div>
                <?php else: ?>
                    <?php foreach ($wsLogs as $log): ?>
                    <div class="log-entry log-<?php echo strtolower($log['level']); ?>">
                        <strong>⏰ [<?php echo $log['time']; ?>]</strong>
                        <span class="log-level">[<?php echo $log['level']; ?>]</span>
                        <?php echo htmlspecialchars($log['message']); ?>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- 测试说明 -->
        <div class="card">
            <h2>🧪 真实支付测试步骤</h2>
            <div style="background: #f0f9ff; padding: 20px; border-radius: 12px; border-left: 4px solid #0ea5e9;">
                <h3>📱 第一步：发起测试支付</h3>
                <ol>
                    <li>访问支付页面创建 <strong>0.01元</strong> 测试订单</li>
                    <li>选择支付宝或微信支付</li>
                    <li>完成真实支付（建议使用沙箱环境）</li>
                </ol>

                <h3>👀 第二步：监控支付流程</h3>
                <ol>
                    <li>支付完成后，<strong>刷新此页面</strong></li>
                    <li>查看"最近订单"中是否出现新订单</li>
                    <li>检查订单状态是否变为"✅ 已支付"</li>
                    <li>观察"WebSocket通知日志"是否有新记录</li>
                </ol>

                <h3>🔗 第三步：前端WebSocket连接</h3>
                <p>在UniApp中连接以下WebSocket地址：</p>
                <code style="background: #1f2937; color: #10b981; padding: 8px 12px; border-radius: 6px; display: block; margin: 10px 0;">
                    ws://ceshi.huisas.com:8081/apps/test_app_key/events?auth_key=test_app_key
                </code>

                <h3>✅ 预期结果</h3>
                <ul>
                    <li>🔔 WebSocket收到支付成功通知</li>
                    <li>🔊 前端播放语音提示</li>
                    <li>📊 订单状态实时更新</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 检查WebSocket服务状态
        function checkWebSocketStatus() {
            const statusEl = document.getElementById('websocket-status');

            // 尝试连接WebSocket
            try {
                const ws = new WebSocket('ws://ceshi.huisas.com:8081/apps/test_app_key/events?auth_key=test_app_key');

                ws.onopen = function() {
                    statusEl.innerHTML = '✅ 连接正常';
                    statusEl.parentElement.className = 'status-card success';
                    ws.close();
                };

                ws.onerror = function() {
                    statusEl.innerHTML = '❌ 连接失败';
                    statusEl.parentElement.className = 'status-card error';
                };

                // 5秒超时
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        statusEl.innerHTML = '⏳ 连接超时';
                        statusEl.parentElement.className = 'status-card warning';
                        ws.close();
                    }
                }, 5000);

            } catch (e) {
                statusEl.innerHTML = '❌ 连接异常';
                statusEl.parentElement.className = 'status-card error';
            }
        }

        // 自动刷新状态
        setInterval(() => {
            checkWebSocketStatus();
        }, 30000);

        // 页面加载时检查
        checkWebSocketStatus();

        // 每30秒自动刷新页面数据
        setInterval(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
