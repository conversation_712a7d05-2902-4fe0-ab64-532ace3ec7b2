{"name": "uniapp-payment-system", "version": "1.0.0", "description": "UniApp支付系统", "main": "main.js", "scripts": {"dev:h5": "uni build --platform h5 --mode development --watch", "build:h5": "uni build --platform h5 --mode production", "dev:mp-weixin": "uni build --platform mp-weixin --mode development --watch", "build:mp-weixin": "uni build --platform mp-weixin --mode production", "dev:app": "uni build --platform app --mode development --watch", "build:app": "uni build --platform app --mode production", "serve": "vite --host 0.0.0.0 --port 5173", "preview": "vite preview"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0", "@dcloudio/uni-components": "^3.0.0", "@dcloudio/uni-h5": "^3.0.0", "@dcloudio/uni-mp-weixin": "^3.0.0", "vue": "^3.3.4", "vuex": "^4.0.2", "axios": "^1.4.0"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^3.0.0", "@dcloudio/vite-plugin-uni": "^3.0.0", "vite": "^4.4.0", "sass": "^1.64.0"}, "uni-app": {"scripts": {}}}