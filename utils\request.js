// utils/request.js - 网络请求工具
import config from '@/config/index';

/**
 * 显示加载提示
 * @param {String} title - 提示内容
 */
const showLoading = (title = '加载中...') => {
  uni.showLoading({
    title,
    mask: true
  });
};

/**
 * 隐藏加载提示
 */
const hideLoading = () => {
  uni.hideLoading();
};

/**
 * 显示错误提示
 * @param {String} message - 错误信息
 */
const showError = (message) => {
  uni.showToast({
    title: message || '请求失败',
    icon: 'none',
    duration: 2000
  });
};

/**
 * 请求配置 - 优化CORS处理
 */
const requestConfig = {
  // H5开发环境使用相对路径，通过代理解决跨域
  // 其他环境使用配置文件中的baseUrl
  baseURL: '', // 使用相对路径，通过manifest.json中的代理配置
  timeout: 20000, // 增加超时时间到20秒
  header: {
    'Content-Type': 'application/x-www-form-urlencoded',
    'X-Requested-With': 'XMLHttpRequest',
    'Accept': 'application/json, text/plain, */*',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache',
    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
  }
}

/**
 * 请求拦截器
 */
const requestInterceptor = {
  invoke(options) {
    return new Promise((resolve) => {
      // 获取用户token
      const user_token = uni.getStorageSync('user_token')
      if (user_token) {
        // 对于跨域请求，在POST数据中添加token
        if (options.method === 'POST') {
          if (typeof options.data !== 'object') {
            options.data = {}
          }
          options.data.user_token = user_token
        } else {
          // 对于GET请求，添加到URL参数
          const separator = options.url.includes('?') ? '&' : '?'
          options.url = `${options.url}${separator}user_token=${encodeURIComponent(user_token)}`
        }
        console.log('添加用户Token到请求:', user_token)
      }
      
      // 添加CSRF Token和Session ID
      const csrf_token = uni.getStorageSync('csrf_token')
      const session_id = uni.getStorageSync('session_id')

      if (csrf_token) {
        if (options.method === 'POST') {
          // 确保data是对象类型
          if (typeof options.data !== 'object') {
            options.data = {}
          }
          options.data.csrf_token = csrf_token
          // 同时添加session_id以支持跨域场景
          if (session_id) {
            options.data.session_id = session_id
          }
          console.log('添加CSRF Token到POST请求:', csrf_token, 'Session ID:', session_id)
        } else if (options.method === 'GET') {
          // 对于GET请求，添加到URL参数
          const separator = options.url.includes('?') ? '&' : '?'
          let urlParams = `csrf_token=${encodeURIComponent(csrf_token)}`
          if (session_id) {
            urlParams += `&session_id=${encodeURIComponent(session_id)}`
          }
          options.url = `${options.url}${separator}${urlParams}`
          console.log('添加CSRF Token到GET请求URL:', options.url)
        }
      }
      
      // 处理POST请求的数据格式
      if (options.method === 'POST') {
        options.header['content-type'] = 'application/x-www-form-urlencoded'
        // 将对象转换为 URL 编码的字符串
        if (typeof options.data === 'object') {
          const formData = Object.keys(options.data)
            .map(key => {
              if (options.data[key] !== null && options.data[key] !== undefined) {
                return `${encodeURIComponent(key)}=${encodeURIComponent(options.data[key])}`
              }
              return ''
            })
            .filter(item => item !== '')
            .join('&')
          
          options.data = formData
          console.log('转换后的表单数据:', formData)
        }
      }
      
      resolve(options)
    });
  }
}

/**
 * 响应拦截器
 */
const responseInterceptor = {
  success(response, options = {}) {
    const { statusCode, data } = response

    // 请求成功
    if (statusCode === 200) {
      // 检查是否有code字段
      if (data.hasOwnProperty('code')) {
        // 有code字段的响应 - 标准API响应格式
        if (data.code === 0 || data.code === 1 || data.code === 2) {
          return data
        } else if (data.code === -3) {
          // 未登录状态，跳转到登录页
          console.log('用户未登录，跳转到登录页');
          uni.removeStorageSync('user_token');
          uni.removeStorageSync('user_uid');
          uni.removeStorageSync('csrf_token');
          uni.removeStorageSync('session_id');
          uni.removeStorageSync('user_info');
          uni.reLaunch({
            url: '/pages/login/index'
          });
          return Promise.reject(data);
        } else {
          // 业务错误处理 - 静默请求不显示错误提示
          if (!options.silent) {
            handleBusinessError(data)
          }
          return Promise.reject(data)
        }
      } else {
        // 没有code字段的响应 - 直接返回数据（如orderList接口）
        if (!options.silent) {
          console.log('响应没有code字段，直接返回数据:', data);
        }
        return data
      }
    } else {
      // HTTP错误处理 - 静默请求不显示错误提示
      if (!options.silent) {
        handleHttpError(statusCode)
      }
      return Promise.reject(response)
    }
  },
  fail(error, options = {}) {
    // 网络错误处理 - 静默请求不显示错误提示
    if (!options.silent) {
      console.error('网络请求失败:', error);
      handleNetworkError(error)
    }
    return Promise.reject(error)
  }
}

/**
 * 业务错误处理
 */
function handleBusinessError(data) {
  const { code, msg } = data
  
  // 根据业务状态码处理不同情况
  switch (code) {
    case -1: // 登录失败
      uni.showToast({
        title: msg || '登录失败',
        icon: 'none'
      })
      break
    case 401: // 未授权
      uni.removeStorageSync('user_token')
      uni.removeStorageSync('user_uid')
      uni.removeStorageSync('csrf_token')
      uni.navigateTo({
        url: '/pages/login/index'
      })
      break
    default:
      uni.showToast({
        title: msg || '操作失败',
        icon: 'none'
      })
  }
}

/**
 * HTTP错误处理
 */
function handleHttpError(statusCode) {
  const errorMap = {
    400: '请求错误',
    401: '未授权，请重新登录',
    403: '拒绝访问',
    404: '请求错误，未找到该资源',
    405: '请求方法未允许',
    408: '请求超时',
    500: '服务器端出错',
    501: '网络未实现',
    502: '网络错误',
    503: '服务不可用',
    504: '网络超时',
    505: 'http版本不支持该请求'
  }
  
  uni.showToast({
    title: errorMap[statusCode] || `连接错误${statusCode}`,
    icon: 'none'
  })
}

/**
 * 网络错误处理
 */
function handleNetworkError(error) {
  uni.showToast({
    title: '网络连接失败，请检查网络设置',
    icon: 'none'
  })
  
  // 可以在这里添加网络状态监听
  uni.onNetworkStatusChange((res) => {
    if (!res.isConnected) {
      uni.showToast({
        title: '网络已断开',
        icon: 'none'
      })
    }
  })
}

// 缓存配置
const CACHE_CONFIG = {
  defaultExpireTime: 5 * 60 * 1000, // 默认缓存时间 5 分钟
  storageKey: 'request_cache'
}

// 缓存管理
const cacheManager = {
  // 获取缓存
  get(key) {
    try {
      const cache = uni.getStorageSync(CACHE_CONFIG.storageKey) || {}
      const item = cache[key]
      if (item && item.expireTime > Date.now()) {
        return item.data
      }
      return null
    } catch (error) {
      console.error('Cache get error:', error)
      return null
    }
  },

  // 设置缓存
  set(key, data, expireTime = CACHE_CONFIG.defaultExpireTime) {
    try {
      const cache = uni.getStorageSync(CACHE_CONFIG.storageKey) || {}
      cache[key] = {
        data,
        expireTime: Date.now() + expireTime
      }
      uni.setStorageSync(CACHE_CONFIG.storageKey, cache)
    } catch (error) {
      console.error('Cache set error:', error)
    }
  },

  // 清除缓存
  clear(key) {
    try {
      if (key) {
        const cache = uni.getStorageSync(CACHE_CONFIG.storageKey) || {}
        delete cache[key]
        uni.setStorageSync(CACHE_CONFIG.storageKey, cache)
      } else {
        uni.removeStorageSync(CACHE_CONFIG.storageKey)
      }
    } catch (error) {
      console.error('Cache clear error:', error)
    }
  }
}

// 请求队列管理
const requestQueue = {
  queue: new Map(),
  
  // 添加请求到队列
  add(key, request) {
    if (!this.queue.has(key)) {
      this.queue.set(key, request)
    }
    return this.queue.get(key)
  },
  
  // 从队列中移除请求
  remove(key) {
    this.queue.delete(key)
  },
  
  // 获取队列中的请求
  get(key) {
    return this.queue.get(key)
  },
  
  // 生成请求key
  generateKey(options) {
    return `${options.method}:${options.url}:${JSON.stringify(options.data)}`
  }
}

/**
 * 通用请求函数 - 优化CORS处理
 * @param {Object} options - 请求配置
 * @returns {Promise} 请求结果
 */
export function request(options) {
  // 静默请求不显示加载状态
  if (options.loading !== false && !options.silent) {
    showLoading(options.loadingText);
  }

  // 构建请求URL - 修复URL构建逻辑
  let url;
  if (options.url.startsWith('http')) {
    // 如果是完整URL，直接使用
    url = options.url;
  } else {
    // 否则拼接baseURL
    url = (requestConfig.baseURL + options.url).replace(/([^:]\/)\/+/g, '$1');
  }

  // 调试信息 - 静默请求减少日志输出
  if (!options.silent) {
    console.log('🚀 发起请求:', {
      url: url,
      method: options.method || 'GET',
      data: options.data || {},
      header: options.header || requestConfig.header
    });
  } else {
    console.log('🔇 静默请求:', options.url);
  }

  return new Promise((resolve, reject) => {
    // 合并配置，确保URL正确，添加CORS支持
    const requestOptions = {
      ...requestConfig,
      ...options,
      url: url, // 使用构建好的URL
      // 添加CORS支持
      withCredentials: true,
      enableHttp2: false,
      enableQuic: false
    }

    // 请求拦截
    requestInterceptor.invoke(requestOptions).then(interceptedOptions => {
      // 检查是否需要缓存
      if (interceptedOptions.cache) {
        const cacheKey = `${interceptedOptions.method}:${interceptedOptions.url}:${JSON.stringify(interceptedOptions.data)}`
        const cachedData = cacheManager.get(cacheKey)
        if (cachedData) {
          return resolve(cachedData)
        }
      }
      
      // 检查是否是重复请求
      const requestKey = requestQueue.generateKey(interceptedOptions)
      if (interceptedOptions.preventDuplicate && requestQueue.get(requestKey)) {
        return requestQueue.get(requestKey)
      }
      
      // 创建新的请求
      const requestPromise = new Promise((innerResolve, innerReject) => {
        uni.request({
          ...interceptedOptions,
          success: (response) => {
            try {
              const result = responseInterceptor.success(response, options)
              // 如果需要缓存，保存结果
              if (interceptedOptions.cache) {
                const cacheKey = `${interceptedOptions.method}:${interceptedOptions.url}:${JSON.stringify(interceptedOptions.data)}`
                cacheManager.set(cacheKey, result, interceptedOptions.cacheExpireTime)
              }
              innerResolve(result)
            } catch (error) {
              innerReject(error)
            }
          },
          fail: (error) => {
            if (!options.silent) {
              console.error('请求失败:', error);
            }
            try {
              const result = responseInterceptor.fail(error, options)
              innerReject(result)
            } catch (error) {
              innerReject(error)
            }
          },
          complete: () => {
            // 请求完成后从队列中移除
            requestQueue.remove(requestKey)
            // 隐藏加载提示 - 静默请求不显示加载状态
            if (options.loading !== false && !options.silent) {
              hideLoading();
            }
          }
        })
      })
      
      // 将请求添加到队列
      if (interceptedOptions.preventDuplicate) {
        requestQueue.add(requestKey, requestPromise)
      }
      
      // 返回请求Promise
      requestPromise.then(resolve).catch(reject)
    }).catch(error => {
      console.error('拦截器错误:', error);
      reject(error);
    });
  });
}

/**
 * GET请求
 * @param {String} url - 请求地址
 * @param {Object} params - 请求参数
 * @param {Object} options - 其他配置
 * @returns {Promise} 请求结果
 */
export function get(url, params = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data: params,
    ...options
  });
}

/**
 * POST请求
 * @param {String} url - 请求地址
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他配置
 * @returns {Promise} 请求结果
 */
export function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
}

/**
 * PUT请求
 * @param {String} url - 请求地址
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他配置
 * @returns {Promise} 请求结果
 */
export function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
}

/**
 * DELETE请求
 * @param {String} url - 请求地址
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他配置
 * @returns {Promise} 请求结果
 */
export function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  });
} 