// models/order.js - 订单数据模型

/**
 * 订单模型类
 */
export default class OrderModel {
  // 订单状态枚举
  static STATUS = {
    PENDING: 0,  // 待支付
    PAID: 1,     // 已支付
    REFUNDED: 2, // 已退款
    FROZEN: 3,   // 已冻结
    PREAUTH: 4   // 预授权
  };
  
  // 支付方式枚举
  static PAYMENT_TYPES = {
    ALIPAY: 'alipay',     // 支付宝
    WXPAY: 'wxpay',       // 微信支付
    BANK: 'bank',         // 银联支付
    QQPAY: 'qqpay',       // QQ钱包
    OTHER: ''             // 其他
  };
  
  /**
   * 构造函数
   * @param {Object} data - 订单数据
   */
  constructor(data = {}) {
    this.trade_no = data.trade_no || '';           // 平台订单号
    this.out_trade_no = data.out_trade_no || '';   // 商户订单号
    this.name = data.name || '';                   // 商品名称
    this.money = data.money || '0.00';             // 订单金额
    this.status = data.status !== undefined ? data.status : OrderModel.STATUS.PENDING; // 订单状态
    this.addtime = data.addtime || '';             // 创建时间
    this.endtime = data.endtime || '';             // 支付时间
    this.type = data.type || '';                   // 支付方式
    this.buyer = data.buyer || '';                 // 买家信息
    this.param = data.param || '';                 // 自定义参数
    this.refundmoney = data.refundmoney || '0.00'; // 退款金额
  }
  
  /**
   * 获取订单状态文本
   * @returns {String} 状态文本
   */
  getStatusText() {
    const statusMap = {
      [OrderModel.STATUS.PENDING]: '待支付',
      [OrderModel.STATUS.PAID]: '已支付',
      [OrderModel.STATUS.REFUNDED]: '已退款',
      [OrderModel.STATUS.FROZEN]: '已冻结',
      [OrderModel.STATUS.PREAUTH]: '预授权'
    };
    return statusMap[this.status] || '未知状态';
  }
  
  /**
   * 判断订单是否可支付
   * @returns {Boolean} 是否可支付
   */
  canPay() {
    return this.status === OrderModel.STATUS.PENDING;
  }
  
  /**
   * 判断订单是否可退款
   * @returns {Boolean} 是否可退款
   */
  canRefund() {
    return this.status === OrderModel.STATUS.PAID;
  }
  
  /**
   * 创建默认订单
   * @param {String} productName - 商品名称
   * @param {Number|String} amount - 金额
   * @returns {OrderModel} 订单模型实例
   */
  static createDefault(productName, amount) {
    const now = new Date();
    const orderNo = 'OUT' + now.getTime() + Math.floor(Math.random() * 1000);
    
    return new OrderModel({
      out_trade_no: orderNo,
      name: productName,
      money: amount,
      addtime: now.toISOString().replace('T', ' ').split('.')[0]
    });
  }
  
  /**
   * 从API响应数据转换为模型数组
   * @param {Array} data - API响应数据
   * @returns {Array<OrderModel>} 订单模型数组
   */
  static fromApiList(data) {
    if (!Array.isArray(data)) return [];
    
    return data.map(item => new OrderModel(item));
  }
} 