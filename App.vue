<template>
  <view id="app">
  </view>
</template>

<script>
	import globalWebSocketService from '@/utils/globalWebSocketService.js'
	import voiceManager from '@/utils/voiceManager.js'

	export default {

		onLaunch: function() {
			console.log('🚀 App Launch')

			// 启动全局 WebSocket 语音播报服务
			this.initGlobalServices()
		},

		onShow: function() {
			console.log('👁️ App Show')

			// 应用从后台回到前台，恢复 WebSocket 连接
			try {
				console.log('🔄 恢复全局WebSocket服务')
				globalWebSocketService.resume()

				// 确保服务正常运行
				const status = globalWebSocketService.getStatus()
				console.log('📊 WebSocket服务状态:', status)

				if (!status.isConnected && status.isStarted) {
					console.log('🔄 尝试重新连接WebSocket')
					globalWebSocketService.connect()
				}
			} catch (error) {
				console.error('❌ 恢复WebSocket服务失败:', error)
			}
		},

		onHide: function() {
			console.log('🙈 App Hide')

			// 应用进入后台，保持 WebSocket 连接但可以降低频率
			globalWebSocketService.pause()
		},

		onError: function(error) {
			console.error('❌ App Error:', error)
			// 可以在这里添加错误上报逻辑
		},

		methods: {
			/**
			 * 初始化全局服务
			 */
			async initGlobalServices() {
				try {
					console.log('🎵 初始化全局语音播报服务')

					// 检查语音设置
					const voiceStatus = voiceManager.getStatus()
					console.log('🔊 当前语音设置:', voiceStatus)

					// 启动全局 WebSocket 服务
					await globalWebSocketService.start()

					console.log('✅ 全局服务初始化完成')

				} catch (error) {
					console.error('❌ 全局服务初始化失败:', error)
				}
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	page {
		font-family: 'Segoe UI', sans-serif;
		background-color: #f5f5f5;
	}

	/* 为底部导航腾出空间 */
	.page-container {
		padding-bottom: 120rpx;
	}

	/* 全局应用容器 */
	#app {
		position: relative;
		width: 100%;
		height: 100%;
	}
</style>
