<template>
  <view class="staff-qrcode-container">
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <view class="navbar-content">
        <view class="navbar-left" @tap="goBack">
          <text class="back-icon">←</text>
          <text class="navbar-title">我的收款码</text>
        </view>
        
        <view class="navbar-right">
          <view class="action-btn" @tap="refreshQRCode">
            <text class="action-icon">🔄</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 员工信息卡片 -->
    <view class="staff-info-card">
      <view class="staff-avatar">
        <text class="avatar-text">{{ staffInfo?.name?.charAt(0) || 'S' }}</text>
      </view>
      <view class="staff-details">
        <text class="staff-name">{{ staffInfo?.name || '员工' }}</text>
        <text class="staff-role">{{ staffInfo?.role || '收银员' }}</text>
        <text class="staff-id">员工编号：{{ staffInfo?.account || 'N/A' }}</text>
      </view>
    </view>

    <!-- 收款码区域 -->
    <view class="qrcode-section">
      <view class="qrcode-card">
        <view class="qrcode-header">
          <text class="qrcode-title">专属收款码</text>
          <text class="qrcode-subtitle">客户扫码向我付款</text>
        </view>
        
        <view class="qrcode-container">
          <view v-if="isGeneratingQR" class="qrcode-loading">
            <view class="loading-spinner"></view>
            <text class="loading-text">生成中...</text>
          </view>
          
          <view v-else class="qrcode-wrapper">
            <image
              :src="qrCodeImage || '/static/images/qrcode-placeholder.png'"
              class="qrcode-image"
              mode="aspectFit"
              @longpress="saveQRCodeImage"
            ></image>
          </view>
        </view>
        
        <view class="qrcode-info">
          <text class="qrcode-amount">收款金额由客户输入</text>
          <text class="qrcode-tip">长按二维码保存图片</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-section">
      <button class="action-button primary" @tap="saveQRCodeImage">
        <text class="button-icon">💾</text>
        <text class="button-text">保存收款码</text>
      </button>
      
      <button class="action-button secondary" @tap="shareQRCode">
        <text class="button-icon">📤</text>
        <text class="button-text">分享收款码</text>
      </button>
    </view>

    <!-- 收款统计 -->
    <view class="stats-section">
      <view class="stats-title">今日收款统计</view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{ todayStats.amount || '0.00' }}</text>
          <text class="stat-label">收款金额</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ todayStats.count || '0' }}</text>
          <text class="stat-label">收款笔数</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { request } from '@/utils/request.js'
import { generateQRCode } from '@/utils/qrcode.js'

export default {
  // 🔧 移除WebSocket Mixin，改用语音设置页面统一管理
  // mixins: [websocketMixin],
  name: 'StaffQRCode',
  data() {
    return {
      staffInfo: null,
      qrCodeUrl: '',
      qrCodeImage: '',
      isGeneratingQR: false,
      todayStats: {
        amount: '0.00',
        count: 0
      }
    }
  },
  
  onLoad() {
    console.log('📱 员工收款码页面加载')
    this.initPage()
  },
  
  onShow() {
    // 页面显示时刷新数据
    this.loadTodayStats()
  },
  
  methods: {
    // 初始化页面
    initPage() {
      this.checkLoginStatus()
      this.loadStaffInfo()
      this.loadPageData()
    },
    
    // 检查登录状态
    checkLoginStatus() {
      const staffToken = uni.getStorageSync('staff_token')
      const staffInfo = uni.getStorageSync('staff_info')
      
      if (!staffToken || !staffInfo) {
        console.log('❌ 员工未登录，跳转到登录页')
        uni.reLaunch({
          url: '/pages/staff-login/index'
        })
        return false
      }
      
      return true
    },
    
    // 加载员工信息
    loadStaffInfo() {
      const staffInfo = uni.getStorageSync('staff_info')
      if (staffInfo) {
        this.staffInfo = staffInfo
        console.log('✅ 员工信息加载成功:', staffInfo)
      }
    },
    
    // 加载页面数据
    async loadPageData() {
      if (!this.checkLoginStatus()) return
      
      try {
        await Promise.all([
          this.generateStaffQRCode(),
          this.loadTodayStats()
        ])
        
        console.log('✅ 员工收款码页面数据加载完成')
        
      } catch (error) {
        console.error('❌ 加载员工收款码页面数据失败:', error)
        uni.showToast({
          title: '数据加载失败',
          icon: 'none'
        })
      }
    },
    
    // 生成员工专属收款码
    async generateStaffQRCode() {
      try {
        this.isGeneratingQR = true
        console.log('🔄 生成员工收款码...')
        
        // 调用后端API生成员工收款码URL
        const response = await request({
          url: '/user/staff.php?act=generateStaffQR',
          method: 'POST',
          data: {
            staff_id: this.staffInfo?.id
          }
        })
        
        if (response && response.code === 0) {
          this.qrCodeUrl = response.data.qr_url
          console.log('✅ 员工收款码URL生成成功:', this.qrCodeUrl)
          
          // 生成二维码图片
          await this.generateQRCodeImage()
          
        } else {
          throw new Error(response?.msg || '生成收款码失败')
        }
        
      } catch (error) {
        console.error('❌ 生成员工收款码失败:', error)
        
        // 使用备用方案
        await this.generateFallbackQRCode()
        
      } finally {
        this.isGeneratingQR = false
      }
    },
    
    // 生成备用收款码
    async generateFallbackQRCode() {
      try {
        const merchantUid = uni.getStorageSync('staff_merchant_uid')
        if (merchantUid && this.staffInfo?.id) {
          // 使用简化的URL格式作为备用
          this.qrCodeUrl = `http://ceshi.huisas.com/paypage/?uid=${merchantUid}&staff_id=${this.staffInfo.id}`
          console.log('⚠️ 使用备用员工收款码URL:', this.qrCodeUrl)
          
          // 生成二维码图片
          await this.generateQRCodeImage()
        }
      } catch (error) {
        console.error('❌ 生成备用收款码失败:', error)
      }
    },
    
    // 生成二维码图片
    async generateQRCodeImage() {
      try {
        if (!this.qrCodeUrl) return
        
        // 使用二维码生成工具
        const qrCodeImage = await generateQRCode(this.qrCodeUrl, {
          width: 300,
          height: 300,
          colorDark: '#000000',
          colorLight: '#FFFFFF'
        })
        
        this.qrCodeImage = qrCodeImage
        console.log('✅ 二维码图片生成成功')
        
      } catch (error) {
        console.error('❌ 生成二维码图片失败:', error)
      }
    },
    
    // 加载今日统计
    async loadTodayStats() {
      try {
        const response = await request({
          url: '/user/staff.php?act=getTodayStats',
          method: 'POST',
          data: {
            staff_id: this.staffInfo?.id
          }
        })
        
        if (response && response.code === 0) {
          this.todayStats = response.data || this.todayStats
          console.log('✅ 今日统计加载成功')
        }
      } catch (error) {
        console.error('❌ 加载今日统计失败:', error)
      }
    },
    
    // 刷新收款码
    async refreshQRCode() {
      uni.showLoading({
        title: '刷新中...'
      })
      
      try {
        await this.generateStaffQRCode()
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        })
      } catch (error) {
        uni.showToast({
          title: '刷新失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },
    
    // 保存收款码图片
    async saveQRCodeImage() {
      if (!this.qrCodeImage) {
        uni.showToast({
          title: '收款码未生成',
          icon: 'none'
        })
        return
      }
      
      try {
        // 保存图片到相册
        await uni.saveImageToPhotosAlbum({
          filePath: this.qrCodeImage
        })
        
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('❌ 保存图片失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      }
    },
    
    // 分享收款码
    shareQRCode() {
      if (!this.qrCodeImage) {
        uni.showToast({
          title: '收款码未生成',
          icon: 'none'
        })
        return
      }
      
      uni.showActionSheet({
        itemList: ['保存到相册', '发送给朋友'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.saveQRCodeImage()
          } else if (res.tapIndex === 1) {
            // 这里可以集成分享功能
            uni.showToast({
              title: '分享功能开发中',
              icon: 'none'
            })
          }
        }
      })
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.staff-qrcode-container {
  min-height: 100vh;
  background: #f5f6fa;
}

.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  color: #ffffff;
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
}

.navbar-right {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 28rpx;
}

.staff-info-card {
  margin: -20rpx 40rpx 40rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.staff-avatar {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.avatar-text {
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
}

.staff-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.staff-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.staff-role {
  font-size: 28rpx;
  color: #666666;
}

.staff-id {
  font-size: 24rpx;
  color: #999999;
}

.qrcode-section {
  padding: 0 40rpx 40rpx;
}

.qrcode-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.qrcode-header {
  margin-bottom: 40rpx;
}

.qrcode-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
}

.qrcode-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666666;
}

.qrcode-container {
  margin-bottom: 40rpx;
}

.qrcode-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

.qrcode-wrapper {
  display: flex;
  justify-content: center;
}

.qrcode-image {
  width: 400rpx;
  height: 400rpx;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
}

.qrcode-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.qrcode-amount {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.qrcode-tip {
  font-size: 24rpx;
  color: #999999;
}

.action-section {
  padding: 0 40rpx 40rpx;
  display: flex;
  gap: 20rpx;
}

.action-button {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.secondary {
  background: #ffffff;
  color: #667eea;
  border: 2rpx solid #667eea;
}

.button-icon {
  font-size: 32rpx;
}

.button-text {
  font-size: 32rpx;
  font-weight: 500;
}

.stats-section {
  padding: 0 40rpx 40rpx;
}

.stats-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
}

.stats-grid {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  flex: 1;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stat-value {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
}

.stat-label {
  font-size: 28rpx;
  color: #666666;
}
</style>
