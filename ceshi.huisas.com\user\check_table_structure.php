<?php
/**
 * 检查数据库表结构
 */
include("../includes/common.php");

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

try {
    $result = [];
    
    // 检查 pay_qrcode_config 表结构
    $tableStructure = $DB->getAll("DESCRIBE {$dbconfig['dbqz']}_qrcode_config");
    $result['table_structure'] = $tableStructure;
    
    // 检查表是否存在
    $tableExists = $DB->getColumn("SHOW TABLES LIKE '{$dbconfig['dbqz']}_qrcode_config'");
    $result['table_exists'] = !empty($tableExists);
    
    // 检查表前缀
    $result['table_prefix'] = $dbconfig['dbqz'];
    $result['full_table_name'] = "{$dbconfig['dbqz']}_qrcode_config";
    
    // 获取表中的数据示例
    $sampleData = $DB->getAll("SELECT * FROM {$dbconfig['dbqz']}_qrcode_config ORDER BY id DESC LIMIT 3");
    $result['sample_data'] = $sampleData;
    
    // 检查字段是否存在
    $fields = [];
    foreach($tableStructure as $field) {
        $fields[] = $field['Field'];
    }
    $result['fields'] = $fields;
    
    // 检查必需字段
    $requiredFields = ['id', 'uid', 'name', 'qr_style', 'amount', 'description', 'status', 'addtime'];
    $missingFields = [];
    foreach($requiredFields as $field) {
        if(!in_array($field, $fields)) {
            $missingFields[] = $field;
        }
    }
    $result['missing_fields'] = $missingFields;
    
    echo json_encode(['code' => 0, 'data' => $result]);
    
} catch (Exception $e) {
    echo json_encode(['code' => -1, 'msg' => '检查失败: ' . $e->getMessage()]);
}
?>
