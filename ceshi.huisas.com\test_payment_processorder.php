<?php
/**
 * 测试正式支付回调的 processOrder 修复
 */

include_once './includes/common.php';

// 测试订单号
$test_trade_no = '2025072718015654664';

echo "<h2>🧪 测试正式支付回调的 processOrder 修复</h2>";

// 获取订单信息
$order = $DB->getRow("SELECT * FROM pre_order WHERE trade_no=:trade_no limit 1", [':trade_no'=>$test_trade_no]);

if(!$order) {
    echo "<p style='color: red;'>❌ 订单不存在: $test_trade_no</p>";
    exit;
}

echo "<h3>📋 订单信息</h3>";
echo "<ul>";
echo "<li><strong>订单号:</strong> {$order['trade_no']}</li>";
echo "<li><strong>当前状态:</strong> {$order['status']} (" . ($order['status'] == 1 ? '已支付' : '未支付') . ")</li>";
echo "<li><strong>金额:</strong> {$order['money']} 元</li>";
echo "<li><strong>支付方式:</strong> {$order['typename']}</li>";
echo "</ul>";

echo "<h3>🔧 测试场景</h3>";

// 场景1：模拟订单状态为1的情况下调用 processOrder
echo "<h4>场景1: 订单状态为1时调用 \\lib\\Payment::processOrder()</h4>";

try {
    // 确保订单状态为1
    if($order['status'] != 1) {
        $DB->exec("UPDATE pre_order SET status=1 WHERE trade_no='$test_trade_no'");
        $order['status'] = 1;
        echo "<p>✅ 已将订单状态设置为1</p>";
    }
    
    echo "<p>🚀 开始调用 \\lib\\Payment::processOrder()...</p>";
    
    // 调用修复后的 processOrder
    \lib\Payment::processOrder(true, $order, '9987704501507524374528', '2088602002631734');
    
    echo "<p>✅ processOrder 调用完成</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<h3>📊 检查日志</h3>";
echo "<p>请检查支付日志文件，确认是否有以下日志条目：</p>";
echo "<ul>";
echo "<li>✅ order_already_paid_direct</li>";
echo "<li>✅ calling_processOrder</li>";
echo "<li>✅ full_order_data</li>";
echo "<li>✅ websocket_check_conditions</li>";
echo "<li>✅ websocket_notify_start</li>";
echo "<li>✅ websocket_notify_result</li>";
echo "</ul>";

echo "<h3>🎯 预期结果</h3>";
echo "<p>修复后，即使订单状态已经是1，也应该触发WebSocket通知和语音播报。</p>";

echo "<hr>";
echo "<p><strong>测试完成时间:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
